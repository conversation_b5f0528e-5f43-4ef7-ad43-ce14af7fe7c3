{"prompt": "Write a python function to remove first and last occurrence of a given character from the string.", "test": ["assert remove_Occ(\"hello\",\"l\") == \"heo\"", "assert remove_Occ(\"abcda\",\"a\") == \"bcd\"", "assert remove_Occ(\"PHP\",\"P\") == \"H\""], "code": "def remove_Occ(s,ch): \r\n    for i in range(len(s)): \r\n        if (s[i] == ch): \r\n            s = s[0 : i] + s[i + 1:] \r\n            break\r\n    for i in range(len(s) - 1,-1,-1):  \r\n        if (s[i] == ch): \r\n            s = s[0 : i] + s[i + 1:] \r\n            break\r\n    return s ", "task_id": 11}
{"prompt": "Write a function to sort a given matrix in ascending order according to the sum of its rows.", "test": ["assert sort_matrix([[1, 2, 3], [2, 4, 5], [1, 1, 1]])==[[1, 1, 1], [1, 2, 3], [2, 4, 5]]", "assert sort_matrix([[1, 2, 3], [-2, 4, -5], [1, -1, 1]])==[[-2, 4, -5], [1, -1, 1], [1, 2, 3]]", "assert sort_matrix([[5,8,9],[6,4,3],[2,1,4]])==[[2, 1, 4], [6, 4, 3], [5, 8, 9]]"], "code": "def sort_matrix(M):\r\n    result = sorted(M, key=sum)\r\n    return result", "task_id": 12}
{"prompt": "Write a function to count the most common words in a dictionary.", "test": ["assert count_common(['red','green','black','pink','black','white','black','eyes','white','black','orange','pink','pink','red','red','white','orange','white',\"black\",'pink','green','green','pink','green','pink','white','orange',\"orange\",'red']) == [('pink', 6), ('black', 5), ('white', 5), ('red', 4)]", "assert count_common(['one', 'two', 'three', 'four', 'five', 'one', 'two', 'one', 'three', 'one']) == [('one', 4), ('two', 2), ('three', 2), ('four', 1)]", "assert count_common(['Facebook', 'Apple', 'Amazon', 'Netflix', 'Google', 'Apple', 'Netflix', 'Amazon']) == [('Apple', 2), ('Amazon', 2), ('Netflix', 2), ('Facebook', 1)]"], "code": "from collections import Counter\r\ndef count_common(words):\r\n  word_counts = Counter(words)\r\n  top_four = word_counts.most_common(4)\r\n  return (top_four)\r\n", "task_id": 13}
{"prompt": "Write a python function to find the volume of a triangular prism.", "test": ["assert find_Volume(10,8,6) == 240", "assert find_Volume(3,2,2) == 6", "assert find_Volume(1,2,1) == 1"], "code": "def find_Volume(l,b,h) : \r\n    return ((l * b * h) / 2) ", "task_id": 14}
{"prompt": "Write a function to split a string at lowercase letters.", "test": ["assert split_lowerstring(\"AbCd\")==['bC','d']", "assert split_lowerstring(\"Python\")==['y', 't', 'h', 'o', 'n']", "assert split_lowerstring(\"Programming\")==['r', 'o', 'g', 'r', 'a', 'm', 'm', 'i', 'n', 'g']"], "code": "import re\r\ndef split_lowerstring(text):\r\n return (re.findall('[a-z][^a-z]*', text))", "task_id": 15}
{"prompt": "Write a function to find sequences of lowercase letters joined with an underscore.", "test": ["assert text_lowercase_underscore(\"aab_cbbbc\")==('Found a match!')", "assert text_lowercase_underscore(\"aab_Abbbc\")==('Not matched!')", "assert text_lowercase_underscore(\"Aaab_abbbc\")==('Not matched!')"], "code": "import re\r\ndef text_lowercase_underscore(text):\r\n        patterns = '^[a-z]+_[a-z]+$'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return('Not matched!')", "task_id": 16}
{"prompt": "Write a function to find the perimeter of a square.", "test": ["assert square_perimeter(10)==40", "assert square_perimeter(5)==20", "assert square_perimeter(4)==16"], "code": "def square_perimeter(a):\r\n  perimeter=4*a\r\n  return perimeter", "task_id": 17}
{"prompt": "Write a function to remove characters from the first string which are present in the second string.", "test": ["assert remove_dirty_chars(\"probasscurve\", \"pros\") == 'bacuve'", "assert remove_dirty_chars(\"digitalindia\", \"talent\") == 'digiidi'", "assert remove_dirty_chars(\"exoticmiles\", \"toxic\") == 'emles' "], "code": "NO_OF_CHARS = 256\r\ndef str_to_list(string): \r\n\ttemp = [] \r\n\tfor x in string: \r\n\t\ttemp.append(x) \r\n\treturn temp \r\ndef lst_to_string(List): \r\n\treturn ''.join(List) \r\ndef get_char_count_array(string): \r\n\tcount = [0] * NO_OF_CHARS \r\n\tfor i in string: \r\n\t\tcount[ord(i)] += 1\r\n\treturn count \r\ndef remove_dirty_chars(string, second_string): \r\n\tcount = get_char_count_array(second_string) \r\n\tip_ind = 0\r\n\tres_ind = 0\r\n\ttemp = '' \r\n\tstr_list = str_to_list(string) \r\n\twhile ip_ind != len(str_list): \r\n\t\ttemp = str_list[ip_ind] \r\n\t\tif count[ord(temp)] == 0: \r\n\t\t\tstr_list[res_ind] = str_list[ip_ind] \r\n\t\t\tres_ind += 1\r\n\t\tip_ind+=1\r\n\treturn lst_to_string(str_list[0:res_ind]) ", "task_id": 18}
{"prompt": "Write a function to find whether a given array of integers contains any duplicate element.", "test": ["assert test_duplicate(([1,2,3,4,5]))==False", "assert test_duplicate(([1,2,3,4, 4]))==True", "assert test_duplicate([1,1,2,2,3,3,4,4,5])==True"], "code": "def test_duplicate(arraynums):\r\n    nums_set = set(arraynums)    \r\n    return len(arraynums) != len(nums_set)     ", "task_id": 19}
{"prompt": "Write a function to check if the given number is woodball or not.", "test": ["assert is_woodall(383) == True", "assert is_woodall(254) == False", "assert is_woodall(200) == False"], "code": "def is_woodall(x): \r\n\tif (x % 2 == 0): \r\n\t\treturn False\r\n\tif (x == 1): \r\n\t\treturn True\r\n\tx = x + 1 \r\n\tp = 0\r\n\twhile (x % 2 == 0): \r\n\t\tx = x/2\r\n\t\tp = p + 1\r\n\t\tif (p == x): \r\n\t\t\treturn True\r\n\treturn False", "task_id": 20}
{"prompt": "Write a function to find m number of multiples of n.", "test": ["assert multiples_of_num(4,3)== [3,6,9,12]", "assert multiples_of_num(2,5)== [5,10]", "assert multiples_of_num(9,2)== [2,4,6,8,10,12,14,16,18]"], "code": "def multiples_of_num(m,n): \r\n    multiples_of_num= list(range(n,(m+1)*n, n)) \r\n    return list(multiples_of_num)", "task_id": 21}
{"prompt": "Write a function to find the first duplicate element in a given array of integers.", "test": ["assert find_first_duplicate(([1, 2, 3, 4, 4, 5]))==4", "assert find_first_duplicate([1, 2, 3, 4])==-1", "assert find_first_duplicate([1, 1, 2, 3, 3, 2, 2])==1"], "code": "def find_first_duplicate(nums):\r\n    num_set = set()\r\n    no_duplicate = -1\r\n\r\n    for i in range(len(nums)):\r\n\r\n        if nums[i] in num_set:\r\n            return nums[i]\r\n        else:\r\n            num_set.add(nums[i])\r\n\r\n    return no_duplicate", "task_id": 22}
{"prompt": "Write a python function to find the maximum sum of elements of list in a list of lists.", "test": ["assert maximum_Sum([[1,2,3],[4,5,6],[10,11,12],[7,8,9]]) == 33", "assert maximum_Sum([[0,1,1],[1,1,2],[3,2,1]]) == 6", "assert maximum_Sum([[0,1,3],[1,2,1],[9,8,2],[0,1,0],[6,4,8]]) == 19"], "code": "def maximum_Sum(list1): \r\n    maxi = -100000\r\n    for x in list1: \r\n        sum = 0 \r\n        for y in x: \r\n            sum+= y      \r\n        maxi = max(sum,maxi)     \r\n    return maxi ", "task_id": 23}
{"prompt": "Write a function to convert the given binary number to its decimal equivalent.", "test": ["assert binary_to_decimal(100) == 4", "assert binary_to_decimal(1011) == 11", "assert binary_to_decimal(1101101) == 109"], "code": "def binary_to_decimal(binary): \r\n    binary1 = binary \r\n    decimal, i, n = 0, 0, 0\r\n    while(binary != 0): \r\n        dec = binary % 10\r\n        decimal = decimal + dec * pow(2, i) \r\n        binary = binary//10\r\n        i += 1\r\n    return (decimal)", "task_id": 24}
{"prompt": "Write a python function to find the product of non-repeated elements in a given array.", "test": ["assert find_Product([1,1,2,3],4) == 6", "assert find_Product([1,2,3,1,1],5) == 6", "assert find_Product([1,1,4,5,6],5) == 120"], "code": "def find_Product(arr,n): \r\n    arr.sort() \r\n    prod = 1\r\n    for i in range(0,n,1): \r\n        if (arr[i - 1] != arr[i]): \r\n            prod = prod * arr[i] \r\n    return prod; ", "task_id": 25}
{"prompt": "Write a function to check if the given tuple list has all k elements.", "test": ["assert check_k_elements([(4, 4), (4, 4, 4), (4, 4), (4, 4, 4, 4), (4, )], 4) == True", "assert check_k_elements([(7, 7, 7), (7, 7)], 7) == True", "assert check_k_elements([(9, 9), (9, 9, 9, 9)], 7) == False"], "code": "def check_k_elements(test_list, K):\r\n  res = True\r\n  for tup in test_list:\r\n    for ele in tup:\r\n      if ele != K:\r\n        res = False\r\n  return (res) ", "task_id": 26}
{"prompt": "Write a python function to remove all digits from a list of strings.", "test": ["assert remove(['4words', '3letters', '4digits']) == ['words', 'letters', 'digits']", "assert remove(['28Jan','12Jan','11Jan']) == ['Jan','Jan','Jan']", "assert remove(['wonder1','wonder2','wonder3']) == ['wonder','wonder','wonder']"], "code": "import re  \r\ndef remove(list): \r\n    pattern = '[0-9]'\r\n    list = [re.sub(pattern, '', i) for i in list] \r\n    return list", "task_id": 27}
{"prompt": "Write a python function to find binomial co-efficient.", "test": ["assert binomial_Coeff(5,2) == 10", "assert binomial_Coeff(4,3) == 4", "assert binomial_Coeff(3,2) == 3"], "code": "def binomial_Coeff(n,k): \r\n    if k > n : \r\n       return 0\r\n    if k==0 or k ==n : \r\n        return 1 \r\n    return binomial_Coeff(n-1,k-1) + binomial_Coeff(n-1,k) ", "task_id": 28}
{"prompt": "Write a python function to find the element occurring odd number of times.", "test": ["assert get_Odd_Occurrence([1,2,3,1,2,3,1],7) == 1", "assert get_Odd_Occurrence([1,2,3,2,3,1,3],7) == 3", "assert get_Odd_Occurrence([2,3,5,4,5,2,4,3,5,2,4,4,2],13) == 5"], "code": "def get_Odd_Occurrence(arr,arr_size):   \r\n    for i in range(0,arr_size): \r\n        count = 0\r\n        for j in range(0,arr_size): \r\n            if arr[i] == arr[j]: \r\n                count+=1     \r\n        if (count % 2 != 0): \r\n            return arr[i]     \r\n    return -1", "task_id": 29}
{"prompt": "Write a python function to count all the substrings starting and ending with same characters.", "test": ["assert count_Substring_With_Equal_Ends(\"abc\") == 3", "assert count_Substring_With_Equal_Ends(\"abcda\") == 6", "assert count_Substring_With_Equal_Ends(\"ab\") == 2"], "code": "def check_Equality(s): \r\n    return (ord(s[0]) == ord(s[len(s) - 1])); \r\ndef count_Substring_With_Equal_Ends(s): \r\n    result = 0; \r\n    n = len(s); \r\n    for i in range(n): \r\n        for j in range(1,n-i+1):  \r\n            if (check_Equality(s[i:i+j])): \r\n                result+=1; \r\n    return result; ", "task_id": 30}
{"prompt": "Write a function to find the top k integers that occur most frequently from given lists of sorted and distinct integers using heap queue algorithm.", "test": ["assert func([[1, 2, 6], [1, 3, 4, 5, 7, 8], [1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12]],3)==[5, 7, 1]", "assert func([[1, 2, 6], [1, 3, 4, 5, 7, 8], [1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12]],1)==[1]", "assert func([[1, 2, 6], [1, 3, 4, 5, 7, 8], [1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12]],5)==[6, 5, 7, 8, 1]"], "code": "def func(nums, k):\r\n    import collections\r\n    d = collections.defaultdict(int)\r\n    for row in nums:\r\n        for i in row:\r\n            d[i] += 1\r\n    temp = []\r\n    import heapq\r\n    for key, v in d.items():\r\n        if len(temp) < k:\r\n            temp.append((v, key))\r\n            if len(temp) == k:\r\n                heapq.heapify(temp)\r\n        else:\r\n            if v > temp[0][0]:\r\n                heapq.heappop(temp)\r\n                heapq.heappush(temp, (v, key))\r\n    result = []\r\n    while temp:\r\n        v, key = heapq.heappop(temp)\r\n        result.append(key)\r\n    return result", "task_id": 31}
{"prompt": "Write a python function to find the largest prime factor of a given number.", "test": ["assert max_Prime_Factors(15) == 5", "assert max_Prime_Factors(6) == 3", "assert max_Prime_Factors(2) == 2"], "code": "import math \r\ndef max_Prime_Factors (n): \r\n    maxPrime = -1 \r\n    while n%2 == 0: \r\n        maxPrime = 2\r\n        n >>= 1    \r\n    for i in range(3,int(math.sqrt(n))+1,2): \r\n        while n % i == 0: \r\n            maxPrime = i \r\n            n = n / i \r\n    if n > 2: \r\n        maxPrime = n  \r\n    return int(maxPrime)", "task_id": 32}
{"prompt": "Write a python function to convert a decimal number to binary number.", "test": ["assert decimal_To_Binary(10) == 1010", "assert decimal_To_Binary(1) == 1", "assert decimal_To_Binary(20) == 10100"], "code": "def decimal_To_Binary(N): \r\n    B_Number = 0\r\n    cnt = 0\r\n    while (N != 0): \r\n        rem = N % 2\r\n        c = pow(10,cnt)  \r\n        B_Number += rem*c  \r\n        N //= 2 \r\n        cnt += 1\r\n    return B_Number  ", "task_id": 33}
{"prompt": "Write a python function to find the missing number in a sorted array.", "test": ["assert find_missing([1,2,3,5],4) == 4", "assert find_missing([1,3,4,5],4) == 2", "assert find_missing([1,2,3,5,6,7],5) == 4"], "code": "def find_missing(ar,N): \r\n    l = 0\r\n    r = N - 1\r\n    while (l <= r):  \r\n        mid = (l + r) / 2\r\n        mid= int (mid) \r\n        if (ar[mid] != mid + 1 and ar[mid - 1] == mid): \r\n            return (mid + 1)  \r\n        elif (ar[mid] != mid + 1): \r\n            r = mid - 1 \r\n        else: \r\n            l = mid + 1\r\n    return (-1) ", "task_id": 34}
{"prompt": "Write a function to find the n-th rectangular number.", "test": ["assert find_rect_num(4) == 20", "assert find_rect_num(5) == 30", "assert find_rect_num(6) == 42"], "code": "def find_rect_num(n):\r\n  return n*(n + 1) ", "task_id": 35}
{"prompt": "Write a python function to find the nth digit in the proper fraction of two given numbers.", "test": ["assert find_Nth_Digit(1,2,1) == 5", "assert find_Nth_Digit(3,5,1) == 6", "assert find_Nth_Digit(5,6,5) == 3"], "code": "def find_Nth_Digit(p,q,N) :  \r\n    while (N > 0) : \r\n        N -= 1;  \r\n        p *= 10;  \r\n        res = p // q;  \r\n        p %= q;  \r\n    return res;  ", "task_id": 36}
{"prompt": "Write a function to sort a given mixed list of integers and strings.", "test": ["assert sort_mixed_list([19,'red',12,'green','blue', 10,'white','green',1])==[1, 10, 12, 19, 'blue', 'green', 'green', 'red', 'white']", "assert sort_mixed_list([19,'red',12,'green','blue', 10,'white','green',1])==[1, 10, 12, 19, 'blue', 'green', 'green', 'red', 'white']", "assert sort_mixed_list([19,'red',12,'green','blue', 10,'white','green',1])==[1, 10, 12, 19, 'blue', 'green', 'green', 'red', 'white']"], "code": "def sort_mixed_list(mixed_list):\r\n    int_part = sorted([i for i in mixed_list if type(i) is int])\r\n    str_part = sorted([i for i in mixed_list if type(i) is str])\r\n    return int_part + str_part", "task_id": 37}
{"prompt": "Write a function to find the division of first even and odd number of a given list.", "test": ["assert div_even_odd([1,3,5,7,4,1,6,8])==4", "assert div_even_odd([1,2,3,4,5,6,7,8,9,10])==2", "assert div_even_odd([1,5,7,9,10])==10"], "code": "def div_even_odd(list1):\r\n    first_even = next((el for el in list1 if el%2==0),-1)\r\n    first_odd = next((el for el in list1 if el%2!=0),-1)\r\n    return (first_even/first_odd)", "task_id": 38}
{"prompt": "Write a function to check if the letters of a given string can be rearranged so that two characters that are adjacent to each other are different.", "test": ["assert rearange_string(\"aab\")==('aba')", "assert rearange_string(\"aabb\")==('abab')", "assert rearange_string(\"abccdd\")==('cdabcd')"], "code": "import heapq\r\nfrom collections import Counter\r\ndef rearange_string(S):\r\n    ctr = Counter(S)\r\n    heap = [(-value, key) for key, value in ctr.items()]\r\n    heapq.heapify(heap)\r\n    if (-heap[0][0]) * 2 > len(S) + 1: \r\n        return \"\"\r\n    ans = []\r\n    while len(heap) >= 2:\r\n        nct1, char1 = heapq.heappop(heap)\r\n        nct2, char2 = heapq.heappop(heap)\r\n        ans.extend([char1, char2])\r\n        if nct1 + 1: heapq.heappush(heap, (nct1 + 1, char1))\r\n        if nct2 + 1: heapq.heappush(heap, (nct2 + 1, char2))\r\n    return \"\".join(ans) + (heap[0][1] if heap else \"\")", "task_id": 39}
{"prompt": "Write a function to find frequency of the elements in a given list of lists using collections module.", "test": ["assert freq_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]])==({2: 3, 1: 2, 5: 2, 3: 1, 4: 1, 6: 1, 7: 1, 9: 1})", "assert freq_element([[1,2,3,4],[5,6,7,8],[9,10,11,12]])==({1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 1, 10: 1, 11: 1, 12: 1})", "assert freq_element([[15,20,30,40],[80,90,100,110],[30,30,80,90]])==({30: 3, 80: 2, 90: 2, 15: 1, 20: 1, 40: 1, 100: 1, 110: 1})"], "code": "from collections import Counter\r\nfrom itertools import chain\r\ndef freq_element(nums):\r\n  result = Counter(chain.from_iterable(nums))\r\n  return result", "task_id": 40}
{"prompt": "Write a function to filter even numbers using lambda function.", "test": ["assert filter_evennumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[2, 4, 6, 8, 10]", "assert filter_evennumbers([10,20,45,67,84,93])==[10,20,84]", "assert filter_evennumbers([5,7,9,8,6,4,3])==[8,6,4]"], "code": "def filter_evennumbers(nums):\r\n even_nums = list(filter(lambda x: x%2 == 0, nums))\r\n return even_nums", "task_id": 41}
{"prompt": "Write a python function to find the sum of repeated elements in a given array.", "test": ["assert find_Sum([1,2,3,1,1,4,5,6],8) == 3", "assert find_Sum([1,2,3,1,1],5) == 3", "assert find_Sum([1,1,2],3) == 2"], "code": "def find_Sum(arr,n): \r\n    return sum([x for x in arr if arr.count(x) > 1])", "task_id": 42}
{"prompt": "Write a function to find sequences of lowercase letters joined with an underscore using regex.", "test": ["assert text_match(\"aab_cbbbc\") == 'Found a match!'", "assert text_match(\"aab_Abbbc\") == 'Not matched!'", "assert text_match(\"Aaab_abbbc\") == 'Not matched!'"], "code": "import re\r\ndef text_match(text):\r\n  patterns = '^[a-z]+_[a-z]+$'\r\n  if re.search(patterns,  text):\r\n    return ('Found a match!')\r\n  else:\r\n    return ('Not matched!')", "task_id": 43}
{"prompt": "Write a function that matches a word at the beginning of a string.", "test": ["assert text_match_string(\" python\")==('Not matched!')", "assert text_match_string(\"python\")==('Found a match!')", "assert text_match_string(\"  lang\")==('Not matched!')"], "code": "import re\r\ndef text_match_string(text):\r\n        patterns = '^\\w+'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return 'Not matched!'", "task_id": 44}
{"prompt": "Write a function to find the gcd of the given array elements.", "test": ["assert get_gcd([2, 4, 6, 8, 16]) == 2", "assert get_gcd([1, 2, 3]) == 1", "assert get_gcd([2, 4, 6, 8]) == 2 "], "code": "def find_gcd(x, y): \r\n\twhile(y): \r\n\t\tx, y = y, x % y \r\n\treturn x \r\ndef get_gcd(l):\r\n  num1 = l[0]\r\n  num2 = l[1]\r\n  gcd = find_gcd(num1, num2)\r\n  for i in range(2, len(l)):\r\n    gcd = find_gcd(gcd, l[i])\r\n  return gcd", "task_id": 45}
{"prompt": "Write a python function to determine whether all the numbers are different from each other are not.", "test": ["assert test_distinct([1,5,7,9]) == True", "assert test_distinct([2,4,5,5,7,9]) == False", "assert test_distinct([1,2,3]) == True"], "code": "def test_distinct(data):\r\n  if len(data) == len(set(data)):\r\n    return True\r\n  else:\r\n    return False;", "task_id": 46}
{"prompt": "Write a python function to find the last digit when factorial of a divides factorial of b.", "test": ["assert compute_Last_Digit(2,4) == 2", "assert compute_Last_Digit(6,8) == 6", "assert compute_Last_Digit(1,2) == 2"], "code": "def compute_Last_Digit(A,B): \r\n    variable = 1\r\n    if (A == B): \r\n        return 1\r\n    elif ((B - A) >= 5):  \r\n        return 0\r\n    else:   \r\n        for i in range(A + 1,B + 1): \r\n            variable = (variable * (i % 10)) % 10\r\n        return variable % 10", "task_id": 47}
{"prompt": "Write a python function to set all odd bits of a given number.", "test": ["assert odd_bit_set_number(10) == 15", "assert odd_bit_set_number(20) == 21", "assert odd_bit_set_number(30) == 31"], "code": "def odd_bit_set_number(n):\r\n    count = 0;res = 0;temp = n\r\n    while temp > 0:\r\n        if count % 2 == 0:\r\n            res |= (1 << count)\r\n        count += 1\r\n        temp >>= 1\r\n    return (n | res)", "task_id": 48}
{"prompt": "Write a function to extract every first or specified element from a given two-dimensional list.", "test": ["assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],0)==[1, 4, 7]", "assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],2)==[3, 6, 9]", "assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],1)==[2,5,1]"], "code": "def specified_element(nums, N):\r\n    result = [i[N] for i in nums]\r\n    return result\r\n    ", "task_id": 49}
{"prompt": "Write a function to find the list with minimum length using lambda function.", "test": ["assert min_length_list([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(1, [0])", "assert min_length_list([[1,2,3,4,5],[1,2,3,4],[1,2,3],[1,2],[1]])==(1,[1])", "assert min_length_list([[3,4,5],[6,7,8,9],[10,11,12],[1,2]])==(2,[1,2])"], "code": "def min_length_list(input_list):\r\n    min_length = min(len(x) for x in input_list )  \r\n    min_list = min(input_list, key = lambda i: len(i))\r\n    return(min_length, min_list)", "task_id": 50}
{"prompt": "Write a function to print check if the triangle is equilateral or not.", "test": ["assert check_equilateral(6,8,12)==False ", "assert check_equilateral(6,6,12)==False", "assert check_equilateral(6,6,6)==True"], "code": "def check_equilateral(x,y,z):\r\n  if x == y == z:\r\n\t   return True\r\n  else:\r\n     return False", "task_id": 51}
{"prompt": "Write a function to caluclate area of a parallelogram.", "test": ["assert parallelogram_area(10,20)==200", "assert parallelogram_area(15,20)==300", "assert parallelogram_area(8,9)==72"], "code": "def parallelogram_area(b,h):\r\n  area=b*h\r\n  return area", "task_id": 52}
{"prompt": "Write a python function to check whether the first and last characters of a given string are equal or not.", "test": ["assert check_Equality(\"abcda\") == \"Equal\"", "assert check_Equality(\"ab\") == \"Not Equal\"", "assert check_Equality(\"mad\") == \"Not Equal\""], "code": "def check_Equality(str):\r\n  if (str[0] == str[-1]):  \r\n    return (\"Equal\") \r\n  else:  \r\n    return (\"Not Equal\") ", "task_id": 53}
{"prompt": "Write a function to sort the given array by using counting sort.", "test": ["assert counting_sort([1,23,4,5,6,7,8]) == [1, 4, 5, 6, 7, 8, 23]", "assert counting_sort([12, 9, 28, 33, 69, 45]) == [9, 12, 28, 33, 45, 69]", "assert counting_sort([8, 4, 14, 3, 2, 1]) == [1, 2, 3, 4, 8, 14]"], "code": "def counting_sort(my_list):\r\n    max_value = 0\r\n    for i in range(len(my_list)):\r\n        if my_list[i] > max_value:\r\n            max_value = my_list[i]\r\n    buckets = [0] * (max_value + 1)\r\n    for i in my_list:\r\n        buckets[i] += 1\r\n    i = 0\r\n    for j in range(max_value + 1):\r\n         for a in range(buckets[j]):\r\n             my_list[i] = j\r\n             i += 1\r\n    return my_list", "task_id": 54}
{"prompt": "Write a function to find t-nth term of geometric series.", "test": ["assert tn_gp(1,5,2)==16", "assert tn_gp(1,5,4)==256", "assert tn_gp(2,6,3)==486"], "code": "import math\r\ndef tn_gp(a,n,r):\r\n  tn = a * (math.pow(r, n - 1))\r\n  return tn", "task_id": 55}
{"prompt": "Write a python function to check if a given number is one less than twice its reverse.", "test": ["assert check(70) == False", "assert check(23) == False", "assert check(73) == True"], "code": "def rev(num):    \r\n    rev_num = 0\r\n    while (num > 0):  \r\n        rev_num = (rev_num * 10 + num % 10) \r\n        num = num // 10  \r\n    return rev_num  \r\ndef check(n):    \r\n    return (2 * rev(n) == n + 1)  ", "task_id": 56}
{"prompt": "Write a python function to find the largest number that can be formed with the given digits.", "test": ["assert find_Max_Num([1,2,3],3) == 321", "assert find_Max_Num([4,5,6,1],4) == 6541", "assert find_Max_Num([1,2,3,9],4) == 9321"], "code": "def find_Max_Num(arr,n) : \r\n    arr.sort(reverse = True) \r\n    num = arr[0] \r\n    for i in range(1,n) : \r\n        num = num * 10 + arr[i] \r\n    return num ", "task_id": 57}
{"prompt": "Write a python function to check whether the given two integers have opposite sign or not.", "test": ["assert opposite_Signs(1,-2) == True", "assert opposite_Signs(3,2) == False", "assert opposite_Signs(-10,-10) == False"], "code": "def opposite_Signs(x,y): \r\n    return ((x ^ y) < 0); ", "task_id": 58}
{"prompt": "Write a function to find the nth octagonal number.", "test": ["assert is_octagonal(5) == 65", "assert is_octagonal(10) == 280", "assert is_octagonal(15) == 645"], "code": "def is_octagonal(n): \r\n\treturn 3 * n * n - 2 * n ", "task_id": 59}
{"prompt": "Write a function to find the maximum length of the subsequence with difference between adjacent elements for the given array.", "test": ["assert max_len_sub([2, 5, 6, 3, 7, 6, 5, 8], 8) == 5", "assert max_len_sub([-2, -1, 5, -1, 4, 0, 3], 7) == 4", "assert max_len_sub([9, 11, 13, 15, 18], 5) == 1"], "code": "def max_len_sub( arr, n): \r\n\tmls=[] \r\n\tmax = 0\r\n\tfor i in range(n): \r\n\t\tmls.append(1) \r\n\tfor i in range(n): \r\n\t\tfor j in range(i): \r\n\t\t\tif (abs(arr[i] - arr[j]) <= 1 and mls[i] < mls[j] + 1): \r\n\t\t\t\tmls[i] = mls[j] + 1\r\n\tfor i in range(n): \r\n\t\tif (max < mls[i]): \r\n\t\t\tmax = mls[i] \r\n\treturn max", "task_id": 60}
{"prompt": "Write a python function to count number of substrings with the sum of digits equal to their length.", "test": ["assert count_Substrings('112112',6) == 6", "assert count_Substrings('111',3) == 6", "assert count_Substrings('1101112',7) == 12"], "code": "from collections import defaultdict\r\ndef count_Substrings(s,n):\r\n    count,sum = 0,0\r\n    mp = defaultdict(lambda : 0)\r\n    mp[0] += 1\r\n    for i in range(n):\r\n        sum += ord(s[i]) - ord('0')\r\n        count += mp[sum - (i + 1)]\r\n        mp[sum - (i + 1)] += 1\r\n    return count", "task_id": 61}
{"prompt": "Write a python function to find smallest number in a list.", "test": ["assert smallest_num([10, 20, 1, 45, 99]) == 1", "assert smallest_num([1, 2, 3]) == 1", "assert smallest_num([45, 46, 50, 60]) == 45"], "code": "def smallest_num(xs):\n  return min(xs)\n", "task_id": 62}
{"prompt": "Write a function to find the maximum difference between available pairs in the given tuple list.", "test": ["assert max_difference([(3, 5), (1, 7), (10, 3), (1, 2)]) == 7", "assert max_difference([(4, 6), (2, 17), (9, 13), (11, 12)]) == 15", "assert max_difference([(12, 35), (21, 27), (13, 23), (41, 22)]) == 23"], "code": "def max_difference(test_list):\r\n  temp = [abs(b - a) for a, b in test_list]\r\n  res = max(temp)\r\n  return (res) ", "task_id": 63}
{"prompt": "Write a function to sort a list of tuples using lambda.", "test": ["assert subject_marks([('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])==[('Social sciences', 82), ('English', 88), ('Science', 90), ('Maths', 97)]", "assert subject_marks([('Telugu',49),('Hindhi',54),('Social',33)])==([('Social',33),('Telugu',49),('Hindhi',54)])", "assert subject_marks([('Physics',96),('Chemistry',97),('Biology',45)])==([('Biology',45),('Physics',96),('Chemistry',97)])"], "code": "def subject_marks(subjectmarks):\r\n#subject_marks = [('English', 88), ('Science', 90), ('Maths', 97), ('Social sciences', 82)])\r\n subjectmarks.sort(key = lambda x: x[1])\r\n return subjectmarks", "task_id": 64}
{"prompt": "Write a function of recursion list sum.", "test": ["assert recursive_list_sum(([1, 2, [3,4],[5,6]]))==21", "assert recursive_list_sum(([7, 10, [15,14],[19,41]]))==106", "assert recursive_list_sum(([10, 20, [30,40],[50,60]]))==210"], "code": "def recursive_list_sum(data_list):\r\n\ttotal = 0\r\n\tfor element in data_list:\r\n\t\tif type(element) == type([]):\r\n\t\t\ttotal = total + recursive_list_sum(element)\r\n\t\telse:\r\n\t\t\ttotal = total + element\r\n\treturn total", "task_id": 65}
{"prompt": "Write a python function to count positive numbers in a list.", "test": ["assert pos_count([1,-2,3,-4]) == 2", "assert pos_count([3,4,5,-1]) == 3", "assert pos_count([1,2,3,4]) == 4"], "code": "def pos_count(list):\r\n  pos_count= 0\r\n  for num in list: \r\n    if num >= 0: \r\n      pos_count += 1\r\n  return pos_count ", "task_id": 66}
{"prompt": "Write a function to find the number of ways to partition a set of bell numbers.", "test": ["assert bell_number(2)==2", "assert bell_number(10)==115975", "assert bell_number(56)==6775685320645824322581483068371419745979053216268760300"], "code": "def bell_number(n):   \r\n    bell = [[0 for i in range(n+1)] for j in range(n+1)] \r\n    bell[0][0] = 1\r\n    for i in range(1, n+1): \r\n        bell[i][0] = bell[i-1][i-1]  \r\n        for j in range(1, i+1): \r\n            bell[i][j] = bell[i-1][j-1] + bell[i][j-1]   \r\n    return bell[n][0] ", "task_id": 67}
{"prompt": "Write a python function to check whether the given array is monotonic or not.", "test": ["assert is_Monotonic([6, 5, 4, 4]) == True", "assert is_Monotonic([1, 2, 2, 3]) == True", "assert is_Monotonic([1, 3, 2]) == False"], "code": "def is_Monotonic(A): \r\n    return (all(A[i] <= A[i + 1] for i in range(len(A) - 1)) or\r\n            all(A[i] >= A[i + 1] for i in range(len(A) - 1))) ", "task_id": 68}
{"prompt": "Write a function to check whether a list contains the given sublist or not.", "test": ["assert is_sublist([2,4,3,5,7],[3,7])==False", "assert is_sublist([2,4,3,5,7],[4,3])==True", "assert is_sublist([2,4,3,5,7],[1,6])==False"], "code": "def is_sublist(l, s):\r\n\tsub_set = False\r\n\tif s == []:\r\n\t\tsub_set = True\r\n\telif s == l:\r\n\t\tsub_set = True\r\n\telif len(s) > len(l):\r\n\t\tsub_set = False\r\n\telse:\r\n\t\tfor i in range(len(l)):\r\n\t\t\tif l[i] == s[0]:\r\n\t\t\t\tn = 1\r\n\t\t\t\twhile (n < len(s)) and (l[i+n] == s[n]):\r\n\t\t\t\t\tn += 1\t\t\t\t\r\n\t\t\t\tif n == len(s):\r\n\t\t\t\t\tsub_set = True\r\n\treturn sub_set", "task_id": 69}
{"prompt": "Write a function to find whether all the given tuples have equal length or not.", "test": ["assert get_equal([(11, 22, 33), (44, 55, 66)], 3) == 'All tuples have same length'", "assert get_equal([(1, 2, 3), (4, 5, 6, 7)], 3) == 'All tuples do not have same length'", "assert get_equal([(1, 2), (3, 4)], 2) == 'All tuples have same length'"], "code": "def find_equal_tuple(Input, k):\r\n  flag = 1\r\n  for tuple in Input:\r\n    if len(tuple) != k:\r\n      flag = 0\r\n      break\r\n  return flag\r\ndef get_equal(Input, k):\r\n  if find_equal_tuple(Input, k) == 1:\r\n    return (\"All tuples have same length\")\r\n  else:\r\n    return (\"All tuples do not have same length\")", "task_id": 70}
{"prompt": "Write a function to sort a list of elements using comb sort.", "test": ["assert comb_sort([5, 15, 37, 25, 79]) == [5, 15, 25, 37, 79]", "assert comb_sort([41, 32, 15, 19, 22]) == [15, 19, 22, 32, 41]", "assert comb_sort([99, 15, 13, 47]) == [13, 15, 47, 99]"], "code": "def comb_sort(nums):\r\n    shrink_fact = 1.3\r\n    gaps = len(nums)\r\n    swapped = True\r\n    i = 0\r\n    while gaps > 1 or swapped:\r\n        gaps = int(float(gaps) / shrink_fact)\r\n        swapped = False\r\n        i = 0\r\n        while gaps + i < len(nums):\r\n            if nums[i] > nums[i+gaps]:\r\n                nums[i], nums[i+gaps] = nums[i+gaps], nums[i]\r\n                swapped = True\r\n            i += 1\r\n    return nums", "task_id": 71}
{"prompt": "Write a python function to check whether the given number can be represented as difference of two squares or not.", "test": ["assert dif_Square(5) == True", "assert dif_Square(10) == False", "assert dif_Square(15) == True"], "code": "def dif_Square(n): \r\n    if (n % 4 != 2): \r\n        return True\r\n    return False", "task_id": 72}
{"prompt": "Write a function to split the given string with multiple delimiters by using regex.", "test": ["assert multiple_split('Forces of the \\ndarkness*are coming into the play.') == ['Forces of the ', 'darkness', 'are coming into the play.']", "assert multiple_split('Mi Box runs on the \\n Latest android*which has google assistance and chromecast.') == ['Mi Box runs on the ', ' Latest android', 'which has google assistance and chromecast.']", "assert multiple_split('Certain services\\nare subjected to change*over the seperate subscriptions.') == ['Certain services', 'are subjected to change', 'over the seperate subscriptions.']"], "code": "import re\r\ndef multiple_split(text):\r\n  return (re.split('; |, |\\*|\\n',text))", "task_id": 73}
{"prompt": "Write a function to check whether it follows the sequence given in the patterns array.", "test": ["assert is_samepatterns([\"red\",\"green\",\"green\"], [\"a\", \"b\", \"b\"])==True ", "assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\",\"b\"])==False ", "assert is_samepatterns([\"red\",\"green\",\"greenn\"], [\"a\",\"b\"])==False "], "code": "def is_samepatterns(colors, patterns):    \r\n    if len(colors) != len(patterns):\r\n        return False    \r\n    sdict = {}\r\n    pset = set()\r\n    sset = set()    \r\n    for i in range(len(patterns)):\r\n        pset.add(patterns[i])\r\n        sset.add(colors[i])\r\n        if patterns[i] not in sdict.keys():\r\n            sdict[patterns[i]] = []\r\n\r\n        keys = sdict[patterns[i]]\r\n        keys.append(colors[i])\r\n        sdict[patterns[i]] = keys\r\n\r\n    if len(pset) != len(sset):\r\n        return False   \r\n\r\n    for values in sdict.values():\r\n\r\n        for i in range(len(values) - 1):\r\n            if values[i] != values[i+1]:\r\n                return False\r\n\r\n    return True", "task_id": 74}
{"prompt": "Write a function to find tuples which have all elements divisible by k from the given list of tuples.", "test": ["assert find_tuples([(6, 24, 12), (7, 9, 6), (12, 18, 21)], 6) == '[(6, 24, 12)]'", "assert find_tuples([(5, 25, 30), (4, 2, 3), (7, 8, 9)], 5) == '[(5, 25, 30)]'", "assert find_tuples([(7, 9, 16), (8, 16, 4), (19, 17, 18)], 4) == '[(8, 16, 4)]'"], "code": "def find_tuples(test_list, K):\r\n  res = [sub for sub in test_list if all(ele % K == 0 for ele in sub)]\r\n  return (str(res)) ", "task_id": 75}
{"prompt": "Write a python function to count the number of squares in a rectangle.", "test": ["assert count_Squares(4,3) == 20", "assert count_Squares(2,2) == 5", "assert count_Squares(1,1) == 1"], "code": "def count_Squares(m,n):\r\n    if(n < m):\r\n        temp = m\r\n        m = n\r\n        n = temp\r\n    return ((m * (m + 1) * (2 * m + 1) / 6 + (n - m) * m * (m + 1) / 2))", "task_id": 76}
{"prompt": "Write a python function to find the difference between sum of even and odd digits.", "test": ["assert is_Diff (12345) == False", "assert is_Diff(1212112) == True", "assert is_Diff(1212) == False"], "code": "def is_Diff(n): \r\n    return (n % 11 == 0) ", "task_id": 77}
{"prompt": "Write a python function to find number of integers with odd number of set bits.", "test": ["assert count_With_Odd_SetBits(5) == 3", "assert count_With_Odd_SetBits(10) == 5", "assert count_With_Odd_SetBits(15) == 8"], "code": "def count_With_Odd_SetBits(n): \r\n    if (n % 2 != 0): \r\n        return (n + 1) / 2\r\n    count = bin(n).count('1') \r\n    ans = n / 2\r\n    if (count % 2 != 0): \r\n        ans += 1\r\n    return ans ", "task_id": 78}
{"prompt": "Write a python function to check whether the length of the word is odd or not.", "test": ["assert word_len(\"Hadoop\") == False", "assert word_len(\"great\") == True", "assert word_len(\"structure\") == True"], "code": "def word_len(s): \r\n    s = s.split(' ')   \r\n    for word in s:    \r\n        if len(word)%2!=0: \r\n            return True  \r\n        else:\r\n          return False", "task_id": 79}
{"prompt": "Write a function to find the nth tetrahedral number.", "test": ["assert tetrahedral_number(5) == 35.0", "assert tetrahedral_number(6) == 56.0", "assert tetrahedral_number(7) == 84.0"], "code": "def tetrahedral_number(n): \r\n\treturn (n * (n + 1) * (n + 2)) / 6", "task_id": 80}
{"prompt": "Write a function to zip the two given tuples.", "test": ["assert zip_tuples((7, 8, 4, 5, 9, 10),(1, 5, 6) ) == [(7, 1), (8, 5), (4, 6), (5, 1), (9, 5), (10, 6)]", "assert zip_tuples((8, 9, 5, 6, 10, 11),(2, 6, 7) ) == [(8, 2), (9, 6), (5, 7), (6, 2), (10, 6), (11, 7)]", "assert zip_tuples((9, 10, 6, 7, 11, 12),(3, 7, 8) ) == [(9, 3), (10, 7), (6, 8), (7, 3), (11, 7), (12, 8)]"], "code": "def zip_tuples(test_tup1, test_tup2):\r\n  res = []\r\n  for i, j in enumerate(test_tup1):\r\n    res.append((j, test_tup2[i % len(test_tup2)])) \r\n  return (res) ", "task_id": 81}
{"prompt": "Write a function to find the volume of a sphere.", "test": ["assert volume_sphere(10)==4188.790204786391", "assert volume_sphere(25)==65449.84694978735", "assert volume_sphere(20)==33510.32163829113"], "code": "import math\r\ndef volume_sphere(r):\r\n  volume=(4/3)*math.pi*r*r*r\r\n  return volume", "task_id": 82}
{"prompt": "Write a python function to find the character made by adding all the characters of the given string.", "test": ["assert get_Char(\"abc\") == \"f\"", "assert get_Char(\"gfg\") == \"t\"", "assert get_Char(\"ab\") == \"c\""], "code": "def get_Char(strr):  \r\n    summ = 0\r\n    for i in range(len(strr)): \r\n        summ += (ord(strr[i]) - ord('a') + 1)  \r\n    if (summ % 26 == 0): \r\n        return ord('z') \r\n    else: \r\n        summ = summ % 26\r\n        return chr(ord('a') + summ - 1)", "task_id": 83}
{"prompt": "Write a function to find the n-th number in newman conway sequence.", "test": ["assert sequence(10) == 6", "assert sequence(2) == 1", "assert sequence(3) == 2"], "code": "def sequence(n): \r\n\tif n == 1 or n == 2: \r\n\t\treturn 1\r\n\telse: \r\n\t\treturn sequence(sequence(n-1)) + sequence(n-sequence(n-1))", "task_id": 84}
{"prompt": "Write a function to find the surface area of a sphere.", "test": ["assert surfacearea_sphere(10)==1256.6370614359173", "assert surfacearea_sphere(15)==2827.4333882308138", "assert surfacearea_sphere(20)==5026.548245743669"], "code": "import math\r\ndef surfacearea_sphere(r):\r\n  surfacearea=4*math.pi*r*r\r\n  return surfacearea", "task_id": 85}
{"prompt": "Write a function to find nth centered hexagonal number.", "test": ["assert centered_hexagonal_number(10) == 271", "assert centered_hexagonal_number(2) == 7", "assert centered_hexagonal_number(9) == 217"], "code": "def centered_hexagonal_number(n):\r\n  return 3 * n * (n - 1) + 1", "task_id": 86}
{"prompt": "Write a function to merge three dictionaries into a single expression.", "test": ["assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{ \"O\": \"Orange\", \"W\": \"White\", \"B\": \"Black\" })=={'B': 'Black', 'R': 'Red', 'P': 'Pink', 'G': 'Green', 'W': 'White', 'O': 'Orange'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" }, { \"G\": \"Green\", \"W\": \"White\" },{\"L\":\"lavender\",\"B\":\"Blue\"})=={'W': 'White', 'P': 'Pink', 'B': 'Black', 'R': 'Red', 'G': 'Green', 'L': 'lavender'}", "assert merge_dictionaries_three({ \"R\": \"Red\", \"B\": \"Black\", \"P\": \"Pink\" },{\"L\":\"lavender\",\"B\":\"Blue\"},{ \"G\": \"Green\", \"W\": \"White\" })=={'B': 'Black', 'P': 'Pink', 'R': 'Red', 'G': 'Green', 'L': 'lavender', 'W': 'White'}"], "code": "import collections as ct\r\ndef merge_dictionaries_three(dict1,dict2, dict3):\r\n    merged_dict = dict(ct.ChainMap({},dict1,dict2,dict3))\r\n    return merged_dict", "task_id": 87}
{"prompt": "Write a function to get the frequency of the elements in a list.", "test": ["assert freq_count([10,10,10,10,20,20,20,20,40,40,50,50,30])==({10: 4, 20: 4, 40: 2, 50: 2, 30: 1}) ", "assert freq_count([1,2,3,4,3,2,4,1,3,1,4])==({1:3, 2:2,3:3,4:3}) ", "assert freq_count([5,6,7,4,9,10,4,5,6,7,9,5])==({10:1,5:3,6:2,7:2,4:2,9:2}) "], "code": "import collections\r\ndef freq_count(list1):\r\n  freq_count= collections.Counter(list1)\r\n  return freq_count", "task_id": 88}
{"prompt": "Write a function to find the closest smaller number than n.", "test": ["assert closest_num(11) == 10", "assert closest_num(7) == 6", "assert closest_num(12) == 11"], "code": "def closest_num(N):\r\n  return (N - 1)", "task_id": 89}
{"prompt": "Write a python function to find the length of the longest word.", "test": ["assert len_log([\"python\",\"PHP\",\"bigdata\"]) == 7", "assert len_log([\"a\",\"ab\",\"abc\"]) == 3", "assert len_log([\"small\",\"big\",\"tall\"]) == 5"], "code": "def len_log(list1):\r\n    max=len(list1[0])\r\n    for i in list1:\r\n        if len(i)>max:\r\n            max=len(i)\r\n    return max", "task_id": 90}
{"prompt": "Write a function to check if a substring is present in a given list of string values.", "test": ["assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ack\")==True", "assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"abc\")==False", "assert find_substring([\"red\", \"black\", \"white\", \"green\", \"orange\"],\"ange\")==True"], "code": "def find_substring(str1, sub_str):\r\n   if any(sub_str in s for s in str1):\r\n       return True\r\n   return False", "task_id": 91}
{"prompt": "Write a function to check whether the given number is undulating or not.", "test": ["assert is_undulating(\"1212121\") == True", "assert is_undulating(\"1991\") == False", "assert is_undulating(\"121\") == True"], "code": "def is_undulating(n): \r\n\tif (len(n) <= 2): \r\n\t\treturn False\r\n\tfor i in range(2, len(n)): \r\n\t\tif (n[i - 2] != n[i]): \r\n\t\t\treturn False\r\n\treturn True", "task_id": 92}
{"prompt": "Write a function to calculate the value of 'a' to the power 'b'.", "test": ["assert power(3,4) == 81", "assert power(2,3) == 8", "assert power(5,5) == 3125"], "code": "def power(a,b):\r\n\tif b==0:\r\n\t\treturn 1\r\n\telif a==0:\r\n\t\treturn 0\r\n\telif b==1:\r\n\t\treturn a\r\n\telse:\r\n\t\treturn a*power(a,b-1)", "task_id": 93}
{"prompt": "Write a function to extract the index minimum value record from the given tuples.", "test": ["assert index_minimum([('Rash', 143), ('Manjeet', 200), ('Varsha', 100)]) == 'Varsha'", "assert index_minimum([('Yash', 185), ('Dawood', 125), ('Sanya', 175)]) == 'Dawood'", "assert index_minimum([('Sai', 345), ('Salman', 145), ('Ayesha', 96)]) == 'Ayesha'"], "code": "from operator import itemgetter \r\ndef index_minimum(test_list):\r\n  res = min(test_list, key = itemgetter(1))[0]\r\n  return (res) ", "task_id": 94}
{"prompt": "Write a python function to find the minimum length of sublist.", "test": ["assert Find_Min_Length([[1],[1,2]]) == 1", "assert Find_Min_Length([[1,2],[1,2,3],[1,2,3,4]]) == 2", "assert Find_Min_Length([[3,3,3],[4,4,4,4]]) == 3"], "code": "def Find_Min_Length(lst):  \r\n    minLength = min(len(x) for x in lst )\r\n    return minLength ", "task_id": 95}
{"prompt": "Write a python function to find the number of divisors of a given integer.", "test": ["assert divisor(15) == 4 ", "assert divisor(12) == 6", "assert divisor(9) == 3"], "code": "def divisor(n):\r\n  for i in range(n):\r\n    x = len([i for i in range(1,n+1) if not n % i])\r\n  return x", "task_id": 96}
{"prompt": "Write a function to find frequency count of list of lists.", "test": ["assert frequency_lists([[1, 2, 3, 2], [4, 5, 6, 2], [7, 8, 9, 5]])=={1: 1, 2: 3, 3: 1, 4: 1, 5: 2, 6: 1, 7: 1, 8: 1, 9: 1}", "assert frequency_lists([[1,2,3,4],[5,6,7,8],[9,10,11,12]])=={1: 1, 2: 1, 3: 1, 4: 1, 5: 1, 6: 1, 7: 1, 8: 1, 9: 1,10:1,11:1,12:1}", "assert frequency_lists([[20,30,40,17],[18,16,14,13],[10,20,30,40]])=={20:2,30:2,40:2,17: 1,18:1, 16: 1,14: 1,13: 1, 10: 1}"], "code": "def frequency_lists(list1):\r\n    list1 = [item for sublist in list1 for item in sublist]\r\n    dic_data = {}\r\n    for num in list1:\r\n        if num in dic_data.keys():\r\n            dic_data[num] += 1\r\n        else:\r\n            key = num\r\n            value = 1\r\n            dic_data[key] = value\r\n    return dic_data\r\n", "task_id": 97}
{"prompt": "Write a function to multiply all the numbers in a list and divide with the length of the list.", "test": ["assert multiply_num((8, 2, 3, -1, 7))==-67.2", "assert multiply_num((-10,-20,-30))==-2000.0", "assert multiply_num((19,15,18))==1710.0"], "code": "def multiply_num(numbers):  \r\n    total = 1\r\n    for x in numbers:\r\n        total *= x  \r\n    return total/len(numbers) ", "task_id": 98}
{"prompt": "Write a function to convert the given decimal number to its binary equivalent.", "test": ["assert decimal_to_binary(8) == '1000'", "assert decimal_to_binary(18) == '10010'", "assert decimal_to_binary(7) == '111' "], "code": "def decimal_to_binary(n): \r\n    return bin(n).replace(\"0b\",\"\") ", "task_id": 99}
{"prompt": "Write a function to find the next smallest palindrome of a specified number.", "test": ["assert next_smallest_palindrome(99)==101", "assert next_smallest_palindrome(1221)==1331", "assert next_smallest_palindrome(120)==121"], "code": "import sys\r\ndef next_smallest_palindrome(num):\r\n    numstr = str(num)\r\n    for i in range(num+1,sys.maxsize):\r\n        if str(i) == str(i)[::-1]:\r\n            return i", "task_id": 100}
{"prompt": "Write a function to find the kth element in the given array.", "test": ["assert kth_element([12,3,5,7,19], 5, 2) == 3", "assert kth_element([17,24,8,23], 4, 3) == 8", "assert kth_element([16,21,25,36,4], 5, 4) == 36"], "code": "def kth_element(arr, n, k):\r\n  for i in range(n):\r\n    for j in range(0, n-i-1):\r\n      if arr[j] > arr[j+1]:\r\n        arr[j], arr[j+1] == arr[j+1], arr[j]\r\n  return arr[k-1]", "task_id": 101}
{"prompt": "Write a function to convert snake case string to camel case string.", "test": ["assert snake_to_camel('python_program')=='PythonProgram'", "assert snake_to_camel('python_language')==('PythonLanguage')", "assert snake_to_camel('programming_language')==('ProgrammingLanguage')"], "code": "def snake_to_camel(word):\r\n        import re\r\n        return ''.join(x.capitalize() or '_' for x in word.split('_'))", "task_id": 102}
{"prompt": "Write a function to find eulerian number a(n, m).", "test": ["assert eulerian_num(3, 1) == 4", "assert eulerian_num(4, 1) == 11", "assert eulerian_num(5, 3) == 26"], "code": "def eulerian_num(n, m): \r\n\tif (m >= n or n == 0): \r\n\t\treturn 0 \r\n\tif (m == 0): \r\n\t\treturn 1 \r\n\treturn ((n - m) * eulerian_num(n - 1, m - 1) +(m + 1) * eulerian_num(n - 1, m))", "task_id": 103}
{"prompt": "Write a function to sort each sublist of strings in a given list of lists using lambda function.", "test": ["assert sort_sublists(([\"green\", \"orange\"], [\"black\", \"white\"], [\"white\", \"black\", \"orange\"]))==[['green', 'orange'], ['black', 'white'], ['black', 'orange', 'white']]", "assert sort_sublists(([\" red \",\"green\" ],[\"blue \",\" black\"],[\" orange\",\"brown\"]))==[[' red ', 'green'], [' black', 'blue '], [' orange', 'brown']]", "assert sort_sublists(([\"zilver\",\"gold\"], [\"magnesium\",\"aluminium\"], [\"steel\", \"bronze\"]))==[['gold', 'zilver'],['aluminium', 'magnesium'], ['bronze', 'steel']]"], "code": "def sort_sublists(input_list):\r\n    result = [sorted(x, key = lambda x:x[0]) for x in input_list] \r\n    return result\r", "task_id": 104}
{"prompt": "Write a python function to count true booleans in the given list.", "test": ["assert count([True,False,True]) == 2", "assert count([False,False]) == 0", "assert count([True,True,True]) == 3"], "code": "def count(lst):   \r\n    return sum(lst) ", "task_id": 105}
{"prompt": "Write a function to add the given list to the given tuples.", "test": ["assert add_lists([5, 6, 7], (9, 10)) == (9, 10, 5, 6, 7)", "assert add_lists([6, 7, 8], (10, 11)) == (10, 11, 6, 7, 8)", "assert add_lists([7, 8, 9], (11, 12)) == (11, 12, 7, 8, 9)"], "code": "def add_lists(test_list, test_tup):\r\n  res = tuple(list(test_tup) + test_list)\r\n  return (res) ", "task_id": 106}
{"prompt": "Write a python function to count hexadecimal numbers for a given range.", "test": ["assert count_Hexadecimal(10,15) == 6", "assert count_Hexadecimal(2,4) == 0", "assert count_Hexadecimal(15,16) == 1"], "code": "def count_Hexadecimal(L,R) :  \r\n    count = 0;  \r\n    for i in range(L,R + 1) : \r\n        if (i >= 10 and i <= 15) : \r\n            count += 1;  \r\n        elif (i > 15) : \r\n            k = i;  \r\n            while (k != 0) :  \r\n                if (k % 16 >= 10) : \r\n                    count += 1;  \r\n                k = k // 16;  \r\n    return count;  ", "task_id": 107}
{"prompt": "Write a function to merge multiple sorted inputs into a single sorted iterator using heap queue algorithm.", "test": ["assert merge_sorted_list([25, 24, 15, 4, 5, 29, 110],[19, 20, 11, 56, 25, 233, 154],[24, 26, 54, 48])==[4, 5, 11, 15, 19, 20, 24, 24, 25, 25, 26, 29, 48, 54, 56, 110, 154, 233]", "assert merge_sorted_list([1, 3, 5, 6, 8, 9], [2, 5, 7, 11], [1, 4, 7, 8, 12])==[1, 1, 2, 3, 4, 5, 5, 6, 7, 7, 8, 8, 9, 11, 12]", "assert merge_sorted_list([18, 14, 10, 9, 8, 7, 9, 3, 2, 4, 1],[25, 35, 22, 85, 14, 65, 75, 25, 58],[12, 74, 9, 50, 61, 41])==[1, 2, 3, 4, 7, 8, 9, 9, 9, 10, 12, 14, 14, 18, 22, 25, 25, 35, 41, 50, 58, 61, 65, 74, 75, 85]"], "code": "import heapq\r\ndef merge_sorted_list(num1,num2,num3):\r\n  num1=sorted(num1)\r\n  num2=sorted(num2)\r\n  num3=sorted(num3)\r\n  result = heapq.merge(num1,num2,num3)\r\n  return list(result)", "task_id": 108}
{"prompt": "Write a python function to find the count of rotations of a binary string with odd value.", "test": ["assert odd_Equivalent(\"011001\",6) == 3", "assert odd_Equivalent(\"11011\",5) == 4", "assert odd_Equivalent(\"1010\",4) == 2"], "code": "def odd_Equivalent(s,n): \r\n    count=0\r\n    for i in range(0,n): \r\n        if (s[i] == '1'): \r\n            count = count + 1\r\n    return count ", "task_id": 109}
{"prompt": "Write a function to extract the ranges that are missing from the given list with the given start range and end range values.", "test": ["assert extract_missing([(6, 9), (15, 34), (48, 70)], 2, 100) == [(2, 6), (9, 100), (9, 15), (34, 100), (34, 48), (70, 100)]", "assert extract_missing([(7, 2), (15, 19), (38, 50)], 5, 60) == [(5, 7), (2, 60), (2, 15), (19, 60), (19, 38), (50, 60)]", "assert extract_missing([(7, 2), (15, 19), (38, 50)], 1, 52) == [(1, 7), (2, 52), (2, 15), (19, 52), (19, 38), (50, 52)]"], "code": "def extract_missing(test_list, strt_val, stop_val):\r\n  res = []\r\n  for sub in test_list:\r\n    if sub[0] > strt_val:\r\n      res.append((strt_val, sub[0]))\r\n      strt_val = sub[1]\r\n    if strt_val < stop_val:\r\n      res.append((strt_val, stop_val))\r\n  return (res) ", "task_id": 110}
{"prompt": "Write a function to find common elements in given nested lists. * list item * list item * list item * list item", "test": ["assert common_in_nested_lists([[12, 18, 23, 25, 45], [7, 12, 18, 24, 28], [1, 5, 8, 12, 15, 16, 18]])==[18, 12]", "assert common_in_nested_lists([[12, 5, 23, 25, 45], [7, 11, 5, 23, 28], [1, 5, 8, 18, 23, 16]])==[5,23]", "assert common_in_nested_lists([[2, 3,4, 1], [4, 5], [6,4, 8],[4, 5], [6, 8,4]])==[4]"], "code": "def common_in_nested_lists(nestedlist):\r\n    result = list(set.intersection(*map(set, nestedlist)))\r\n    return result", "task_id": 111}
{"prompt": "Write a python function to find the perimeter of a cylinder.", "test": ["assert perimeter(2,4) == 12", "assert perimeter(1,2) == 6", "assert perimeter(3,1) == 8"], "code": "def perimeter(diameter,height) : \r\n    return 2*(diameter+height)  ", "task_id": 112}
{"prompt": "Write a function to check if a string represents an integer or not.", "test": ["assert check_integer(\"python\")==False", "assert check_integer(\"1\")==True", "assert check_integer(\"12345\")==True"], "code": "def check_integer(text):\r\n text = text.strip()\r\n if len(text) < 1:\r\n    return None\r\n else:\r\n     if all(text[i] in \"0123456789\" for i in range(len(text))):\r\n          return True\r\n     elif (text[0] in \"+-\") and \\\r\n         all(text[i] in \"0123456789\" for i in range(1,len(text))):\r\n         return True\r\n     else:\r\n        return False", "task_id": 113}
{"prompt": "Write a function to assign frequency to each tuple in the given tuple list.", "test": ["assert assign_freq([(6, 5, 8), (2, 7), (6, 5, 8), (6, 5, 8), (9, ), (2, 7)] ) == '[(6, 5, 8, 3), (2, 7, 2), (9, 1)]'", "assert assign_freq([(4, 2, 4), (7, 1), (4, 8), (4, 2, 4), (9, 2), (7, 1)] ) == '[(4, 2, 4, 2), (7, 1, 2), (4, 8, 1), (9, 2, 1)]'", "assert assign_freq([(11, 13, 10), (17, 21), (4, 2, 3), (17, 21), (9, 2), (4, 2, 3)] ) == '[(11, 13, 10, 1), (17, 21, 2), (4, 2, 3, 2), (9, 2, 1)]'"], "code": "from collections import Counter \r\ndef assign_freq(test_list):\r\n  res = [(*key, val) for key, val in Counter(test_list).items()]\r\n  return (str(res)) ", "task_id": 114}
{"prompt": "Write a function to check whether all dictionaries in a list are empty or not.", "test": ["assert empty_dit([{},{},{}])==True", "assert empty_dit([{1,2},{},{}])==False", "assert empty_dit({})==True"], "code": "def empty_dit(list1):\r\n empty_dit=all(not d for d in list1)\r\n return empty_dit", "task_id": 115}
{"prompt": "Write a function to convert a given tuple of positive integers into an integer.", "test": ["assert tuple_to_int((1,2,3))==123", "assert tuple_to_int((4,5,6))==456", "assert tuple_to_int((5,6,7))==567"], "code": "def tuple_to_int(nums):\r\n    result = int(''.join(map(str,nums)))\r\n    return result", "task_id": 116}
{"prompt": "Write a function to convert all possible convertible elements in the list to float.", "test": ["assert list_to_float( [(\"3\", \"4\"), (\"1\", \"26.45\"), (\"7.32\", \"8\"), (\"4\", \"8\")] ) == '[(3.0, 4.0), (1.0, 26.45), (7.32, 8.0), (4.0, 8.0)]'", "assert list_to_float( [(\"4\", \"4\"), (\"2\", \"27\"), (\"4.12\", \"9\"), (\"7\", \"11\")] ) == '[(4.0, 4.0), (2.0, 27.0), (4.12, 9.0), (7.0, 11.0)]'", "assert list_to_float( [(\"6\", \"78\"), (\"5\", \"26.45\"), (\"1.33\", \"4\"), (\"82\", \"13\")] ) == '[(6.0, 78.0), (5.0, 26.45), (1.33, 4.0), (82.0, 13.0)]'"], "code": "def list_to_float(test_list):\r\n  res = []\r\n  for tup in test_list:\r\n    temp = []\r\n    for ele in tup:\r\n      if ele.isalpha():\r\n        temp.append(ele)\r\n      else:\r\n        temp.append(float(ele))\r\n    res.append((temp[0],temp[1])) \r\n  return (str(res)) ", "task_id": 117}
{"prompt": "[link text](https:// [link text](https:// [link text](https://)))write a function to convert a string to a list.", "test": ["assert string_to_list(\"python programming\")==['python','programming']", "assert string_to_list(\"lists tuples strings\")==['lists','tuples','strings']", "assert string_to_list(\"write a program\")==['write','a','program']"], "code": "def string_to_list(string): \r\n    lst = list(string.split(\" \")) \r\n    return lst", "task_id": 118}
{"prompt": "Write a python function to find the element that appears only once in a sorted array.", "test": ["assert search([1,1,2,2,3],5) == 3", "assert search([1,1,3,3,4,4,5,5,7,7,8],11) == 8", "assert search([1,2,2,3,3,4,4],7) == 1"], "code": "def search(arr,n) :\r\n    XOR = 0\r\n    for i in range(n) :\r\n        XOR = XOR ^ arr[i]\r\n    return (XOR)", "task_id": 119}
{"prompt": "Write a function to find the maximum product from the pairs of tuples within a given list.", "test": ["assert max_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==36", "assert max_product_tuple([(10,20), (15,2), (5,10)] )==200", "assert max_product_tuple([(11,44), (10,15), (20,5), (12, 9)] )==484"], "code": "def max_product_tuple(list1):\r\n    result_max = max([abs(x * y) for x, y in list1] )\r\n    return result_max", "task_id": 120}
{"prompt": "Write a function to find the triplet with sum of the given array", "test": ["assert check_triplet([2, 7, 4, 0, 9, 5, 1, 3], 8, 6, 0) == True", "assert check_triplet([1, 4, 5, 6, 7, 8, 5, 9], 8, 6, 0) == False", "assert check_triplet([10, 4, 2, 3, 5], 5, 15, 0) == True"], "code": "def check_triplet(A, n, sum, count):\r\n    if count == 3 and sum == 0:\r\n        return True\r\n    if count == 3 or n == 0 or sum < 0:\r\n        return False\r\n    return check_triplet(A, n - 1, sum - A[n - 1], count + 1) or\\\r\n           check_triplet(A, n - 1, sum, count)", "task_id": 121}
{"prompt": "Write a function to find n\u2019th smart number.", "test": ["assert smartNumber(1) == 30", "assert smartNumber(50) == 273", "assert smartNumber(1000) == 2664"], "code": "MAX = 3000 \r\ndef smartNumber(n): \r\n\tprimes = [0] * MAX \r\n\tresult = [] \r\n\tfor i in range(2, MAX): \r\n\t\tif (primes[i] == 0): \r\n\t\t\tprimes[i] = 1 \r\n\t\t\tj = i * 2 \r\n\t\t\twhile (j < MAX): \r\n\t\t\t\tprimes[j] -= 1 \r\n\t\t\t\tif ( (primes[j] + 3) == 0): \r\n\t\t\t\t\tresult.append(j) \r\n\t\t\t\tj = j + i \r\n\tresult.sort() \r\n\treturn result[n - 1] ", "task_id": 122}
{"prompt": "Write a function to sum all amicable numbers from 1 to a specified number.", "test": ["assert amicable_numbers_sum(999)==504", "assert amicable_numbers_sum(9999)==31626", "assert amicable_numbers_sum(99)==0"], "code": "def amicable_numbers_sum(limit):\r\n    if not isinstance(limit, int):\r\n        return \"Input is not an integer!\"\r\n    if limit < 1:\r\n        return \"Input must be bigger than 0!\"\r\n    amicables = set()\r\n    for num in range(2, limit+1):\r\n        if num in amicables:\r\n            continue\r\n        sum_fact = sum([fact for fact in range(1, num) if num % fact == 0])\r\n        sum_fact2 = sum([fact for fact in range(1, sum_fact) if sum_fact % fact == 0])\r\n        if num == sum_fact2 and num != sum_fact:\r\n            amicables.add(num)\r\n            amicables.add(sum_fact2)\r\n    return sum(amicables)", "task_id": 123}
{"prompt": "Write a function to get the angle of a complex number.", "test": ["assert angle_complex(0,1j)==1.5707963267948966 ", "assert angle_complex(2,1j)==0.4636476090008061", "assert angle_complex(0,2j)==1.5707963267948966"], "code": "import cmath\r\ndef angle_complex(a,b):\r\n  cn=complex(a,b)\r\n  angle=cmath.phase(a+b)\r\n  return angle", "task_id": 124}
{"prompt": "Write a function to find the maximum difference between the number of 0s and number of 1s in any sub-string of the given binary string.", "test": ["assert find_length(\"11000010001\", 11) == 6", "assert find_length(\"10111\", 5) == 1", "assert find_length(\"11011101100101\", 14) == 2 "], "code": "def find_length(string, n): \r\n\tcurrent_sum = 0\r\n\tmax_sum = 0\r\n\tfor i in range(n): \r\n\t\tcurrent_sum += (1 if string[i] == '0' else -1) \r\n\t\tif current_sum < 0: \r\n\t\t\tcurrent_sum = 0\r\n\t\tmax_sum = max(current_sum, max_sum) \r\n\treturn max_sum if max_sum else 0", "task_id": 125}
{"prompt": "Write a python function to find the sum of common divisors of two given numbers.", "test": ["assert sum(10,15) == 6", "assert sum(100,150) == 93", "assert sum(4,6) == 3"], "code": "def sum(a,b): \r\n    sum = 0\r\n    for i in range (1,min(a,b)): \r\n        if (a % i == 0 and b % i == 0): \r\n            sum += i \r\n    return sum", "task_id": 126}
{"prompt": "Write a function to multiply two integers without using the * operator in python.", "test": ["assert multiply_int(10,20)==200", "assert multiply_int(5,10)==50", "assert multiply_int(4,8)==32"], "code": "def multiply_int(x, y):\r\n    if y < 0:\r\n        return -multiply_int(x, -y)\r\n    elif y == 0:\r\n        return 0\r\n    elif y == 1:\r\n        return x\r\n    else:\r\n        return x + multiply_int(x, y - 1)", "task_id": 127}
{"prompt": "Write a function to shortlist words that are longer than n from a given list of words.", "test": ["assert long_words(3,\"python is a programming language\")==['python','programming','language']", "assert long_words(2,\"writing a program\")==['writing','program']", "assert long_words(5,\"sorting list\")==['sorting']"], "code": "def long_words(n, str):\r\n    word_len = []\r\n    txt = str.split(\" \")\r\n    for x in txt:\r\n        if len(x) > n:\r\n            word_len.append(x)\r\n    return word_len\t", "task_id": 128}
{"prompt": "Write a function to calculate magic square.", "test": ["assert magic_square_test([[7, 12, 1, 14], [2, 13, 8, 11], [16, 3, 10, 5], [9, 6, 15, 4]])==True", "assert magic_square_test([[2, 7, 6], [9, 5, 1], [4, 3, 8]])==True", "assert magic_square_test([[2, 7, 6], [9, 5, 1], [4, 3, 7]])==False"], "code": "def magic_square_test(my_matrix):\r\n    iSize = len(my_matrix[0])\r\n    sum_list = []\r\n    sum_list.extend([sum (lines) for lines in my_matrix])   \r\n    for col in range(iSize):\r\n        sum_list.append(sum(row[col] for row in my_matrix))\r\n    result1 = 0\r\n    for i in range(0,iSize):\r\n        result1 +=my_matrix[i][i]\r\n    sum_list.append(result1)      \r\n    result2 = 0\r\n    for i in range(iSize-1,-1,-1):\r\n        result2 +=my_matrix[i][i]\r\n    sum_list.append(result2)\r\n    if len(set(sum_list))>1:\r\n        return False\r\n    return True", "task_id": 129}
{"prompt": "Write a function to find the item with maximum frequency in a given list.", "test": ["assert max_occurrences([2,3,8,4,7,9,8,2,6,5,1,6,1,2,3,2,4,6,9,1,2])==(2, 5)", "assert max_occurrences([2,3,8,4,7,9,8,7,9,15,14,10,12,13,16,16,18])==(8, 2)", "assert max_occurrences([10,20,20,30,40,90,80,50,30,20,50,10])==(20, 3)"], "code": "from collections import defaultdict\r\ndef max_occurrences(nums):\r\n    dict = defaultdict(int)\r\n    for i in nums:\r\n        dict[i] += 1\r\n    result = max(dict.items(), key=lambda x: x[1]) \r\n    return result", "task_id": 130}
{"prompt": "Write a python function to reverse only the vowels of a given string.", "test": ["assert reverse_vowels(\"Python\") == \"Python\"", "assert reverse_vowels(\"USA\") == \"ASU\"", "assert reverse_vowels(\"ab\") == \"ab\""], "code": "def reverse_vowels(str1):\r\n\tvowels = \"\"\r\n\tfor char in str1:\r\n\t\tif char in \"aeiouAEIOU\":\r\n\t\t\tvowels += char\r\n\tresult_string = \"\"\r\n\tfor char in str1:\r\n\t\tif char in \"aeiouAEIOU\":\r\n\t\t\tresult_string += vowels[-1]\r\n\t\t\tvowels = vowels[:-1]\r\n\t\telse:\r\n\t\t\tresult_string += char\r\n\treturn result_string", "task_id": 131}
{"prompt": "Write a function to convert tuple to a string.", "test": ["assert tup_string(('e', 'x', 'e', 'r', 'c', 'i', 's', 'e', 's'))==(\"exercises\")", "assert tup_string(('p','y','t','h','o','n'))==(\"python\")", "assert tup_string(('p','r','o','g','r','a','m'))==(\"program\")"], "code": "def tup_string(tup1):\r\n  str =  ''.join(tup1)\r\n  return str", "task_id": 132}
{"prompt": "Write a function to calculate the sum of the negative numbers of a given list of numbers using lambda function.", "test": ["assert sum_negativenum([2, 4, -6, -9, 11, -12, 14, -5, 17])==-32", "assert sum_negativenum([10,15,-14,13,-18,12,-20])==-52", "assert sum_negativenum([19, -65, 57, 39, 152,-639, 121, 44, 90, -190])==-894"], "code": "def sum_negativenum(nums):\r\n  sum_negativenum = list(filter(lambda nums:nums<0,nums))\r\n  return sum(sum_negativenum)", "task_id": 133}
{"prompt": "Write a python function to check whether the last element of given array is even or odd after performing an operation p times.", "test": ["assert check_last([5,7,10],3,1) == \"ODD\"", "assert check_last([2,3],2,3) == \"EVEN\"", "assert check_last([1,2,3],3,1) == \"ODD\""], "code": "def check_last (arr,n,p): \r\n    _sum = 0\r\n    for i in range(n): \r\n        _sum = _sum + arr[i] \r\n    if p == 1: \r\n        if _sum % 2 == 0: \r\n            return \"ODD\"\r\n        else: \r\n            return \"EVEN\"\r\n    return \"EVEN\"\r\n      ", "task_id": 134}
{"prompt": "Write a function to find the nth hexagonal number.", "test": ["assert hexagonal_num(10) == 190", "assert hexagonal_num(5) == 45", "assert hexagonal_num(7) == 91"], "code": "def hexagonal_num(n): \r\n\treturn n*(2*n - 1) ", "task_id": 135}
{"prompt": "Write a function to calculate electricity bill.", "test": ["assert cal_electbill(75)==246.25", "assert cal_electbill(265)==1442.75", "assert cal_electbill(100)==327.5"], "code": "def cal_electbill(units):\r\n if(units < 50):\r\n    amount = units * 2.60\r\n    surcharge = 25\r\n elif(units <= 100):\r\n    amount = 130 + ((units - 50) * 3.25)\r\n    surcharge = 35\r\n elif(units <= 200):\r\n    amount = 130 + 162.50 + ((units - 100) * 5.26)\r\n    surcharge = 45\r\n else:\r\n    amount = 130 + 162.50 + 526 + ((units - 200) * 8.45)\r\n    surcharge = 75\r\n total = amount + surcharge\r\n return total", "task_id": 136}
{"prompt": "Write a function to find the ration of zeroes in an array of integers.", "test": ["assert zero_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.15", "assert zero_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==0.00", "assert zero_count([2, 4, -6, -9, 11, -12, 14, -5, 17])==0.00"], "code": "from array import array\r\ndef zero_count(nums):\r\n    n = len(nums)\r\n    n1 = 0\r\n    for x in nums:\r\n        if x == 0:\r\n            n1 += 1\r\n        else:\r\n          None\r\n    return round(n1/n,2)", "task_id": 137}
{"prompt": "Write a python function to check whether the given number can be represented as sum of non-zero powers of 2 or not.", "test": ["assert is_Sum_Of_Powers_Of_Two(10) == True", "assert is_Sum_Of_Powers_Of_Two(7) == False", "assert is_Sum_Of_Powers_Of_Two(14) == True"], "code": "def is_Sum_Of_Powers_Of_Two(n): \r\n    if (n % 2 == 1): \r\n        return False\r\n    else: \r\n        return True", "task_id": 138}
{"prompt": "Write a function to find the circumference of a circle.", "test": ["assert circle_circumference(10)==62.830000000000005", "assert circle_circumference(5)==31.415000000000003", "assert circle_circumference(4)==25.132"], "code": "def circle_circumference(r):\r\n  perimeter=2*3.1415*r\r\n  return perimeter", "task_id": 139}
{"prompt": "Write a function to extract elements that occur singly in the given tuple list.", "test": ["assert extract_singly([(3, 4, 5), (4, 5, 7), (1, 4)]) == [3, 4, 5, 7, 1]", "assert extract_singly([(1, 2, 3), (4, 2, 3), (7, 8)]) == [1, 2, 3, 4, 7, 8]", "assert extract_singly([(7, 8, 9), (10, 11, 12), (10, 11)]) == [7, 8, 9, 10, 11, 12]"], "code": "def extract_singly(test_list):\r\n  res = []\r\n  temp = set()\r\n  for inner in test_list:\r\n    for ele in inner:\r\n      if not ele in temp:\r\n        temp.add(ele)\r\n        res.append(ele)\r\n  return (res) ", "task_id": 140}
{"prompt": "Write a function to sort a list of elements using pancake sort.", "test": ["assert pancake_sort([15, 79, 25, 38, 69]) == [15, 25, 38, 69, 79]", "assert pancake_sort([98, 12, 54, 36, 85]) == [12, 36, 54, 85, 98]", "assert pancake_sort([41, 42, 32, 12, 23]) == [12, 23, 32, 41, 42]"], "code": "def pancake_sort(nums):\r\n    arr_len = len(nums)\r\n    while arr_len > 1:\r\n        mi = nums.index(max(nums[0:arr_len]))\r\n        nums = nums[mi::-1] + nums[mi+1:len(nums)]\r\n        nums = nums[arr_len-1::-1] + nums[arr_len:len(nums)]\r\n        arr_len -= 1\r\n    return nums", "task_id": 141}
{"prompt": "Write a function to count the same pair in three given lists.", "test": ["assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,9],[2,1,3,1,2,6,7,9])==3", "assert count_samepair([1,2,3,4,5,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==4", "assert count_samepair([1,2,3,4,2,6,7,8],[2,2,3,1,2,6,7,8],[2,1,3,1,2,6,7,8])==5"], "code": "def count_samepair(list1,list2,list3):\r\n    result = sum(m == n == o for m, n, o in zip(list1,list2,list3))\r\n    return result", "task_id": 142}
{"prompt": "Write a function to find number of lists present in the given tuple.", "test": ["assert find_lists(([1, 2, 3, 4], [5, 6, 7, 8])) == 2", "assert find_lists(([1, 2], [3, 4], [5, 6]))  == 3", "assert find_lists(([9, 8, 7, 6, 5, 4, 3, 2, 1])) == 1"], "code": "def find_lists(Input): \r\n\tif isinstance(Input, list): \r\n\t\treturn 1\r\n\telse: \r\n\t\treturn len(Input) ", "task_id": 143}
{"prompt": "Write a python function to find the sum of absolute differences in all pairs of the given array.", "test": ["assert sum_Pairs([1,8,9,15,16],5) == 74", "assert sum_Pairs([1,2,3,4],4) == 10", "assert sum_Pairs([1,2,3,4,5,7,9,11,14],9) == 188"], "code": "def sum_Pairs(arr,n): \r\n    sum = 0\r\n    for i in range(n - 1,-1,-1): \r\n        sum += i*arr[i] - (n-1-i) * arr[i] \r\n    return sum", "task_id": 144}
{"prompt": "Write a python function to find the maximum difference between any two elements in a given array.", "test": ["assert max_Abs_Diff((2,1,5,3),4) == 4", "assert max_Abs_Diff((9,3,2,5,1),5) == 8", "assert max_Abs_Diff((3,2,1),3) == 2"], "code": "def max_Abs_Diff(arr,n): \r\n    minEle = arr[0] \r\n    maxEle = arr[0] \r\n    for i in range(1, n): \r\n        minEle = min(minEle,arr[i]) \r\n        maxEle = max(maxEle,arr[i]) \r\n    return (maxEle - minEle) ", "task_id": 145}
{"prompt": "Write a function to find the ascii value of total characters in a string.", "test": ["assert ascii_value_string(\"python\")==112", "assert ascii_value_string(\"Program\")==80", "assert ascii_value_string(\"Language\")==76"], "code": "def ascii_value_string(str1):\r\n  for i in range(len(str1)):\r\n   return ord(str1[i])", "task_id": 146}
{"prompt": "Write a function to find the maximum total path sum in the given triangle.", "test": ["assert max_path_sum([[1, 0, 0], [4, 8, 0], [1, 5, 3]], 2, 2) == 14", "assert max_path_sum([[13, 0, 0], [7, 4, 0], [2, 4, 6]], 2, 2) == 24 ", "assert max_path_sum([[2, 0, 0], [11, 18, 0], [21, 25, 33]], 2, 2) == 53"], "code": "def max_path_sum(tri, m, n): \r\n\tfor i in range(m-1, -1, -1): \r\n\t\tfor j in range(i+1): \r\n\t\t\tif (tri[i+1][j] > tri[i+1][j+1]): \r\n\t\t\t\ttri[i][j] += tri[i+1][j] \r\n\t\t\telse: \r\n\t\t\t\ttri[i][j] += tri[i+1][j+1] \r\n\treturn tri[0][0]", "task_id": 147}
{"prompt": "Write a function to divide a number into two parts such that the sum of digits is maximum.", "test": ["assert sum_digits_twoparts(35)==17", "assert sum_digits_twoparts(7)==7", "assert sum_digits_twoparts(100)==19"], "code": "def sum_digits_single(x) : \r\n    ans = 0\r\n    while x : \r\n        ans += x % 10\r\n        x //= 10  \r\n    return ans \r\ndef closest(x) : \r\n    ans = 0\r\n    while (ans * 10 + 9 <= x) : \r\n        ans = ans * 10 + 9  \r\n    return ans   \r\ndef sum_digits_twoparts(N) : \r\n    A = closest(N)  \r\n    return sum_digits_single(A) + sum_digits_single(N - A) ", "task_id": 148}
{"prompt": "Write a function to find the longest subsequence such that the difference between adjacents is one for the given array.", "test": ["assert longest_subseq_with_diff_one([1, 2, 3, 4, 5, 3, 2], 7) == 6", "assert longest_subseq_with_diff_one([10, 9, 4, 5, 4, 8, 6], 7) == 3", "assert longest_subseq_with_diff_one([1, 2, 3, 2, 3, 7, 2, 1], 8) == 7"], "code": "def longest_subseq_with_diff_one(arr, n): \r\n\tdp = [1 for i in range(n)] \r\n\tfor i in range(n): \r\n\t\tfor j in range(i): \r\n\t\t\tif ((arr[i] == arr[j]+1) or (arr[i] == arr[j]-1)): \r\n\t\t\t\tdp[i] = max(dp[i], dp[j]+1) \r\n\tresult = 1\r\n\tfor i in range(n): \r\n\t\tif (result < dp[i]): \r\n\t\t\tresult = dp[i] \r\n\treturn result", "task_id": 149}
{"prompt": "Write a python function to find whether the given number is present in the infinite sequence or not.", "test": ["assert does_Contain_B(1,7,3) == True", "assert does_Contain_B(1,-3,5) == False", "assert does_Contain_B(3,2,5) == False"], "code": "def does_Contain_B(a,b,c): \r\n    if (a == b): \r\n        return True\r\n    if ((b - a) * c > 0 and (b - a) % c == 0): \r\n        return True\r\n    return False", "task_id": 150}
{"prompt": "Write a python function to check whether the given number is co-prime or not.", "test": ["assert is_coprime(17,13) == True", "assert is_coprime(15,21) == False", "assert is_coprime(25,45) == False"], "code": "def gcd(p,q):\r\n    while q != 0:\r\n        p, q = q,p%q\r\n    return p\r\ndef is_coprime(x,y):\r\n    return gcd(x,y) == 1", "task_id": 151}
{"prompt": "Write a function to sort the given array by using merge sort.", "test": ["assert merge_sort([3, 4, 2, 6, 5, 7, 1, 9]) == [1, 2, 3, 4, 5, 6, 7, 9]", "assert merge_sort([7, 25, 45, 78, 11, 33, 19]) == [7, 11, 19, 25, 33, 45, 78]", "assert merge_sort([3, 1, 4, 9, 8]) == [1, 3, 4, 8, 9]"], "code": "def merge(a,b):\r\n    c = []\r\n    while len(a) != 0 and len(b) != 0:\r\n        if a[0] < b[0]:\r\n            c.append(a[0])\r\n            a.remove(a[0])\r\n        else:\r\n            c.append(b[0])\r\n            b.remove(b[0])\r\n    if len(a) == 0:\r\n        c += b\r\n    else:\r\n        c += a\r\n    return c\r\ndef merge_sort(x):\r\n    if len(x) == 0 or len(x) == 1:\r\n        return x\r\n    else:\r\n        middle = len(x)//2\r\n        a = merge_sort(x[:middle])\r\n        b = merge_sort(x[middle:])\r\n        return merge(a,b)\r\n", "task_id": 152}
{"prompt": "Write a function to find the vertex of a parabola.", "test": ["assert parabola_vertex(5,3,2)==(-0.3, 1.55)", "assert parabola_vertex(9,8,4)==(-0.4444444444444444, 2.2222222222222223)", "assert parabola_vertex(2,4,6)==(-1.0, 4.0)"], "code": "def parabola_vertex(a, b, c): \r\n  vertex=(((-b / (2 * a)),(((4 * a * c) - (b * b)) / (4 * a))))\r\n  return vertex", "task_id": 153}
{"prompt": "Write a function to extract every specified element from a given two dimensional list.", "test": ["assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],0)==[1, 4, 7]", "assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],2)==[3, 6, 9]", "assert specified_element([[1, 2, 3, 2], [4, 5, 6, 2], [7, 1, 9, 5]],3)==[2,2,5]"], "code": "def specified_element(nums, N):\r\n    result = [i[N] for i in nums]\r\n    return result", "task_id": 154}
{"prompt": "Write a python function to toggle all even bits of a given number.", "test": ["assert even_bit_toggle_number(10) == 0", "assert even_bit_toggle_number(20) == 30", "assert even_bit_toggle_number(30) == 20"], "code": "def even_bit_toggle_number(n) : \r\n    res = 0; count = 0; temp = n \r\n    while (temp > 0) :     \r\n        if (count % 2 == 1) : \r\n            res = res | (1 << count)      \r\n        count = count + 1\r\n        temp >>= 1 \r\n    return n ^ res ", "task_id": 155}
{"prompt": "Write a function to convert a tuple of string values to a tuple of integer values.", "test": ["assert tuple_int_str((('333', '33'), ('1416', '55')))==((333, 33), (1416, 55))", "assert tuple_int_str((('999', '99'), ('1000', '500')))==((999, 99), (1000, 500))", "assert tuple_int_str((('666', '66'), ('1500', '555')))==((666, 66), (1500, 555))"], "code": "def tuple_int_str(tuple_str):\r\n    result = tuple((int(x[0]), int(x[1])) for x in tuple_str)\r\n    return result", "task_id": 156}
{"prompt": "Write a function to reflect the run-length encoding from a list.", "test": ["assert encode_list([1,1,2,3,4,4.3,5,1])==[[2, 1], [1, 2], [1, 3], [1, 4], [1, 4.3], [1, 5], [1, 1]]", "assert encode_list('automatically')==[[1, 'a'], [1, 'u'], [1, 't'], [1, 'o'], [1, 'm'], [1, 'a'], [1, 't'], [1, 'i'], [1, 'c'], [1, 'a'], [2, 'l'], [1, 'y']]", "assert encode_list('python')==[[1, 'p'], [1, 'y'], [1, 't'], [1, 'h'], [1, 'o'], [1, 'n']]"], "code": "from itertools import groupby\r\ndef encode_list(list1):\r\n    return [[len(list(group)), key] for key, group in groupby(list1)]", "task_id": 157}
{"prompt": "Write a python function to find k number of operations required to make all elements equal.", "test": ["assert min_Ops([2,2,2,2],4,3) == 0", "assert min_Ops([4,2,6,8],4,3) == -1", "assert min_Ops([21,33,9,45,63],5,6) == 24"], "code": "def min_Ops(arr,n,k): \r\n    max1 = max(arr) \r\n    res = 0\r\n    for i in range(0,n):  \r\n        if ((max1 - arr[i]) % k != 0): \r\n            return -1 \r\n        else: \r\n            res += (max1 - arr[i]) / k \r\n    return int(res) ", "task_id": 158}
{"prompt": "Write a function to print the season for the given month and day.", "test": ["assert month_season('January',4)==('winter')", "assert month_season('October',28)==('autumn')", "assert month_season('June',6)==('spring')"], "code": "def month_season(month,days):\r\n if month in ('January', 'February', 'March'):\r\n\t season = 'winter'\r\n elif month in ('April', 'May', 'June'):\r\n\t season = 'spring'\r\n elif month in ('July', 'August', 'September'):\r\n\t season = 'summer'\r\n else:\r\n\t season = 'autumn'\r\n if (month == 'March') and (days > 19):\r\n\t season = 'spring'\r\n elif (month == 'June') and (days > 20):\r\n\t season = 'summer'\r\n elif (month == 'September') and (days > 21):\r\n\t season = 'autumn'\r\n elif (month == 'October') and (days > 21):\r\n\t season = 'autumn'\r\n elif (month == 'November') and (days > 21):\r\n\t season = 'autumn'\r\n elif (month == 'December') and (days > 20):\r\n\t season = 'winter'\r\n return season", "task_id": 159}
{"prompt": "Write a function to find x and y that satisfies ax + by = n.", "test": ["assert solution(2, 3, 7) == ('x = ', 2, ', y = ', 1)", "assert solution(4, 2, 7) == 'No solution'", "assert solution(1, 13, 17) == ('x = ', 4, ', y = ', 1)"], "code": "def solution (a, b, n): \r\n\ti = 0\r\n\twhile i * a <= n: \r\n\t\tif (n - (i * a)) % b == 0: \r\n\t\t\treturn (\"x = \",i ,\", y = \", \r\n\t\t\tint((n - (i * a)) / b)) \r\n\t\t\treturn 0\r\n\t\ti = i + 1\r\n\treturn (\"No solution\") ", "task_id": 160}
{"prompt": "Write a function to remove all elements from a given list present in another list.", "test": ["assert remove_elements([1,2,3,4,5,6,7,8,9,10],[2,4,6,8])==[1, 3, 5, 7, 9, 10]", "assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],[1, 3, 5, 7])==[2, 4, 6, 8, 9, 10]", "assert remove_elements([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],[5,7])==[1, 2, 3, 4, 6, 8, 9, 10]"], "code": "def remove_elements(list1, list2):\r\n    result = [x for x in list1 if x not in list2]\r\n    return result", "task_id": 161}
{"prompt": "Write a function to calculate the sum of the positive integers of n+(n-2)+(n-4)... (until n-x =< 0).", "test": ["assert sum_series(6)==12", "assert sum_series(10)==30", "assert sum_series(9)==25"], "code": "def sum_series(n):\r\n  if n < 1:\r\n    return 0\r\n  else:\r\n    return n + sum_series(n - 2)", "task_id": 162}
{"prompt": "Write a function to calculate the area of a regular polygon.", "test": ["assert area_polygon(4,20)==400.00000000000006", "assert area_polygon(10,15)==1731.1969896610804", "assert area_polygon(9,7)==302.90938549487214"], "code": "from math import tan, pi\r\ndef area_polygon(s,l):\r\n  area = s * (l ** 2) / (4 * tan(pi / s))\r\n  return area", "task_id": 163}
{"prompt": "Write a python function to check whether the sum of divisors are same or not.", "test": ["assert areEquivalent(36,57) == False", "assert areEquivalent(2,4) == False", "assert areEquivalent(23,47) == True"], "code": "import math \r\ndef divSum(n): \r\n    sum = 1; \r\n    i = 2; \r\n    while(i * i <= n): \r\n        if (n % i == 0): \r\n            sum = (sum + i +math.floor(n / i)); \r\n        i += 1; \r\n    return sum; \r\ndef areEquivalent(num1,num2): \r\n    return divSum(num1) == divSum(num2); ", "task_id": 164}
{"prompt": "Write a python function to count characters at same position in a given string (lower and uppercase characters) as in english alphabet.", "test": ["assert count_char_position(\"xbcefg\") == 2", "assert count_char_position(\"ABcED\") == 3", "assert count_char_position(\"AbgdeF\") == 5"], "code": "def count_char_position(str1): \r\n    count_chars = 0\r\n    for i in range(len(str1)):\r\n        if ((i == ord(str1[i]) - ord('A')) or \r\n            (i == ord(str1[i]) - ord('a'))): \r\n            count_chars += 1\r\n    return count_chars ", "task_id": 165}
{"prompt": "Write a python function to count the pairs with xor as an even number.", "test": ["assert find_even_Pair([5,4,7,2,1],5) == 4", "assert find_even_Pair([7,2,8,1,0,5,11],7) == 9", "assert find_even_Pair([1,2,3],3) == 1"], "code": "def find_even_Pair(A,N): \r\n    evenPair = 0\r\n    for i in range(0,N): \r\n        for j in range(i+1,N): \r\n            if ((A[i] ^ A[j]) % 2 == 0): \r\n                evenPair+=1\r\n    return evenPair; ", "task_id": 166}
{"prompt": "Write a python function to find smallest power of 2 greater than or equal to n.", "test": ["assert next_Power_Of_2(0) == 1", "assert next_Power_Of_2(5) == 8", "assert next_Power_Of_2(17) == 32"], "code": "def next_Power_Of_2(n): \r\n    count = 0; \r\n    if (n and not(n & (n - 1))): \r\n        return n   \r\n    while( n != 0): \r\n        n >>= 1\r\n        count += 1\r\n    return 1 << count; ", "task_id": 167}
{"prompt": "Write a python function to find the frequency of a number in a given array.", "test": ["assert frequency([1,2,3],4) == 0", "assert frequency([1,2,2,3,3,3,4],3) == 3", "assert frequency([0,1,2,3,1,2],1) == 2"], "code": "def frequency(a,x): \r\n    count = 0  \r\n    for i in a: \r\n        if i == x: count += 1\r\n    return count ", "task_id": 168}
{"prompt": "Write a function to calculate the nth pell number.", "test": ["assert get_pell(4) == 12", "assert get_pell(7) == 169", "assert get_pell(8) == 408"], "code": "def get_pell(n): \r\n\tif (n <= 2): \r\n\t\treturn n \r\n\ta = 1\r\n\tb = 2\r\n\tfor i in range(3, n+1): \r\n\t\tc = 2 * b + a \r\n\t\ta = b \r\n\t\tb = c \r\n\treturn b ", "task_id": 169}
{"prompt": "Write a function to find sum of the numbers in a list between the indices of a specified range.", "test": ["assert sum_range_list( [2,1,5,6,8,3,4,9,10,11,8,12],8,10)==29", "assert sum_range_list( [2,1,5,6,8,3,4,9,10,11,8,12],5,7)==16", "assert sum_range_list( [2,1,5,6,8,3,4,9,10,11,8,12],7,10)==38"], "code": "def sum_range_list(list1, m, n):                                                                                                                                                                                                \r\n    sum_range = 0                                                                                                                                                                                                         \r\n    for i in range(m, n+1, 1):                                                                                                                                                                                        \r\n        sum_range += list1[i]                                                                                                                                                                                                  \r\n    return sum_range   ", "task_id": 170}
{"prompt": "Write a function to find the perimeter of a pentagon.", "test": ["assert perimeter_pentagon(5)==25", "assert perimeter_pentagon(10)==50", "assert perimeter_pentagon(15)==75"], "code": "import math\r\ndef perimeter_pentagon(a):\r\n  perimeter=(5*a)\r\n  return perimeter", "task_id": 171}
{"prompt": "Write a function to find the occurence of characters 'std' in the given string 1. list item 1. list item 1. list item 2. list item 2. list item 2. list item", "test": ["assert count_occurance(\"letstdlenstdporstd\") == 3", "assert count_occurance(\"truststdsolensporsd\") == 1", "assert count_occurance(\"makestdsostdworthit\") == 2"], "code": "def count_occurance(s):\r\n  count=0\r\n  for i in range(len(s)):\r\n    if (s[i]== 's' and s[i+1]=='t' and s[i+2]== 'd'):\r\n      count = count + 1\r\n  return count", "task_id": 172}
{"prompt": "Write a function to remove everything except alphanumeric characters from a string.", "test": ["assert remove_splchar('python  @#&^%$*program123')==('pythonprogram123')", "assert remove_splchar('python %^$@!^&*()  programming24%$^^()    language')==('pythonprogramming24language')", "assert remove_splchar('python   ^%&^()(+_)(_^&67)                  program')==('python67program')"], "code": "import re\r\ndef remove_splchar(text): \r\n pattern = re.compile('[\\W_]+')\r\n return (pattern.sub('', text))", "task_id": 173}
{"prompt": "Write a function to group a sequence of key-value pairs into a dictionary of lists.", "test": ["assert group_keyvalue([('yellow', 1), ('blue', 2), ('yellow', 3), ('blue', 4), ('red', 1)])=={'yellow': [1, 3], 'blue': [2, 4], 'red': [1]}", "assert group_keyvalue([('python', 1), ('python', 2), ('python', 3), ('python', 4), ('python', 5)])=={'python': [1,2,3,4,5]}", "assert group_keyvalue([('yellow',100), ('blue', 200), ('yellow', 300), ('blue', 400), ('red', 100)])=={'yellow': [100, 300], 'blue': [200, 400], 'red': [100]}"], "code": "def group_keyvalue(l):\r\n    result = {}\r\n    for k, v in l:\r\n         result.setdefault(k, []).append(v)\r\n    return result", "task_id": 174}
{"prompt": "Write a function to verify validity of a string of parentheses.", "test": ["assert is_valid_parenthese(\"(){}[]\")==True", "assert is_valid_parenthese(\"()[{)}\")==False", "assert is_valid_parenthese(\"()\")==True"], "code": "def is_valid_parenthese( str1):\r\n        stack, pchar = [], {\"(\": \")\", \"{\": \"}\", \"[\": \"]\"}\r\n        for parenthese in str1:\r\n            if parenthese in pchar:\r\n                stack.append(parenthese)\r\n            elif len(stack) == 0 or pchar[stack.pop()] != parenthese:\r\n                return False\r\n        return len(stack) == 0", "task_id": 175}
{"prompt": "Write a function to find the perimeter of a triangle.", "test": ["assert perimeter_triangle(10,20,30)==60", "assert perimeter_triangle(3,4,5)==12", "assert perimeter_triangle(25,35,45)==105"], "code": "def perimeter_triangle(a,b,c):\r\n  perimeter=a+b+c\r\n  return perimeter", "task_id": 176}
{"prompt": "Write a python function to find two distinct numbers such that their lcm lies within the given range.", "test": ["assert answer(3,8) == (3,6)", "assert answer(2,6) == (2,4)", "assert answer(1,3) == (1,2)"], "code": "def answer(L,R): \r\n    if (2 * L <= R): \r\n        return (L ,2*L)\r\n    else: \r\n        return (-1) ", "task_id": 177}
{"prompt": "Write a function to search some literals strings in a string.", "test": ["assert string_literals(['language'],'python language')==('Matched!')", "assert string_literals(['program'],'python language')==('Not Matched!')", "assert string_literals(['python'],'programming language')==('Not Matched!')"], "code": "import re\r\ndef string_literals(patterns,text):\r\n  for pattern in patterns:\r\n     if re.search(pattern,  text):\r\n       return ('Matched!')\r\n     else:\r\n       return ('Not Matched!')", "task_id": 178}
{"prompt": "Write a function to find if the given number is a keith number or not.", "test": ["assert is_num_keith(14) == True", "assert is_num_keith(12) == False", "assert is_num_keith(197) == True"], "code": "def is_num_keith(x): \r\n\tterms = [] \r\n\ttemp = x \r\n\tn = 0 \r\n\twhile (temp > 0): \r\n\t\tterms.append(temp % 10) \r\n\t\ttemp = int(temp / 10) \r\n\t\tn+=1 \r\n\tterms.reverse() \r\n\tnext_term = 0 \r\n\ti = n \r\n\twhile (next_term < x): \r\n\t\tnext_term = 0 \r\n\t\tfor j in range(1,n+1): \r\n\t\t\tnext_term += terms[i - j] \r\n\t\tterms.append(next_term) \r\n\t\ti+=1 \r\n\treturn (next_term == x) ", "task_id": 179}
{"prompt": "Write a function to calculate distance between two points using latitude and longitude.", "test": ["assert distance_lat_long(23.5,67.5,25.5,69.5)==12179.372041317429", "assert distance_lat_long(10.5,20.5,30.5,40.5)==6069.397933300514", "assert distance_lat_long(10,20,30,40)==6783.751974994595"], "code": "from math import radians, sin, cos, acos\r\ndef distance_lat_long(slat,slon,elat,elon):\r\n dist = 6371.01 * acos(sin(slat)*sin(elat) + cos(slat)*cos(elat)*cos(slon - elon))\r\n return dist", "task_id": 180}
{"prompt": "Write a function to find the longest common prefix in the given set of strings.", "test": ["assert common_prefix([\"tablets\", \"tables\", \"taxi\", \"tamarind\"], 4) == 'ta'", "assert common_prefix([\"apples\", \"ape\", \"april\"], 3) == 'ap'", "assert common_prefix([\"teens\", \"teenager\", \"teenmar\"], 3) == 'teen'"], "code": "def common_prefix_util(str1, str2): \r\n\tresult = \"\"; \r\n\tn1 = len(str1) \r\n\tn2 = len(str2) \r\n\ti = 0\r\n\tj = 0\r\n\twhile i <= n1 - 1 and j <= n2 - 1: \r\n\t\tif (str1[i] != str2[j]): \r\n\t\t\tbreak\r\n\t\tresult += str1[i] \r\n\t\ti += 1\r\n\t\tj += 1\r\n\treturn (result) \r\ndef common_prefix (arr, n): \r\n\tprefix = arr[0] \r\n\tfor i in range (1, n): \r\n\t\tprefix = common_prefix_util(prefix, arr[i]) \r\n\treturn (prefix) ", "task_id": 181}
{"prompt": "Write a function to find uppercase, lowercase, special character and numeric values using regex.", "test": ["assert find_character(\"ThisIsGeeksforGeeks\") == (['T', 'I', 'G', 'G'], ['h', 'i', 's', 's', 'e', 'e', 'k', 's', 'f', 'o', 'r', 'e', 'e', 'k', 's'], [], [])", "assert find_character(\"Hithere2\") == (['H'], ['i', 't', 'h', 'e', 'r', 'e'], ['2'], [])", "assert find_character(\"HeyFolks32\") == (['H', 'F'], ['e', 'y', 'o', 'l', 'k', 's'], ['3', '2'], [])"], "code": "import re\r\ndef find_character(string):\r\n  uppercase_characters = re.findall(r\"[A-Z]\", string) \r\n  lowercase_characters = re.findall(r\"[a-z]\", string) \r\n  numerical_characters = re.findall(r\"[0-9]\", string) \r\n  special_characters = re.findall(r\"[, .!?]\", string) \r\n  return uppercase_characters, lowercase_characters, numerical_characters, special_characters", "task_id": 182}
{"prompt": "Write a function to count all the distinct pairs having a difference of k in any array.", "test": ["assert count_pairs([1, 5, 3, 4, 2], 5, 3) == 2", "assert count_pairs([8, 12, 16, 4, 0, 20], 6, 4) == 5", "assert count_pairs([2, 4, 1, 3, 4], 5, 2) == 3"], "code": "def count_pairs(arr, n, k):\r\n  count=0;\r\n  for i in range(0,n):\r\n    for j in range(i+1, n):\r\n      if arr[i] - arr[j] == k or arr[j] - arr[i] == k:\r\n        count += 1\r\n  return count", "task_id": 183}
{"prompt": "Write a function to find all the values in a list that are greater than a specified number.", "test": ["assert greater_specificnum([220, 330, 500],200)==True", "assert greater_specificnum([12, 17, 21],20)==False", "assert greater_specificnum([1,2,3,4],10)==False"], "code": "def greater_specificnum(list,num):\r\n greater_specificnum=all(x >= num for x in list)\r\n return greater_specificnum", "task_id": 184}
{"prompt": "Write a function to find the focus of a parabola.", "test": ["assert parabola_focus(5,3,2)==(-0.3, 1.6)", "assert parabola_focus(9,8,4)==(-0.4444444444444444, 2.25)", "assert parabola_focus(2,4,6)==(-1.0, 4.125)"], "code": "def parabola_focus(a, b, c): \r\n  focus= (((-b / (2 * a)),(((4 * a * c) - (b * b) + 1) / (4 * a))))\r\n  return focus", "task_id": 185}
{"prompt": "Write a function to search some literals strings in a string by using regex.", "test": ["assert check_literals('The quick brown fox jumps over the lazy dog.',['fox']) == 'Matched!'", "assert check_literals('The quick brown fox jumps over the lazy dog.',['horse']) == 'Not Matched!'", "assert check_literals('The quick brown fox jumps over the lazy dog.',['lazy']) == 'Matched!'"], "code": "import re\r\ndef check_literals(text, patterns):\r\n  for pattern in patterns:\r\n    if re.search(pattern,  text):\r\n        return ('Matched!')\r\n    else:\r\n        return ('Not Matched!')", "task_id": 186}
{"prompt": "Write a function to find the longest common subsequence for the given two sequences.", "test": ["assert longest_common_subsequence(\"AGGTAB\" , \"GXTXAYB\", 6, 7) == 4", "assert longest_common_subsequence(\"ABCDGH\" , \"AEDFHR\", 6, 6) == 3", "assert longest_common_subsequence(\"AXYT\" , \"AYZX\", 4, 4) == 2"], "code": "def longest_common_subsequence(X, Y, m, n): \r\n    if m == 0 or n == 0: \r\n       return 0 \r\n    elif X[m-1] == Y[n-1]: \r\n       return 1 + longest_common_subsequence(X, Y, m-1, n-1) \r\n    else: \r\n       return max(longest_common_subsequence(X, Y, m, n-1), longest_common_subsequence(X, Y, m-1, n))", "task_id": 187}
{"prompt": "Write a python function to check whether the given number can be represented by product of two squares or not.", "test": ["assert prod_Square(25) == False", "assert prod_Square(30) == False", "assert prod_Square(16) == True"], "code": "def prod_Square(n):\r\n    for i in range(2,(n) + 1):\r\n        if (i*i < (n+1)):\r\n            for j in range(2,n + 1):\r\n                if ((i*i*j*j) == n):\r\n                    return True;\r\n    return False;", "task_id": 188}
{"prompt": "Write a python function to find the first missing positive number.", "test": ["assert first_Missing_Positive([1,2,3,-1,5],5) == 4", "assert first_Missing_Positive([0,-1,-2,1,5,8],6) == 2", "assert first_Missing_Positive([0,1,2,5,-8],5) == 3"], "code": "def first_Missing_Positive(arr,n): \r\n    ptr = 0\r\n    for i in range(n):\r\n        if arr[i] == 1:\r\n            ptr = 1\r\n            break\r\n    if ptr == 0:\r\n        return(1)\r\n    for i in range(n):\r\n        if arr[i] <= 0 or arr[i] > n:\r\n            arr[i] = 1\r\n    for i in range(n):\r\n        arr[(arr[i] - 1) % n] += n\r\n    for i in range(n):\r\n        if arr[i] <= n:\r\n            return(i + 1)\r\n    return(n + 1)", "task_id": 189}
{"prompt": "Write a python function to count the number of integral co-ordinates that lie inside a square.", "test": ["assert count_Intgral_Points(1,1,4,4) == 4", "assert count_Intgral_Points(1,2,1,2) == 1", "assert count_Intgral_Points(4,2,6,4) == 1"], "code": "def count_Intgral_Points(x1,y1,x2,y2): \r\n    return ((y2 - y1 - 1) * (x2 - x1 - 1)) ", "task_id": 190}
{"prompt": "Write a function to check whether the given month name contains 30 days or not.", "test": ["assert check_monthnumber(\"February\")==False", "assert check_monthnumber(\"June\")==True", "assert check_monthnumber(\"April\")==True"], "code": "def check_monthnumber(monthname3):\r\n  if monthname3 ==\"April\" or monthname3== \"June\" or monthname3== \"September\" or monthname3== \"November\":\r\n    return True\r\n  else:\r\n    return False", "task_id": 191}
{"prompt": "Write a python function to check whether a string has atleast one letter and one number.", "test": ["assert check_String('thishasboth29') == True", "assert check_String('python') == False", "assert check_String ('string') == False"], "code": "def check_String(str): \r\n    flag_l = False\r\n    flag_n = False\r\n    for i in str: \r\n        if i.isalpha(): \r\n            flag_l = True  \r\n        if i.isdigit(): \r\n            flag_n = True\r\n    return flag_l and flag_n ", "task_id": 192}
{"prompt": "Write a function to remove the duplicates from the given tuple.", "test": ["assert remove_tuple((1, 3, 5, 2, 3, 5, 1, 1, 3)) == (1, 2, 3, 5)", "assert remove_tuple((2, 3, 4, 4, 5, 6, 6, 7, 8, 8)) == (2, 3, 4, 5, 6, 7, 8)", "assert remove_tuple((11, 12, 13, 11, 11, 12, 14, 13)) == (11, 12, 13, 14)"], "code": "def remove_tuple(test_tup):\r\n  res = tuple(set(test_tup))\r\n  return (res) ", "task_id": 193}
{"prompt": "Write a python function to convert octal number to decimal number.", "test": ["assert octal_To_Decimal(25) == 21", "assert octal_To_Decimal(30) == 24", "assert octal_To_Decimal(40) == 32"], "code": "def octal_To_Decimal(n):  \r\n    num = n; \r\n    dec_value = 0; \r\n    base = 1; \r\n    temp = num; \r\n    while (temp): \r\n        last_digit = temp % 10; \r\n        temp = int(temp / 10); \r\n        dec_value += last_digit*base; \r\n        base = base * 8; \r\n    return dec_value; ", "task_id": 194}
{"prompt": "Write a python function to find the first position of an element in a sorted array.", "test": ["assert first([1,2,3,4,5,6,6],6,6) == 5", "assert first([1,2,2,2,3,2,2,4,2],2,9) == 1", "assert first([1,2,3],1,3) == 0"], "code": "def first(arr,x,n): \r\n    low = 0\r\n    high = n - 1\r\n    res = -1  \r\n    while (low <= high):\r\n        mid = (low + high) // 2 \r\n        if arr[mid] > x:\r\n            high = mid - 1\r\n        elif arr[mid] < x:\r\n            low = mid + 1\r\n        else:\r\n            res = mid\r\n            high = mid - 1\r\n    return res", "task_id": 195}
{"prompt": "Write a function to remove all the tuples with length k.", "test": ["assert remove_tuples([(4, 5), (4, ), (8, 6, 7), (1, ), (3, 4, 6, 7)] , 1) == [(4, 5), (8, 6, 7), (3, 4, 6, 7)]", "assert remove_tuples([(4, 5), (4,5), (6, 7), (1, 2, 3), (3, 4, 6, 7)] ,2) == [(1, 2, 3), (3, 4, 6, 7)]", "assert remove_tuples([(1, 4, 4), (4, 3), (8, 6, 7), (1, ), (3, 6, 7)] , 3) == [(4, 3), (1,)]"], "code": "def remove_tuples(test_list, K):\r\n  res = [ele for ele in test_list if len(ele) != K]\r\n  return (res) ", "task_id": 196}
{"prompt": "Write a function to perform the exponentiation of the given two tuples.", "test": ["assert find_exponentio((10, 4, 5, 6), (5, 6, 7, 5)) == (100000, 4096, 78125, 7776)", "assert find_exponentio((11, 5, 6, 7), (6, 7, 8, 6)) == (1771561, 78125, 1679616, 117649)", "assert find_exponentio((12, 6, 7, 8), (7, 8, 9, 7)) == (35831808, 1679616, 40353607, 2097152)"], "code": "def find_exponentio(test_tup1, test_tup2):\r\n  res = tuple(ele1 ** ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\r\n  return (res)\r\n", "task_id": 197}
{"prompt": "Write a function to find the largest triangle that can be inscribed in an ellipse.", "test": ["assert largest_triangle(4,2)==10.392304845413264", "assert largest_triangle(5,7)==4.639421805988064", "assert largest_triangle(9,1)==105.2220865598093"], "code": "import math\r\ndef largest_triangle(a,b): \r\n    if (a < 0 or b < 0): \r\n        return -1 \r\n    area = (3 * math.sqrt(3) * pow(a, 2)) / (4 * b);  \r\n    return area ", "task_id": 198}
{"prompt": "Write a python function to find highest power of 2 less than or equal to given number.", "test": ["assert highest_Power_of_2(10) == 8", "assert highest_Power_of_2(19) == 16", "assert highest_Power_of_2(32) == 32"], "code": "def highest_Power_of_2(n): \r\n    res = 0; \r\n    for i in range(n, 0, -1):  \r\n        if ((i & (i - 1)) == 0): \r\n            res = i; \r\n            break;      \r\n    return res; ", "task_id": 199}
{"prompt": "Write a function to find all index positions of the maximum values in a given list.", "test": ["assert position_max([12,33,23,10,67,89,45,667,23,12,11,10,54])==[7]", "assert position_max([1,2,2,2,4,4,4,5,5,5,5])==[7,8,9,10]", "assert position_max([2,1,5,6,8,3,4,9,10,11,8,12])==[11]"], "code": "def position_max(list1):\r\n    max_val = max(list1)\r\n    max_result = [i for i, j in enumerate(list1) if j == max_val]\r\n    return max_result", "task_id": 200}
{"prompt": "Write a python function to check whether the elements in a list are same or not.", "test": ["assert chkList(['one','one','one']) == True", "assert chkList(['one','Two','Three']) == False", "assert chkList(['bigdata','python','Django']) == False"], "code": "def chkList(lst): \r\n    return len(set(lst)) == 1", "task_id": 201}
{"prompt": "Write a function to remove even characters in a string.", "test": ["assert remove_even(\"python\")==(\"pto\")", "assert remove_even(\"program\")==(\"porm\")", "assert remove_even(\"language\")==(\"lnug\")"], "code": "def remove_even(str1):\r\n str2 = ''\r\n for i in range(1, len(str1) + 1):\r\n    if(i % 2 != 0):\r\n        str2 = str2 + str1[i - 1]\r\n return str2", "task_id": 202}
{"prompt": "Write a python function to find the hamming distance between given two integers.", "test": ["assert hamming_Distance(4,8) == 2", "assert hamming_Distance(2,4) == 2", "assert hamming_Distance(1,2) == 2"], "code": "def hamming_Distance(n1,n2) : \r\n    x = n1 ^ n2  \r\n    setBits = 0\r\n    while (x > 0) : \r\n        setBits += x & 1\r\n        x >>= 1\r\n    return setBits  ", "task_id": 203}
{"prompt": "Write a python function to count the occurrence of a given character in a string.", "test": ["assert count(\"abcc\",\"c\") == 2", "assert count(\"ababca\",\"a\") == 3", "assert count(\"mnmm0pm\",\"m\") == 4"], "code": "def count(s,c) : \r\n    res = 0 \r\n    for i in range(len(s)) : \r\n        if (s[i] == c): \r\n            res = res + 1\r\n    return res ", "task_id": 204}
{"prompt": "Write a function to find the inversions of tuple elements in the given tuple list.", "test": ["assert inversion_elements((7, 8, 9, 1, 10, 7)) == (-8, -9, -10, -2, -11, -8)", "assert inversion_elements((2, 4, 5, 6, 1, 7)) == (-3, -5, -6, -7, -2, -8)", "assert inversion_elements((8, 9, 11, 14, 12, 13)) == (-9, -10, -12, -15, -13, -14)"], "code": "def inversion_elements(test_tup):\r\n  res = tuple(list(map(lambda x: ~x, list(test_tup))))\r\n  return (res) ", "task_id": 205}
{"prompt": "Write a function to perform the adjacent element concatenation in the given tuples.", "test": ["assert concatenate_elements((\"DSP \", \"IS \", \"BEST \", \"FOR \", \"ALL \", \"UTS\")) == ('DSP IS ', 'IS BEST ', 'BEST FOR ', 'FOR ALL ', 'ALL UTS')", "assert concatenate_elements((\"RES \", \"IS \", \"BEST \", \"FOR \", \"ALL \", \"QESR\")) == ('RES IS ', 'IS BEST ', 'BEST FOR ', 'FOR ALL ', 'ALL QESR')", "assert concatenate_elements((\"MSAM\", \"IS \", \"BEST \", \"FOR \", \"ALL \", \"SKD\")) == ('MSAMIS ', 'IS BEST ', 'BEST FOR ', 'FOR ALL ', 'ALL SKD')"], "code": "def concatenate_elements(test_tup):\r\n  res = tuple(i + j for i, j in zip(test_tup, test_tup[1:]))\r\n  return (res) ", "task_id": 206}
{"prompt": "Write a function to count the longest repeating subsequences such that the two subsequences don\u2019t have same string characters at same positions.", "test": ["assert find_longest_repeating_subseq(\"AABEBCDD\") == 3", "assert find_longest_repeating_subseq(\"aabb\") == 2", "assert find_longest_repeating_subseq(\"aab\") == 1"], "code": "def find_longest_repeating_subseq(str): \r\n\tn = len(str) \r\n\tdp = [[0 for k in range(n+1)] for l in range(n+1)] \r\n\tfor i in range(1, n+1): \r\n\t\tfor j in range(1, n+1): \r\n\t\t\tif (str[i-1] == str[j-1] and i != j): \r\n\t\t\t\tdp[i][j] = 1 + dp[i-1][j-1] \r\n\t\t\telse: \r\n\t\t\t\tdp[i][j] = max(dp[i][j-1], dp[i-1][j]) \r\n\treturn dp[n][n]", "task_id": 207}
{"prompt": "Write a function to check the given decimal with a precision of 2 by using regex.", "test": ["assert is_decimal('123.11') == True", "assert is_decimal('0.21') == True", "assert is_decimal('123.1214') == False"], "code": "import re\r\ndef is_decimal(num):\r\n  num_fetch = re.compile(r\"\"\"^[0-9]+(\\.[0-9]{1,2})?$\"\"\")\r\n  result = num_fetch.search(num)\r\n  return bool(result)", "task_id": 208}
{"prompt": "Write a function to delete the smallest element from the given heap and then insert a new item.", "test": ["assert heap_replace( [25, 44, 68, 21, 39, 23, 89],21)==[21, 25, 23, 44, 39, 68, 89]", "assert heap_replace([25, 44, 68, 21, 39, 23, 89],110)== [23, 25, 68, 44, 39, 110, 89]", "assert heap_replace([25, 44, 68, 21, 39, 23, 89],500)==[23, 25, 68, 44, 39, 500, 89]"], "code": "import heapq as hq\r\ndef heap_replace(heap,a):\r\n  hq.heapify(heap)\r\n  hq.heapreplace(heap, a)\r\n  return heap", "task_id": 209}
{"prompt": "Write a function to check that the given string contains only a certain set of characters(in this case a-z, a-z and 0-9) by using regex.", "test": ["assert is_allowed_specific_char(\"ABCDEFabcdef123450\") == True", "assert is_allowed_specific_char(\"*&%@#!}{\") == False", "assert is_allowed_specific_char(\"HELLOhowareyou98765\") == True"], "code": "import re\r\ndef is_allowed_specific_char(string):\r\n    get_char = re.compile(r'[^a-zA-Z0-9.]')\r\n    string = get_char.search(string)\r\n    return not bool(string)", "task_id": 210}
{"prompt": "Write a python function to count numbers whose oth and nth bits are set.", "test": ["assert count_Num(2) == 1", "assert count_Num(3) == 2", "assert count_Num(1) == 1"], "code": "def count_Num(n): \r\n    if (n == 1): \r\n        return 1\r\n    count = pow(2,n - 2) \r\n    return count ", "task_id": 211}
{"prompt": "Write a python function to find the sum of fourth power of n natural numbers.", "test": ["assert fourth_Power_Sum(2) == 17", "assert fourth_Power_Sum(4) == 354", "assert fourth_Power_Sum(6) == 2275"], "code": "import math  \r\ndef fourth_Power_Sum(n): \r\n    sum = 0\r\n    for i in range(1,n+1) : \r\n        sum = sum + (i*i*i*i) \r\n    return sum", "task_id": 212}
{"prompt": "Write a function to perform the concatenation of two string tuples.", "test": ["assert concatenate_strings((\"Manjeet\", \"Nikhil\", \"Akshat\"), (\" Singh\", \" Meherwal\", \" Garg\")) == ('Manjeet Singh', 'Nikhil Meherwal', 'Akshat Garg')", "assert concatenate_strings((\"Shaik\", \"Ayesha\", \"Sanya\"), (\" Dawood\", \" Begum\", \" Singh\")) == ('Shaik Dawood', 'Ayesha Begum', 'Sanya Singh')", "assert concatenate_strings((\"Harpreet\", \"Priyanka\", \"Muskan\"), (\"Kour\", \" Agarwal\", \"Sethi\")) == ('HarpreetKour', 'Priyanka Agarwal', 'MuskanSethi')"], "code": "def concatenate_strings(test_tup1, test_tup2):\r\n  res = tuple(ele1 + ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 213}
{"prompt": "Write a function to convert radians to degrees.", "test": ["assert degree_radian(90)==5156.620156177409", "assert degree_radian(60)==3437.746770784939", "assert degree_radian(120)==6875.493541569878"], "code": "import math\r\ndef degree_radian(radian):\r\n degree = radian*(180/math.pi)\r\n return degree", "task_id": 214}
{"prompt": "Write a function to decode a run-length encoded given list.", "test": ["assert decode_list([[2, 1], 2, 3, [2, 4], 5,1])==[1,1,2,3,4,4,5,1]", "assert decode_list(['a', 'u', 't', 'o', 'm', 'a', 't', 'i', 'c', 'a', [2, 'l'], 'y'])==['a', 'u', 't', 'o', 'm', 'a', 't', 'i', 'c', 'a', 'l', 'l', 'y']", "assert decode_list(['p', 'y', 't', 'h', 'o', 'n'])==['p', 'y', 't', 'h', 'o', 'n']"], "code": "def decode_list(alist):\r\n    def aux(g):\r\n        if isinstance(g, list):\r\n            return [(g[1], range(g[0]))]\r\n        else:\r\n            return [(g, [0])]\r\n    return [x for g in alist for x, R in aux(g) for i in R]", "task_id": 215}
{"prompt": "Write a function to check if a nested list is a subset of another nested list.", "test": ["assert check_subset_list([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],[[12, 18, 23, 25, 45], [7, 11, 19, 24, 28], [1, 5, 8, 18, 15, 16]])==False", "assert check_subset_list([[2, 3, 1], [4, 5], [6, 8]],[[4, 5], [6, 8]])==True", "assert check_subset_list([['a', 'b'], ['e'], ['c', 'd']],[['g']])==False"], "code": "def check_subset_list(list1, list2): \r\n    l1, l2 = list1[0], list2[0] \r\n    exist = True\r\n    for i in list2: \r\n        if i not in list1: \r\n            exist = False\r\n    return exist ", "task_id": 216}
{"prompt": "Write a python function to find the first repeated character in a given string.", "test": ["assert first_Repeated_Char(\"Google\") == \"o\"", "assert first_Repeated_Char(\"data\") == \"a\"", "assert first_Repeated_Char(\"python\") == '\\0'"], "code": "def first_Repeated_Char(str): \r\n    h = {}\r\n    for ch in str:\r\n        if ch in h: \r\n            return ch;\r\n        else: \r\n            h[ch] = 0\r\n    return '\\0'", "task_id": 217}
{"prompt": "Write a python function to find the minimum operations required to make two numbers equal.", "test": ["assert min_Operations(2,4) == 1", "assert min_Operations(4,10) == 4", "assert min_Operations(1,4) == 3"], "code": "import math   \r\ndef min_Operations(A,B):  \r\n    if (A > B): \r\n        swap(A,B)  \r\n    B = B // math.gcd(A,B);  \r\n    return B - 1", "task_id": 218}
{"prompt": "Write a function to extract maximum and minimum k elements in the given tuple.", "test": ["assert extract_min_max((5, 20, 3, 7, 6, 8), 2) == (3, 5, 8, 20)", "assert extract_min_max((4, 5, 6, 1, 2, 7), 3) == (1, 2, 4, 5, 6, 7)", "assert extract_min_max((2, 3, 4, 8, 9, 11, 7), 4) == (2, 3, 4, 7, 8, 9, 11)"], "code": "\r\ndef extract_min_max(test_tup, K):\r\n  res = []\r\n  test_tup = list(test_tup)\r\n  temp = sorted(test_tup)\r\n  for idx, val in enumerate(temp):\r\n    if idx < K or idx >= len(temp) - K:\r\n      res.append(val)\r\n  res = tuple(res)\r\n  return (res) ", "task_id": 219}
{"prompt": "Write a function to replace maximum n occurrences of spaces, commas, or dots with a colon.", "test": ["assert replace_max_specialchar('Python language, Programming language.',2)==('Python:language: Programming language.')", "assert replace_max_specialchar('a b c,d e f',3)==('a:b:c:d e f')", "assert replace_max_specialchar('ram reshma,ram rahim',1)==('ram:reshma,ram rahim')"], "code": "import re\r\ndef replace_max_specialchar(text,n):\r\n return (re.sub(\"[ ,.]\", \":\", text, n))", "task_id": 220}
{"prompt": "Write a python function to find the first even number in a given list of numbers.", "test": ["assert first_even ([1, 3, 5, 7, 4, 1, 6, 8]) == 4", "assert first_even([2, 3, 4]) == 2", "assert first_even([5, 6, 7]) == 6"], "code": "def first_even(nums):\r\n    first_even = next((el for el in nums if el%2==0),-1)\r\n    return first_even", "task_id": 221}
{"prompt": "Write a function to check if all the elements in tuple have same data type or not.", "test": ["assert check_type((5, 6, 7, 3, 5, 6) ) == True", "assert check_type((1, 2, \"4\") ) == False", "assert check_type((3, 2, 1, 4, 5) ) == True"], "code": "def check_type(test_tuple):\r\n  res = True\r\n  for ele in test_tuple:\r\n    if not isinstance(ele, type(test_tuple[0])):\r\n      res = False\r\n      break\r\n  return (res) ", "task_id": 222}
{"prompt": "Write a function to check for majority element in the given sorted array.", "test": ["assert is_majority([1, 2, 3, 3, 3, 3, 10], 7, 3) == True", "assert is_majority([1, 1, 2, 4, 4, 4, 6, 6], 8, 4) == False", "assert is_majority([1, 1, 1, 2, 2], 5, 1) == True"], "code": "def is_majority(arr, n, x):\r\n\ti = binary_search(arr, 0, n-1, x)\r\n\tif i == -1:\r\n\t\treturn False\r\n\tif ((i + n//2) <= (n -1)) and arr[i + n//2] == x:\r\n\t\treturn True\r\n\telse:\r\n\t\treturn False\r\ndef binary_search(arr, low, high, x):\r\n\tif high >= low:\r\n\t\tmid = (low + high)//2 \r\n\t\tif (mid == 0 or x > arr[mid-1]) and (arr[mid] == x):\r\n\t\t\treturn mid\r\n\t\telif x > arr[mid]:\r\n\t\t\treturn binary_search(arr, (mid + 1), high, x)\r\n\t\telse:\r\n\t\t\treturn binary_search(arr, low, (mid -1), x)\r\n\treturn -1", "task_id": 223}
{"prompt": "Write a python function to count set bits of a given number.", "test": ["assert count_Set_Bits(2) == 1", "assert count_Set_Bits(4) == 1", "assert count_Set_Bits(6) == 2"], "code": "def count_Set_Bits(n): \r\n    count = 0\r\n    while (n): \r\n        count += n & 1\r\n        n >>= 1\r\n    return count ", "task_id": 224}
{"prompt": "Write a python function to find the minimum element in a sorted and rotated array.", "test": ["assert find_Min([1,2,3,4,5],0,4) == 1", "assert find_Min([4,6,8],0,2) == 4", "assert find_Min([2,3,5,7,9],0,4) == 2"], "code": "def find_Min(arr,low,high): \r\n    while (low < high): \r\n        mid = low + (high - low) // 2;   \r\n        if (arr[mid] == arr[high]): \r\n            high -= 1; \r\n        elif (arr[mid] > arr[high]): \r\n            low = mid + 1; \r\n        else: \r\n            high = mid; \r\n    return arr[high]; ", "task_id": 225}
{"prompt": "Write a python function to remove the characters which have odd index values of a given string.", "test": ["assert odd_values_string('abcdef') == 'ace'", "assert odd_values_string('python') == 'pto'", "assert odd_values_string('data') == 'dt'"], "code": "def odd_values_string(str):\r\n  result = \"\" \r\n  for i in range(len(str)):\r\n    if i % 2 == 0:\r\n      result = result + str[i]\r\n  return result", "task_id": 226}
{"prompt": "Write a function to find minimum of three numbers.", "test": ["assert min_of_three(10,20,0)==0", "assert min_of_three(19,15,18)==15", "assert min_of_three(-10,-20,-30)==-30"], "code": "def min_of_three(a,b,c): \r\n      if (a <= b) and (a <= c): \r\n        smallest = a \r\n      elif (b <= a) and (b <= c): \r\n        smallest = b \r\n      else: \r\n        smallest = c \r\n      return smallest ", "task_id": 227}
{"prompt": "Write a python function to check whether all the bits are unset in the given range or not.", "test": ["assert all_Bits_Set_In_The_Given_Range(4,1,2) == True", "assert all_Bits_Set_In_The_Given_Range(17,2,4) == True", "assert all_Bits_Set_In_The_Given_Range(39,4,6) == False"], "code": "def all_Bits_Set_In_The_Given_Range(n,l,r):  \r\n    num = (((1 << r) - 1) ^ ((1 << (l - 1)) - 1)) \r\n    new_num = n & num\r\n    if (new_num == 0): \r\n        return True\r\n    return False", "task_id": 228}
{"prompt": "Write a function to re-arrange the elements of the given array so that all negative elements appear before positive ones.", "test": ["assert re_arrange_array([-1, 2, -3, 4, 5, 6, -7, 8, 9], 9) == [-1, -3, -7, 4, 5, 6, 2, 8, 9]", "assert re_arrange_array([12, -14, -26, 13, 15], 5) == [-14, -26, 12, 13, 15]", "assert re_arrange_array([10, 24, 36, -42, -39, -78, 85], 7) == [-42, -39, -78, 10, 24, 36, 85]"], "code": "def re_arrange_array(arr, n):\r\n  j=0\r\n  for i in range(0, n):\r\n    if (arr[i] < 0):\r\n      temp = arr[i]\r\n      arr[i] = arr[j]\r\n      arr[j] = temp\r\n      j = j + 1\r\n  return arr", "task_id": 229}
{"prompt": "Write a function to replace blank spaces with any character in a string.", "test": ["assert replace_blank(\"hello people\",'@')==(\"hello@people\")", "assert replace_blank(\"python program language\",'$')==(\"python$program$language\")", "assert replace_blank(\"blank space\",\"-\")==(\"blank-space\")"], "code": "def replace_blank(str1,char):\r\n str2 = str1.replace(' ', char)\r\n return str2", "task_id": 230}
{"prompt": "Write a function to find the maximum sum in the given right triangle of numbers.", "test": ["assert max_sum([[1], [2,1], [3,3,2]], 3) == 6", "assert max_sum([[1], [1, 2], [4, 1, 12]], 3) == 15 ", "assert max_sum([[2], [3,2], [13,23,12]], 3) == 28"], "code": "def max_sum(tri, n): \r\n\tif n > 1: \r\n\t\ttri[1][1] = tri[1][1]+tri[0][0] \r\n\t\ttri[1][0] = tri[1][0]+tri[0][0] \r\n\tfor i in range(2, n): \r\n\t\ttri[i][0] = tri[i][0] + tri[i-1][0] \r\n\t\ttri[i][i] = tri[i][i] + tri[i-1][i-1] \r\n\t\tfor j in range(1, i): \r\n\t\t\tif tri[i][j]+tri[i-1][j-1] >= tri[i][j]+tri[i-1][j]: \r\n\t\t\t\ttri[i][j] = tri[i][j] + tri[i-1][j-1] \r\n\t\t\telse: \r\n\t\t\t\ttri[i][j] = tri[i][j]+tri[i-1][j] \r\n\treturn (max(tri[n-1]))", "task_id": 231}
{"prompt": "Write a function to get the n largest items from a dataset.", "test": ["assert larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],2)==[100,90]", "assert larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],5)==[100,90,80,70,60]", "assert larg_nnum([10, 20, 50, 70, 90, 20, 50, 40, 60, 80, 100],3)==[100,90,80]"], "code": "import heapq\r\ndef larg_nnum(list1,n):\r\n largest=heapq.nlargest(n,list1)\r\n return largest", "task_id": 232}
{"prompt": "Write a function to find the lateral surface area of a cylinder.", "test": ["assert lateralsuface_cylinder(10,5)==314.15000000000003", "assert lateralsuface_cylinder(4,5)==125.66000000000001", "assert lateralsuface_cylinder(4,10)==251.32000000000002"], "code": "def lateralsuface_cylinder(r,h):\r\n  lateralsurface= 2*3.1415*r*h\r\n  return lateralsurface", "task_id": 233}
{"prompt": "Write a function to find the volume of a cube.", "test": ["assert volume_cube(3)==27", "assert volume_cube(2)==8", "assert volume_cube(5)==125"], "code": "def volume_cube(l):\r\n  volume = l * l * l\r\n  return volume", "task_id": 234}
{"prompt": "Write a python function to set all even bits of a given number.", "test": ["assert even_bit_set_number(10) == 10", "assert even_bit_set_number(20) == 30", "assert even_bit_set_number(30) == 30"], "code": "def even_bit_set_number(n): \r\n    count = 0;res = 0;temp = n \r\n    while(temp > 0): \r\n        if (count % 2 == 1): \r\n            res |= (1 << count)\r\n        count+=1\r\n        temp >>= 1\r\n    return (n | res) ", "task_id": 235}
{"prompt": "Write a python function to count the maximum number of equilateral triangles that can be formed within a given equilateral triangle.", "test": ["assert No_of_Triangle(4,2) == 7", "assert No_of_Triangle(4,3) == 3", "assert No_of_Triangle(1,3) == -1"], "code": "def No_of_Triangle(N,K):\r\n    if (N < K):\r\n        return -1;\r\n    else:\r\n        Tri_up = 0;\r\n        Tri_up = ((N - K + 1) *(N - K + 2)) // 2;\r\n        Tri_down = 0;\r\n        Tri_down = ((N - 2 * K + 1) *(N - 2 * K + 2)) // 2;\r\n        return Tri_up + Tri_down;", "task_id": 236}
{"prompt": "Write a function to check the occurrences of records which occur similar times in the given tuples.", "test": ["assert check_occurences([(3, 1), (1, 3), (2, 5), (5, 2), (6, 3)] ) == {(1, 3): 2, (2, 5): 2, (3, 6): 1}", "assert check_occurences([(4, 2), (2, 4), (3, 6), (6, 3), (7, 4)] ) == {(2, 4): 2, (3, 6): 2, (4, 7): 1}", "assert check_occurences([(13, 2), (11, 23), (12, 25), (25, 12), (16, 23)] ) == {(2, 13): 1, (11, 23): 1, (12, 25): 2, (16, 23): 1}"], "code": "from collections import Counter \r\ndef check_occurences(test_list):\r\n  res = dict(Counter(tuple(ele) for ele in map(sorted, test_list)))\r\n  return  (res) ", "task_id": 237}
{"prompt": "Write a python function to count number of non-empty substrings of a given string.", "test": ["assert number_of_substrings(\"abc\") == 6", "assert number_of_substrings(\"abcd\") == 10", "assert number_of_substrings(\"abcde\") == 15"], "code": "def number_of_substrings(str): \r\n\tstr_len = len(str); \r\n\treturn int(str_len * (str_len + 1) / 2); ", "task_id": 238}
{"prompt": "Write a function to find the number of possible sequences of length n such that each of the next element is greater than or equal to twice of the previous element but less than or equal to m.", "test": ["assert get_total_number_of_sequences(10, 4) == 4", "assert get_total_number_of_sequences(5, 2) == 6", "assert get_total_number_of_sequences(16, 3) == 84"], "code": "def get_total_number_of_sequences(m,n): \r\n\tT=[[0 for i in range(n+1)] for i in range(m+1)] \r\n\tfor i in range(m+1): \r\n\t\tfor j in range(n+1): \r\n\t\t\tif i==0 or j==0: \r\n\t\t\t\tT[i][j]=0\r\n\t\t\telif i<j: \r\n\t\t\t\tT[i][j]=0\r\n\t\t\telif j==1: \r\n\t\t\t\tT[i][j]=i \r\n\t\t\telse: \r\n\t\t\t\tT[i][j]=T[i-1][j]+T[i//2][j-1] \r\n\treturn T[m][n]", "task_id": 239}
{"prompt": "Write a function to replace the last element of the list with another list.", "test": ["assert replace_list([1, 3, 5, 7, 9, 10],[2, 4, 6, 8])==[1, 3, 5, 7, 9, 2, 4, 6, 8]", "assert replace_list([1,2,3,4,5],[5,6,7,8])==[1,2,3,4,5,6,7,8]", "assert replace_list([\"red\",\"blue\",\"green\"],[\"yellow\"])==[\"red\",\"blue\",\"yellow\"]"], "code": "def replace_list(list1,list2):\r\n list1[-1:] = list2\r\n replace_list=list1\r\n return replace_list\r\n", "task_id": 240}
{"prompt": "Write a function to generate a 3d array having each element as '*'.", "test": ["assert array_3d(6,4,3)==[[['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*']], [['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*']], [['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*', '*']]]", "assert array_3d(5,3,4)==[[['*', '*', '*', '*', '*'], ['*', '*', '*', '*','*'], ['*', '*', '*', '*', '*']], [['*', '*', '*', '*', '*'],['*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*']], [['*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*']], [['*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*'], ['*', '*', '*', '*', '*']]]", "assert array_3d(1,2,3)==[[['*'],['*']],[['*'],['*']],[['*'],['*']]]"], "code": "def array_3d(m,n,o):\r\n array_3d = [[ ['*' for col in range(m)] for col in range(n)] for row in range(o)]\r\n return array_3d", "task_id": 241}
{"prompt": "Write a function to count total characters in a string.", "test": ["assert count_charac(\"python programming\")==18", "assert count_charac(\"language\")==8", "assert count_charac(\"words\")==5"], "code": "def count_charac(str1):\r\n total = 0\r\n for i in str1:\r\n    total = total + 1\r\n return total", "task_id": 242}
{"prompt": "Write a function to sort the given list based on the occurrence of first element of tuples.", "test": ["assert sort_on_occurence([(1, 'Jake'), (2, 'Bob'), (1, 'Cara')]) == [(1, 'Jake', 'Cara', 2), (2, 'Bob', 1)]", "assert sort_on_occurence([('b', 'ball'), ('a', 'arm'), ('b', 'b'), ('a', 'ant')]) == [('b', 'ball', 'b', 2), ('a', 'arm', 'ant', 2)]", "assert sort_on_occurence([(2, 'Mark'), (3, 'Maze'), (2, 'Sara')]) == [(2, 'Mark', 'Sara', 2), (3, 'Maze', 1)]"], "code": "def sort_on_occurence(lst): \r\n\tdct = {} \r\n\tfor i, j in lst: \r\n\t\tdct.setdefault(i, []).append(j) \r\n\treturn ([(i, *dict.fromkeys(j), len(j)) \r\n\t\t\t\tfor i, j in dct.items()]) ", "task_id": 243}
{"prompt": "Write a python function to find the next perfect square greater than a given number.", "test": ["assert next_Perfect_Square(35) == 36", "assert next_Perfect_Square(6) == 9", "assert next_Perfect_Square(9) == 16"], "code": "import math  \r\ndef next_Perfect_Square(N): \r\n    nextN = math.floor(math.sqrt(N)) + 1\r\n    return nextN * nextN ", "task_id": 244}
{"prompt": "Write a function to find the maximum sum of bi-tonic sub-sequence for the given array.", "test": ["assert max_sum([1, 15, 51, 45, 33, 100, 12, 18, 9], 9) == 194", "assert max_sum([80, 60, 30, 40, 20, 10], 6) == 210", "assert max_sum([2, 3 ,14, 16, 21, 23, 29, 30], 8) == 138"], "code": "def max_sum(arr, n): \r\n\tMSIBS = arr[:] \r\n\tfor i in range(n): \r\n\t\tfor j in range(0, i): \r\n\t\t\tif arr[i] > arr[j] and MSIBS[i] < MSIBS[j] + arr[i]: \r\n\t\t\t\tMSIBS[i] = MSIBS[j] + arr[i] \r\n\tMSDBS = arr[:] \r\n\tfor i in range(1, n + 1): \r\n\t\tfor j in range(1, i): \r\n\t\t\tif arr[-i] > arr[-j] and MSDBS[-i] < MSDBS[-j] + arr[-i]: \r\n\t\t\t\tMSDBS[-i] = MSDBS[-j] + arr[-i] \r\n\tmax_sum = float(\"-Inf\") \r\n\tfor i, j, k in zip(MSIBS, MSDBS, arr): \r\n\t\tmax_sum = max(max_sum, i + j - k) \r\n\treturn max_sum", "task_id": 245}
{"prompt": "Write a function for computing square roots using the babylonian method.", "test": ["assert babylonian_squareroot(10)==3.162277660168379", "assert babylonian_squareroot(2)==1.***************", "assert babylonian_squareroot(9)==3.0"], "code": "def babylonian_squareroot(number):\r\n    if(number == 0):\r\n        return 0;\r\n    g = number/2.0;\r\n    g2 = g + 1;\r\n    while(g != g2):\r\n        n = number/ g;\r\n        g2 = g;\r\n        g = (g + n)/2;\r\n    return g;", "task_id": 246}
{"prompt": "Write a function to find the longest palindromic subsequence in the given string.", "test": ["assert lps(\"TENS FOR TENS\") == 5 ", "assert lps(\"CARDIO FOR CARDS\") == 7", "assert lps(\"PART OF THE JOURNEY IS PART\") == 9 "], "code": "def lps(str): \r\n\tn = len(str) \r\n\tL = [[0 for x in range(n)] for x in range(n)] \r\n\tfor i in range(n): \r\n\t\tL[i][i] = 1\r\n\tfor cl in range(2, n+1): \r\n\t\tfor i in range(n-cl+1): \r\n\t\t\tj = i+cl-1\r\n\t\t\tif str[i] == str[j] and cl == 2: \r\n\t\t\t\tL[i][j] = 2\r\n\t\t\telif str[i] == str[j]: \r\n\t\t\t\tL[i][j] = L[i+1][j-1] + 2\r\n\t\t\telse: \r\n\t\t\t\tL[i][j] = max(L[i][j-1], L[i+1][j]); \r\n\treturn L[0][n-1]", "task_id": 247}
{"prompt": "Write a function to calculate the harmonic sum of n-1.", "test": ["assert harmonic_sum(7) == 2.5928571428571425", "assert harmonic_sum(4) == 2.083333333333333", "assert harmonic_sum(19) == 3.547739657143682"], "code": "def harmonic_sum(n):\r\n  if n < 2:\r\n    return 1\r\n  else:\r\n    return 1 / n + (harmonic_sum(n - 1)) ", "task_id": 248}
{"prompt": "Write a function to find the intersection of two arrays using lambda function.", "test": ["assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[1, 2, 4, 8, 9])==[1, 2, 8, 9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[3,5,7,9])==[3,5,7,9]", "assert intersection_array([1, 2, 3, 5, 7, 8, 9, 10],[10,20,30,40])==[10]"], "code": "def intersection_array(array_nums1,array_nums2):\r\n result = list(filter(lambda x: x in array_nums1, array_nums2)) \r\n return result", "task_id": 249}
{"prompt": "Write a python function to count the occcurences of an element in a tuple.", "test": ["assert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),4) == 0", "assert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),10) == 3", "assert count_X((10, 8, 5, 2, 10, 15, 10, 8, 5, 8, 8, 2),8) == 4"], "code": "def count_X(tup, x): \r\n    count = 0\r\n    for ele in tup: \r\n        if (ele == x): \r\n            count = count + 1\r\n    return count ", "task_id": 250}
{"prompt": "Write a function to insert an element before each element of a list.", "test": ["assert insert_element(['Red', 'Green', 'Black'] ,'c')==['c', 'Red', 'c', 'Green', 'c', 'Black'] ", "assert insert_element(['python', 'java'] ,'program')==['program', 'python', 'program', 'java'] ", "assert insert_element(['happy', 'sad'] ,'laugh')==['laugh', 'happy', 'laugh', 'sad'] "], "code": "def insert_element(list,element):\r\n list = [v for elt in list for v in (element, elt)]\r\n return list", "task_id": 251}
{"prompt": "Write a python function to convert complex numbers to polar coordinates.", "test": ["assert convert(1) == (1.0, 0.0)", "assert convert(4) == (4.0,0.0)", "assert convert(5) == (5.0,0.0)"], "code": "import cmath  \r\ndef convert(numbers):    \r\n  num = cmath.polar(numbers)  \r\n  return (num) ", "task_id": 252}
{"prompt": "Write a python function to count integers from a given list.", "test": ["assert count_integer([1,2,'abc',1.2]) == 2", "assert count_integer([1,2,3]) == 3", "assert count_integer([1,1.2,4,5.1]) == 2"], "code": "def count_integer(list1):\r\n    ctr = 0\r\n    for i in list1:\r\n        if isinstance(i, int):\r\n            ctr = ctr + 1\r\n    return ctr", "task_id": 253}
{"prompt": "Write a function to find all words starting with 'a' or 'e' in a given string.", "test": ["assert words_ae(\"python programe\")==['ame']", "assert words_ae(\"python programe language\")==['ame','anguage']", "assert words_ae(\"assert statement\")==['assert', 'atement']"], "code": "import re\r\ndef words_ae(text):\r\n list = re.findall(\"[ae]\\w+\", text)\r\n return list", "task_id": 254}
{"prompt": "Write a function to choose specified number of colours from three different colours and generate all the combinations with repetitions.", "test": ["assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],1)==[('Red',), ('Green',), ('Blue',)]", "assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],2)==[('Red', 'Red'), ('Red', 'Green'), ('Red', 'Blue'), ('Green', 'Green'), ('Green', 'Blue'), ('Blue', 'Blue')]", "assert combinations_colors( [\"Red\",\"Green\",\"Blue\"],3)==[('Red', 'Red', 'Red'), ('Red', 'Red', 'Green'), ('Red', 'Red', 'Blue'), ('Red', 'Green', 'Green'), ('Red', 'Green', 'Blue'), ('Red', 'Blue', 'Blue'), ('Green', 'Green', 'Green'), ('Green', 'Green', 'Blue'), ('Green', 'Blue', 'Blue'), ('Blue', 'Blue', 'Blue')]"], "code": "from itertools import combinations_with_replacement \r\ndef combinations_colors(l, n):\r\n    return list(combinations_with_replacement(l,n))\r", "task_id": 255}
{"prompt": "Write a python function to count the number of prime numbers less than a given non-negative number.", "test": ["assert count_Primes_nums(5) == 2", "assert count_Primes_nums(10) == 4", "assert count_Primes_nums(100) == 25"], "code": "def count_Primes_nums(n):\r\n    ctr = 0\r\n    for num in range(n):\r\n        if num <= 1:\r\n            continue\r\n        for i in range(2,num):\r\n            if (num % i) == 0:\r\n                break\r\n        else:\r\n            ctr += 1\r\n    return ctr", "task_id": 256}
{"prompt": "Write a function to swap two numbers.", "test": ["assert swap_numbers(10,20)==(20,10)", "assert swap_numbers(15,17)==(17,15)", "assert swap_numbers(100,200)==(200,100)"], "code": "def swap_numbers(a,b):\r\n temp = a\r\n a = b\r\n b = temp\r\n return (a,b)", "task_id": 257}
{"prompt": "Write a function to find number of odd elements in the given list using lambda function.", "test": ["assert count_odd([1, 2, 3, 5, 7, 8, 10])==4", "assert count_odd([10,15,14,13,-18,12,-20])==2", "assert count_odd([1, 2, 4, 8, 9])==2"], "code": "def count_odd(array_nums):\r\n   count_odd = len(list(filter(lambda x: (x%2 != 0) , array_nums)))\r\n   return count_odd", "task_id": 258}
{"prompt": "Write a function to maximize the given two tuples.", "test": ["assert maximize_elements(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((6, 7), (4, 9), (2, 9), (7, 10))", "assert maximize_elements(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((7, 8), (5, 10), (3, 10), (8, 11))", "assert maximize_elements(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((8, 9), (6, 11), (4, 11), (9, 12))"], "code": "def maximize_elements(test_tup1, test_tup2):\r\n  res = tuple(tuple(max(a, b) for a, b in zip(tup1, tup2))\r\n   for tup1, tup2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 259}
{"prompt": "Write a function to find the nth newman\u2013shanks\u2013williams prime number.", "test": ["assert newman_prime(3) == 7 ", "assert newman_prime(4) == 17", "assert newman_prime(5) == 41"], "code": "def newman_prime(n): \r\n\tif n == 0 or n == 1: \r\n\t\treturn 1\r\n\treturn 2 * newman_prime(n - 1) + newman_prime(n - 2)", "task_id": 260}
{"prompt": "Write a function to perform mathematical division operation across the given tuples.", "test": ["assert division_elements((10, 4, 6, 9),(5, 2, 3, 3)) == (2, 2, 2, 3)", "assert division_elements((12, 6, 8, 16),(6, 3, 4, 4)) == (2, 2, 2, 4)", "assert division_elements((20, 14, 36, 18),(5, 7, 6, 9)) == (4, 2, 6, 2)"], "code": "def division_elements(test_tup1, test_tup2):\r\n  res = tuple(ele1 // ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 261}
{"prompt": "Write a function to split a given list into two parts where the length of the first part of the list is given.", "test": ["assert split_two_parts([1,1,2,3,4,4,5,1],3)==([1, 1, 2], [3, 4, 4, 5, 1])", "assert split_two_parts(['a', 'b', 'c', 'd'],2)==(['a', 'b'], ['c', 'd'])", "assert split_two_parts(['p', 'y', 't', 'h', 'o', 'n'],4)==(['p', 'y', 't', 'h'], ['o', 'n'])"], "code": "def split_two_parts(list1, L):\r\n    return list1[:L], list1[L:]", "task_id": 262}
{"prompt": "Write a function to merge two dictionaries.", "test": ["assert merge_dict({'a': 100, 'b': 200},{'x': 300, 'y': 200})=={'x': 300, 'y': 200, 'a': 100, 'b': 200}", "assert merge_dict({'a':900,'b':900,'d':900},{'a':900,'b':900,'d':900})=={'a':900,'b':900,'d':900,'a':900,'b':900,'d':900}", "assert merge_dict({'a':10,'b':20},{'x':30,'y':40})=={'x':30,'y':40,'a':10,'b':20}"], "code": "def merge_dict(d1,d2):\r\n d = d1.copy()\r\n d.update(d2)\r\n return d", "task_id": 263}
{"prompt": "Write a function to calculate a dog's age in dog's years.", "test": ["assert dog_age(12)==61", "assert dog_age(15)==73", "assert dog_age(24)==109"], "code": "def dog_age(h_age):\r\n if h_age < 0:\r\n \texit()\r\n elif h_age <= 2:\r\n\t d_age = h_age * 10.5\r\n else:\r\n\t d_age = 21 + (h_age - 2)*4\r\n return d_age", "task_id": 264}
{"prompt": "Write a function to split a list for every nth element.", "test": ["assert list_split(['a', 'b', 'c', 'd', 'e', 'f', 'g', 'h', 'i', 'j', 'k', 'l', 'm', 'n'],3)==[['a', 'd', 'g', 'j', 'm'], ['b', 'e', 'h', 'k', 'n'], ['c', 'f', 'i', 'l']] ", "assert list_split([1,2,3,4,5,6,7,8,9,10,11,12,13,14],3)==[[1,4,7,10,13], [2,5,8,11,14], [3,6,9,12]] ", "assert list_split(['python','java','C','C++','DBMS','SQL'],2)==[['python', 'C', 'DBMS'], ['java', 'C++', 'SQL']] "], "code": "def list_split(S, step):\r\n    return [S[i::step] for i in range(step)]", "task_id": 265}
{"prompt": "Write a function to find the lateral surface area of a cube.", "test": ["assert lateralsurface_cube(5)==100", "assert lateralsurface_cube(9)==324", "assert lateralsurface_cube(10)==400"], "code": "def lateralsurface_cube(l):\r\n  LSA = 4 * (l * l)\r\n  return LSA", "task_id": 266}
{"prompt": "Write a python function to find the sum of squares of first n odd natural numbers.", "test": ["assert square_Sum(2) == 10", "assert square_Sum(3) == 35", "assert square_Sum(4) == 84"], "code": "def square_Sum(n):  \r\n    return int(n*(4*n*n-1)/3) ", "task_id": 267}
{"prompt": "Write a function to find the n'th star number.", "test": ["assert find_star_num(3) == 37", "assert find_star_num(4) == 73", "assert find_star_num(5) == 121"], "code": "def find_star_num(n): \r\n\treturn (6 * n * (n - 1) + 1) ", "task_id": 268}
{"prompt": "Write a function to find the ascii value of a character.", "test": ["assert ascii_value('A')==65", "assert ascii_value('R')==82", "assert ascii_value('S')==83"], "code": "def ascii_value(k):\r\n  ch=k\r\n  return ord(ch)", "task_id": 269}
{"prompt": "Write a python function to find the sum of even numbers at even positions.", "test": ["assert sum_even_and_even_index([5, 6, 12, 1, 18, 8],6) == 30", "assert sum_even_and_even_index([3, 20, 17, 9, 2, 10, 18, 13, 6, 18],10) == 26", "assert sum_even_and_even_index([5, 6, 12, 1],4) == 12"], "code": "def sum_even_and_even_index(arr,n):  \r\n    i = 0\r\n    sum = 0\r\n    for i in range(0,n,2): \r\n        if (arr[i] % 2 == 0) : \r\n            sum += arr[i]  \r\n    return sum", "task_id": 270}
{"prompt": "Write a python function to find the sum of fifth power of first n even natural numbers.", "test": ["assert even_Power_Sum(2) == 1056", "assert even_Power_Sum(3) == 8832", "assert even_Power_Sum(1) == 32"], "code": "def even_Power_Sum(n): \r\n    sum = 0; \r\n    for i in range(1,n+1): \r\n        j = 2*i; \r\n        sum = sum + (j*j*j*j*j); \r\n    return sum; ", "task_id": 271}
{"prompt": "Write a function to perfom the rear element extraction from list of tuples records.", "test": ["assert rear_extract([(1, 'Rash', 21), (2, 'Varsha', 20), (3, 'Kil', 19)]) == [21, 20, 19]", "assert rear_extract([(1, 'Sai', 36), (2, 'Ayesha', 25), (3, 'Salman', 45)]) == [36, 25, 45]", "assert rear_extract([(1, 'Sudeep', 14), (2, 'Vandana', 36), (3, 'Dawood', 56)]) == [14, 36, 56]"], "code": "def rear_extract(test_list):\r\n  res = [lis[-1] for lis in test_list]\r\n  return (res) ", "task_id": 272}
{"prompt": "Write a function to substract the contents of one tuple with corresponding index of other tuple.", "test": ["assert substract_elements((10, 4, 5), (2, 5, 18)) == (8, -1, -13)", "assert substract_elements((11, 2, 3), (24, 45 ,16)) == (-13, -43, -13)", "assert substract_elements((7, 18, 9), (10, 11, 12)) == (-3, 7, -3)"], "code": "def substract_elements(test_tup1, test_tup2):\r\n  res = tuple(map(lambda i, j: i - j, test_tup1, test_tup2))\r\n  return (res) ", "task_id": 273}
{"prompt": "Write a python function to find sum of even index binomial coefficients.", "test": ["assert even_binomial_Coeff_Sum(4) == 8", "assert even_binomial_Coeff_Sum(6) == 32", "assert even_binomial_Coeff_Sum(2) == 2"], "code": "import math  \r\ndef even_binomial_Coeff_Sum( n): \r\n    return (1 << (n - 1)) ", "task_id": 274}
{"prompt": "Write a python function to find the position of the last removed element from the given array.", "test": ["assert get_Position([2,5,4],3,2) == 2", "assert get_Position([4,3],2,2) == 2", "assert get_Position([1,2,3,4],4,1) == 4"], "code": "import math as mt \r\ndef get_Position(a,n,m): \r\n    for i in range(n): \r\n        a[i] = (a[i] // m + (a[i] % m != 0))  \r\n    result,maxx = -1,-1\r\n    for i in range(n - 1,-1,-1): \r\n        if (maxx < a[i]): \r\n            maxx = a[i] \r\n            result = i \r\n    return result + 1", "task_id": 275}
{"prompt": "Write a function to find the volume of a cylinder.", "test": ["assert volume_cylinder(10,5)==1570.7500000000002", "assert volume_cylinder(4,5)==251.32000000000002", "assert volume_cylinder(4,10)==502.64000000000004"], "code": "def volume_cylinder(r,h):\r\n  volume=3.1415*r*r*h\r\n  return volume", "task_id": 276}
{"prompt": "Write a function to filter a dictionary based on values.", "test": ["assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},170)=={'Cierra Vega': 175, 'Alden Cantrell': 180, 'Pierre Cox': 190}", "assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},180)=={ 'Alden Cantrell': 180, 'Pierre Cox': 190}", "assert dict_filter({'Cierra Vega': 175, 'Alden Cantrell': 180, 'Kierra Gentry': 165, 'Pierre Cox': 190},190)=={ 'Pierre Cox': 190}"], "code": "def dict_filter(dict,n):\r\n result = {key:value for (key, value) in dict.items() if value >=n}\r\n return result", "task_id": 277}
{"prompt": "Write a function to find the element count that occurs before the record in the given tuple.", "test": ["assert count_first_elements((1, 5, 7, (4, 6), 10) ) == 3", "assert count_first_elements((2, 9, (5, 7), 11) ) == 2", "assert count_first_elements((11, 15, 5, 8, (2, 3), 8) ) == 4"], "code": "def count_first_elements(test_tup):\r\n  for count, ele in enumerate(test_tup):\r\n    if isinstance(ele, tuple):\r\n      break\r\n  return (count) ", "task_id": 278}
{"prompt": "Write a function to find the nth decagonal number.", "test": ["assert is_num_decagonal(3) == 27", "assert is_num_decagonal(7) == 175", "assert is_num_decagonal(10) == 370"], "code": "def is_num_decagonal(n): \r\n\treturn 4 * n * n - 3 * n ", "task_id": 279}
{"prompt": "Write a function to search an element in the given array by using sequential search.", "test": ["assert sequential_search([11,23,58,31,56,77,43,12,65,19],31) == (True, 3)", "assert sequential_search([12, 32, 45, 62, 35, 47, 44, 61],61) == (True, 7)", "assert sequential_search([9, 10, 17, 19, 22, 39, 48, 56],48) == (True, 6)"], "code": "def sequential_search(dlist, item):\r\n    pos = 0\r\n    found = False\r\n    while pos < len(dlist) and not found:\r\n        if dlist[pos] == item:\r\n            found = True\r\n        else:\r\n            pos = pos + 1\r\n    return found, pos", "task_id": 280}
{"prompt": "Write a python function to check if the elements of a given list are unique or not.", "test": ["assert all_unique([1,2,3]) == True", "assert all_unique([1,2,1,2]) == False", "assert all_unique([1,2,3,4,5]) == True"], "code": "def all_unique(test_list):\r\n    if len(test_list) > len(set(test_list)):\r\n        return False\r\n    return True", "task_id": 281}
{"prompt": "Write a function to substaract two lists using map and lambda function.", "test": ["assert sub_list([1, 2, 3],[4,5,6])==[-3,-3,-3]", "assert sub_list([1,2],[3,4])==[-2,-2]", "assert sub_list([90,120],[50,70])==[40,50]"], "code": "def sub_list(nums1,nums2):\r\n  result = map(lambda x, y: x - y, nums1, nums2)\r\n  return list(result)", "task_id": 282}
{"prompt": "Write a python function to check whether the frequency of each digit is less than or equal to the digit itself.", "test": ["assert validate(1234) == True", "assert validate(51241) == False", "assert validate(321) == True"], "code": "def validate(n): \r\n    for i in range(10): \r\n        temp = n;  \r\n        count = 0; \r\n        while (temp): \r\n            if (temp % 10 == i): \r\n                count+=1;  \r\n            if (count > i): \r\n                return False\r\n            temp //= 10; \r\n    return True", "task_id": 283}
{"prompt": "Write a function to check whether all items of a list are equal to a given string.", "test": ["assert check_element([\"green\", \"orange\", \"black\", \"white\"],'blue')==False", "assert check_element([1,2,3,4],7)==False", "assert check_element([\"green\", \"green\", \"green\", \"green\"],'green')==True"], "code": "def check_element(list,element):\r\n  check_element=all(v== element for v in list)\r\n  return check_element", "task_id": 284}
{"prompt": "Write a function that matches a string that has an a followed by two to three 'b'.", "test": ["assert text_match_two_three(\"ac\")==('Not matched!')", "assert text_match_two_three(\"dc\")==('Not matched!')", "assert text_match_two_three(\"abbbba\")==('Found a match!')"], "code": "import re\r\ndef text_match_two_three(text):\r\n        patterns = 'ab{2,3}'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return('Not matched!')", "task_id": 285}
{"prompt": "Write a function to find the largest sum of contiguous array in the modified array which is formed by repeating the given array k times.", "test": ["assert max_sub_array_sum_repeated([10, 20, -30, -1], 4, 3) == 30", "assert max_sub_array_sum_repeated([-1, 10, 20], 3, 2) == 59", "assert max_sub_array_sum_repeated([-1, -2, -3], 3, 3) == -1"], "code": "def max_sub_array_sum_repeated(a, n, k): \r\n\tmax_so_far = -2147483648\r\n\tmax_ending_here = 0\r\n\tfor i in range(n*k): \r\n\t\tmax_ending_here = max_ending_here + a[i%n] \r\n\t\tif (max_so_far < max_ending_here): \r\n\t\t\tmax_so_far = max_ending_here \r\n\t\tif (max_ending_here < 0): \r\n\t\t\tmax_ending_here = 0\r\n\treturn max_so_far", "task_id": 286}
{"prompt": "Write a python function to find the sum of squares of first n even natural numbers.", "test": ["assert square_Sum(2) == 20", "assert square_Sum(3) == 56", "assert square_Sum(4) == 120"], "code": "def square_Sum(n):  \r\n    return int(2*n*(n+1)*(2*n+1)/3)", "task_id": 287}
{"prompt": "Write a function to count array elements having modular inverse under given prime number p equal to itself.", "test": ["assert modular_inverse([ 1, 6, 4, 5 ], 4, 7) == 2", "assert modular_inverse([1, 3, 8, 12, 12], 5, 13) == 3", "assert modular_inverse([2, 3, 4, 5], 4, 6) == 1"], "code": "def modular_inverse(arr, N, P):\r\n\tcurrent_element = 0\r\n\tfor i in range(0, N):\r\n\t\tif ((arr[i] * arr[i]) % P == 1):\r\n\t\t\tcurrent_element = current_element + 1\r\n\treturn current_element", "task_id": 288}
{"prompt": "Write a python function to calculate the number of odd days in a given year.", "test": ["assert odd_Days(100) == 5", "assert odd_Days(50) ==6", "assert odd_Days(75) == 2"], "code": "def odd_Days(N): \r\n    hund1 = N // 100\r\n    hund4 = N // 400\r\n    leap = N >> 2\r\n    ordd = N - leap \r\n    if (hund1): \r\n        ordd += hund1 \r\n        leap -= hund1 \r\n    if (hund4): \r\n        ordd -= hund4 \r\n        leap += hund4 \r\n    days = ordd + leap * 2\r\n    odd = days % 7\r\n    return odd ", "task_id": 289}
{"prompt": "Write a function to find the list of lists with maximum length.", "test": ["assert max_length([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])", "assert max_length([[1], [5, 7], [10, 12, 14,15]])==(4, [10, 12, 14,15])", "assert max_length([[5], [15,20,25]])==(3, [15,20,25])"], "code": "def max_length(list1):\r\n    max_length = max(len(x) for x in  list1 )  \r\n    max_list = max((x) for x in   list1)\r\n    return(max_length, max_list)", "task_id": 290}
{"prompt": "Write a function to find out the number of ways of painting the fence such that at most 2 adjacent posts have the same color for the given fence with n posts and k colors.", "test": ["assert count_no_of_ways(2, 4) == 16", "assert count_no_of_ways(3, 2) == 6", "assert count_no_of_ways(4, 4) == 228"], "code": "def count_no_of_ways(n, k): \r\n\tdp = [0] * (n + 1) \r\n\ttotal = k \r\n\tmod = 1000000007\r\n\tdp[1] = k \r\n\tdp[2] = k * k\t \r\n\tfor i in range(3,n+1): \r\n\t\tdp[i] = ((k - 1) * (dp[i - 1] + dp[i - 2])) % mod \r\n\treturn dp[n]", "task_id": 291}
{"prompt": "Write a python function to find quotient of two numbers.", "test": ["assert find(10,3) == 3", "assert find(4,2) == 2", "assert find(20,5) == 4"], "code": "def find(n,m):  \r\n    q = n//m \r\n    return (q)", "task_id": 292}
{"prompt": "Write a function to find the third side of a right angled triangle.", "test": ["assert otherside_rightangle(7,8)==10.63014581273465", "assert otherside_rightangle(3,4)==5", "assert otherside_rightangle(7,15)==16.55294535724685"], "code": "import math\r\ndef otherside_rightangle(w,h):\r\n  s=math.sqrt((w*w)+(h*h))\r\n  return s", "task_id": 293}
{"prompt": "Write a function to find the maximum value in a given heterogeneous list.", "test": ["assert max_val(['Python', 3, 2, 4, 5, 'version'])==5", "assert max_val(['Python', 15, 20, 25])==25", "assert max_val(['Python', 30, 20, 40, 50, 'version'])==50"], "code": "def max_val(listval):\r\n     max_val = max(i for i in listval if isinstance(i, int)) \r\n     return(max_val)", "task_id": 294}
{"prompt": "Write a function to return the sum of all divisors of a number.", "test": ["assert sum_div(8)==7", "assert sum_div(12)==16", "assert sum_div(7)==1"], "code": "def sum_div(number):\r\n    divisors = [1]\r\n    for i in range(2, number):\r\n        if (number % i)==0:\r\n            divisors.append(i)\r\n    return sum(divisors)", "task_id": 295}
{"prompt": "Write a python function to count inversions in an array.", "test": ["assert get_Inv_Count([1,20,6,4,5],5) == 5", "assert get_Inv_Count([1,2,1],3) == 1", "assert get_Inv_Count([1,2,5,6,1],5) == 3"], "code": "def get_Inv_Count(arr,n): \r\n    inv_count = 0\r\n    for i in range(n): \r\n        for j in range(i + 1,n): \r\n            if (arr[i] > arr[j]): \r\n                inv_count += 1\r\n    return inv_count ", "task_id": 296}
{"prompt": "Write a function to flatten a given nested list structure.", "test": ["assert flatten_list([0, 10, [20, 30], 40, 50, [60, 70, 80], [90, 100, 110, 120]])==[0, 10, 20, 30, 40, 50, 60, 70, 80, 90, 100, 110, 120]", "assert flatten_list([[10, 20], [40], [30, 56, 25], [10, 20], [33], [40]])==[10, 20, 40, 30, 56, 25, 10, 20, 33, 40]", "assert flatten_list([[1,2,3], [4,5,6], [10,11,12], [7,8,9]])==[1, 2, 3, 4, 5, 6, 10, 11, 12, 7, 8, 9]"], "code": "def flatten_list(list1):\r\n    result_list = []\r\n    if not list1: return result_list\r\n    stack = [list(list1)]\r\n    while stack:\r\n        c_num = stack.pop()\r\n        next = c_num.pop()\r\n        if c_num: stack.append(c_num)\r\n        if isinstance(next, list):\r\n            if next: stack.append(list(next))\r\n        else: result_list.append(next)\r\n    result_list.reverse()\r\n    return result_list ", "task_id": 297}
{"prompt": "Write a function to find the nested list elements which are present in another list.", "test": ["assert intersection_nested_lists( [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14],[[12, 18, 23, 25, 45], [7, 11, 19, 24, 28], [1, 5, 8, 18, 15, 16]])==[[12], [7, 11], [1, 5, 8]]", "assert intersection_nested_lists([[2, 3, 1], [4, 5], [6, 8]], [[4, 5], [6, 8]])==[[], []]", "assert intersection_nested_lists(['john','amal','joel','george'],[['john'],['jack','john','mary'],['howard','john'],['jude']])==[['john'], ['john'], ['john'], []]"], "code": "def intersection_nested_lists(l1, l2):\r\n    result = [[n for n in lst if n in l1] for lst in l2]\r\n    return result", "task_id": 298}
{"prompt": "Write a function to calculate the maximum aggregate from the list of tuples.", "test": ["assert max_aggregate([('Juan Whelan',90),('Sabah Colley',88),('Peter Nichols',7),('Juan Whelan',122),('Sabah Colley',84)])==('Juan Whelan', 212)", "assert max_aggregate([('Juan Whelan',50),('Sabah Colley',48),('Peter Nichols',37),('Juan Whelan',22),('Sabah Colley',14)])==('Juan Whelan', 72)", "assert max_aggregate([('Juan Whelan',10),('Sabah Colley',20),('Peter Nichols',30),('Juan Whelan',40),('Sabah Colley',50)])==('Sabah Colley', 70)"], "code": "from collections import defaultdict\r\ndef max_aggregate(stdata):\r\n    temp = defaultdict(int)\r\n    for name, marks in stdata:\r\n        temp[name] += marks\r\n    return max(temp.items(), key=lambda x: x[1])", "task_id": 299}
{"prompt": "Write a function to find the count of all binary sequences of length 2n such that sum of first n bits is same as sum of last n bits.", "test": ["assert count_binary_seq(1) == 2.0", "assert count_binary_seq(2) == 6.0", "assert count_binary_seq(3) == 20.0"], "code": "def count_binary_seq(n): \r\n\tnCr = 1\r\n\tres = 1\r\n\tfor r in range(1, n + 1): \r\n\t\tnCr = (nCr * (n + 1 - r)) / r \r\n\t\tres += nCr * nCr \r\n\treturn res ", "task_id": 300}
{"prompt": "Write a function to find the depth of a dictionary.", "test": ["assert dict_depth({'a':1, 'b': {'c': {'d': {}}}})==4", "assert dict_depth({'a':1, 'b': {'c':'python'}})==2", "assert dict_depth({1: 'Sun', 2: {3: {4:'Mon'}}})==3"], "code": "def dict_depth(d):\r\n    if isinstance(d, dict):\r\n        return 1 + (max(map(dict_depth, d.values())) if d else 0)\r\n    return 0", "task_id": 301}
{"prompt": "Write a python function to find the most significant bit number which is also a set bit.", "test": ["assert set_Bit_Number(6) == 4", "assert set_Bit_Number(10) == 8", "assert set_Bit_Number(18) == 16"], "code": "def set_Bit_Number(n): \r\n    if (n == 0): \r\n        return 0; \r\n    msb = 0; \r\n    n = int(n / 2); \r\n    while (n > 0): \r\n        n = int(n / 2); \r\n        msb += 1; \r\n    return (1 << msb)", "task_id": 302}
{"prompt": "Write a python function to check whether the count of inversion of two types are same or not.", "test": ["assert solve([1,0,2],3) == True", "assert solve([1,2,0],3) == False", "assert solve([1,2,1],3) == True"], "code": "import sys \r\ndef solve(a,n):   \r\n    mx = -sys.maxsize - 1\r\n    for j in range(1,n):  \r\n        if (mx > a[j]):  \r\n            return False  \r\n        mx = max(mx,a[j - 1])    \r\n    return True", "task_id": 303}
{"prompt": "Write a python function to find element at a given index after number of rotations.", "test": ["assert find_Element([1,2,3,4,5],[[0,2],[0,3]],2,1) == 3", "assert find_Element([1,2,3,4],[[0,1],[0,2]],1,2) == 3", "assert find_Element([1,2,3,4,5,6],[[0,1],[0,2]],1,1) == 1"], "code": "def find_Element(arr,ranges,rotations,index) :  \r\n    for i in range(rotations - 1,-1,-1 ) : \r\n        left = ranges[i][0] \r\n        right = ranges[i][1] \r\n        if (left <= index and right >= index) : \r\n            if (index == left) : \r\n                index = right \r\n            else : \r\n                index = index - 1 \r\n    return arr[index] ", "task_id": 304}
{"prompt": "Write a function to match two words from a list of words starting with letter 'p'.", "test": ["assert start_withp([\"Python PHP\", \"Java JavaScript\", \"c c++\"])==('Python', 'PHP')", "assert start_withp([\"Python Programming\",\"Java Programming\"])==('Python','Programming')", "assert start_withp([\"Pqrst Pqr\",\"qrstuv\"])==('Pqrst','Pqr')"], "code": "import re\r\ndef start_withp(words):\r\n for w in words:\r\n        m = re.match(\"(P\\w+)\\W(P\\w+)\", w)\r\n        if m:\r\n            return m.groups()", "task_id": 305}
{"prompt": "Write a function to find the maximum sum of increasing subsequence from prefix till ith index and also including a given kth element which is after i, i.e., k > i .", "test": ["assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 4, 6) == 11", "assert max_sum_increasing_subseq([1, 101, 2, 3, 100, 4, 5 ], 7, 2, 5) == 7", "assert max_sum_increasing_subseq([11, 15, 19, 21, 26, 28, 31], 7, 2, 4) == 71"], "code": "def max_sum_increasing_subseq(a, n, index, k):\r\n\tdp = [[0 for i in range(n)] \r\n\t\t\tfor i in range(n)]\r\n\tfor i in range(n):\r\n\t\tif a[i] > a[0]:\r\n\t\t\tdp[0][i] = a[i] + a[0]\r\n\t\telse:\r\n\t\t\tdp[0][i] = a[i]\r\n\tfor i in range(1, n):\r\n\t\tfor j in range(n):\r\n\t\t\tif a[j] > a[i] and j > i:\r\n\t\t\t\tif dp[i - 1][i] + a[j] > dp[i - 1][j]:\r\n\t\t\t\t\tdp[i][j] = dp[i - 1][i] + a[j]\r\n\t\t\t\telse:\r\n\t\t\t\t\tdp[i][j] = dp[i - 1][j]\r\n\t\t\telse:\r\n\t\t\t\tdp[i][j] = dp[i - 1][j]\r\n\treturn dp[index][k]", "task_id": 306}
{"prompt": "Write a function to get a colon of a tuple.", "test": ["assert colon_tuplex((\"HELLO\", 5, [], True) ,2,50)==(\"HELLO\", 5, [50], True) ", "assert colon_tuplex((\"HELLO\", 5, [], True) ,2,100)==((\"HELLO\", 5, [100],True))", "assert colon_tuplex((\"HELLO\", 5, [], True) ,2,500)==(\"HELLO\", 5, [500], True)"], "code": "from copy import deepcopy\r\ndef colon_tuplex(tuplex,m,n):\r\n  tuplex_colon = deepcopy(tuplex)\r\n  tuplex_colon[m].append(n)\r\n  return tuplex_colon", "task_id": 307}
{"prompt": "Write a function to find the specified number of largest products from two given lists.", "test": ["assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],3)==[60, 54, 50]", "assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],4)==[60, 54, 50, 48]", "assert large_product([1, 2, 3, 4, 5, 6],[3, 6, 8, 9, 10, 6],5)==[60, 54, 50, 48, 45]"], "code": "def large_product(nums1, nums2, N):\r\n    result = sorted([x*y for x in nums1 for y in nums2], reverse=True)[:N]\r\n    return result", "task_id": 308}
{"prompt": "Write a python function to find the maximum of two numbers.", "test": ["assert maximum(5,10) == 10", "assert maximum(-1,-2) == -1", "assert maximum(9,7) == 9"], "code": "def maximum(a,b):   \r\n    if a >= b: \r\n        return a \r\n    else: \r\n        return b ", "task_id": 309}
{"prompt": "Write a function to convert a given string to a tuple.", "test": ["assert string_to_tuple(\"python 3.0\")==('p', 'y', 't', 'h', 'o', 'n', '3', '.', '0')", "assert string_to_tuple(\"item1\")==('i', 't', 'e', 'm', '1')", "assert string_to_tuple(\"15.10\")==('1', '5', '.', '1', '0')"], "code": "def string_to_tuple(str1):\r\n    result = tuple(x for x in str1 if not x.isspace()) \r\n    return result", "task_id": 310}
{"prompt": "Write a python function to set the left most unset bit.", "test": ["assert set_left_most_unset_bit(10) == 14", "assert set_left_most_unset_bit(12) == 14", "assert set_left_most_unset_bit(15) == 15"], "code": "def set_left_most_unset_bit(n): \r\n    if not (n & (n + 1)): \r\n        return n \r\n    pos, temp, count = 0, n, 0 \r\n    while temp: \r\n        if not (temp & 1): \r\n            pos = count      \r\n        count += 1; temp>>=1\r\n    return (n | (1 << (pos))) ", "task_id": 311}
{"prompt": "Write a function to find the volume of a cone.", "test": ["assert volume_cone(5,12)==314.15926535897927", "assert volume_cone(10,15)==1570.7963267948965", "assert volume_cone(19,17)==6426.651371693521"], "code": "import math\r\ndef volume_cone(r,h):\r\n  volume = (1.0/3) * math.pi * r * r * h\r\n  return volume", "task_id": 312}
{"prompt": "Write a python function to print positive numbers in a list.", "test": ["assert pos_nos([-1,-2,1,2]) == 1,2", "assert pos_nos([3,4,-5]) == 3,4", "assert pos_nos([-2,-3,1]) == 1"], "code": "def pos_nos(list1):\r\n  for num in list1: \r\n    if num >= 0: \r\n       return num ", "task_id": 313}
{"prompt": "Write a function to find out the maximum sum such that no two chosen numbers are adjacent for the given rectangular grid of dimension 2 x n.", "test": ["assert max_sum_rectangular_grid([ [1, 4, 5], [2, 0, 0 ] ], 3) == 7", "assert max_sum_rectangular_grid([ [ 1, 2, 3, 4, 5], [ 6, 7, 8, 9, 10] ], 5) == 24", "assert max_sum_rectangular_grid([ [7, 9, 11, 15, 19], [21, 25, 28, 31, 32] ], 5) == 81"], "code": "def max_sum_rectangular_grid(grid, n) : \r\n\tincl = max(grid[0][0], grid[1][0]) \r\n\texcl = 0\r\n\tfor i in range(1, n) : \r\n\t\texcl_new = max(excl, incl) \r\n\t\tincl = excl + max(grid[0][i], grid[1][i]) \r\n\t\texcl = excl_new \r\n\treturn max(excl, incl)", "task_id": 314}
{"prompt": "Write a python function to find the first maximum length of even word.", "test": ["assert find_Max_Len_Even(\"python language\") == \"language\"", "assert find_Max_Len_Even(\"maximum even length\") == \"length\"", "assert find_Max_Len_Even(\"eve\") == \"-1\""], "code": "def find_Max_Len_Even(str): \r\n    n = len(str) \r\n    i = 0\r\n    currlen = 0\r\n    maxlen = 0\r\n    st = -1\r\n    while (i < n): \r\n        if (str[i] == ' '): \r\n            if (currlen % 2 == 0): \r\n                if (maxlen < currlen): \r\n                    maxlen = currlen \r\n                    st = i - currlen \r\n            currlen = 0 \r\n        else : \r\n            currlen += 1\r\n        i += 1\r\n    if (currlen % 2 == 0): \r\n        if (maxlen < currlen): \r\n            maxlen = currlen \r\n            st = i - currlen \r\n    if (st == -1): \r\n        return \"-1\" \r\n    return str[st: st + maxlen] ", "task_id": 315}
{"prompt": "Write a function to find the index of the last occurrence of a given number in a sorted array.", "test": ["assert find_last_occurrence([2, 5, 5, 5, 6, 6, 8, 9, 9, 9], 5) == 3", "assert find_last_occurrence([2, 3, 5, 8, 6, 6, 8, 9, 9, 9], 9) == 9", "assert find_last_occurrence([2, 2, 1, 5, 6, 6, 6, 9, 9, 9], 6) == 6"], "code": "def find_last_occurrence(A, x):\r\n    (left, right) = (0, len(A) - 1)\r\n    result = -1\r\n    while left <= right:\r\n        mid = (left + right) // 2\r\n        if x == A[mid]:\r\n            result = mid\r\n            left = mid + 1\r\n        elif x < A[mid]:\r\n            right = mid - 1\r\n        else:\r\n            left = mid + 1\r\n    return result ", "task_id": 316}
{"prompt": "Write a function to reflect the modified run-length encoding from a list.", "test": ["assert modified_encode([1,1,2,3,4,4,5,1])==[[2, 1], 2, 3, [2, 4], 5, 1]", "assert modified_encode('automatically')==['a', 'u', 't', 'o', 'm', 'a', 't', 'i', 'c', 'a', [2, 'l'], 'y']", "assert modified_encode('python')==['p', 'y', 't', 'h', 'o', 'n']"], "code": "from itertools import groupby\r\ndef modified_encode(alist):\r\n        def ctr_ele(el):\r\n            if len(el)>1: return [len(el), el[0]]\r\n            else: return el[0]\r\n        return [ctr_ele(list(group)) for key, group in groupby(alist)]", "task_id": 317}
{"prompt": "Write a python function to find the maximum volume of a cuboid with given sum of sides.", "test": ["assert max_volume(8) == 18", "assert max_volume(4) == 2", "assert max_volume(1) == 0"], "code": "def max_volume (s): \r\n    maxvalue = 0\r\n    i = 1\r\n    for i in range(s - 1): \r\n        j = 1\r\n        for j in range(s): \r\n            k = s - i - j \r\n            maxvalue = max(maxvalue, i * j * k)         \r\n    return maxvalue ", "task_id": 318}
{"prompt": "Write a function to find all five characters long word in the given string by using regex.", "test": ["assert find_long_word('Please move back to strem') == ['strem']", "assert find_long_word('4K Ultra HD streaming player') == ['Ultra']", "assert find_long_word('Streaming Media Player') == ['Media']"], "code": "import re\r\ndef find_long_word(text):\r\n  return (re.findall(r\"\\b\\w{5}\\b\", text))", "task_id": 319}
{"prompt": "Write a function to calculate the difference between the squared sum of first n natural numbers and the sum of squared first n natural numbers.", "test": ["assert sum_difference(12)==5434", "assert sum_difference(20)==41230", "assert sum_difference(54)==2151270"], "code": "def sum_difference(n):\r\n    sumofsquares = 0\r\n    squareofsum = 0\r\n    for num in range(1, n+1):\r\n        sumofsquares += num * num\r\n        squareofsum += num\r\n    squareofsum = squareofsum ** 2\r\n    return squareofsum - sumofsquares", "task_id": 320}
{"prompt": "Write a function to find the demlo number for the given number.", "test": ["assert find_demlo(\"111111\") == '12345654321'", "assert find_demlo(\"1111\") == '1234321'", "assert find_demlo(\"13333122222\") == '123456789101110987654321'"], "code": "def find_demlo(s): \r\n\tl = len(s) \r\n\tres = \"\" \r\n\tfor i in range(1,l+1): \r\n\t\tres = res + str(i) \r\n\tfor i in range(l-1,0,-1): \r\n\t\tres = res + str(i) \r\n\treturn res \t", "task_id": 321}
{"prompt": "Write a function to find all index positions of the minimum values in a given list.", "test": ["assert position_min([12,33,23,10,67,89,45,667,23,12,11,10,54])==[3,11]", "assert position_min([1,2,2,2,4,4,4,5,5,5,5])==[0]", "assert position_min([2,1,5,6,8,3,4,9,10,11,8,12])==[1]"], "code": "def position_min(list1):\r\n    min_val = min(list1)\r\n    min_result = [i for i, j in enumerate(list1) if j == min_val]\r\n    return min_result", "task_id": 322}
{"prompt": "Write a function to re-arrange the given array in alternating positive and negative items.", "test": ["assert re_arrange([-5, -2, 5, 2, 4,\t7, 1, 8, 0, -8], 10) == [-5, 5, -2, 2, -8, 4, 7, 1, 8, 0]", "assert re_arrange([1, 2, 3, -4, -1, 4], 6) == [-4, 1, -1, 2, 3, 4]", "assert re_arrange([4, 7, 9, 77, -4, 5, -3, -9], 8) == [-4, 4, -3, 7, -9, 9, 77, 5]"], "code": "def right_rotate(arr, n, out_of_place, cur):\r\n\ttemp = arr[cur]\r\n\tfor i in range(cur, out_of_place, -1):\r\n\t\tarr[i] = arr[i - 1]\r\n\tarr[out_of_place] = temp\r\n\treturn arr\r\ndef re_arrange(arr, n):\r\n\tout_of_place = -1\r\n\tfor index in range(n):\r\n\t\tif (out_of_place >= 0):\r\n\t\t\tif ((arr[index] >= 0 and arr[out_of_place] < 0) or\r\n\t\t\t(arr[index] < 0 and arr[out_of_place] >= 0)):\r\n\t\t\t\tarr = right_rotate(arr, n, out_of_place, index)\r\n\t\t\t\tif (index-out_of_place > 2):\r\n\t\t\t\t\tout_of_place += 2\r\n\t\t\t\telse:\r\n\t\t\t\t\tout_of_place = - 1\r\n\t\tif (out_of_place == -1):\r\n\t\t\tif ((arr[index] >= 0 and index % 2 == 0) or\r\n\t\t\t (arr[index] < 0 and index % 2 == 1)):\r\n\t\t\t\tout_of_place = index\r\n\treturn arr", "task_id": 323}
{"prompt": "Write a function to extract the sum of alternate chains of tuples.", "test": ["assert sum_of_alternates((5, 6, 3, 6, 10, 34)) == (46, 18)", "assert sum_of_alternates((1, 2, 3, 4, 5)) == (6, 9)", "assert sum_of_alternates((6, 7, 8, 9, 4, 5)) == (21, 18)"], "code": "def sum_of_alternates(test_tuple):\r\n  sum1 = 0\r\n  sum2 = 0\r\n  for idx, ele in enumerate(test_tuple):\r\n    if idx % 2:\r\n      sum1 += ele\r\n    else:\r\n      sum2 += ele\r\n  return ((sum1),(sum2)) ", "task_id": 324}
{"prompt": "Write a python function to find the minimum number of squares whose sum is equal to a given number.", "test": ["assert get_Min_Squares(6) == 3", "assert get_Min_Squares(2) == 2", "assert get_Min_Squares(4) == 1"], "code": "def get_Min_Squares(n):\r\n    if n <= 3:\r\n        return n;\r\n    res = n \r\n    for x in range(1,n + 1):\r\n        temp = x * x;\r\n        if temp > n:\r\n            break\r\n        else:\r\n            res = min(res,1 + get_Min_Squares(n  - temp)) \r\n    return res;", "task_id": 325}
{"prompt": "Write a function to get the word with most number of occurrences in the given strings list.", "test": ["assert most_occurrences([\"UTS is best for RTF\", \"RTF love UTS\", \"UTS is best\"] ) == 'UTS'", "assert most_occurrences([\"Its been a great year\", \"this year is so worse\", \"this year is okay\"] ) == 'year'", "assert most_occurrences([\"Families can be reunited\", \"people can be reunited\", \"Tasks can be achieved \"] ) == 'can'"], "code": "from collections import defaultdict \r\n\r\ndef most_occurrences(test_list):\r\n  temp = defaultdict(int)\r\n  for sub in test_list:\r\n    for wrd in sub.split():\r\n      temp[wrd] += 1\r\n  res = max(temp, key=temp.get)\r\n  return (str(res)) ", "task_id": 326}
{"prompt": "Write a function to print check if the triangle is isosceles or not.", "test": ["assert check_isosceles(6,8,12)==False ", "assert check_isosceles(6,6,12)==True", "assert check_isosceles(6,16,20)==False"], "code": "def check_isosceles(x,y,z):\r\n  if x==y or y==z or z==x:\r\n\t   return True\r\n  else:\r\n     return False", "task_id": 327}
{"prompt": "Write a function to rotate a given list by specified number of items to the left direction.", "test": ["assert rotate_left([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],3,4)==[4, 5, 6, 7, 8, 9, 10, 1, 2, 3, 4]", "assert rotate_left([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],2,2)==[3, 4, 5, 6, 7, 8, 9, 10, 1, 2]", "assert rotate_left([1, 2, 3, 4, 5, 6, 7, 8, 9, 10],5,2)==[6, 7, 8, 9, 10, 1, 2]"], "code": "def rotate_left(list1,m,n):\r\n  result =  list1[m:]+list1[:n]\r\n  return result", "task_id": 328}
{"prompt": "Write a python function to count negative numbers in a list.", "test": ["assert neg_count([-1,-2,3,-4,-5]) == 4", "assert neg_count([1,2,3]) == 0", "assert neg_count([1,2,-3,-10,20]) == 2"], "code": "def neg_count(list):\r\n  neg_count= 0\r\n  for num in list: \r\n    if num <= 0: \r\n      neg_count += 1\r\n  return neg_count ", "task_id": 329}
{"prompt": "Write a function to find all three, four, five characters long words in the given string by using regex.", "test": ["assert find_char('For the four consumer complaints contact manager AKR reddy') == ['For', 'the', 'four', 'AKR', 'reddy']", "assert find_char('Certain service are subject to change MSR') == ['are', 'MSR']", "assert find_char('Third party legal desclaimers') == ['Third', 'party', 'legal']"], "code": "import re\r\ndef find_char(text):\r\n  return (re.findall(r\"\\b\\w{3,5}\\b\", text))", "task_id": 330}
{"prompt": "Write a python function to count unset bits of a given number.", "test": ["assert count_unset_bits(2) == 1", "assert count_unset_bits(4) == 2", "assert count_unset_bits(6) == 1"], "code": "def count_unset_bits(n): \r\n    count = 0\r\n    x = 1\r\n    while(x < n + 1): \r\n        if ((x & n) == 0): \r\n            count += 1\r\n        x = x << 1\r\n    return count  ", "task_id": 331}
{"prompt": "Write a function to count character frequency of a given string.", "test": ["assert char_frequency('python')=={'p': 1, 'y': 1, 't': 1, 'h': 1, 'o': 1, 'n': 1}", "assert char_frequency('program')=={'p': 1, 'r': 2, 'o': 1, 'g': 1, 'a': 1, 'm': 1}", "assert char_frequency('language')=={'l': 1, 'a': 2, 'n': 1, 'g': 2, 'u': 1, 'e': 1}"], "code": "def char_frequency(str1):\r\n    dict = {}\r\n    for n in str1:\r\n        keys = dict.keys()\r\n        if n in keys:\r\n            dict[n] += 1\r\n        else:\r\n            dict[n] = 1\r\n    return dict", "task_id": 332}
{"prompt": "Write a python function to sort a list according to the second element in sublist.", "test": ["assert Sort([['a', 10], ['b', 5], ['c', 20], ['d', 15]]) == [['b', 5], ['a', 10], ['d', 15], ['c', 20]]", "assert Sort([['452', 10], ['256', 5], ['100', 20], ['135', 15]]) == [['256', 5], ['452', 10], ['135', 15], ['100', 20]]", "assert Sort([['rishi', 10], ['akhil', 5], ['ramya', 20], ['gaur', 15]]) == [['akhil', 5], ['rishi', 10], ['gaur', 15], ['ramya', 20]]"], "code": "def Sort(sub_li): \r\n    sub_li.sort(key = lambda x: x[1]) \r\n    return sub_li ", "task_id": 333}
{"prompt": "Write a python function to check whether the triangle is valid or not if sides are given.", "test": ["assert check_Validity(1,2,3) == False", "assert check_Validity(2,3,5) == False", "assert check_Validity(7,10,5) == True"], "code": "def check_Validity(a,b,c):  \r\n    if (a + b <= c) or (a + c <= b) or (b + c <= a) : \r\n        return False\r\n    else: \r\n        return True        ", "task_id": 334}
{"prompt": "Write a function to find the sum of arithmetic progression.", "test": ["assert ap_sum(1,5,2)==25", "assert ap_sum(2,6,4)==72", "assert ap_sum(1,4,5)==34"], "code": "def ap_sum(a,n,d):\r\n  total = (n * (2 * a + (n - 1) * d)) / 2\r\n  return total", "task_id": 335}
{"prompt": "Write a function to check whether the given month name contains 28 days or not.", "test": ["assert check_monthnum(\"February\")==True", "assert check_monthnum(\"January\")==False", "assert check_monthnum(\"March\")==False"], "code": "def check_monthnum(monthname1):\r\n  if monthname1 == \"February\":\r\n    return True\r\n  else:\r\n    return False", "task_id": 336}
{"prompt": "Write a function that matches a word at the end of a string, with optional punctuation.", "test": ["assert text_match_word(\"python.\")==('Found a match!')", "assert text_match_word(\"python.\")==('Found a match!')", "assert text_match_word(\"  lang  .\")==('Not matched!')"], "code": "import re\r\ndef text_match_word(text):\r\n        patterns = '\\w+\\S*$'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return 'Not matched!'", "task_id": 337}
{"prompt": "Write a python function to count the number of substrings with same first and last characters.", "test": ["assert count_Substring_With_Equal_Ends('aba') == 4", "assert count_Substring_With_Equal_Ends('abcab') == 7", "assert count_Substring_With_Equal_Ends('abc') == 3"], "code": "def check_Equality(s): \r\n    return (ord(s[0]) == ord(s[len(s) - 1])); \r\ndef count_Substring_With_Equal_Ends(s): \r\n    result = 0; \r\n    n = len(s); \r\n    for i in range(n):\r\n        for j in range(1,n-i+1): \r\n            if (check_Equality(s[i:i+j])): \r\n                result+=1; \r\n    return result; ", "task_id": 338}
{"prompt": "Write a python function to find the maximum occuring divisor in an interval.", "test": ["assert find_Divisor(2,2) == 2", "assert find_Divisor(2,5) == 2", "assert find_Divisor(5,10) == 2"], "code": "def find_Divisor(x,y):  \r\n    if (x==y): \r\n        return y \r\n    return 2", "task_id": 339}
{"prompt": "Write a python function to find the sum of the three lowest positive numbers from a given list of numbers.", "test": ["assert sum_three_smallest_nums([10,20,30,40,50,60,7]) == 37", "assert sum_three_smallest_nums([1,2,3,4,5]) == 6", "assert sum_three_smallest_nums([0,1,2,3,4,5]) == 6"], "code": "def sum_three_smallest_nums(lst):\r\n\treturn sum(sorted([x for x in lst if x > 0])[:3])", "task_id": 340}
{"prompt": "Write a function to convert the given set into ordered tuples.", "test": ["assert set_to_tuple({1, 2, 3, 4, 5}) == (1, 2, 3, 4, 5)", "assert set_to_tuple({6, 7, 8, 9, 10, 11}) == (6, 7, 8, 9, 10, 11)", "assert set_to_tuple({12, 13, 14, 15, 16}) == (12, 13, 14, 15, 16)"], "code": "def set_to_tuple(s):\r\n  t = tuple(sorted(s))\r\n  return (t)", "task_id": 341}
{"prompt": "Write a function to find the smallest range that includes at-least one element from each of the given arrays.", "test": ["assert find_minimum_range([[3, 6, 8, 10, 15], [1, 5, 12], [4, 8, 15, 16], [2, 6]]) == (4, 6)", "assert find_minimum_range([[ 2, 3, 4, 8, 10, 15 ], [1, 5, 12], [7, 8, 15, 16], [3, 6]]) == (4, 7)", "assert find_minimum_range([[4, 7, 9, 11, 16], [2, 6, 13], [5, 9, 16, 17], [3, 7]]) == (5, 7)"], "code": "from heapq import heappop, heappush\r\nclass Node:\r\n    def __init__(self, value, list_num, index):\r\n        self.value = value\r\n        self.list_num = list_num\r\n        self.index = index\r\n    def __lt__(self, other):\r\n        return self.value < other.value\r\ndef find_minimum_range(list):\r\n    high = float('-inf')\r\n    p = (0, float('inf'))\r\n    pq = []\r\n    for i in range(len(list)):\r\n        heappush(pq, Node(list[i][0], i, 0))\r\n        high = max(high, list[i][0])\r\n    while True:\r\n        top = heappop(pq)\r\n        low = top.value\r\n        i = top.list_num\r\n        j = top.index\r\n        if high - low < p[1] - p[0]:\r\n            p = (low, high)\r\n        if j == len(list[i]) - 1:\r\n            return p\r\n        heappush(pq, Node(list[i][j + 1], i, j + 1))\r\n        high = max(high, list[i][j + 1])", "task_id": 342}
{"prompt": "Write a function to calculate the number of digits and letters in a string.", "test": ["assert dig_let(\"python\")==(6,0)", "assert dig_let(\"program\")==(7,0)", "assert dig_let(\"python3.0\")==(6,2)"], "code": "def dig_let(s):\r\n d=l=0\r\n for c in s:\r\n    if c.isdigit():\r\n        d=d+1\r\n    elif c.isalpha():\r\n        l=l+1\r\n    else:\r\n        pass\r\n return (l,d)", "task_id": 343}
{"prompt": "Write a python function to find number of elements with odd factors in a given range.", "test": ["assert count_Odd_Squares(5,100) == 8", "assert count_Odd_Squares(8,65) == 6", "assert count_Odd_Squares(2,5) == 1"], "code": "def count_Odd_Squares(n,m): \r\n    return int(m**0.5) - int((n-1)**0.5) ", "task_id": 344}
{"prompt": "Write a function to find the difference between two consecutive numbers in a given list.", "test": ["assert diff_consecutivenums([1, 1, 3, 4, 4, 5, 6, 7])==[0, 2, 1, 0, 1, 1, 1]", "assert diff_consecutivenums([4, 5, 8, 9, 6, 10])==[1, 3, 1, -3, 4]", "assert diff_consecutivenums([0, 1, 2, 3, 4, 4, 4, 4, 5, 7])==[1, 1, 1, 1, 0, 0, 0, 1, 2]"], "code": "def diff_consecutivenums(nums):\r\n    result = [b-a for a, b in zip(nums[:-1], nums[1:])]\r\n    return result", "task_id": 345}
{"prompt": "Write a function to find entringer number e(n, k).", "test": ["assert zigzag(4, 3) == 5", "assert zigzag(4, 2) == 4", "assert zigzag(3, 1) == 1"], "code": "def zigzag(n, k): \r\n\tif (n == 0 and k == 0): \r\n\t\treturn 1\r\n\tif (k == 0): \r\n\t\treturn 0\r\n\treturn zigzag(n, k - 1) + zigzag(n - 1, n - k)", "task_id": 346}
{"prompt": "Write a python function to count the number of squares in a rectangle.", "test": ["assert count_Squares(4,3) == 20", "assert count_Squares(1,2) == 2", "assert count_Squares(2,2) == 5"], "code": "def count_Squares(m,n): \r\n    if (n < m): \r\n        temp = m \r\n        m = n \r\n        n = temp \r\n    return n * (n + 1) * (3 * m - n + 1) // 6", "task_id": 347}
{"prompt": "Write a function to count sequences of given length having non-negative prefix sums that can be generated by given values.", "test": ["assert find_ways(4) == 2", "assert find_ways(6) == 5", "assert find_ways(8) == 14"], "code": "def bin_coff(n, r): \r\n\tval = 1\r\n\tif (r > (n - r)): \r\n\t\tr = (n - r) \r\n\tfor i in range(0, r): \r\n\t\tval *= (n - i) \r\n\t\tval //= (i + 1) \r\n\treturn val \r\ndef find_ways(M): \r\n\tn = M // 2\r\n\ta = bin_coff(2 * n, n) \r\n\tb = a // (n + 1) \r\n\treturn (b) ", "task_id": 348}
{"prompt": "Write a python function to check whether the given string is a binary string or not.", "test": ["assert check(\"01010101010\") == \"Yes\"", "assert check(\"name0\") == \"No\"", "assert check(\"101\") == \"Yes\""], "code": "def check(string) :\r\n    p = set(string) \r\n    s = {'0', '1'} \r\n    if s == p or p == {'0'} or p == {'1'}: \r\n        return (\"Yes\") \r\n    else : \r\n        return (\"No\") ", "task_id": 349}
{"prompt": "Write a python function to minimize the length of the string by removing occurrence of only one character.", "test": ["assert minimum_Length(\"mnm\") == 1", "assert minimum_Length(\"abcda\") == 3", "assert minimum_Length(\"abcb\") == 2"], "code": "def minimum_Length(s) : \r\n    maxOcc = 0\r\n    n = len(s) \r\n    arr = [0]*26\r\n    for i in range(n) : \r\n        arr[ord(s[i]) -ord('a')] += 1\r\n    for i in range(26) : \r\n        if arr[i] > maxOcc : \r\n            maxOcc = arr[i] \r\n    return n - maxOcc ", "task_id": 350}
{"prompt": "Write a python function to find the first element occurring k times in a given array.", "test": ["assert first_Element([0,1,2,3,4,5],6,1) == 0", "assert first_Element([1,2,1,3,4],5,2) == 1", "assert first_Element([2,3,4,3,5,7,1,2,3,5],10,2) == 2"], "code": "def first_Element(arr,n,k): \r\n    count_map = {}; \r\n    for i in range(0, n): \r\n        if(arr[i] in count_map.keys()): \r\n            count_map[arr[i]] += 1\r\n        else: \r\n            count_map[arr[i]] = 1\r\n        i += 1\r\n    for i in range(0, n):  \r\n        if (count_map[arr[i]] == k): \r\n            return arr[i] \r\n        i += 1 \r\n    return -1", "task_id": 351}
{"prompt": "Write a python function to check whether all the characters in a given string are unique.", "test": ["assert unique_Characters('aba') == False", "assert unique_Characters('abc') == True", "assert unique_Characters('abab') == False"], "code": "def unique_Characters(str):\r\n    for i in range(len(str)):\r\n        for j in range(i + 1,len(str)): \r\n            if (str[i] == str[j]):\r\n                return False;\r\n    return True;", "task_id": 352}
{"prompt": "Write a function to remove a specified column from a given nested list.", "test": ["assert remove_column([[1, 2, 3], [2, 4, 5], [1, 1, 1]],0)==[[2, 3], [4, 5], [1, 1]]", "assert remove_column([[1, 2, 3], [-2, 4, -5], [1, -1, 1]],2)==[[1, 2], [-2, 4], [1, -1]]", "assert remove_column([[1, 3], [5, 7], [1, 3], [13, 15, 17], [5, 7], [9, 11]],0)==[[3], [7], [3], [15, 17], [7], [11]]"], "code": "def remove_column(list1, n):\r\n   for i in list1: \r\n    del i[n] \r\n   return list1", "task_id": 353}
{"prompt": "Write a function to find t-nth term of arithemetic progression.", "test": ["assert tn_ap(1,5,2)==9", "assert tn_ap(2,6,4)==22", "assert tn_ap(1,4,5)==16"], "code": "def tn_ap(a,n,d):\r\n  tn = a + (n - 1) * d\r\n  return tn", "task_id": 354}
{"prompt": "Write a python function to count the number of rectangles in a circle of radius r.", "test": ["assert count_Rectangles(2) == 8", "assert count_Rectangles(1) == 1", "assert count_Rectangles(0) == 0"], "code": "def count_Rectangles(radius):  \r\n    rectangles = 0 \r\n    diameter = 2 * radius \r\n    diameterSquare = diameter * diameter \r\n    for a in range(1, 2 * radius):  \r\n        for b in range(1, 2 * radius): \r\n            diagnalLengthSquare = (a * a +  b * b)  \r\n            if (diagnalLengthSquare <= diameterSquare) : \r\n                rectangles += 1\r\n    return rectangles ", "task_id": 355}
{"prompt": "Write a function to find the third angle of a triangle using two angles.", "test": ["assert find_angle(47,89)==44", "assert find_angle(45,95)==40", "assert find_angle(50,40)==90"], "code": "def find_angle(a,b):\r\n c = 180 - (a + b)\r\n return c\r\n", "task_id": 356}
{"prompt": "Write a function to find the maximum element of all the given tuple records.", "test": ["assert find_max([(2, 4), (6, 7), (5, 1), (6, 10), (8, 7)]) == 10", "assert find_max([(3, 5), (7, 8), (6, 2), (7, 11), (9, 8)]) == 11", "assert find_max([(4, 6), (8, 9), (7, 3), (8, 12), (10, 9)]) == 12"], "code": "def find_max(test_list):\r\n  res = max(int(j) for i in test_list for j in i)\r\n  return (res) ", "task_id": 357}
{"prompt": "Write a function to find modulo division of two lists using map and lambda function.", "test": ["assert moddiv_list([4,5,6],[1, 2, 3])==[0, 1, 0]", "assert moddiv_list([3,2],[1,4])==[0, 2]", "assert moddiv_list([90,120],[50,70])==[40, 50]"], "code": "def moddiv_list(nums1,nums2):\r\n  result = map(lambda x, y: x % y, nums1, nums2)\r\n  return list(result)", "task_id": 358}
{"prompt": "Write a python function to check whether one root of the quadratic equation is twice of the other or not.", "test": ["assert Check_Solution(1,3,2) == \"Yes\"", "assert Check_Solution(1,2,3) == \"No\"", "assert Check_Solution(1,-5,6) == \"No\""], "code": "def Check_Solution(a,b,c): \r\n    if (2*b*b == 9*a*c): \r\n        return (\"Yes\"); \r\n    else: \r\n        return (\"No\"); ", "task_id": 359}
{"prompt": "Write a function to find the n\u2019th carol number.", "test": ["assert get_carol(2) == 7", "assert get_carol(4) == 223", "assert get_carol(5) == 959"], "code": "def get_carol(n): \r\n\tresult = (2**n) - 1\r\n\treturn result * result - 2", "task_id": 360}
{"prompt": "Write a function to remove empty lists from a given list of lists.", "test": ["assert remove_empty([[], [], [], 'Red', 'Green', [1,2], 'Blue', [], []])==['Red', 'Green', [1, 2], 'Blue']", "assert remove_empty([[], [], [],[],[], 'Green', [1,2], 'Blue', [], []])==[ 'Green', [1, 2], 'Blue']", "assert remove_empty([[], [], [], 'Python',[],[], 'programming', 'language',[],[],[], [], []])==['Python', 'programming', 'language']"], "code": "def remove_empty(list1):\r\n  remove_empty = [x for x in list1 if x]\r\n  return remove_empty", "task_id": 361}
{"prompt": "Write a python function to find the item with maximum occurrences in a given list.", "test": ["assert max_occurrences([1,2,3,1,2,3,12,4,2]) ==  2", "assert max_occurrences([1,2,6,7,0,1,0,1,0]) == 1,0", "assert max_occurrences([1,2,3,1,2,4,1]) == 1"], "code": "def max_occurrences(nums):\r\n    max_val = 0\r\n    result = nums[0] \r\n    for i in nums:\r\n        occu = nums.count(i)\r\n        if occu > max_val:\r\n            max_val = occu\r\n            result = i \r\n    return result", "task_id": 362}
{"prompt": "Write a function to add the k elements to each element in the tuple.", "test": ["assert add_K_element([(1, 3, 4), (2, 4, 6), (3, 8, 1)], 4) == [(5, 7, 8), (6, 8, 10), (7, 12, 5)]", "assert add_K_element([(1, 2, 3), (4, 5, 6), (7, 8, 9)], 8) == [(9, 10, 11), (12, 13, 14), (15, 16, 17)]", "assert add_K_element([(11, 12, 13), (14, 15, 16), (17, 18, 19)], 9) == [(20, 21, 22), (23, 24, 25), (26, 27, 28)]"], "code": "def add_K_element(test_list, K):\r\n  res = [tuple(j + K for j in sub ) for sub in test_list]\r\n  return (res) ", "task_id": 363}
{"prompt": "Write a function to find the number of flips required to make the given binary string a sequence of alternate characters.", "test": ["assert min_flip_to_make_string_alternate(\"0001010111\") == 2", "assert min_flip_to_make_string_alternate(\"001\") == 1", "assert min_flip_to_make_string_alternate(\"010111011\") == 2 "], "code": "def make_flip(ch): \r\n\treturn '1' if (ch == '0') else '0'\r\ndef get_flip_with_starting_charcter(str, expected): \r\n\tflip_count = 0\r\n\tfor i in range(len( str)): \r\n\t\tif (str[i] != expected): \r\n\t\t\tflip_count += 1\r\n\t\texpected = make_flip(expected) \r\n\treturn flip_count \r\ndef min_flip_to_make_string_alternate(str): \r\n\treturn min(get_flip_with_starting_charcter(str, '0'),get_flip_with_starting_charcter(str, '1')) ", "task_id": 364}
{"prompt": "Write a python function to count the number of digits of a given number.", "test": ["assert count_Digit(12345) == 5", "assert count_Digit(11223305) == 8", "assert count_Digit(4123459) == 7"], "code": "def count_Digit(n):\r\n    count = 0\r\n    while n != 0:\r\n        n //= 10\r\n        count += 1\r\n    return count", "task_id": 365}
{"prompt": "Write a python function to find the largest product of the pair of adjacent elements from a given list of integers.", "test": ["assert adjacent_num_product([1,2,3,4,5,6]) == 30", "assert adjacent_num_product([1,2,3,4,5]) == 20", "assert adjacent_num_product([2,3]) == 6"], "code": "def adjacent_num_product(list_nums):\r\n    return max(a*b for a, b in zip(list_nums, list_nums[1:]))", "task_id": 366}
{"prompt": "Write a function to check if a binary tree is balanced or not.", "test": ["assert is_tree_balanced(root) == False", "assert is_tree_balanced(root1) == True", "assert is_tree_balanced(root2) == False "], "code": "class Node: \r\n\tdef __init__(self, data): \r\n\t\tself.data = data \r\n\t\tself.left = None\r\n\t\tself.right = None\r\ndef get_height(root): \r\n\tif root is None: \r\n\t\treturn 0\r\n\treturn max(get_height(root.left), get_height(root.right)) + 1\r\ndef is_tree_balanced(root): \r\n\tif root is None: \r\n\t\treturn True\r\n\tlh = get_height(root.left) \r\n\trh = get_height(root.right) \r\n\tif (abs(lh - rh) <= 1) and is_tree_balanced( \r\n\troot.left) is True and is_tree_balanced( root.right) is True: \r\n\t\treturn True\r\n\treturn False", "task_id": 367}
{"prompt": "Write a function to repeat the given tuple n times.", "test": ["assert repeat_tuples((1, 3), 4) == ((1, 3), (1, 3), (1, 3), (1, 3))", "assert repeat_tuples((1, 2), 3) == ((1, 2), (1, 2), (1, 2))", "assert repeat_tuples((3, 4), 5) == ((3, 4), (3, 4), (3, 4), (3, 4), (3, 4))"], "code": "def repeat_tuples(test_tup, N):\r\n  res = ((test_tup, ) * N)\r\n  return (res) ", "task_id": 368}
{"prompt": "Write a function to find the lateral surface area of cuboid", "test": ["assert lateralsurface_cuboid(8,5,6)==156", "assert lateralsurface_cuboid(7,9,10)==320", "assert lateralsurface_cuboid(10,20,30)==1800"], "code": "def lateralsurface_cuboid(l,w,h):\r\n  LSA = 2*h*(l+w)\r\n  return LSA", "task_id": 369}
{"prompt": "Write a function to sort a tuple by its float element.", "test": ["assert float_sort([('item1', '12.20'), ('item2', '15.10'), ('item3', '24.5')])==[('item3', '24.5'), ('item2', '15.10'), ('item1', '12.20')] ", "assert float_sort([('item1', '15'), ('item2', '10'), ('item3', '20')])==[('item3', '20'), ('item1', '15'), ('item2', '10')] ", "assert float_sort([('item1', '5'), ('item2', '10'), ('item3', '14')])==[('item3', '14'), ('item2', '10'), ('item1', '5')] "], "code": "def float_sort(price):\r\n  float_sort=sorted(price, key=lambda x: float(x[1]), reverse=True)\r\n  return float_sort", "task_id": 370}
{"prompt": "Write a function to find the smallest missing element in a sorted array.", "test": ["assert smallest_missing([0, 1, 2, 3, 4, 5, 6], 0, 6) == 7", "assert smallest_missing([0, 1, 2, 6, 9, 11, 15], 0, 6) == 3", "assert smallest_missing([1, 2, 3, 4, 6, 9, 11, 15], 0, 7) == 0"], "code": "def smallest_missing(A, left_element, right_element):\r\n    if left_element > right_element:\r\n        return left_element\r\n    mid = left_element + (right_element - left_element) // 2\r\n    if A[mid] == mid:\r\n        return smallest_missing(A, mid + 1, right_element)\r\n    else:\r\n        return smallest_missing(A, left_element, mid - 1)", "task_id": 371}
{"prompt": "Write a function to sort a given list of elements in ascending order using heap queue algorithm.", "test": ["assert heap_assending([18, 14, 10, 9, 8, 7, 9, 3, 2, 4, 1])==[1, 2, 3, 4, 7, 8, 9, 9, 10, 14, 18]", "assert heap_assending([25, 35, 22, 85, 14, 65, 75, 25, 58])==[14, 22, 25, 25, 35, 58, 65, 75, 85]", "assert heap_assending([1, 3, 5, 7, 9, 2, 4, 6, 8, 0])==[0, 1, 2, 3, 4, 5, 6, 7, 8, 9]"], "code": "import heapq as hq\r\ndef heap_assending(nums):\r\n  hq.heapify(nums)\r\n  s_result = [hq.heappop(nums) for i in range(len(nums))]\r\n  return s_result", "task_id": 372}
{"prompt": "Write a function to find the volume of a cuboid.", "test": ["assert volume_cuboid(1,2,3)==6", "assert volume_cuboid(5,7,9)==315", "assert volume_cuboid(10,15,21)==3150"], "code": "def volume_cuboid(l,w,h):\r\n  volume=l*w*h\r\n  return volume", "task_id": 373}
{"prompt": "Write a function to print all permutations of a given string including duplicates.", "test": ["assert permute_string('ab')==['ab', 'ba']", "assert permute_string('abc')==['abc', 'bac', 'bca', 'acb', 'cab', 'cba']", "assert permute_string('abcd')==['abcd', 'bacd', 'bcad', 'bcda', 'acbd', 'cabd', 'cbad', 'cbda', 'acdb', 'cadb', 'cdab', 'cdba', 'abdc', 'badc', 'bdac', 'bdca', 'adbc', 'dabc', 'dbac', 'dbca', 'adcb', 'dacb', 'dcab', 'dcba']"], "code": "def permute_string(str):\r\n    if len(str) == 0:\r\n        return ['']\r\n    prev_list = permute_string(str[1:len(str)])\r\n    next_list = []\r\n    for i in range(0,len(prev_list)):\r\n        for j in range(0,len(str)):\r\n            new_str = prev_list[i][0:j]+str[0]+prev_list[i][j:len(str)-1]\r\n            if new_str not in next_list:\r\n                next_list.append(new_str)\r\n    return next_list", "task_id": 374}
{"prompt": "Write a function to round the given number to the nearest multiple of a specific number.", "test": ["assert round_num(4722,10)==4720", "assert round_num(1111,5)==1110", "assert round_num(219,2)==218"], "code": "def round_num(n,m):\r\n    a = (n //m) * m\r\n    b = a + m\r\n    return (b if n - a > b - n else a)", "task_id": 375}
{"prompt": "Write a function to remove tuple elements that occur more than once and replace the duplicates with some custom value.", "test": ["assert remove_replica((1, 1, 4, 4, 4, 5, 5, 6, 7, 7)) == (1, 'MSP', 4, 'MSP', 'MSP', 5, 'MSP', 6, 7, 'MSP')", "assert remove_replica((2, 3, 4, 4, 5, 6, 6, 7, 8, 9, 9)) == (2, 3, 4, 'MSP', 5, 6, 'MSP', 7, 8, 9, 'MSP')", "assert remove_replica((2, 2, 5, 4, 5, 7, 5, 6, 7, 7)) == (2, 'MSP', 5, 4, 'MSP', 7, 'MSP', 6, 'MSP', 'MSP')"], "code": "def remove_replica(test_tup):\r\n  temp = set()\r\n  res = tuple(ele if ele not in temp and not temp.add(ele) \r\n\t\t\t\telse 'MSP' for ele in test_tup)\r\n  return (res)", "task_id": 376}
{"prompt": "Write a python function to remove all occurrences of a character in a given string.", "test": ["assert remove_Char(\"aba\",'a') == \"b\"", "assert remove_Char(\"toggle\",'g') == \"tole\"", "assert remove_Char(\"aabbc\",'b') == \"aac\""], "code": "def remove_Char(s,c) :  \r\n    counts = s.count(c) \r\n    s = list(s) \r\n    while counts :  \r\n        s.remove(c) \r\n        counts -= 1 \r\n    s = '' . join(s)   \r\n    return (s) ", "task_id": 377}
{"prompt": "Write a python function to shift last element to first position in the given list.", "test": ["assert move_first([1,2,3,4]) == [4,1,2,3]", "assert move_first([0,1,2,3]) == [3,0,1,2]", "assert move_first([9,8,7,1]) == [1,9,8,7]"], "code": "def move_first(test_list):\r\n  test_list = test_list[-1:] + test_list[:-1]  \r\n  return test_list", "task_id": 378}
{"prompt": "Write a function to find the surface area of a cuboid.", "test": ["assert surfacearea_cuboid(1,2,3)==22", "assert surfacearea_cuboid(5,7,9)==286", "assert surfacearea_cuboid(10,15,21)==1350"], "code": "def surfacearea_cuboid(l,w,h):\r\n  SA = 2*(l*w + l * h + w * h)\r\n  return SA", "task_id": 379}
{"prompt": "Write a function to generate a two-dimensional array.", "test": ["assert multi_list(3,4)==[[0, 0, 0, 0], [0, 1, 2, 3], [0, 2, 4, 6]] ", "assert multi_list(5,7)==[[0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6], [0, 2, 4, 6, 8, 10, 12], [0, 3, 6, 9, 12, 15, 18], [0, 4, 8, 12, 16, 20, 24]]", "assert multi_list(10,15)==[[0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0], [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24, 26, 28], [0, 3, 6, 9, 12, 15, 18, 21, 24, 27, 30, 33, 36, 39, 42], [0, 4, 8, 12, 16, 20, 24, 28, 32, 36, 40, 44, 48, 52, 56], [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60, 65, 70], [0, 6, 12, 18, 24, 30, 36, 42, 48, 54, 60, 66, 72, 78, 84], [0, 7, 14, 21, 28, 35, 42, 49, 56, 63, 70, 77, 84, 91, 98], [0, 8, 16, 24, 32, 40, 48, 56, 64, 72, 80, 88, 96, 104, 112], [0, 9, 18, 27, 36, 45, 54, 63, 72, 81, 90, 99, 108, 117, 126]]"], "code": "def multi_list(rownum,colnum):\r\n  multi_list = [[0 for col in range(colnum)] for row in range(rownum)]\r\n  for row in range(rownum):\r\n    for col in range(colnum):\r\n        multi_list[row][col]= row*col\r\n  return multi_list\r\n", "task_id": 380}
{"prompt": "Write a function to sort a list of lists by a given index of the inner list.", "test": ["assert index_on_inner_list([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,0)==[('Beau Turnbull', 94, 98), ('Brady Kent', 97, 96), ('Greyson Fulton', 98, 99), ('Wyatt Knott', 91, 94)]", "assert index_on_inner_list([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,1)==[('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98), ('Brady Kent', 97, 96), ('Greyson Fulton', 98, 99)]", "assert index_on_inner_list([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,2)==[('Wyatt Knott', 91, 94), ('Brady Kent', 97, 96), ('Beau Turnbull', 94, 98), ('Greyson Fulton', 98, 99)]"], "code": "from operator import itemgetter\r\ndef index_on_inner_list(list_data, index_no):\r\n    result = sorted(list_data, key=itemgetter(index_no))\r\n    return result", "task_id": 381}
{"prompt": "Write a function to find the number of rotations in a circularly sorted array.", "test": ["assert find_rotation_count([8, 9, 10, 1, 2, 3, 4, 5, 6, 7]) == 3", "assert find_rotation_count([8, 9, 10,2, 5, 6]) == 3", "assert find_rotation_count([2, 5, 6, 8, 9, 10]) == 0"], "code": "def find_rotation_count(A):\r\n    (left, right) = (0, len(A) - 1)\r\n    while left <= right:\r\n        if A[left] <= A[right]:\r\n            return left\r\n        mid = (left + right) // 2\r\n        next = (mid + 1) % len(A)\r\n        prev = (mid - 1 + len(A)) % len(A)\r\n        if A[mid] <= A[next] and A[mid] <= A[prev]:\r\n            return mid\r\n        elif A[mid] <= A[right]:\r\n            right = mid - 1\r\n        elif A[mid] >= A[left]:\r\n            left = mid + 1\r\n    return -1", "task_id": 382}
{"prompt": "Write a python function to toggle all odd bits of a given number.", "test": ["assert even_bit_toggle_number(10) == 15", "assert even_bit_toggle_number(20) == 1", "assert even_bit_toggle_number(30) == 11"], "code": "def even_bit_toggle_number(n) : \r\n    res = 0; count = 0; temp = n    \r\n    while(temp > 0 ) : \r\n        if (count % 2 == 0) : \r\n            res = res | (1 << count)      \r\n        count = count + 1\r\n        temp >>= 1 \r\n    return n ^ res ", "task_id": 383}
{"prompt": "Write a python function to find the frequency of the smallest value in a given array.", "test": ["assert frequency_Of_Smallest(5,[1,2,3,4,3]) == 1", "assert frequency_Of_Smallest(7,[3,1,2,5,6,2,3]) == 1", "assert frequency_Of_Smallest(7,[3,3,6,3,7,4,9]) == 3"], "code": "def frequency_Of_Smallest(n,arr): \r\n    mn = arr[0] \r\n    freq = 1\r\n    for i in range(1,n): \r\n        if (arr[i] < mn): \r\n            mn = arr[i] \r\n            freq = 1\r\n        elif (arr[i] == mn): \r\n            freq += 1\r\n    return freq ", "task_id": 384}
{"prompt": "Write a function to find the n'th perrin number using recursion.", "test": ["assert get_perrin(9) == 12", "assert get_perrin(4) == 2", "assert get_perrin(6) == 5"], "code": "def get_perrin(n):\r\n  if (n == 0):\r\n    return 3\r\n  if (n == 1):\r\n    return 0\r\n  if (n == 2):\r\n    return 2 \r\n  return get_perrin(n - 2) + get_perrin(n - 3)", "task_id": 385}
{"prompt": "Write a function to find out the minimum no of swaps required for bracket balancing in the given string.", "test": ["assert swap_count(\"[]][][\") == 2", "assert swap_count(\"[[][]]\") == 0", "assert swap_count(\"[[][]]][\") == 1"], "code": "def swap_count(s):\r\n\tchars = s\r\n\tcount_left = 0\r\n\tcount_right = 0\r\n\tswap = 0\r\n\timbalance = 0; \r\n\tfor i in range(len(chars)):\r\n\t\tif chars[i] == '[':\r\n\t\t\tcount_left += 1\r\n\t\t\tif imbalance > 0:\r\n\t\t\t\tswap += imbalance\r\n\t\t\t\timbalance -= 1\r\n\t\telif chars[i] == ']':\r\n\t\t\tcount_right += 1\r\n\t\t\timbalance = (count_right - count_left) \r\n\treturn swap", "task_id": 386}
{"prompt": "Write a python function to check whether the hexadecimal number is even or odd.", "test": ["assert even_or_odd(\"AB3454D\") ==\"Odd\"", "assert even_or_odd(\"ABC\") == \"Even\"", "assert even_or_odd(\"AAD\") == \"Odd\""], "code": "def even_or_odd(N): \r\n    l = len(N) \r\n    if (N[l-1] =='0'or N[l-1] =='2'or \r\n        N[l-1] =='4'or N[l-1] =='6'or \r\n        N[l-1] =='8'or N[l-1] =='A'or \r\n        N[l-1] =='C'or N[l-1] =='E'): \r\n        return (\"Even\") \r\n    else: \r\n        return (\"Odd\") ", "task_id": 387}
{"prompt": "Write a python function to find the highest power of 2 that is less than or equal to n.", "test": ["assert highest_Power_of_2(10) == 8", "assert highest_Power_of_2(19) == 16", "assert highest_Power_of_2(32) == 32"], "code": "def highest_Power_of_2(n): \r\n    res = 0; \r\n    for i in range(n, 0, -1): \r\n        if ((i & (i - 1)) == 0): \r\n            res = i; \r\n            break; \r\n    return res; ", "task_id": 388}
{"prompt": "Write a function to find the n'th lucas number.", "test": ["assert find_lucas(9) == 76", "assert find_lucas(4) == 7", "assert find_lucas(3) == 4"], "code": "def find_lucas(n): \r\n\tif (n == 0): \r\n\t\treturn 2\r\n\tif (n == 1): \r\n\t\treturn 1\r\n\treturn find_lucas(n - 1) + find_lucas(n - 2) ", "task_id": 389}
{"prompt": "Write a function to insert a given string at the beginning of all items in a list.", "test": ["assert add_string([1,2,3,4],'temp{0}')==['temp1', 'temp2', 'temp3', 'temp4']", "assert add_string(['a','b','c','d'], 'python{0}')==[ 'pythona', 'pythonb', 'pythonc', 'pythond']", "assert add_string([5,6,7,8],'string{0}')==['string5', 'string6', 'string7', 'string8']"], "code": "def add_string(list,string):\r\n add_string=[string.format(i) for i in  list]\r\n return add_string", "task_id": 390}
{"prompt": "Write a function to convert more than one list to nested dictionary.", "test": ["assert convert_list_dictionary([\"S001\", \"S002\", \"S003\", \"S004\"],[\"Adina Park\", \"Leyton Marsh\", \"Duncan Boyle\", \"Saim Richards\"] ,[85, 98, 89, 92])==[{'S001': {'Adina Park': 85}}, {'S002': {'Leyton Marsh': 98}}, {'S003': {'Duncan Boyle': 89}}, {'S004': {'Saim Richards': 92}}]", "assert convert_list_dictionary([\"abc\",\"def\",\"ghi\",\"jkl\"],[\"python\",\"program\",\"language\",\"programs\"],[100,200,300,400])==[{'abc':{'python':100}},{'def':{'program':200}},{'ghi':{'language':300}},{'jkl':{'programs':400}}]", "assert convert_list_dictionary([\"A1\",\"A2\",\"A3\",\"A4\"],[\"java\",\"C\",\"C++\",\"DBMS\"],[10,20,30,40])==[{'A1':{'java':10}},{'A2':{'C':20}},{'A3':{'C++':30}},{'A4':{'DBMS':40}}]"], "code": "def convert_list_dictionary(l1, l2, l3):\r\n     result = [{x: {y: z}} for (x, y, z) in zip(l1, l2, l3)]\r\n     return result", "task_id": 391}
{"prompt": "Write a function to find the maximum sum possible by using the given equation f(n) = max( (f(n/2) + f(n/3) + f(n/4) + f(n/5)), n).", "test": ["assert get_max_sum(60) == 106", "assert get_max_sum(10) == 12", "assert get_max_sum(2) == 2"], "code": "def get_max_sum (n):\r\n\tres = list()\r\n\tres.append(0)\r\n\tres.append(1)\r\n\ti = 2\r\n\twhile i<n + 1:\r\n\t\tres.append(max(i, (res[int(i / 2)] \r\n\t\t\t\t\t\t+ res[int(i / 3)] +\r\n\t\t\t\t\t\t\tres[int(i / 4)]\r\n\t\t\t\t\t\t+ res[int(i / 5)])))\r\n\t\ti = i + 1\r\n\treturn res[n]", "task_id": 392}
{"prompt": "Write a function to find the list with maximum length using lambda function.", "test": ["assert max_length_list([[0], [1, 3], [5, 7], [9, 11], [13, 15, 17]])==(3, [13, 15, 17])", "assert max_length_list([[1,2,3,4,5],[1,2,3,4],[1,2,3],[1,2],[1]])==(5,[1,2,3,4,5])", "assert max_length_list([[3,4,5],[6,7,8,9],[10,11,12]])==(4,[6,7,8,9])"], "code": "def max_length_list(input_list):\r\n    max_length = max(len(x) for x in input_list )   \r\n    max_list = max(input_list, key = lambda i: len(i))    \r\n    return(max_length, max_list)", "task_id": 393}
{"prompt": "Write a function to check if given tuple is distinct or not.", "test": ["assert check_distinct((1, 4, 5, 6, 1, 4)) == False", "assert check_distinct((1, 4, 5, 6)) == True", "assert check_distinct((2, 3, 4, 5, 6)) == True"], "code": "def check_distinct(test_tup):\r\n  res = True\r\n  temp = set()\r\n  for ele in test_tup:\r\n    if ele in temp:\r\n      res = False\r\n      break\r\n    temp.add(ele)\r\n  return (res) ", "task_id": 394}
{"prompt": "Write a python function to find the first non-repeated character in a given string.", "test": ["assert first_non_repeating_character(\"abcabc\") == None", "assert first_non_repeating_character(\"abc\") == \"a\"", "assert first_non_repeating_character(\"ababc\") == \"c\""], "code": "def first_non_repeating_character(str1):\r\n  char_order = []\r\n  ctr = {}\r\n  for c in str1:\r\n    if c in ctr:\r\n      ctr[c] += 1\r\n    else:\r\n      ctr[c] = 1 \r\n      char_order.append(c)\r\n  for c in char_order:\r\n    if ctr[c] == 1:\r\n      return c\r\n  return None", "task_id": 395}
{"prompt": "Write a function to check whether the given string starts and ends with the same character or not using regex.", "test": ["assert check_char(\"abba\") == \"Valid\"", "assert check_char(\"a\") == \"Valid\"", "assert check_char(\"abcd\") == \"Invalid\""], "code": "import re  \r\nregex = r'^[a-z]$|^([a-z]).*\\1$'\r\ndef check_char(string): \r\n\tif(re.search(regex, string)): \r\n\t\treturn \"Valid\" \r\n\telse: \r\n\t\treturn \"Invalid\" ", "task_id": 396}
{"prompt": "Write a function to find the median of three specific numbers.", "test": ["assert median_numbers(25,55,65)==55.0", "assert median_numbers(20,10,30)==20.0", "assert median_numbers(15,45,75)==45.0"], "code": "def median_numbers(a,b,c):\r\n if a > b:\r\n    if a < c:\r\n        median = a\r\n    elif b > c:\r\n        median = b\r\n    else:\r\n        median = c\r\n else:\r\n    if a > c:\r\n        median = a\r\n    elif b < c:\r\n        median = b\r\n    else:\r\n        median = c\r\n return median", "task_id": 397}
{"prompt": "Write a function to compute the sum of digits of each number of a given list.", "test": ["assert sum_of_digits([10,2,56])==14", "assert sum_of_digits([[10,20,4,5,'b',70,'a']])==19", "assert sum_of_digits([10,20,-4,5,-70])==19"], "code": "def sum_of_digits(nums):\r\n    return sum(int(el) for n in nums for el in str(n) if el.isdigit())", "task_id": 398}
{"prompt": "Write a function to perform the mathematical bitwise xor operation across the given tuples.", "test": ["assert bitwise_xor((10, 4, 6, 9), (5, 2, 3, 3)) == (15, 6, 5, 10)", "assert bitwise_xor((11, 5, 7, 10), (6, 3, 4, 4)) == (13, 6, 3, 14)", "assert bitwise_xor((12, 6, 8, 11), (7, 4, 5, 6)) == (11, 2, 13, 13)"], "code": "def bitwise_xor(test_tup1, test_tup2):\r\n  res = tuple(ele1 ^ ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 399}
{"prompt": "Write a function to extract the frequency of unique tuples in the given list order irrespective.", "test": ["assert extract_freq([(3, 4), (1, 2), (4, 3), (5, 6)] ) == 3", "assert extract_freq([(4, 15), (2, 3), (5, 4), (6, 7)] ) == 4", "assert extract_freq([(5, 16), (2, 3), (6, 5), (6, 9)] ) == 4"], "code": "def extract_freq(test_list):\r\n  res = len(list(set(tuple(sorted(sub)) for sub in test_list)))\r\n  return (res)", "task_id": 400}
{"prompt": "Write a function to perform index wise addition of tuple elements in the given two nested tuples.", "test": ["assert add_nested_tuples(((1, 3), (4, 5), (2, 9), (1, 10)), ((6, 7), (3, 9), (1, 1), (7, 3))) == ((7, 10), (7, 14), (3, 10), (8, 13))", "assert add_nested_tuples(((2, 4), (5, 6), (3, 10), (2, 11)), ((7, 8), (4, 10), (2, 2), (8, 4))) == ((9, 12), (9, 16), (5, 12), (10, 15))", "assert add_nested_tuples(((3, 5), (6, 7), (4, 11), (3, 12)), ((8, 9), (5, 11), (3, 3), (9, 5))) == ((11, 14), (11, 18), (7, 14), (12, 17))"], "code": "def add_nested_tuples(test_tup1, test_tup2):\r\n  res = tuple(tuple(a + b for a, b in zip(tup1, tup2))\r\n   for tup1, tup2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 401}
{"prompt": "Write a function to compute the value of ncr%p.", "test": ["assert ncr_modp(10,2,13)==6", "assert ncr_modp(15,12,43)==25", "assert ncr_modp(17,9,18)==10"], "code": "def ncr_modp(n, r, p): \r\n    C = [0 for i in range(r+1)]   \r\n    C[0] = 1\r\n    for i in range(1, n+1): \r\n        for j in range(min(i, r), 0, -1): \r\n            C[j] = (C[j] + C[j-1]) % p   \r\n    return C[r] ", "task_id": 402}
{"prompt": "Write a function to check if a url is valid or not using regex.", "test": ["assert is_valid_URL(\"https://www.google.com\") == True", "assert is_valid_URL(\"https:/www.gmail.com\") == False", "assert is_valid_URL(\"https:// www.redit.com\") == False"], "code": "import re\r\ndef is_valid_URL(str):\r\n\tregex = (\"((http|https)://)(www.)?\" +\r\n\t\t\t\"[a-zA-Z0-9@:%._\\\\+~#?&//=]\" +\r\n\t\t\t\"{2,256}\\\\.[a-z]\" +\r\n\t\t\t\"{2,6}\\\\b([-a-zA-Z0-9@:%\" +\r\n\t\t\t\"._\\\\+~#?&//=]*)\")\r\n\tp = re.compile(regex)\r\n\tif (str == None):\r\n\t\treturn False\r\n\tif(re.search(p, str)):\r\n\t\treturn True\r\n\telse:\r\n\t\treturn False", "task_id": 403}
{"prompt": "Write a python function to find the minimum of two numbers.", "test": ["assert minimum(1,2) == 1", "assert minimum(-5,-4) == -5", "assert minimum(0,0) == 0"], "code": "def minimum(a,b):   \r\n    if a <= b: \r\n        return a \r\n    else: \r\n        return b ", "task_id": 404}
{"prompt": "Write a function to check whether an element exists within a tuple.", "test": ["assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'r')==True", "assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\", \"e\"),'5')==False", "assert check_tuplex((\"w\", 3, \"r\", \"e\", \"s\", \"o\", \"u\", \"r\", \"c\",\"e\"),3)==True"], "code": "def check_tuplex(tuplex,tuple1): \r\n  if tuple1 in tuplex:\r\n    return True\r\n  else:\r\n     return False", "task_id": 405}
{"prompt": "Write a python function to find the parity of a given number.", "test": ["assert find_Parity(12) == \"Even Parity\"", "assert find_Parity(7) == \"Odd Parity\"", "assert find_Parity(10) == \"Even Parity\""], "code": "def find_Parity(x): \r\n    y = x ^ (x >> 1); \r\n    y = y ^ (y >> 2); \r\n    y = y ^ (y >> 4); \r\n    y = y ^ (y >> 8); \r\n    y = y ^ (y >> 16); \r\n    if (y & 1): \r\n        return (\"Odd Parity\"); \r\n    return (\"Even Parity\"); ", "task_id": 406}
{"prompt": "Write a function to create the next bigger number by rearranging the digits of a given number.", "test": ["assert rearrange_bigger(12)==21", "assert rearrange_bigger(10)==False", "assert rearrange_bigger(102)==120"], "code": "def rearrange_bigger(n):\r\n    nums = list(str(n))\r\n    for i in range(len(nums)-2,-1,-1):\r\n        if nums[i] < nums[i+1]:\r\n            z = nums[i:]\r\n            y = min(filter(lambda x: x > z[0], z))\r\n            z.remove(y)\r\n            z.sort()\r\n            nums[i:] = [y] + z\r\n            return int(\"\".join(nums))\r\n    return False", "task_id": 407}
{"prompt": "Write a function to find k number of pairs which consist of one element from the first array and one element from the second array.", "test": ["assert k_smallest_pairs([1,3,7],[2,4,6],2)==[[1, 2], [1, 4]]", "assert k_smallest_pairs([1,3,7],[2,4,6],1)==[[1, 2]]", "assert k_smallest_pairs([1,3,7],[2,4,6],7)==[[1, 2], [1, 4], [3, 2], [1, 6], [3, 4], [3, 6], [7, 2]]"], "code": "import heapq\r\ndef k_smallest_pairs(nums1, nums2, k):\r\n   queue = []\r\n   def push(i, j):\r\n       if i < len(nums1) and j < len(nums2):\r\n           heapq.heappush(queue, [nums1[i] + nums2[j], i, j])\r\n   push(0, 0)\r\n   pairs = []\r\n   while queue and len(pairs) < k:\r\n       _, i, j = heapq.heappop(queue)\r\n       pairs.append([nums1[i], nums2[j]])\r\n       push(i, j + 1)\r\n       if j == 0:\r\n           push(i + 1, 0)\r\n   return pairs", "task_id": 408}
{"prompt": "Write a function to find the minimum product from the pairs of tuples within a given list.", "test": ["assert min_product_tuple([(2, 7), (2, 6), (1, 8), (4, 9)] )==8", "assert min_product_tuple([(10,20), (15,2), (5,10)] )==30", "assert min_product_tuple([(11,44), (10,15), (20,5), (12, 9)] )==100"], "code": "def min_product_tuple(list1):\r\n    result_min = min([abs(x * y) for x, y in list1] )\r\n    return result_min", "task_id": 409}
{"prompt": "Write a function to find the minimum value in a given heterogeneous list.", "test": ["assert min_val(['Python', 3, 2, 4, 5, 'version'])==2", "assert min_val(['Python', 15, 20, 25])==15", "assert min_val(['Python', 30, 20, 40, 50, 'version'])==20"], "code": "def min_val(listval):\r\n     min_val = min(i for i in listval if isinstance(i, int))\r\n     return min_val", "task_id": 410}
{"prompt": "Write a function to convert the given snake case string to camel case string by using regex.", "test": ["assert snake_to_camel('android_tv') == 'AndroidTv'", "assert snake_to_camel('google_pixel') == 'GooglePixel'", "assert snake_to_camel('apple_watch') == 'AppleWatch'"], "code": "import re\r\ndef snake_to_camel(word):\r\n  return ''.join(x.capitalize() or '_' for x in word.split('_'))", "task_id": 411}
{"prompt": "Write a python function to remove odd numbers from a given list.", "test": ["assert remove_odd([1,2,3]) == [2]", "assert remove_odd([2,4,6]) == [2,4,6]", "assert remove_odd([10,20,3]) == [10,20]"], "code": "def remove_odd(l):\r\n    for i in l:\r\n        if i % 2 != 0:\r\n            l.remove(i)\r\n    return l", "task_id": 412}
{"prompt": "Write a function to extract the nth element from a given list of tuples.", "test": ["assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,0)==['Greyson Fulton', 'Brady Kent', 'Wyatt Knott', 'Beau Turnbull']", "assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)] ,2)==[99, 96, 94, 98]", "assert extract_nth_element([('Greyson Fulton', 98, 99), ('Brady Kent', 97, 96), ('Wyatt Knott', 91, 94), ('Beau Turnbull', 94, 98)],1)==[98, 97, 91, 94]"], "code": "def extract_nth_element(list1, n):\r\n    result = [x[n] for x in list1]\r\n    return result", "task_id": 413}
{"prompt": "Write a python function to check whether the value exists in a sequence or not.", "test": ["assert overlapping([1,2,3,4,5],[6,7,8,9]) == False", "assert overlapping([1,2,3],[4,5,6]) == False", "assert overlapping([1,4,5],[1,4,5]) == True"], "code": "def overlapping(list1,list2):  \r\n    c=0\r\n    d=0\r\n    for i in list1: \r\n        c+=1\r\n    for i in list2: \r\n        d+=1\r\n    for i in range(0,c): \r\n        for j in range(0,d): \r\n            if(list1[i]==list2[j]): \r\n                return 1\r\n    return 0", "task_id": 414}
{"prompt": "Write a python function to find a pair with highest product from a given array of integers.", "test": ["assert max_Product([1,2,3,4,7,0,8,4]) == (7,8)", "assert max_Product([0,-1,-2,-4,5,0,-6]) == (-4,-6)", "assert max_Product([1,2,3]) == (2,3)"], "code": "def max_Product(arr): \r\n    arr_len = len(arr) \r\n    if (arr_len < 2): \r\n        return (\"No pairs exists\")           \r\n    x = arr[0]; y = arr[1]      \r\n    for i in range(0,arr_len): \r\n        for j in range(i + 1,arr_len): \r\n            if (arr[i] * arr[j] > x * y): \r\n                x = arr[i]; y = arr[j] \r\n    return x,y    ", "task_id": 415}
{"prompt": "Write a function to find the maximum sum we can make by dividing number in three parts recursively and summing them up together for the given number.", "test": ["assert breakSum(12) == 13", "assert breakSum(24) == 27", "assert breakSum(23) == 23"], "code": "MAX = 1000000\r\ndef breakSum(n): \r\n\tdp = [0]*(n+1) \r\n\tdp[0] = 0\r\n\tdp[1] = 1\r\n\tfor i in range(2, n+1): \r\n\t\tdp[i] = max(dp[int(i/2)] + dp[int(i/3)] + dp[int(i/4)], i); \r\n\treturn dp[n]", "task_id": 416}
{"prompt": "Write a function to find common first element in given list of tuple.", "test": ["assert group_tuples([('x', 'y'), ('x', 'z'), ('w', 't')]) == [('x', 'y', 'z'), ('w', 't')]", "assert group_tuples([('a', 'b'), ('a', 'c'), ('d', 'e')]) == [('a', 'b', 'c'), ('d', 'e')]", "assert group_tuples([('f', 'g'), ('f', 'g'), ('h', 'i')]) == [('f', 'g', 'g'), ('h', 'i')]"], "code": "def group_tuples(Input): \r\n\tout = {} \r\n\tfor elem in Input: \r\n\t\ttry: \r\n\t\t\tout[elem[0]].extend(elem[1:]) \r\n\t\texcept KeyError: \r\n\t\t\tout[elem[0]] = list(elem) \r\n\treturn [tuple(values) for values in out.values()] ", "task_id": 417}
{"prompt": "Write a python function to find the sublist having maximum length.", "test": ["assert Find_Max([['A'],['A','B'],['A','B','C']]) == ['A','B','C']", "assert Find_Max([[1],[1,2],[1,2,3]]) == [1,2,3]", "assert Find_Max([[1,1],[1,2,3],[1,5,6,1]]) == [1,5,6,1]"], "code": "def Find_Max(lst): \r\n    maxList = max((x) for x in lst) \r\n    return maxList", "task_id": 418}
{"prompt": "Write a function to round every number of a given list of numbers and print the total sum multiplied by the length of the list.", "test": ["assert round_and_sum([22.4, 4.0, -16.22, -9.10, 11.00, -12.22, 14.20, -5.20, 17.50])==243", "assert round_and_sum([5,2,9,24.3,29])==345", "assert round_and_sum([25.0,56.7,89.2])==513"], "code": "def round_and_sum(list1):\r\n  lenght=len(list1)\r\n  round_and_sum=sum(list(map(round,list1))* lenght)\r\n  return round_and_sum", "task_id": 419}
{"prompt": "Write a python function to find the cube sum of first n even natural numbers.", "test": ["assert cube_Sum(2) == 72", "assert cube_Sum(3) == 288", "assert cube_Sum(4) == 800"], "code": "def cube_Sum(n): \r\n    sum = 0\r\n    for i in range(1,n + 1): \r\n        sum += (2*i)*(2*i)*(2*i) \r\n    return sum", "task_id": 420}
{"prompt": "Write a function to concatenate each element of tuple by the delimiter.", "test": ["assert concatenate_tuple((\"ID\", \"is\", 4, \"UTS\") ) == 'ID-is-4-UTS'", "assert concatenate_tuple((\"QWE\", \"is\", 4, \"RTY\") ) == 'QWE-is-4-RTY'", "assert concatenate_tuple((\"ZEN\", \"is\", 4, \"OP\") ) == 'ZEN-is-4-OP'"], "code": "def concatenate_tuple(test_tup):\r\n    delim = \"-\"\r\n    res = ''.join([str(ele) + delim for ele in test_tup])\r\n    res = res[ : len(res) - len(delim)]\r\n    return (str(res)) ", "task_id": 421}
{"prompt": "Write a python function to find the average of cubes of first n natural numbers.", "test": ["assert find_Average_Of_Cube(2) == 4.5", "assert find_Average_Of_Cube(3) == 12", "assert find_Average_Of_Cube(1) == 1"], "code": "def find_Average_Of_Cube(n):  \r\n    sum = 0\r\n    for i in range(1, n + 1): \r\n        sum += i * i * i  \r\n    return round(sum / n, 6) ", "task_id": 422}
{"prompt": "Write a function to solve gold mine problem.", "test": ["assert get_maxgold([[1, 3, 1, 5],[2, 2, 4, 1],[5, 0, 2, 3],[0, 6, 1, 2]],4,4)==16", "assert get_maxgold([[10,20],[30,40]],2,2)==70", "assert get_maxgold([[4,9],[3,7]],2,2)==13"], "code": "def get_maxgold(gold, m, n): \r\n    goldTable = [[0 for i in range(n)] \r\n                        for j in range(m)]   \r\n    for col in range(n-1, -1, -1): \r\n        for row in range(m):  \r\n            if (col == n-1): \r\n                right = 0\r\n            else: \r\n                right = goldTable[row][col+1] \r\n            if (row == 0 or col == n-1): \r\n                right_up = 0\r\n            else: \r\n                right_up = goldTable[row-1][col+1] \r\n            if (row == m-1 or col == n-1): \r\n                right_down = 0\r\n            else: \r\n                right_down = goldTable[row+1][col+1] \r\n            goldTable[row][col] = gold[row][col] + max(right, right_up, right_down) \r\n    res = goldTable[0][0] \r\n    for i in range(1, m): \r\n        res = max(res, goldTable[i][0])  \r\n    return res ", "task_id": 423}
{"prompt": "Write a function to extract only the rear index element of each string in the given tuple.", "test": ["assert extract_rear(('Mers', 'for', 'Vers') ) == ['s', 'r', 's']", "assert extract_rear(('Avenge', 'for', 'People') ) == ['e', 'r', 'e']", "assert extract_rear(('Gotta', 'get', 'go') ) == ['a', 't', 'o']"], "code": "def extract_rear(test_tuple):\r\n  res = list(sub[len(sub) - 1] for sub in test_tuple)\r\n  return (res) ", "task_id": 424}
{"prompt": "Write a function to count the number of sublists containing a particular element.", "test": ["assert count_element_in_list([[1, 3], [5, 7], [1, 11], [1, 15, 7]],1)==3", "assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'A')==3", "assert count_element_in_list([['A', 'B'], ['A', 'C'], ['A', 'D', 'E'], ['B', 'C', 'D']],'E')==1"], "code": "def count_element_in_list(list1, x): \r\n    ctr = 0\r\n    for i in range(len(list1)): \r\n        if x in list1[i]: \r\n            ctr+= 1          \r\n    return ctr", "task_id": 425}
{"prompt": "Write a function to filter odd numbers using lambda function.", "test": ["assert filter_oddnumbers([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1,3,5,7,9]", "assert filter_oddnumbers([10,20,45,67,84,93])==[45,67,93]", "assert filter_oddnumbers([5,7,9,8,6,4,3])==[5,7,9,3]"], "code": "def filter_oddnumbers(nums):\r\n odd_nums = list(filter(lambda x: x%2 != 0, nums))\r\n return odd_nums", "task_id": 426}
{"prompt": "Write a function to convert a date of yyyy-mm-dd format to dd-mm-yyyy format by using regex.", "test": ["assert change_date_format(\"2026-01-02\") == '02-01-2026'", "assert change_date_format(\"2020-11-13\") == '13-11-2020'", "assert change_date_format(\"2021-04-26\") == '26-04-2021'"], "code": "import re\r\ndef change_date_format(dt):\r\n        return re.sub(r'(\\d{4})-(\\d{1,2})-(\\d{1,2})', '\\\\3-\\\\2-\\\\1', dt)", "task_id": 427}
{"prompt": "Write a function to sort the given array by using shell sort.", "test": ["assert shell_sort([12, 23, 4, 5, 3, 2, 12, 81, 56, 95]) == [2, 3, 4, 5, 12, 12, 23, 56, 81, 95]", "assert shell_sort([24, 22, 39, 34, 87, 73, 68]) == [22, 24, 34, 39, 68, 73, 87]", "assert shell_sort([32, 30, 16, 96, 82, 83, 74]) == [16, 30, 32, 74, 82, 83, 96]"], "code": "def shell_sort(my_list):\r\n    gap = len(my_list) // 2\r\n    while gap > 0:\r\n        for i in range(gap, len(my_list)):\r\n            current_item = my_list[i]\r\n            j = i\r\n            while j >= gap and my_list[j - gap] > current_item:\r\n                my_list[j] = my_list[j - gap]\r\n                j -= gap\r\n            my_list[j] = current_item\r\n        gap //= 2\r\n\r\n    return my_list", "task_id": 428}
{"prompt": "Write a function to extract the elementwise and tuples from the given two tuples.", "test": ["assert and_tuples((10, 4, 6, 9), (5, 2, 3, 3)) == (0, 0, 2, 1)", "assert and_tuples((1, 2, 3, 4), (5, 6, 7, 8)) == (1, 2, 3, 0)", "assert and_tuples((8, 9, 11, 12), (7, 13, 14, 17)) == (0, 9, 10, 0)"], "code": "def and_tuples(test_tup1, test_tup2):\r\n  res = tuple(ele1 & ele2 for ele1, ele2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 429}
{"prompt": "Write a function to find the directrix of a parabola.", "test": ["assert parabola_directrix(5,3,2)==-198", "assert parabola_directrix(9,8,4)==-2336", "assert parabola_directrix(2,4,6)==-130"], "code": "def parabola_directrix(a, b, c): \r\n  directrix=((int)(c - ((b * b) + 1) * 4 * a ))\r\n  return directrix", "task_id": 430}
{"prompt": "Write a function that takes two lists and returns true if they have at least one common element.", "test": ["assert common_element([1,2,3,4,5], [5,6,7,8,9])==True", "assert common_element([1,2,3,4,5], [6,7,8,9])==None", "assert common_element(['a','b','c'], ['d','b','e'])==True"], "code": "def common_element(list1, list2):\r\n     result = False\r\n     for x in list1:\r\n         for y in list2:\r\n             if x == y:\r\n                 result = True\r\n                 return result", "task_id": 431}
{"prompt": "Write a function to find the median of a trapezium.", "test": ["assert median_trapezium(15,25,35)==20", "assert median_trapezium(10,20,30)==15", "assert median_trapezium(6,9,4)==7.5"], "code": "def median_trapezium(base1,base2,height):\r\n median = 0.5 * (base1+ base2)\r\n return median", "task_id": 432}
{"prompt": "Write a function to check whether the entered number is greater than the elements of the given array.", "test": ["assert check_greater([1, 2, 3, 4, 5], 4) == 'No, entered number is less than those in the array'", "assert check_greater([2, 3, 4, 5, 6], 8) == 'Yes, the entered number is greater than those in the array'", "assert check_greater([9, 7, 4, 8, 6, 1], 11) == 'Yes, the entered number is greater than those in the array'"], "code": "def check_greater(arr, number):\r\n  arr.sort()\r\n  if number > arr[-1]:\r\n    return ('Yes, the entered number is greater than those in the array')\r\n  else:\r\n    return ('No, entered number is less than those in the array')", "task_id": 433}
{"prompt": "Write a function that matches a string that has an a followed by one or more b's.", "test": ["assert text_match_one(\"ac\")==('Not matched!')", "assert text_match_one(\"dc\")==('Not matched!')", "assert text_match_one(\"abba\")==('Found a match!')"], "code": "import re\r\ndef text_match_one(text):\r\n        patterns = 'ab+?'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return('Not matched!')\r\n", "task_id": 434}
{"prompt": "Write a python function to find the last digit of a given number.", "test": ["assert last_Digit(123) == 3", "assert last_Digit(25) == 5", "assert last_Digit(30) == 0"], "code": "def last_Digit(n) :\r\n    return (n % 10) ", "task_id": 435}
{"prompt": "Write a python function to print negative numbers in a list.", "test": ["assert neg_nos([-1,4,5,-6]) == -1,-6", "assert neg_nos([-1,-2,3,4]) == -1,-2", "assert neg_nos([-7,-6,8,9]) == -7,-6"], "code": "def neg_nos(list1):\r\n  for num in list1: \r\n    if num < 0: \r\n       return num ", "task_id": 436}
{"prompt": "Write a function to remove odd characters in a string.", "test": ["assert remove_odd(\"python\")==(\"yhn\")", "assert remove_odd(\"program\")==(\"rga\")", "assert remove_odd(\"language\")==(\"agae\")"], "code": "def remove_odd(str1):\r\n str2 = ''\r\n for i in range(1, len(str1) + 1):\r\n    if(i % 2 == 0):\r\n        str2 = str2 + str1[i - 1]\r\n return str2", "task_id": 437}
{"prompt": "Write a function to count bidirectional tuple pairs.", "test": ["assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 1), (6, 5), (2, 1)] ) == '3'", "assert count_bidirectional([(5, 6), (1, 3), (6, 5), (9, 1), (6, 5), (2, 1)] ) == '2'", "assert count_bidirectional([(5, 6), (1, 2), (6, 5), (9, 2), (6, 5), (2, 1)] ) == '4'"], "code": "def count_bidirectional(test_list):\r\n  res = 0\r\n  for idx in range(0, len(test_list)):\r\n    for iidx in range(idx + 1, len(test_list)):\r\n      if test_list[iidx][0] == test_list[idx][1] and test_list[idx][1] == test_list[iidx][0]:\r\n        res += 1\r\n  return (str(res)) ", "task_id": 438}
{"prompt": "Write a function to convert a list of multiple integers into a single integer.", "test": ["assert multiple_to_single([11, 33, 50])==113350", "assert multiple_to_single([-1,2,3,4,5,6])==-123456", "assert multiple_to_single([10,15,20,25])==10152025"], "code": "def multiple_to_single(L):\r\n  x = int(\"\".join(map(str, L)))\r\n  return x", "task_id": 439}
{"prompt": "Write a function to find all adverbs and their positions in a given sentence.", "test": ["assert find_adverb_position(\"clearly!! we can see the sky\")==(0, 7, 'clearly')", "assert find_adverb_position(\"seriously!! there are many roses\")==(0, 9, 'seriously')", "assert find_adverb_position(\"unfortunately!! sita is going to home\")==(0, 13, 'unfortunately')"], "code": "import re\r\ndef find_adverb_position(text):\r\n for m in re.finditer(r\"\\w+ly\", text):\r\n    return (m.start(), m.end(), m.group(0))", "task_id": 440}
{"prompt": "Write a function to find the surface area of a cube.", "test": ["assert surfacearea_cube(5)==150", "assert surfacearea_cube(3)==54", "assert surfacearea_cube(10)==600"], "code": "def surfacearea_cube(l):\r\n  surfacearea= 6*l*l\r\n  return surfacearea", "task_id": 441}
{"prompt": "Write a function to find the ration of positive numbers in an array of integers.", "test": ["assert positive_count([0, 1, 2, -1, -5, 6, 0, -3, -2, 3, 4, 6, 8])==0.54", "assert positive_count([2, 1, 2, -1, -5, 6, 4, -3, -2, 3, 4, 6, 8])==0.69", "assert positive_count([2, 4, -6, -9, 11, -12, 14, -5, 17])==0.56"], "code": "from array import array\r\ndef positive_count(nums):\r\n    n = len(nums)\r\n    n1 = 0\r\n    for x in nums:\r\n        if x > 0:\r\n            n1 += 1\r\n        else:\r\n          None\r\n    return round(n1/n,2)", "task_id": 442}
{"prompt": "Write a python function to find the largest negative number from the given list.", "test": ["assert largest_neg([1,2,3,-4,-6]) == -6", "assert largest_neg([1,2,3,-8,-9]) == -9", "assert largest_neg([1,2,3,4,-1]) == -1"], "code": "def largest_neg(list1): \r\n    max = list1[0] \r\n    for x in list1: \r\n        if x < max : \r\n             max = x  \r\n    return max", "task_id": 443}
{"prompt": "Write a function to trim each tuple by k in the given tuple list.", "test": ["assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1),(9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 2) == '[(2,), (9,), (2,), (2,)]'", "assert trim_tuple([(5, 3, 2, 1, 4), (3, 4, 9, 2, 1), (9, 1, 2, 3, 5), (4, 8, 2, 1, 7)], 1) == '[(3, 2, 1), (4, 9, 2), (1, 2, 3), (8, 2, 1)]'", "assert trim_tuple([(7, 8, 4, 9), (11, 8, 12, 4),(4, 1, 7, 8), (3, 6, 9, 7)], 1) == '[(8, 4), (8, 12), (1, 7), (6, 9)]'"], "code": "def trim_tuple(test_list, K):\r\n  res = []\r\n  for ele in test_list:\r\n    N = len(ele)\r\n    res.append(tuple(list(ele)[K: N - K]))\r\n  return (str(res)) ", "task_id": 444}
{"prompt": "Write a function to perform index wise multiplication of tuple elements in the given two tuples.", "test": ["assert index_multiplication(((1, 3), (4, 5), (2, 9), (1, 10)),((6, 7), (3, 9), (1, 1), (7, 3)) ) == ((6, 21), (12, 45), (2, 9), (7, 30))", "assert index_multiplication(((2, 4), (5, 6), (3, 10), (2, 11)),((7, 8), (4, 10), (2, 2), (8, 4)) ) == ((14, 32), (20, 60), (6, 20), (16, 44))", "assert index_multiplication(((3, 5), (6, 7), (4, 11), (3, 12)),((8, 9), (5, 11), (3, 3), (9, 5)) ) == ((24, 45), (30, 77), (12, 33), (27, 60))"], "code": "def index_multiplication(test_tup1, test_tup2):\r\n  res = tuple(tuple(a * b for a, b in zip(tup1, tup2))\r\n   for tup1, tup2 in zip(test_tup1, test_tup2))\r\n  return (res) ", "task_id": 445}
{"prompt": "Write a python function to count the occurence of all elements of list in a tuple.", "test": ["assert count_Occurrence(('a', 'a', 'c', 'b', 'd'),['a', 'b'] ) == 3", "assert count_Occurrence((1, 2, 3, 1, 4, 6, 7, 1, 4),[1, 4, 7]) == 6", "assert count_Occurrence((1,2,3,4,5,6),[1,2]) == 2"], "code": "from collections import Counter \r\ndef count_Occurrence(tup, lst): \r\n    count = 0\r\n    for item in tup: \r\n        if item in lst: \r\n            count+= 1 \r\n    return count  ", "task_id": 446}
{"prompt": "Write a function to find cubes of individual elements in a list using lambda function.", "test": ["assert cube_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[1, 8, 27, 64, 125, 216, 343, 512, 729, 1000]", "assert cube_nums([10,20,30])==([1000, 8000, 27000])", "assert cube_nums([12,15])==([1728, 3375])"], "code": "def cube_nums(nums):\r\n cube_nums = list(map(lambda x: x ** 3, nums))\r\n return cube_nums", "task_id": 447}
{"prompt": "Write a function to calculate the sum of perrin numbers.", "test": ["assert cal_sum(9) == 49", "assert cal_sum(10) == 66", "assert cal_sum(11) == 88"], "code": "def cal_sum(n): \r\n\ta = 3\r\n\tb = 0\r\n\tc = 2\r\n\tif (n == 0): \r\n\t\treturn 3\r\n\tif (n == 1): \r\n\t\treturn 3\r\n\tif (n == 2): \r\n\t\treturn 5\r\n\tsum = 5\r\n\twhile (n > 2): \r\n\t\td = a + b \r\n\t\tsum = sum + d \r\n\t\ta = b \r\n\t\tb = c \r\n\t\tc = d \r\n\t\tn = n-1\r\n\treturn sum", "task_id": 448}
{"prompt": "Write a python function to check whether the triangle is valid or not if 3 points are given.", "test": ["assert check_Triangle(1,5,2,5,4,6) == 'Yes'", "assert check_Triangle(1,1,1,4,1,5) == 'No'", "assert check_Triangle(1,1,1,1,1,1) == 'No'"], "code": "def check_Triangle(x1,y1,x2,y2,x3,y3): \r\n    a = (x1*(y2-y3)+x2*(y3-y1)+x3*(y1-y2))   \r\n    if a == 0: \r\n        return ('No') \r\n    else: \r\n        return ('Yes') ", "task_id": 449}
{"prompt": "Write a function to extract specified size of strings from a give list of string values.", "test": ["assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,8)==['practice', 'solution']", "assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,6)==['Python']", "assert extract_string(['Python', 'list', 'exercises', 'practice', 'solution'] ,9)==['exercises']"], "code": "def extract_string(str, l):\r\n    result = [e for e in str if len(e) == l] \r\n    return result", "task_id": 450}
{"prompt": "Write a function to remove all whitespaces from the given string using regex.", "test": ["assert remove_whitespaces(' Google    Flutter ') == 'GoogleFlutter'", "assert remove_whitespaces(' Google    Dart ') == 'GoogleDart'", "assert remove_whitespaces(' iOS    Swift ') == 'iOSSwift'"], "code": "import re\r\ndef remove_whitespaces(text1):\r\n  return (re.sub(r'\\s+', '',text1))", "task_id": 451}
{"prompt": "Write a function that gives loss amount if the given amount has loss else return none.", "test": ["assert loss_amount(1500,1200)==None", "assert loss_amount(100,200)==100", "assert loss_amount(2000,5000)==3000"], "code": "def loss_amount(actual_cost,sale_amount): \r\n  if(sale_amount > actual_cost):\r\n    amount = sale_amount - actual_cost\r\n    return amount\r\n  else:\r\n    return None", "task_id": 452}
{"prompt": "Write a python function to find the sum of even factors of a number.", "test": ["assert sumofFactors(18) == 26", "assert sumofFactors(30) == 48", "assert sumofFactors(6) == 8"], "code": "import math \r\ndef sumofFactors(n) : \r\n    if (n % 2 != 0) : \r\n        return 0\r\n    res = 1\r\n    for i in range(2, (int)(math.sqrt(n)) + 1) :    \r\n        count = 0\r\n        curr_sum = 1\r\n        curr_term = 1\r\n        while (n % i == 0) : \r\n            count= count + 1\r\n            n = n // i \r\n            if (i == 2 and count == 1) : \r\n                curr_sum = 0\r\n            curr_term = curr_term * i \r\n            curr_sum = curr_sum + curr_term \r\n        res = res * curr_sum  \r\n    if (n >= 2) : \r\n        res = res * (1 + n) \r\n    return res ", "task_id": 453}
{"prompt": "Write a function that matches a word containing 'z'.", "test": ["assert text_match_wordz(\"pythonz.\")==('Found a match!')", "assert text_match_wordz(\"xyz.\")==('Found a match!')", "assert text_match_wordz(\"  lang  .\")==('Not matched!')"], "code": "import re\r\ndef text_match_wordz(text):\r\n        patterns = '\\w*z.\\w*'\r\n        if re.search(patterns,  text):\r\n                return 'Found a match!'\r\n        else:\r\n                return('Not matched!')", "task_id": 454}
{"prompt": "Write a function to check whether the given month number contains 31 days or not.", "test": ["assert check_monthnumb_number(5)==True", "assert check_monthnumb_number(2)==False", "assert check_monthnumb_number(6)==False"], "code": "def check_monthnumb_number(monthnum2):\r\n  if(monthnum2==1 or monthnum2==3 or monthnum2==5 or monthnum2==7 or monthnum2==8 or monthnum2==10 or monthnum2==12):\r\n    return True\r\n  else:\r\n    return False", "task_id": 455}
{"prompt": "Write a function to reverse strings in a given list of string values.", "test": ["assert reverse_string_list(['Red', 'Green', 'Blue', 'White', 'Black'])==['deR', 'neerG', 'eulB', 'etihW', 'kcalB']", "assert reverse_string_list(['john','amal','joel','george'])==['nhoj','lama','leoj','egroeg']", "assert reverse_string_list(['jack','john','mary'])==['kcaj','nhoj','yram']"], "code": "def reverse_string_list(stringlist):\r\n    result = [x[::-1] for x in stringlist]\r\n    return result", "task_id": 456}
{"prompt": "Write a python function to find the sublist having minimum length.", "test": ["assert Find_Min([[1],[1,2],[1,2,3]]) == [1]", "assert Find_Min([[1,1],[1,1,1],[1,2,7,8]]) == [1,1]", "assert Find_Min([['x'],['x','y'],['x','y','z']]) == ['x']"], "code": "def Find_Min(lst): \r\n    minList = min((x) for x in lst) \r\n    return minList", "task_id": 457}
{"prompt": "Write a function to find the area of a rectangle.", "test": ["assert rectangle_area(10,20)==200", "assert rectangle_area(10,5)==50", "assert rectangle_area(4,2)==8"], "code": "def rectangle_area(l,b):\r\n  area=l*b\r\n  return area", "task_id": 458}
{"prompt": "Write a function to remove uppercase substrings from a given string by using regex.", "test": ["assert remove_uppercase('cAstyoUrFavoRitETVshoWs') == 'cstyoravoitshos'", "assert remove_uppercase('wAtchTheinTernEtrAdIo') == 'wtchheinerntrdo'", "assert remove_uppercase('VoicESeaRchAndreComMendaTionS') == 'oiceachndreomendaion'"], "code": "import re\r\ndef remove_uppercase(str1):\r\n  remove_upper = lambda text: re.sub('[A-Z]', '', text)\r\n  result =  remove_upper(str1)\r\n  return (result)", "task_id": 459}
{"prompt": "Write a python function to get the first element of each sublist.", "test": ["assert Extract([[1, 2], [3, 4, 5], [6, 7, 8, 9]]) == [1, 3, 6]", "assert Extract([[1,2,3],[4, 5]]) == [1,4]", "assert Extract([[9,8,1],[1,2]]) == [9,1]"], "code": "def Extract(lst): \r\n    return [item[0] for item in lst] ", "task_id": 460}
{"prompt": "Write a python function to count the upper case characters in a given string.", "test": ["assert upper_ctr('PYthon') == 1", "assert upper_ctr('BigData') == 1", "assert upper_ctr('program') == 0"], "code": "def upper_ctr(str):\r\n    upper_ctr = 0\r\n    for i in range(len(str)):\r\n          if str[i] >= 'A' and str[i] <= 'Z': upper_ctr += 1\r\n          return upper_ctr", "task_id": 461}
{"prompt": "Write a function to find all possible combinations of the elements of a given list.", "test": ["assert combinations_list(['orange', 'red', 'green', 'blue'])==[[], ['orange'], ['red'], ['red', 'orange'], ['green'], ['green', 'orange'], ['green', 'red'], ['green', 'red', 'orange'], ['blue'], ['blue', 'orange'], ['blue', 'red'], ['blue', 'red', 'orange'], ['blue', 'green'], ['blue', 'green', 'orange'], ['blue', 'green', 'red'], ['blue', 'green', 'red', 'orange']]", "assert combinations_list(['red', 'green', 'blue', 'white', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['blue'], ['blue', 'red'], ['blue', 'green'], ['blue', 'green', 'red'], ['white'], ['white', 'red'], ['white', 'green'], ['white', 'green', 'red'], ['white', 'blue'], ['white', 'blue', 'red'], ['white', 'blue', 'green'], ['white', 'blue', 'green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['black', 'blue'], ['black', 'blue', 'red'], ['black', 'blue', 'green'], ['black', 'blue', 'green', 'red'], ['black', 'white'], ['black', 'white', 'red'], ['black', 'white', 'green'], ['black', 'white', 'green', 'red'], ['black', 'white', 'blue'], ['black', 'white', 'blue', 'red'], ['black', 'white', 'blue', 'green'], ['black', 'white', 'blue', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'blue'], ['orange', 'blue', 'red'], ['orange', 'blue', 'green'], ['orange', 'blue', 'green', 'red'], ['orange', 'white'], ['orange', 'white', 'red'], ['orange', 'white', 'green'], ['orange', 'white', 'green', 'red'], ['orange', 'white', 'blue'], ['orange', 'white', 'blue', 'red'], ['orange', 'white', 'blue', 'green'], ['orange', 'white', 'blue', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red'], ['orange', 'black', 'blue'], ['orange', 'black', 'blue', 'red'], ['orange', 'black', 'blue', 'green'], ['orange', 'black', 'blue', 'green', 'red'], ['orange', 'black', 'white'], ['orange', 'black', 'white', 'red'], ['orange', 'black', 'white', 'green'], ['orange', 'black', 'white', 'green', 'red'], ['orange', 'black', 'white', 'blue'], ['orange', 'black', 'white', 'blue', 'red'], ['orange', 'black', 'white', 'blue', 'green'], ['orange', 'black', 'white', 'blue', 'green', 'red']]", "assert combinations_list(['red', 'green', 'black', 'orange'])==[[], ['red'], ['green'], ['green', 'red'], ['black'], ['black', 'red'], ['black', 'green'], ['black', 'green', 'red'], ['orange'], ['orange', 'red'], ['orange', 'green'], ['orange', 'green', 'red'], ['orange', 'black'], ['orange', 'black', 'red'], ['orange', 'black', 'green'], ['orange', 'black', 'green', 'red']]"], "code": "def combinations_list(list1):\r\n    if len(list1) == 0:\r\n        return [[]]\r\n    result = []\r\n    for el in combinations_list(list1[1:]):\r\n        result += [el, el+[list1[0]]]\r\n    return result", "task_id": 462}
{"prompt": "Write a function to find the maximum product subarray of the given array.", "test": ["assert max_subarray_product([1, -2, -3, 0, 7, -8, -2]) == 112", "assert max_subarray_product([6, -3, -10, 0, 2]) == 180 ", "assert max_subarray_product([-2, -40, 0, -2, -3]) == 80"], "code": "def max_subarray_product(arr):\r\n\tn = len(arr)\r\n\tmax_ending_here = 1\r\n\tmin_ending_here = 1\r\n\tmax_so_far = 0\r\n\tflag = 0\r\n\tfor i in range(0, n):\r\n\t\tif arr[i] > 0:\r\n\t\t\tmax_ending_here = max_ending_here * arr[i]\r\n\t\t\tmin_ending_here = min (min_ending_here * arr[i], 1)\r\n\t\t\tflag = 1\r\n\t\telif arr[i] == 0:\r\n\t\t\tmax_ending_here = 1\r\n\t\t\tmin_ending_here = 1\r\n\t\telse:\r\n\t\t\ttemp = max_ending_here\r\n\t\t\tmax_ending_here = max (min_ending_here * arr[i], 1)\r\n\t\t\tmin_ending_here = temp * arr[i]\r\n\t\tif (max_so_far < max_ending_here):\r\n\t\t\tmax_so_far = max_ending_here\r\n\tif flag == 0 and max_so_far == 0:\r\n\t\treturn 0\r\n\treturn max_so_far", "task_id": 463}
{"prompt": "Write a function to check if all values are same in a dictionary.", "test": ["assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},10)==False", "assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},12)==True", "assert check_value({'Cierra Vega': 12, 'Alden Cantrell': 12, 'Kierra Gentry': 12, 'Pierre Cox': 12},5)==False"], "code": "def check_value(dict, n):\r\n    result = all(x == n for x in dict.values()) \r\n    return result", "task_id": 464}
{"prompt": "Write a function to drop empty items from a given dictionary.", "test": ["assert drop_empty({'c1': 'Red', 'c2': 'Green', 'c3':None})=={'c1': 'Red', 'c2': 'Green'}", "assert drop_empty({'c1': 'Red', 'c2': None, 'c3':None})=={'c1': 'Red'}", "assert drop_empty({'c1': None, 'c2': 'Green', 'c3':None})=={ 'c2': 'Green'}"], "code": "def drop_empty(dict1):\r\n  dict1 = {key:value for (key, value) in dict1.items() if value is not None}\r\n  return dict1", "task_id": 465}
{"prompt": "Write a function to find the peak element in the given array.", "test": ["assert find_peak([1, 3, 20, 4, 1, 0], 6) == 2", "assert find_peak([2, 3, 4, 5, 6], 5) == 4", "assert find_peak([8, 9, 11, 12, 14, 15], 6) == 5 "], "code": "def find_peak_util(arr, low, high, n): \r\n\tmid = low + (high - low)/2\r\n\tmid = int(mid) \r\n\tif ((mid == 0 or arr[mid - 1] <= arr[mid]) and\r\n\t\t(mid == n - 1 or arr[mid + 1] <= arr[mid])): \r\n\t\treturn mid \r\n\telif (mid > 0 and arr[mid - 1] > arr[mid]): \r\n\t\treturn find_peak_util(arr, low, (mid - 1), n) \r\n\telse: \r\n\t\treturn find_peak_util(arr, (mid + 1), high, n) \r\ndef find_peak(arr, n): \r\n\treturn find_peak_util(arr, 0, n - 1, n) ", "task_id": 466}
{"prompt": "Write a python function to convert decimal number to octal number.", "test": ["assert decimal_to_Octal(10) == 12", "assert decimal_to_Octal(2) == 2", "assert decimal_to_Octal(33) == 41"], "code": "def decimal_to_Octal(deciNum):\r\n    octalNum = 0\r\n    countval = 1;\r\n    dNo = deciNum;\r\n    while (deciNum!= 0):\r\n        remainder= deciNum % 8;\r\n        octalNum+= remainder*countval;\r\n        countval= countval*10;\r\n        deciNum //= 8; \r\n    return (octalNum)", "task_id": 467}
{"prompt": "Write a function to find the maximum product formed by multiplying numbers of an increasing subsequence of that array.", "test": ["assert max_product([3, 100, 4, 5, 150, 6], 6) == 45000 ", "assert max_product([4, 42, 55, 68, 80], 5) == 50265600", "assert max_product([10, 22, 9, 33, 21, 50, 41, 60], 8) == 21780000 "], "code": "def max_product(arr, n ): \r\n\tmpis =[0] * (n) \r\n\tfor i in range(n): \r\n\t\tmpis[i] = arr[i] \r\n\tfor i in range(1, n): \r\n\t\tfor j in range(i): \r\n\t\t\tif (arr[i] > arr[j] and\r\n\t\t\t\t\tmpis[i] < (mpis[j] * arr[i])): \r\n\t\t\t\t\t\tmpis[i] = mpis[j] * arr[i] \r\n\treturn max(mpis)", "task_id": 468}
{"prompt": "Write a function to find the maximum profit earned from a maximum of k stock transactions", "test": ["assert max_profit([1, 5, 2, 3, 7, 6, 4, 5], 3) == 10", "assert max_profit([2, 4, 7, 5, 4, 3, 5], 2) == 7", "assert max_profit([10, 6, 8, 4, 2], 2) == 2"], "code": "def max_profit(price, k):\r\n    n = len(price)\r\n    final_profit = [[None for x in range(n)] for y in range(k + 1)]\r\n    for i in range(k + 1):\r\n        for j in range(n):\r\n            if i == 0 or j == 0:\r\n                final_profit[i][j] = 0\r\n            else:\r\n                max_so_far = 0\r\n                for x in range(j):\r\n                    curr_price = price[j] - price[x] + final_profit[i-1][x]\r\n                    if max_so_far < curr_price:\r\n                        max_so_far = curr_price\r\n                final_profit[i][j] = max(final_profit[i][j-1], max_so_far)\r\n    return final_profit[k][n-1]", "task_id": 469}
{"prompt": "Write a function to find the pairwise addition of the elements of the given tuples.", "test": ["assert add_pairwise((1, 5, 7, 8, 10)) == (6, 12, 15, 18)", "assert add_pairwise((2, 6, 8, 9, 11)) == (8, 14, 17, 20)", "assert add_pairwise((3, 7, 9, 10, 12)) == (10, 16, 19, 22)"], "code": "def add_pairwise(test_tup):\r\n  res = tuple(i + j for i, j in zip(test_tup, test_tup[1:]))\r\n  return (res) ", "task_id": 470}
{"prompt": "Write a python function to find remainder of array multiplication divided by n.", "test": ["assert find_remainder([ 100, 10, 5, 25, 35, 14 ],6,11) ==9", "assert find_remainder([1,1,1],3,1) == 0", "assert find_remainder([1,2,1],3,2) == 0"], "code": "def find_remainder(arr, lens, n): \r\n    mul = 1\r\n    for i in range(lens):  \r\n        mul = (mul * (arr[i] % n)) % n \r\n    return mul % n ", "task_id": 471}
{"prompt": "Write a python function to check whether the given list contains consecutive numbers or not.", "test": ["assert check_Consecutive([1,2,3,4,5]) == True", "assert check_Consecutive([1,2,3,5,6]) == False", "assert check_Consecutive([1,2,1]) == False"], "code": "def check_Consecutive(l): \r\n    return sorted(l) == list(range(min(l),max(l)+1)) ", "task_id": 472}
{"prompt": "Write a function to find the tuple intersection of elements in the given tuple list irrespective of their order.", "test": ["assert tuple_intersection([(3, 4), (5, 6), (9, 10), (4, 5)] , [(5, 4), (3, 4), (6, 5), (9, 11)]) == {(4, 5), (3, 4), (5, 6)}", "assert tuple_intersection([(4, 1), (7, 4), (11, 13), (17, 14)] , [(1, 4), (7, 4), (16, 12), (10, 13)]) == {(4, 7), (1, 4)}", "assert tuple_intersection([(2, 1), (3, 2), (1, 3), (1, 4)] , [(11, 2), (2, 3), (6, 2), (1, 3)]) == {(1, 3), (2, 3)}"], "code": "def tuple_intersection(test_list1, test_list2):\r\n  res = set([tuple(sorted(ele)) for ele in test_list1]) & set([tuple(sorted(ele)) for ele in test_list2])\r\n  return (res)", "task_id": 473}
{"prompt": "Write a function to replace characters in a string.", "test": ["assert replace_char(\"polygon\",'y','l')==(\"pollgon\")", "assert replace_char(\"character\",'c','a')==(\"aharaater\")", "assert replace_char(\"python\",'l','a')==(\"python\")"], "code": "def replace_char(str1,ch,newch):\r\n str2 = str1.replace(ch, newch)\r\n return str2", "task_id": 474}
{"prompt": "Write a function to sort counter by value.", "test": ["assert sort_counter({'Math':81, 'Physics':83, 'Chemistry':87})==[('Chemistry', 87), ('Physics', 83), ('Math', 81)]", "assert sort_counter({'Math':400, 'Physics':300, 'Chemistry':250})==[('Math', 400), ('Physics', 300), ('Chemistry', 250)]", "assert sort_counter({'Math':900, 'Physics':1000, 'Chemistry':1250})==[('Chemistry', 1250), ('Physics', 1000), ('Math', 900)]"], "code": "from collections import Counter\r\ndef sort_counter(dict1):\r\n x = Counter(dict1)\r\n sort_counter=x.most_common()\r\n return sort_counter", "task_id": 475}
{"prompt": "Write a python function to find the sum of the largest and smallest value in a given array.", "test": ["assert big_sum([1,2,3]) == 4", "assert big_sum([-1,2,3,4]) == 3", "assert big_sum([2,3,6]) == 8"], "code": "def big_sum(nums):\r\n      sum= max(nums)+min(nums)\r\n      return sum", "task_id": 476}
{"prompt": "Write a python function to convert the given string to lower case.", "test": ["assert is_lower(\"InValid\") == \"invalid\"", "assert is_lower(\"TruE\") == \"true\"", "assert is_lower(\"SenTenCE\") == \"sentence\""], "code": "def is_lower(string):\r\n  return (string.lower())", "task_id": 477}
{"prompt": "Write a function to remove lowercase substrings from a given string.", "test": ["assert remove_lowercase(\"PYTHon\")==('PYTH')", "assert remove_lowercase(\"FInD\")==('FID')", "assert remove_lowercase(\"STRinG\")==('STRG')"], "code": "import re\r\ndef remove_lowercase(str1):\r\n remove_lower = lambda text: re.sub('[a-z]', '', text)\r\n result =  remove_lower(str1)\r\n return result", "task_id": 478}
{"prompt": "Write a python function to find the first digit of a given number.", "test": ["assert first_Digit(123) == 1", "assert first_Digit(456) == 4", "assert first_Digit(12) == 1"], "code": "def first_Digit(n) :  \r\n    while n >= 10:  \r\n        n = n / 10; \r\n    return int(n) ", "task_id": 479}
{"prompt": "Write a python function to find the maximum occurring character in a given string.", "test": ["assert get_max_occuring_char(\"data\") == \"a\"", "assert get_max_occuring_char(\"create\") == \"e\"", "assert get_max_occuring_char(\"brilliant girl\") == \"i\""], "code": "def get_max_occuring_char(str1):\r\n  ASCII_SIZE = 256\r\n  ctr = [0] * ASCII_SIZE\r\n  max = -1\r\n  ch = ''\r\n  for i in str1:\r\n    ctr[ord(i)]+=1;\r\n  for i in str1:\r\n    if max < ctr[ord(i)]:\r\n      max = ctr[ord(i)]\r\n      ch = i\r\n  return ch", "task_id": 480}
{"prompt": "Write a function to determine if there is a subset of the given set with sum equal to the given sum.", "test": ["assert is_subset_sum([3, 34, 4, 12, 5, 2], 6, 9) == True", "assert is_subset_sum([3, 34, 4, 12, 5, 2], 6, 30) == False", "assert is_subset_sum([3, 34, 4, 12, 5, 2], 6, 15) == True"], "code": "def is_subset_sum(set, n, sum):\r\n\tif (sum == 0):\r\n\t\treturn True\r\n\tif (n == 0):\r\n\t\treturn False\r\n\tif (set[n - 1] > sum):\r\n\t\treturn is_subset_sum(set, n - 1, sum)\r\n\treturn is_subset_sum(set, n-1, sum) or is_subset_sum(set, n-1, sum-set[n-1])", "task_id": 481}
{"prompt": "Write a function to find sequences of one upper case letter followed by lower case letters in the given string by using regex.", "test": ["assert match(\"Geeks\") == 'Yes'", "assert match(\"geeksforGeeks\") == 'Yes'", "assert match(\"geeks\") == 'No'"], "code": "import re \r\ndef match(text): \r\n\t\tpattern = '[A-Z]+[a-z]+$'\r\n\t\tif re.search(pattern, text): \r\n\t\t\t\treturn('Yes') \r\n\t\telse: \r\n\t\t\t\treturn('No') ", "task_id": 482}
{"prompt": "Write a python function to find the first natural number whose factorial is divisible by x.", "test": ["assert first_Factorial_Divisible_Number(10) == 5", "assert first_Factorial_Divisible_Number(15) == 5", "assert first_Factorial_Divisible_Number(5) == 4"], "code": "def first_Factorial_Divisible_Number(x): \r\n    i = 1;\r\n    fact = 1; \r\n    for i in range(1,x): \r\n        fact = fact * i \r\n        if (fact % x == 0): \r\n            break\r\n    return i ", "task_id": 483}
{"prompt": "Write a function to remove the matching tuples from the given two tuples.", "test": ["assert remove_matching_tuple([('Hello', 'dude'), ('How', 'are'), ('you', '?')], [('Hello', 'dude'), ('How', 'are')]) == [('you', '?')]", "assert remove_matching_tuple([('Part', 'of'), ('the', 'journey'), ('is ', 'end')], [('Journey', 'the'), ('is', 'end')]) == [('Part', 'of'), ('the', 'journey'), ('is ', 'end')]", "assert remove_matching_tuple([('Its', 'been'), ('a', 'long'), ('day', 'without')], [('a', 'long'), ('my', 'friend')]) == [('Its', 'been'), ('day', 'without')]"], "code": "def remove_matching_tuple(test_list1, test_list2):\r\n  res = [sub for sub in test_list1 if sub not in test_list2]\r\n  return (res) ", "task_id": 484}
{"prompt": "Write a function to find the largest palindromic number in the given array.", "test": ["assert largest_palindrome([1, 232, 54545, 999991], 4) == 54545", "assert largest_palindrome([1, 2, 3, 4, 5, 50], 6) == 5", "assert largest_palindrome([1, 3, 7, 9, 45], 5)  == 9"], "code": "def is_palindrome(n) : \r\n\tdivisor = 1\r\n\twhile (n / divisor >= 10) : \r\n\t\tdivisor *= 10\r\n\twhile (n != 0) : \r\n\t\tleading = n // divisor \r\n\t\ttrailing = n % 10\r\n\t\tif (leading != trailing) : \r\n\t\t\treturn False\r\n\t\tn = (n % divisor) // 10\r\n\t\tdivisor = divisor // 100\r\n\treturn True\r\ndef largest_palindrome(A, n) : \r\n\tA.sort() \r\n\tfor i in range(n - 1, -1, -1) : \r\n\t\tif (is_palindrome(A[i])) : \r\n\t\t\treturn A[i] \r\n\treturn -1", "task_id": 485}
{"prompt": "Write a function to compute binomial probability for the given number.", "test": ["assert binomial_probability(10, 5, 1.0/3) == 0.13656454808718185", "assert binomial_probability(11, 6, 2.0/4) == 0.2255859375", "assert binomial_probability(12, 7, 3.0/5) == 0.227030335488"], "code": "def nCr(n, r): \r\n\tif (r > n / 2): \r\n\t\tr = n - r \r\n\tanswer = 1 \r\n\tfor i in range(1, r + 1): \r\n\t\tanswer *= (n - r + i) \r\n\t\tanswer /= i \r\n\treturn answer \r\ndef binomial_probability(n, k, p): \r\n\treturn (nCr(n, k) * pow(p, k) *\tpow(1 - p, n - k)) ", "task_id": 486}
{"prompt": "Write a function to sort a list of tuples in increasing order by the last element in each tuple.", "test": ["assert sort_tuple([(1, 3), (3, 2), (2, 1)] ) == [(2, 1), (3, 2), (1, 3)]", "assert sort_tuple([(2, 4), (3, 3), (1, 1)] ) == [(1, 1), (3, 3), (2, 4)]", "assert sort_tuple([(3, 9), (6, 7), (4, 3)] ) == [(4, 3), (6, 7), (3, 9)]"], "code": "def sort_tuple(tup): \r\n\tlst = len(tup) \r\n\tfor i in range(0, lst): \r\n\t\tfor j in range(0, lst-i-1): \r\n\t\t\tif (tup[j][-1] > tup[j + 1][-1]): \r\n\t\t\t\ttemp = tup[j] \r\n\t\t\t\ttup[j]= tup[j + 1] \r\n\t\t\t\ttup[j + 1]= temp \r\n\treturn tup", "task_id": 487}
{"prompt": "Write a function to find the area of a pentagon.", "test": ["assert area_pentagon(5)==43.01193501472417", "assert area_pentagon(10)==172.0477400588967", "assert area_pentagon(15)==387.10741513251753"], "code": "import math\r\ndef area_pentagon(a):\r\n  area=(math.sqrt(5*(5+2*math.sqrt(5)))*pow(a,2))/4.0\r\n  return area", "task_id": 488}
{"prompt": "Write a python function to find the frequency of the largest value in a given array.", "test": ["assert frequency_Of_Largest(5,[1,2,3,4,4]) == 2", "assert frequency_Of_Largest(3,[5,6,5]) == 1", "assert frequency_Of_Largest(4,[2,7,7,7]) == 3"], "code": "def frequency_Of_Largest(n,arr): \r\n    mn = arr[0] \r\n    freq = 1\r\n    for i in range(1,n): \r\n        if (arr[i] >mn): \r\n            mn = arr[i] \r\n            freq = 1\r\n        elif (arr[i] == mn): \r\n            freq += 1\r\n    return freq ", "task_id": 489}
{"prompt": "Write a function to extract all the pairs which are symmetric in the given tuple list.", "test": ["assert extract_symmetric([(6, 7), (2, 3), (7, 6), (9, 8), (10, 2), (8, 9)] ) == {(8, 9), (6, 7)}", "assert extract_symmetric([(7, 8), (3, 4), (8, 7), (10, 9), (11, 3), (9, 10)] ) == {(9, 10), (7, 8)}", "assert extract_symmetric([(8, 9), (4, 5), (9, 8), (11, 10), (12, 4), (10, 11)] ) == {(8, 9), (10, 11)}"], "code": "def extract_symmetric(test_list):\r\n  temp = set(test_list) & {(b, a) for a, b in test_list}\r\n  res = {(a, b) for a, b in temp if a < b}\r\n  return (res) ", "task_id": 490}
{"prompt": "Write a function to find the sum of geometric progression series.", "test": ["assert sum_gp(1,5,2)==31", "assert sum_gp(1,5,4)==341", "assert sum_gp(2,6,3)==728"], "code": "import math\r\ndef sum_gp(a,n,r):\r\n total = (a * (1 - math.pow(r, n ))) / (1- r)\r\n return total", "task_id": 491}
{"prompt": "Write a function to search an element in the given array by using binary search.", "test": ["assert binary_search([1,2,3,5,8], 6) == False", "assert binary_search([7, 8, 9, 10, 13], 10) == True", "assert binary_search([11, 13, 14, 19, 22, 36], 23) == False"], "code": "def binary_search(item_list,item):\r\n\tfirst = 0\r\n\tlast = len(item_list)-1\r\n\tfound = False\r\n\twhile( first<=last and not found):\r\n\t\tmid = (first + last)//2\r\n\t\tif item_list[mid] == item :\r\n\t\t\tfound = True\r\n\t\telse:\r\n\t\t\tif item < item_list[mid]:\r\n\t\t\t\tlast = mid - 1\r\n\t\t\telse:\r\n\t\t\t\tfirst = mid + 1\t\r\n\treturn found", "task_id": 492}
{"prompt": "Write a function to calculate a grid of hexagon coordinates where function returns a list of lists containing 6 tuples of x, y point coordinates.", "test": ["assert calculate_polygons(1,1, 4, 4, 3)==[[(-5.0, -4.196152422706632), (-5.0, -0.7320508075688767), (-2.0, 1.0), (1.0, -0.7320508075688767), (1.0, -4.196152422706632), (-2.0, -5.928203230275509), (-5.0, -4.196152422706632)], [(1.0, -4.196152422706632), (1.0, -0.7320508075688767), (4.0, 1.0), (7.0, -0.7320508075688767), (7.0, -4.196152422706632), (4.0, -5.928203230275509), (1.0, -4.196152422706632)], [(7.0, -4.196152422706632), (7.0, -0.7320508075688767), (10.0, 1.0), (13.0, -0.7320508075688767), (13.0, -4.196152422706632), (10.0, -5.928203230275509), (7.0, -4.196152422706632)], [(-2.0, 1.0000000000000004), (-2.0, 4.464101615137755), (1.0, 6.196152422706632), (4.0, 4.464101615137755), (4.0, 1.0000000000000004), (1.0, -0.7320508075688767), (-2.0, 1.0000000000000004)], [(4.0, 1.0000000000000004), (4.0, 4.464101615137755), (7.0, 6.196152422706632), (10.0, 4.464101615137755), (10.0, 1.0000000000000004), (7.0, -0.7320508075688767), (4.0, 1.0000000000000004)], [(-5.0, 6.196152422706632), (-5.0, 9.660254037844387), (-2.0, 11.392304845413264), (1.0, 9.660254037844387), (1.0, 6.196152422706632), (-2.0, 4.464101615137755), (-5.0, 6.196152422706632)], [(1.0, 6.196152422706632), (1.0, 9.660254037844387), (4.0, 11.392304845413264), (7.0, 9.660254037844387), (7.0, 6.196152422706632), (4.0, 4.464101615137755), (1.0, 6.196152422706632)], [(7.0, 6.196152422706632), (7.0, 9.660254037844387), (10.0, 11.392304845413264), (13.0, 9.660254037844387), (13.0, 6.196152422706632), (10.0, 4.464101615137755), (7.0, 6.196152422706632)], [(-2.0, 11.392304845413264), (-2.0, 14.85640646055102), (1.0, 16.588457268119896), (4.0, 14.85640646055102), (4.0, 11.392304845413264), (1.0, 9.660254037844387), (-2.0, 11.392304845413264)], [(4.0, 11.392304845413264), (4.0, 14.85640646055102), (7.0, 16.588457268119896), (10.0, 14.85640646055102), (10.0, 11.392304845413264), (7.0, 9.660254037844387), (4.0, 11.392304845413264)]]", "assert calculate_polygons(5,4,7,9,8)==[[(-11.0, -9.856406460551018), (-11.0, -0.6188021535170058), (-3.0, 4.0), (5.0, -0.6188021535170058), (5.0, -9.856406460551018), (-3.0, -14.475208614068023), (-11.0, -9.856406460551018)], [(5.0, -9.856406460551018), (5.0, -0.6188021535170058), (13.0, 4.0), (21.0, -0.6188021535170058), (21.0, -9.856406460551018), (13.0, -14.475208614068023), (5.0, -9.856406460551018)], [(21.0, -9.856406460551018), (21.0, -0.6188021535170058), (29.0, 4.0), (37.0, -0.6188021535170058), (37.0, -9.856406460551018), (29.0, -14.475208614068023), (21.0, -9.856406460551018)], [(-3.0, 4.0), (-3.0, 13.237604307034012), (5.0, 17.856406460551018), (13.0, 13.237604307034012), (13.0, 4.0), (5.0, -0.6188021535170058), (-3.0, 4.0)], [(13.0, 4.0), (13.0, 13.237604307034012), (21.0, 17.856406460551018), (29.0, 13.237604307034012), (29.0, 4.0), (21.0, -0.6188021535170058), (13.0, 4.0)], [(-11.0, 17.856406460551018), (-11.0, 27.09401076758503), (-3.0, 31.712812921102035), (5.0, 27.09401076758503), (5.0, 17.856406460551018), (-3.0, 13.237604307034012), (-11.0, 17.856406460551018)], [(5.0, 17.856406460551018), (5.0, 27.09401076758503), (13.0, 31.712812921102035), (21.0, 27.09401076758503), (21.0, 17.856406460551018), (13.0, 13.237604307034012), (5.0, 17.856406460551018)], [(21.0, 17.856406460551018), (21.0, 27.09401076758503), (29.0, 31.712812921102035), (37.0, 27.09401076758503), (37.0, 17.856406460551018), (29.0, 13.237604307034012), (21.0, 17.856406460551018)], [(-3.0, 31.712812921102035), (-3.0, 40.95041722813605), (5.0, 45.569219381653056), (13.0, 40.95041722813605), (13.0, 31.712812921102035), (5.0, 27.09401076758503), (-3.0, 31.712812921102035)], [(13.0, 31.712812921102035), (13.0, 40.95041722813605), (21.0, 45.569219381653056), (29.0, 40.95041722813605), (29.0, 31.712812921102035), (21.0, 27.09401076758503), (13.0, 31.712812921102035)]]", "assert calculate_polygons(9,6,4,3,2)==[[(5.0, 2.5358983848622456), (5.0, 4.8452994616207485), (7.0, 6.0), (9.0, 4.8452994616207485), (9.0, 2.5358983848622456), (7.0, 1.3811978464829942), (5.0, 2.5358983848622456)], [(7.0, 6.0), (7.0, 8.309401076758503), (9.0, 9.464101615137753), (11.0, 8.309401076758503), (11.0, 6.0), (9.0, 4.8452994616207485), (7.0, 6.0)]]"], "code": "import math\r\ndef calculate_polygons(startx, starty, endx, endy, radius):\r\n    sl = (2 * radius) * math.tan(math.pi / 6)\r\n    p = sl * 0.5\r\n    b = sl * math.cos(math.radians(30))\r\n    w = b * 2\r\n    h = 2 * sl   \r\n    startx = startx - w\r\n    starty = starty - h\r\n    endx = endx + w\r\n    endy = endy + h\r\n    origx = startx\r\n    origy = starty\r\n    xoffset = b\r\n    yoffset = 3 * p\r\n    polygons = []\r\n    row = 1\r\n    counter = 0\r\n    while starty < endy:\r\n        if row % 2 == 0:\r\n            startx = origx + xoffset\r\n        else:\r\n            startx = origx\r\n        while startx < endx:\r\n            p1x = startx\r\n            p1y = starty + p\r\n            p2x = startx\r\n            p2y = starty + (3 * p)\r\n            p3x = startx + b\r\n            p3y = starty + h\r\n            p4x = startx + w\r\n            p4y = starty + (3 * p)\r\n            p5x = startx + w\r\n            p5y = starty + p\r\n            p6x = startx + b\r\n            p6y = starty\r\n            poly = [\r\n                (p1x, p1y),\r\n                (p2x, p2y),\r\n                (p3x, p3y),\r\n                (p4x, p4y),\r\n                (p5x, p5y),\r\n                (p6x, p6y),\r\n                (p1x, p1y)]\r\n            polygons.append(poly)\r\n            counter += 1\r\n            startx += w\r\n        starty += yoffset\r\n        row += 1\r\n    return polygons", "task_id": 493}
{"prompt": "Write a function to convert the given binary tuple to integer.", "test": ["assert binary_to_integer((1, 1, 0, 1, 0, 0, 1)) == '105'", "assert binary_to_integer((0, 1, 1, 0, 0, 1, 0, 1)) == '101'", "assert binary_to_integer((1, 1, 0, 1, 0, 1)) == '53'"], "code": "def binary_to_integer(test_tup):\r\n  res = int(\"\".join(str(ele) for ele in test_tup), 2)\r\n  return (str(res)) ", "task_id": 494}
{"prompt": "Write a function to remove lowercase substrings from a given string by using regex.", "test": ["assert remove_lowercase('KDeoALOklOOHserfLoAJSIskdsf') == 'KDALOOOHLAJSI'", "assert remove_lowercase('ProducTnamEstreAmIngMediAplAYer') == 'PTEAIMAAY'", "assert remove_lowercase('maNufacTuredbYSheZenTechNolOGIes') == 'NTYSZTNOGI'"], "code": "import re\r\ndef remove_lowercase(str1):\r\n  remove_lower = lambda text: re.sub('[a-z]', '', text)\r\n  result =  remove_lower(str1)\r\n  return (result)", "task_id": 495}
{"prompt": "Write a function to find the smallest integers from a given list of numbers using heap queue algorithm.", "test": ["assert heap_queue_smallest( [25, 35, 22, 85, 14, 65, 75, 25, 58],3)==[14, 22, 25] ", "assert heap_queue_smallest( [25, 35, 22, 85, 14, 65, 75, 25, 58],2)==[14, 22]", "assert heap_queue_smallest( [25, 35, 22, 85, 14, 65, 75, 22, 58],5)==[14, 22, 22, 25, 35]"], "code": "import heapq as hq\r\ndef heap_queue_smallest(nums,n):\r\n  smallest_nums = hq.nsmallest(n, nums)\r\n  return smallest_nums", "task_id": 496}
{"prompt": "Write a function to find the surface area of a cone.", "test": ["assert surfacearea_cone(5,12)==282.7433388230814", "assert surfacearea_cone(10,15)==880.5179353159282", "assert surfacearea_cone(19,17)==2655.923961165254"], "code": "import math\r\ndef surfacearea_cone(r,h):\r\n  l = math.sqrt(r * r + h * h)\r\n  SA = math.pi * r * (r + l)\r\n  return SA", "task_id": 497}
{"prompt": "Write a python function to find gcd of two positive integers.", "test": ["assert gcd(12, 17) == 1", "assert gcd(4,6) == 2", "assert gcd(2,9) == 1"], "code": "def gcd(x, y):\r\n    gcd = 1\r\n    if x % y == 0:\r\n        return y\r\n    for k in range(int(y / 2), 0, -1):\r\n        if x % k == 0 and y % k == 0:\r\n            gcd = k\r\n            break  \r\n    return gcd", "task_id": 498}
{"prompt": "Write a function to find the diameter of a circle.", "test": ["assert diameter_circle(10)==20", "assert diameter_circle(40)==80", "assert diameter_circle(15)==30"], "code": "def diameter_circle(r):\r\n  diameter=2*r\r\n  return diameter", "task_id": 499}
{"prompt": "Write a function to concatenate all elements of the given list into a string.", "test": ["assert concatenate_elements(['hello','there','have','a','rocky','day'] ) == '  hello there have a rocky day'", "assert concatenate_elements([ 'Hi', 'there', 'How','are', 'you'] ) == '  Hi there How are you'", "assert concatenate_elements([ 'Part', 'of', 'the','journey', 'is', 'end'] ) == '  Part of the journey is end'"], "code": "def concatenate_elements(list):\r\n  ans = ' '\r\n  for i in list:\r\n    ans = ans+ ' '+i\r\n  return (ans) ", "task_id": 500}
{"prompt": "Write a python function to find common divisor between two numbers in a given pair.", "test": ["assert num_comm_div(2,4) == 2", "assert num_comm_div(2,8) == 2", "assert num_comm_div(12,24) == 6"], "code": "def ngcd(x,y):\r\n    i=1\r\n    while(i<=x and i<=y):\r\n        if(x%i==0 and y%i == 0):\r\n            gcd=i;\r\n        i+=1\r\n    return gcd;\r\ndef num_comm_div(x,y):\r\n  n = ngcd(x,y)\r\n  result = 0\r\n  z = int(n**0.5)\r\n  i = 1\r\n  while(i <= z):\r\n    if(n % i == 0):\r\n      result += 2 \r\n      if(i == n/i):\r\n        result-=1\r\n    i+=1\r\n  return result", "task_id": 501}
{"prompt": "Write a python function to find remainder of two numbers.", "test": ["assert find(3,3) == 0", "assert find(10,3) == 1", "assert find(16,5) == 1"], "code": "def find(n,m):\r\n  r = n%m\r\n  return (r)", "task_id": 502}
{"prompt": "Write a function to add consecutive numbers of a given list.", "test": ["assert add_consecutive_nums([1, 1, 3, 4, 4, 5, 6, 7])==[2, 4, 7, 8, 9, 11, 13]", "assert add_consecutive_nums([4, 5, 8, 9, 6, 10])==[9, 13, 17, 15, 16]", "assert add_consecutive_nums([1, 2, 3, 4, 5, 6, 7, 8, 9, 10])==[3, 5, 7, 9, 11, 13, 15, 17, 19]"], "code": "def add_consecutive_nums(nums):\r\n    result = [b+a for a, b in zip(nums[:-1], nums[1:])]\r\n    return result", "task_id": 503}
{"prompt": "Write a python function to find the cube sum of first n natural numbers.", "test": ["assert sum_Of_Series(5) == 225", "assert sum_Of_Series(2) == 9", "assert sum_Of_Series(3) == 36"], "code": "def sum_Of_Series(n): \r\n    sum = 0\r\n    for i in range(1,n + 1): \r\n        sum += i * i*i       \r\n    return sum", "task_id": 504}
{"prompt": "Write a function to move all zeroes to the end of the given array.", "test": ["assert re_order([6, 0, 8, 2, 3, 0, 4, 0, 1]) == [6, 8, 2, 3, 4, 1, 0, 0, 0]", "assert re_order([4, 0, 2, 7, 0, 9, 0, 12, 0]) == [4, 2, 7, 9, 12, 0, 0, 0, 0]", "assert re_order([3, 11, 0, 74, 14, 0, 1, 0, 2]) == [3, 11, 74, 14, 1, 2, 0, 0, 0]"], "code": "def re_order(A):\r\n    k = 0\r\n    for i in A:\r\n        if i:\r\n            A[k] = i\r\n            k = k + 1\r\n    for i in range(k, len(A)):\r\n        A[i] = 0\r\n    return A", "task_id": 505}
{"prompt": "Write a function to calculate the permutation coefficient of given p(n, k).", "test": ["assert permutation_coefficient(10, 2) == 90", "assert permutation_coefficient(10, 3) == 720", "assert permutation_coefficient(10, 1) == 10"], "code": "def permutation_coefficient(n, k): \r\n\tP = [[0 for i in range(k + 1)] \r\n\t\t\tfor j in range(n + 1)] \r\n\tfor i in range(n + 1): \r\n\t\tfor j in range(min(i, k) + 1): \r\n\t\t\tif (j == 0): \r\n\t\t\t\tP[i][j] = 1\r\n\t\t\telse: \r\n\t\t\t\tP[i][j] = P[i - 1][j] + ( \r\n\t\t\t\t\t\tj * P[i - 1][j - 1]) \r\n\t\t\tif (j < k): \r\n\t\t\t\tP[i][j + 1] = 0\r\n\treturn P[n][k] ", "task_id": 506}
{"prompt": "Write a function to remove specific words from a given list.", "test": ["assert remove_words(['red', 'green', 'blue', 'white', 'black', 'orange'],['white', 'orange'])==['red', 'green', 'blue', 'black']", "assert remove_words(['red', 'green', 'blue', 'white', 'black', 'orange'],['black', 'orange'])==['red', 'green', 'blue', 'white']", "assert remove_words(['red', 'green', 'blue', 'white', 'black', 'orange'],['blue', 'white'])==['red', 'green', 'black', 'orange']"], "code": "def remove_words(list1, removewords):\r\n    for word in list(list1):\r\n        if word in removewords:\r\n            list1.remove(word)\r\n    return list1  ", "task_id": 507}
{"prompt": "Write a function to check if the common elements between two given lists are in the same order or not.", "test": ["assert same_order([\"red\",\"green\",\"black\",\"orange\"],[\"red\",\"pink\",\"green\",\"white\",\"black\"])==True", "assert same_order([\"red\",\"pink\",\"green\",\"white\",\"black\"],[\"white\",\"orange\",\"pink\",\"black\"])==False", "assert same_order([\"red\",\"green\",\"black\",\"orange\"],[\"red\",\"pink\",\"green\",\"white\",\"black\"])==True"], "code": "def same_order(l1, l2):\r\n    common_elements = set(l1) & set(l2)\r\n    l1 = [e for e in l1 if e in common_elements]\r\n    l2 = [e for e in l2 if e in common_elements]\r\n    return l1 == l2", "task_id": 508}
{"prompt": "Write a python function to find the average of odd numbers till a given odd number.", "test": ["assert average_Odd(9) == 5", "assert average_Odd(5) == 3", "assert average_Odd(11) == 6"], "code": "def average_Odd(n) : \r\n    if (n%2==0) : \r\n        return (\"Invalid Input\") \r\n        return -1 \r\n    sm =0\r\n    count =0\r\n    while (n>=1) : \r\n        count=count+1\r\n        sm = sm + n \r\n        n = n-2\r\n    return sm//count ", "task_id": 509}
{"prompt": "Write a function to find the number of subsequences having product smaller than k for the given non negative array.", "test": ["assert no_of_subsequences([1,2,3,4], 10) == 11", "assert no_of_subsequences([4,8,7,2], 50) == 9", "assert no_of_subsequences([5,6,7,8], 15) == 4"], "code": "def no_of_subsequences(arr, k): \r\n\tn = len(arr) \r\n\tdp = [[0 for i in range(n + 1)] \r\n\t\t\tfor j in range(k + 1)] \r\n\tfor i in range(1, k + 1): \r\n\t\tfor j in range(1, n + 1): \r\n\t\t\tdp[i][j] = dp[i][j - 1] \r\n\t\t\tif arr[j - 1] <= i and arr[j - 1] > 0: \r\n\t\t\t\tdp[i][j] += dp[i // arr[j - 1]][j - 1] + 1\r\n\treturn dp[k][n]", "task_id": 510}
