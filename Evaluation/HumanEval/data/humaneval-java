{"name": "HumanEval_23_strlen", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return length of given string\n    // >>> stringLength((\"\"))\n    // (0l)\n    // >>> stringLength((\"abc\"))\n    // (3l)\n    public static long strlen(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(strlen((\"\")) == (0l));\n    assert(strlen((\"x\")) == (1l));\n    assert(strlen((\"asdasnakj\")) == (9l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_23_strlen"}
{"name": "HumanEval_89_encrypt", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function encrypt that takes a string as an argument and\n    // returns a string encrypted with the alphabet being rotated. \n    // The alphabet should be rotated in a manner such that the letters \n    // shift down by two multiplied to two places.\n    // For example:\n    // >>> encrypt((\"hi\"))\n    // (\"lm\")\n    // >>> encrypt((\"asdfghjkl\"))\n    // (\"ewhjklnop\")\n    // >>> encrypt((\"gf\"))\n    // (\"kj\")\n    // >>> encrypt((\"et\"))\n    // (\"ix\")\n    public static String encrypt(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(encrypt((\"hi\")).equals((\"lm\")));\n    assert(encrypt((\"asdfghjkl\")).equals((\"ewhjklnop\")));\n    assert(encrypt((\"gf\")).equals((\"kj\")));\n    assert(encrypt((\"et\")).equals((\"ix\")));\n    assert(encrypt((\"faewfawefaewg\")).equals((\"jeiajeaijeiak\")));\n    assert(encrypt((\"hellomyfriend\")).equals((\"lippsqcjvmirh\")));\n    assert(encrypt((\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\")).equals((\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\")));\n    assert(encrypt((\"a\")).equals((\"e\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_89_encrypt"}
{"name": "HumanEval_95_check_dict_case", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a hash map, return true if all keys are strings in lower \n    // case or all keys are strings in upper case, else return false.\n    // The function should return false is the given hash map is empty.\n    // Examples:\n    // >>> checkDictCase((new HashMap<String,String>(Map.of(\"a\", \"apple\", \"b\", \"banana\"))))\n    // (true)\n    // >>> checkDictCase((new HashMap<String,String>(Map.of(\"a\", \"apple\", \"A\", \"banana\", \"B\", \"banana\"))))\n    // (false)\n    // >>> checkDictCase((new HashMap<String,String>(Map.of(\"a\", \"apple\", 8l, \"banana\", \"a\", \"apple\"))))\n    // (false)\n    // >>> checkDictCase((new HashMap<String,String>(Map.of(\"Name\", \"John\", \"Age\", \"36\", \"City\", \"Houston\"))))\n    // (false)\n    // >>> checkDictCase((new HashMap<String,String>(Map.of(\"STATE\", \"NC\", \"ZIP\", \"12345\"))))\n    // (true)\n    public static boolean checkDictCase(HashMap<String,String> dict) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"p\", \"pineapple\", \"b\", \"banana\")))) == (true));\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"p\", \"pineapple\", \"A\", \"banana\", \"B\", \"banana\")))) == (false));\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"p\", \"pineapple\", \"5\", \"banana\", \"a\", \"apple\")))) == (false));\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"Name\", \"John\", \"Age\", \"36\", \"City\", \"Houston\")))) == (false));\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"STATE\", \"NC\", \"ZIP\", \"12345\")))) == (true));\n    assert(checkDictCase((new HashMap<String,String>(Map.of(\"fruit\", \"Orange\", \"taste\", \"Sweet\")))) == (true));\n    assert(checkDictCase((new HashMap<String,String>())) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_95_check_dict_case"}
{"name": "HumanEval_85_add", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a non-empty array list of integers lst. add the even elements that are at odd indices..\n    // Examples:\n    // >>> add((new ArrayList<Long>(Arrays.asList((long)4l, (long)2l, (long)6l, (long)7l))))\n    // (2l)\n    public static long add(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(add((new ArrayList<Long>(Arrays.asList((long)4l, (long)88l)))) == (88l));\n    assert(add((new ArrayList<Long>(Arrays.asList((long)4l, (long)5l, (long)6l, (long)7l, (long)2l, (long)122l)))) == (122l));\n    assert(add((new ArrayList<Long>(Arrays.asList((long)4l, (long)0l, (long)6l, (long)7l)))) == (0l));\n    assert(add((new ArrayList<Long>(Arrays.asList((long)4l, (long)4l, (long)6l, (long)8l)))) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_85_add"}
{"name": "HumanEval_140_fix_spaces", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string text, replace all spaces in it with underscores, \n    // and if a string has more than 2 consecutive spaces, \n    // then replace all consecutive spaces with - \n    // >>> fixSpaces((\" Example\"))\n    // (\"Example\")\n    // >>> fixSpaces((\" Example 1\"))\n    // (\"Example_1\")\n    // >>> fixSpaces((\" Example 2\"))\n    // (\"_Example_2\")\n    // >>> fixSpaces((\" Example 3\"))\n    // (\"_Example-3\")\n    public static String fixSpaces(String text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fixSpaces((\"Example\")).equals((\"Example\")));\n    assert(fixSpaces((\"Mudasir Hanif \")).equals((\"Mudasir_Hanif_\")));\n    assert(fixSpaces((\"Yellow Yellow  Dirty  Fellow\")).equals((\"Yellow_Yellow__Dirty__Fellow\")));\n    assert(fixSpaces((\"Exa   mple\")).equals((\"Exa-mple\")));\n    assert(fixSpaces((\"   Exa 1 2 2 mple\")).equals((\"-Exa_1_2_2_mple\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_140_fix_spaces"}
{"name": "HumanEval_63_fibfib", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    // fibfib(0) == 0\n    // fibfib(1) == 0\n    // fibfib(2) == 1\n    // fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n    // Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n    // >>> fibfib((1l))\n    // (0l)\n    // >>> fibfib((5l))\n    // (4l)\n    // >>> fibfib((8l))\n    // (24l)\n    public static long fibfib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fibfib((2l)) == (1l));\n    assert(fibfib((1l)) == (0l));\n    assert(fibfib((5l)) == (4l));\n    assert(fibfib((8l)) == (24l));\n    assert(fibfib((10l)) == (81l));\n    assert(fibfib((12l)) == (274l));\n    assert(fibfib((14l)) == (927l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_63_fibfib"}
{"name": "HumanEval_151_double_the_difference", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of numbers, return the sum of squares of the numbers\n    // in the array list that are odd. Ignore numbers that are negative or not integers.\n    // >>> doubleTheDifference((new ArrayList<Float>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)0l))))\n    // (10l)\n    // >>> doubleTheDifference((new ArrayList<Float>(Arrays.asList((long)-1l, (long)-2l, (long)0l))))\n    // (0l)\n    // >>> doubleTheDifference((new ArrayList<Float>(Arrays.asList((long)9l, (long)-2l))))\n    // (81l)\n    // >>> doubleTheDifference((new ArrayList<Float>(Arrays.asList((long)0l))))\n    // (0l)\n    // If the input array list is empty, return 0.\n    public static long doubleTheDifference(ArrayList<Float> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList()))) == (0l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)5.0f, (float)4.0f)))) == (25l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)0.1f, (float)0.2f, (float)0.3f)))) == (0l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)-10.0f, (float)-20.0f, (float)-30.0f)))) == (0l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)-1.0f, (float)-2.0f, (float)8.0f)))) == (0l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)0.2f, (float)3.0f, (float)5.0f)))) == (34l));\n    assert(doubleTheDifference((new ArrayList<Float>(Arrays.asList((float)-9.0f, (float)-7.0f, (float)-5.0f, (float)-3.0f, (float)-1.0f, (float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f, (float)9.0f)))) == (165l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_151_double_the_difference"}
{"name": "HumanEval_22_filter_integers", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Filter given array list of any javathon values only for integers\n    // >>> filterIntegers((new ArrayList<Object>(Arrays.asList((String)\"a\", (String)3.14f, (String)5l))))\n    // (new ArrayList<Long>(Arrays.asList((long)5l)))\n    // >>> filterIntegers((new ArrayList<Object>(Arrays.asList(1l, 2l, 3l, \"abc\", new HashMap<Long,Long>(Map.of()), new ArrayList<Long>(Arrays.asList())))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))\n    public static ArrayList<Long> filterIntegers(ArrayList<Object> values) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(filterIntegers((new ArrayList<Object>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(filterIntegers((new ArrayList<Object>(Arrays.asList(4l, new HashMap<Long,Long>(Map.of()), new ArrayList<Long>(Arrays.asList()), 23.2f, 9l, \"adasd\")))).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)9l)))));\n    assert(filterIntegers((new ArrayList<Object>(Arrays.asList(3l, \"c\", 3l, 3l, \"a\", \"b\")))).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)3l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_22_filter_integers"}
{"name": "HumanEval_41_car_race_collision", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Imagine a road that's a perfectly straight infinitely long line.\n    // n cars are driving left to right;  simultaneously, a different set of n cars\n    // are driving right to left.   The two sets of cars start out being very far from\n    // each other.  All cars move in the same speed.  Two cars are said to collide\n    // when a car that's moving left to right hits a car that's moving right to left.\n    // However, the cars are infinitely sturdy and strong; as a result, they continue moving\n    // in their trajectory as if they did not collide.\n    // This function outputs the number of such collisions.\n    public static long carRaceCollision(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(carRaceCollision((2l)) == (4l));\n    assert(carRaceCollision((3l)) == (9l));\n    assert(carRaceCollision((4l)) == (16l));\n    assert(carRaceCollision((8l)) == (64l));\n    assert(carRaceCollision((10l)) == (100l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_41_car_race_collision"}
{"name": "HumanEval_17_parse_music", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Input to this function is a string representing musical notes in a special ASCII format.\n    // Your task is to parse this string and return array list of integers corresponding to how many beats does each\n    // not last.\n    // Here is a legend:\n    // 'o' - whole note, lasts four beats\n    // 'o|' - half note, lasts two beats\n    // '.|' - quater note, lasts one beat\n    // >>> parseMusic((\"o o| .| o| o| .| .| .| .| o o\"))\n    // (new ArrayList<Long>(Arrays.asList((long)4l, (long)2l, (long)1l, (long)2l, (long)2l, (long)1l, (long)1l, (long)1l, (long)1l, (long)4l, (long)4l)))\n    public static ArrayList<Long> parseMusic(String music_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(parseMusic((\"\")).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(parseMusic((\"o o o o\")).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)4l, (long)4l, (long)4l)))));\n    assert(parseMusic((\".| .| .| .|\")).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l)))));\n    assert(parseMusic((\"o| o| .| .| o o o o\")).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)1l, (long)1l, (long)4l, (long)4l, (long)4l, (long)4l)))));\n    assert(parseMusic((\"o| .| o| .| o o| o o|\")).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l, (long)2l, (long)1l, (long)4l, (long)2l, (long)4l, (long)2l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_17_parse_music"}
{"name": "HumanEval_79_decimal_to_binary", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You will be given a number in decimal form and your task is to convert it to\n    // binary format. The function should return a string, with each character representing a binary\n    // number. Each character in the string will be '0' or '1'.\n    // There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n    // The extra characters are there to help with the format.\n    // Examples:\n    // >>> decimalToBinary((15l))\n    // (\"db1111db\")\n    // >>> decimalToBinary((32l))\n    // (\"db100000db\")\n    public static String decimalToBinary(long decimal) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(decimalToBinary((0l)).equals((\"db0db\")));\n    assert(decimalToBinary((32l)).equals((\"db100000db\")));\n    assert(decimalToBinary((103l)).equals((\"db1100111db\")));\n    assert(decimalToBinary((15l)).equals((\"db1111db\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_79_decimal_to_binary"}
{"name": "HumanEval_14_all_prefixes", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return array list of all prefixes from shortest to longest of the input string\n    // >>> allPrefixes((\"abc\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"ab\", (String)\"abc\")))\n    public static ArrayList<String> allPrefixes(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(allPrefixes((\"\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(allPrefixes((\"asdfgh\")).equals((new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"as\", (String)\"asd\", (String)\"asdf\", (String)\"asdfg\", (String)\"asdfgh\")))));\n    assert(allPrefixes((\"WWW\")).equals((new ArrayList<String>(Arrays.asList((String)\"W\", (String)\"WW\", (String)\"WWW\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_14_all_prefixes"}
{"name": "HumanEval_53_add", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Add two numbers x and y\n    // >>> add((2l), (3l))\n    // (5l)\n    // >>> add((5l), (7l))\n    // (12l)\n    public static long add(long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(add((0l), (1l)) == (1l));\n    assert(add((1l), (0l)) == (1l));\n    assert(add((2l), (3l)) == (5l));\n    assert(add((5l), (7l)) == (12l));\n    assert(add((7l), (5l)) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_53_add"}
{"name": "HumanEval_159_eat", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You're a hungry rabbit, and you already have eaten a certain number of carrots,\n    // but now you need to eat more carrots to complete the day's meals.\n    // you should return an array array list of [ total number of eaten carrots after your meals,\n    // the number of carrots left after your meals ]\n    // if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n    // Example:\n    // >>> eat((5l), (6l), (10l))\n    // (new ArrayList<Long>(Arrays.asList((long)11l, (long)4l)))\n    // >>> eat((4l), (8l), (9l))\n    // (new ArrayList<Long>(Arrays.asList((long)12l, (long)1l)))\n    // >>> eat((1l), (10l), (10l))\n    // (new ArrayList<Long>(Arrays.asList((long)11l, (long)0l)))\n    // >>> eat((2l), (11l), (5l))\n    // (new ArrayList<Long>(Arrays.asList((long)7l, (long)0l)))\n    // Variables:\n    // @number : integer\n    // the number of carrots that you have eaten.\n    // @need : integer\n    // the number of carrots that you need to eat.\n    // @remaining : integer\n    // the number of remaining carrots thet exist in stock\n    // Constrain:\n    // * 0 <= number <= 1000\n    // * 0 <= need <= 1000\n    // * 0 <= remaining <= 1000\n    // Have fun :)\n    public static ArrayList<Long> eat(long number, long need, long remaining) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(eat((5l), (6l), (10l)).equals((new ArrayList<Long>(Arrays.asList((long)11l, (long)4l)))));\n    assert(eat((4l), (8l), (9l)).equals((new ArrayList<Long>(Arrays.asList((long)12l, (long)1l)))));\n    assert(eat((1l), (10l), (10l)).equals((new ArrayList<Long>(Arrays.asList((long)11l, (long)0l)))));\n    assert(eat((2l), (11l), (5l)).equals((new ArrayList<Long>(Arrays.asList((long)7l, (long)0l)))));\n    assert(eat((4l), (5l), (7l)).equals((new ArrayList<Long>(Arrays.asList((long)9l, (long)2l)))));\n    assert(eat((4l), (5l), (1l)).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)0l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_159_eat"}
{"name": "HumanEval_115_max_fill", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a rectangular grid of wells. Each row represents a single well,\n    // and each 1 in a row represents a single unit of water.\n    // Each well has a corresponding bucket that can be used to extract water from it, \n    // and all buckets have the same capacity.\n    // Your task is to use the buckets to empty the wells.\n    // Output the number of times you need to lower the buckets.\n    // Example 1:\n    // >>> maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)1l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l))))), (1l))\n    // (6l)\n    // Example 2:\n    // >>> maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)1l, (long)1l))))), (2l))\n    // (5l)\n    // Example 3:\n    // >>> maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l))))), (5l))\n    // (0l)\n    // Constraints:\n    // * all wells have the same length\n    // * 1 <= grid.length <= 10^2\n    // * 1 <= grid[:,1].length <= 10^2\n    // * grid[i][j] -> 0 | 1\n    // * 1 <= capacity <= 10\n    public static long maxFill(ArrayList<ArrayList<Long>> grid, long capacity) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)1l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l))))), (1l)) == (6l));\n    assert(maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)1l, (long)1l))))), (2l)) == (5l));\n    assert(maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l))))), (5l)) == (0l));\n    assert(maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l))))), (2l)) == (4l));\n    assert(maxFill((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l))))), (9l)) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_115_max_fill"}
{"name": "HumanEval_160_do_algebra", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given two array lists operator, and operand. The first array list has basic algebra operations, and \n    // the second array list is an array array list of integers. Use the two given array lists to build the algebric \n    // expression and return the evaluation of this expression.\n    // The basic algebra operations:\n    // Addition ( + ) \n    // Subtraction ( - ) \n    // Multiplication ( * ) \n    // Floor division ( // ) \n    // Exponentiation ( ** ) \n    // Example:\n    // operator['+', '*', '-']\n    // array array list = [2, 3, 4, 5]\n    // result = 2 + 3 * 4 - 5\n    // => result = 9\n    // Note:\n    // The length of operator array list is equal to the length of operand array list minus one.\n    // Operand is an array array list of of non-negative integers.\n    // Operator array list has at least one operator, and operand array list has at least two operands.\n    public static long doAlgebra(ArrayList<String> op, ArrayList<Long> operand) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(doAlgebra((new ArrayList<String>(Arrays.asList((String)\"**\", (String)\"*\", (String)\"+\"))), (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l, (long)5l)))) == (37l));\n    assert(doAlgebra((new ArrayList<String>(Arrays.asList((String)\"+\", (String)\"*\", (String)\"-\"))), (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l, (long)5l)))) == (9l));\n    assert(doAlgebra((new ArrayList<String>(Arrays.asList((String)\"//\", (String)\"*\"))), (new ArrayList<Long>(Arrays.asList((long)7l, (long)3l, (long)4l)))) == (8l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_160_do_algebra"}
{"name": "HumanEval_27_flip_case", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n    // >>> flipCase((\"Hello\"))\n    // (\"hELLO\")\n    public static String flipCase(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(flipCase((\"\")).equals((\"\")));\n    assert(flipCase((\"Hello!\")).equals((\"hELLO!\")));\n    assert(flipCase((\"These violent delights have violent ends\")).equals((\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_27_flip_case"}
{"name": "HumanEval_105_by_length", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of integers, sort the integers that are between 1 and 9 inclusive,\n    // reverse the resulting array array list, and then replace each digit by its corresponding name from\n    // \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n    // For example:\n    // >>> byLength((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l, (long)1l, (long)4l, (long)5l, (long)8l, (long)2l, (long)3l))))\n    // (new ArrayList<String>(Arrays.asList((String)\"Eight\", (String)\"Five\", (String)\"Four\", (String)\"Three\", (String)\"Two\", (String)\"Two\", (String)\"One\", (String)\"One\")))\n    // If the array array list is empty, return an empty array array list:\n    // >>> byLength((new ArrayList<Long>(Arrays.asList())))\n    // (new ArrayList<String>(Arrays.asList()))\n    // If the array array list has any strange number ignore it:\n    // >>> byLength((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l, (long)55l))))\n    // (new ArrayList<String>(Arrays.asList((String)\"One\")))\n    public static ArrayList<String> byLength(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(byLength((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l, (long)1l, (long)4l, (long)5l, (long)8l, (long)2l, (long)3l)))).equals((new ArrayList<String>(Arrays.asList((String)\"Eight\", (String)\"Five\", (String)\"Four\", (String)\"Three\", (String)\"Two\", (String)\"Two\", (String)\"One\", (String)\"One\")))));\n    assert(byLength((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(byLength((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l, (long)55l)))).equals((new ArrayList<String>(Arrays.asList((String)\"One\")))));\n    assert(byLength((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l, (long)3l, (long)2l)))).equals((new ArrayList<String>(Arrays.asList((String)\"Three\", (String)\"Two\", (String)\"One\")))));\n    assert(byLength((new ArrayList<Long>(Arrays.asList((long)9l, (long)4l, (long)8l)))).equals((new ArrayList<String>(Arrays.asList((String)\"Nine\", (String)\"Eight\", (String)\"Four\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_105_by_length"}
{"name": "HumanEval_25_factorize", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return array list of prime factors of given integer in the order from smallest to largest.\n    // Each of the factors should be array listed number of times corresponding to how many times it appeares in factorization.\n    // Input number should be equal to the product of all factors\n    // >>> factorize((8l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)2l)))\n    // >>> factorize((25l))\n    // (new ArrayList<Long>(Arrays.asList((long)5l, (long)5l)))\n    // >>> factorize((70l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)5l, (long)7l)))\n    public static ArrayList<Long> factorize(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(factorize((2l)).equals((new ArrayList<Long>(Arrays.asList((long)2l)))));\n    assert(factorize((4l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l)))));\n    assert(factorize((8l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)2l)))));\n    assert(factorize((57l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)19l)))));\n    assert(factorize((3249l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)19l, (long)19l)))));\n    assert(factorize((185193l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)3l, (long)19l, (long)19l, (long)19l)))));\n    assert(factorize((20577l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)19l, (long)19l, (long)19l)))));\n    assert(factorize((18l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)3l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_25_factorize"}
{"name": "HumanEval_96_count_up_to", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Implement a function that takes an non-negative integer and returns an array array list of the first n\n    // integers that are prime numbers and less than n.\n    // for example:\n    // >>> countUpTo((5l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l)))\n    // >>> countUpTo((11l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l)))\n    // >>> countUpTo((0l))\n    // (new ArrayList<Long>(Arrays.asList()))\n    // >>> countUpTo((20l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l, (long)19l)))\n    // >>> countUpTo((1l))\n    // (new ArrayList<Long>(Arrays.asList()))\n    // >>> countUpTo((18l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l)))\n    public static ArrayList<Long> countUpTo(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(countUpTo((5l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l)))));\n    assert(countUpTo((6l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l)))));\n    assert(countUpTo((7l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l)))));\n    assert(countUpTo((10l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l)))));\n    assert(countUpTo((0l)).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(countUpTo((22l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l, (long)19l)))));\n    assert(countUpTo((1l)).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(countUpTo((18l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l)))));\n    assert(countUpTo((47l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l, (long)19l, (long)23l, (long)29l, (long)31l, (long)37l, (long)41l, (long)43l)))));\n    assert(countUpTo((101l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l, (long)19l, (long)23l, (long)29l, (long)31l, (long)37l, (long)41l, (long)43l, (long)47l, (long)53l, (long)59l, (long)61l, (long)67l, (long)71l, (long)73l, (long)79l, (long)83l, (long)89l, (long)97l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_96_count_up_to"}
{"name": "HumanEval_34_unique", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return sorted unique elements in an array array list\n    // >>> unique((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)5l, (long)2l, (long)3l, (long)3l, (long)9l, (long)0l, (long)123l))))\n    // (new ArrayList<Long>(Arrays.asList((long)0l, (long)2l, (long)3l, (long)5l, (long)9l, (long)123l)))\n    public static ArrayList<Long> unique(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(unique((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)5l, (long)2l, (long)3l, (long)3l, (long)9l, (long)0l, (long)123l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)2l, (long)3l, (long)5l, (long)9l, (long)123l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_34_unique"}
{"name": "HumanEval_74_total_match", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that accepts two array lists of strings and returns the array list that has \n    // total number of chars in the all strings of the array list less than the other array list.\n    // if the two array lists have the same number of chars, return the first array list.\n    // Examples\n    // >>> totalMatch((new ArrayList<String>(Arrays.asList())), (new ArrayList<String>(Arrays.asList())))\n    // (new ArrayList<String>(Arrays.asList()))\n    // >>> totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"Hi\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"Hi\")))\n    // >>> totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"hi\", (String)\"admin\", (String)\"project\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\")))\n    // >>> totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"hi\", (String)\"hi\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"hi\", (String)\"hi\")))\n    // >>> totalMatch((new ArrayList<String>(Arrays.asList((String)\"4\"))), (new ArrayList<String>(Arrays.asList((String)\"1\", (String)\"2\", (String)\"3\", (String)\"4\", (String)\"5\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"4\")))\n    public static ArrayList<String> totalMatch(ArrayList<String> lst1, ArrayList<String> lst2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(totalMatch((new ArrayList<String>(Arrays.asList())), (new ArrayList<String>(Arrays.asList()))).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"hi\")))).equals((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"hi\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"hi\", (String)\"admin\", (String)\"project\")))).equals((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"4\"))), (new ArrayList<String>(Arrays.asList((String)\"1\", (String)\"2\", (String)\"3\", (String)\"4\", (String)\"5\")))).equals((new ArrayList<String>(Arrays.asList((String)\"4\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"Hi\")))).equals((new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"Hi\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"hi\", (String)\"hi\")))).equals((new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"hi\", (String)\"hi\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\"))), (new ArrayList<String>(Arrays.asList((String)\"hI\", (String)\"hi\", (String)\"hii\")))).equals((new ArrayList<String>(Arrays.asList((String)\"hi\", (String)\"admin\")))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList())), (new ArrayList<String>(Arrays.asList((String)\"this\")))).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(totalMatch((new ArrayList<String>(Arrays.asList((String)\"this\"))), (new ArrayList<String>(Arrays.asList()))).equals((new ArrayList<String>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_74_total_match"}
{"name": "HumanEval_35_max_element", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return maximum element in the array list.\n    // >>> maxElement((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (3l)\n    // >>> maxElement((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)-5l, (long)2l, (long)-3l, (long)3l, (long)9l, (long)0l, (long)123l, (long)1l, (long)-10l))))\n    // (123l)\n    public static long maxElement(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(maxElement((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))) == (3l));\n    assert(maxElement((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)-5l, (long)2l, (long)-3l, (long)3l, (long)9l, (long)0l, (long)124l, (long)1l, (long)-10l)))) == (124l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_35_max_element"}
{"name": "HumanEval_132_is_nested", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function that takes a string as input which contains only square brackets.\n    // The function should return true if and only if there is a valid subsequence of brackets \n    // where at least one bracket in the subsequence is nested.\n    // >>> isNested((\"[[]]\"))\n    // (true)\n    // >>> isNested((\"[]]]]]]][[[[[]\"))\n    // (false)\n    // >>> isNested((\"[][]\"))\n    // (false)\n    // >>> isNested((\"[]\"))\n    // (false)\n    // >>> isNested((\"[[][]]\"))\n    // (true)\n    // >>> isNested((\"[[]][[\"))\n    // (true)\n    public static boolean isNested(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isNested((\"[[]]\")) == (true));\n    assert(isNested((\"[]]]]]]][[[[[]\")) == (false));\n    assert(isNested((\"[][]\")) == (false));\n    assert(isNested((\"[]\")) == (false));\n    assert(isNested((\"[[[[]]]]\")) == (true));\n    assert(isNested((\"[]]]]]]]]]]\")) == (false));\n    assert(isNested((\"[][][[]]\")) == (true));\n    assert(isNested((\"[[]\")) == (false));\n    assert(isNested((\"[]]\")) == (false));\n    assert(isNested((\"[[]][[\")) == (true));\n    assert(isNested((\"[[][]]\")) == (true));\n    assert(isNested((\"\")) == (false));\n    assert(isNested((\"[[[[[[[[\")) == (false));\n    assert(isNested((\"]]]]]]]]\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_132_is_nested"}
{"name": "HumanEval_113_odd_count", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of strings, where each string consists of only digits, return an array array list.\n    // Each element i of the output should be \"the number of odd elements in the\n    // string i of the input.\" where all the i's should be replaced by the number\n    // of odd digits in the i'th string of the input.\n    // >>> oddCount((new ArrayList<String>(Arrays.asList((String)\"1234567\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"the number of odd elements 4n the str4ng 4 of the 4nput.\")))\n    // >>> oddCount((new ArrayList<String>(Arrays.asList((String)\"3\", (String)\"11111111\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"the number of odd elements 1n the str1ng 1 of the 1nput.\", (String)\"the number of odd elements 8n the str8ng 8 of the 8nput.\")))\n    public static ArrayList<String> oddCount(ArrayList<String> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(oddCount((new ArrayList<String>(Arrays.asList((String)\"1234567\")))).equals((new ArrayList<String>(Arrays.asList((String)\"the number of odd elements 4n the str4ng 4 of the 4nput.\")))));\n    assert(oddCount((new ArrayList<String>(Arrays.asList((String)\"3\", (String)\"11111111\")))).equals((new ArrayList<String>(Arrays.asList((String)\"the number of odd elements 1n the str1ng 1 of the 1nput.\", (String)\"the number of odd elements 8n the str8ng 8 of the 8nput.\")))));\n    assert(oddCount((new ArrayList<String>(Arrays.asList((String)\"271\", (String)\"137\", (String)\"314\")))).equals((new ArrayList<String>(Arrays.asList((String)\"the number of odd elements 2n the str2ng 2 of the 2nput.\", (String)\"the number of odd elements 3n the str3ng 3 of the 3nput.\", (String)\"the number of odd elements 2n the str2ng 2 of the 2nput.\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_113_odd_count"}
{"name": "HumanEval_109_move_one_ball", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // We have an array array list 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n    // numbers in the array array list will be randomly ordered. Your task is to determine if\n    // it is possible to get an array array list sorted in non-decreasing order by performing \n    // the following operation on the given array array list:\n    // You are allowed to perform right shift operation any number of times.\n    // One right shift operation means shifting all elements of the array array list by one\n    // position in the right direction. The last element of the array array list will be moved to\n    // the starting position in the array array list i.e. 0th index. \n    // If it is possible to obtain the sorted array array list by performing the above operation\n    // then return true else return false.\n    // If the given array array list is empty then return true.\n    // Note: The given array list is guaranteed to have unique elements.\n    // For Example:\n    // >>> moveOneBall((new ArrayList<Long>(Arrays.asList((long)3l, (long)4l, (long)5l, (long)1l, (long)2l))))\n    // (true)\n    // Explanation: By performin 2 right shift operations, non-decreasing order can\n    // be achieved for the given array array list.\n    // >>> moveOneBall((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)4l, (long)1l, (long)2l))))\n    // (false)\n    // Explanation:It is not possible to get non-decreasing order for the given\n    // array array list by performing any number of right shift operations.\n    public static boolean moveOneBall(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(moveOneBall((new ArrayList<Long>(Arrays.asList((long)3l, (long)4l, (long)5l, (long)1l, (long)2l)))) == (true));\n    assert(moveOneBall((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)10l, (long)1l, (long)2l)))) == (true));\n    assert(moveOneBall((new ArrayList<Long>(Arrays.asList((long)4l, (long)3l, (long)1l, (long)2l)))) == (false));\n    assert(moveOneBall((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)4l, (long)1l, (long)2l)))) == (false));\n    assert(moveOneBall((new ArrayList<Long>(Arrays.asList()))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_109_move_one_ball"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer n, return a pair that has the number of even and odd\n    // integer palindromes that fall within the range(1, n), inclusive.\n    // Example 1:\n    // >>> evenOddPalindrome((3l))\n    // (Pair.with(1l, 2l))\n    // Explanation:\n    // Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n    // Example 2:\n    // >>> evenOddPalindrome((12l))\n    // (Pair.with(4l, 6l))\n    // Explanation:\n    // Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n    // Note:\n    // 1. 1 <= n <= 10^3\n    // 2. returned pair has the number of even and odd integer palindromes respectively.\n    public static Pair<Long, Long> evenOddPalindrome(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(evenOddPalindrome((123l)).equals((Pair.with(8l, 13l))));\n    assert(evenOddPalindrome((12l)).equals((Pair.with(4l, 6l))));\n    assert(evenOddPalindrome((3l)).equals((Pair.with(1l, 2l))));\n    assert(evenOddPalindrome((63l)).equals((Pair.with(6l, 8l))));\n    assert(evenOddPalindrome((25l)).equals((Pair.with(5l, 6l))));\n    assert(evenOddPalindrome((19l)).equals((Pair.with(4l, 6l))));\n    assert(evenOddPalindrome((9l)).equals((Pair.with(4l, 5l))));\n    assert(evenOddPalindrome((1l)).equals((Pair.with(0l, 1l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_107_even_odd_palindrome"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n    // Example\n    // >>> isEqualToSumEven((4l))\n    // (false)\n    // >>> isEqualToSumEven((6l))\n    // (false)\n    // >>> isEqualToSumEven((8l))\n    // (true)\n    public static boolean isEqualToSumEven(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isEqualToSumEven((4l)) == (false));\n    assert(isEqualToSumEven((6l)) == (false));\n    assert(isEqualToSumEven((8l)) == (true));\n    assert(isEqualToSumEven((10l)) == (true));\n    assert(isEqualToSumEven((11l)) == (false));\n    assert(isEqualToSumEven((12l)) == (true));\n    assert(isEqualToSumEven((13l)) == (false));\n    assert(isEqualToSumEven((16l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_138_is_equal_to_sum_even"}
{"name": "HumanEval_62_derivative", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // xs represent coefficients of a polynomial.\n    // xs[0] + xs[1] * x + xs[2] * x^2 + ....\n    // Return derivative of this polynomial in the same form.\n    // >>> derivative((new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)2l, (long)4l, (long)5l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)12l, (long)20l)))\n    // >>> derivative((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)6l)))\n    public static ArrayList<Long> derivative(ArrayList<Long> xs) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(derivative((new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)2l, (long)4l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)12l, (long)20l)))));\n    assert(derivative((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)6l)))));\n    assert(derivative((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l)))));\n    assert(derivative((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)1l, (long)0l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)0l, (long)16l)))));\n    assert(derivative((new ArrayList<Long>(Arrays.asList((long)1l)))).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_62_derivative"}
{"name": "HumanEval_126_is_sorted", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of numbers, return whether or not they are sorted\n    // in ascending order. If array list has more than 1 duplicate of the same\n    // number, return false. Assume no negative numbers and only integers.\n    // Examples\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)5l))))\n    // (true)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l))))\n    // (true)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)4l, (long)5l))))\n    // (false)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l))))\n    // (true)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l, (long)7l))))\n    // (true)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)4l, (long)5l, (long)6l, (long)7l))))\n    // (false)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)3l, (long)3l, (long)4l))))\n    // (true)\n    // >>> isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)2l, (long)3l, (long)4l))))\n    // (false)\n    public static boolean isSorted(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)5l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)4l, (long)5l)))) == (false));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l, (long)7l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)4l, (long)5l, (long)6l, (long)7l)))) == (false));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList()))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)1l)))) == (false));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)2l, (long)3l, (long)4l)))) == (false));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)3l, (long)3l, (long)4l)))) == (false));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)3l, (long)3l, (long)4l)))) == (true));\n    assert(isSorted((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_126_is_sorted"}
{"name": "HumanEval_161_solve", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a string s.\n    // if s[i] is a letter, reverse its case from lower to upper or vise versa, \n    // otherwise keep it as it is.\n    // If the string contains no letters, reverse the string.\n    // The function should return the resulted string.\n    // Examples\n    // >>> solve((\"1234\"))\n    // (\"4321\")\n    // >>> solve((\"ab\"))\n    // (\"AB\")\n    // >>> solve((\"#a@C\"))\n    // (\"#A@c\")\n    public static String solve(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(solve((\"AsDf\")).equals((\"aSdF\")));\n    assert(solve((\"1234\")).equals((\"4321\")));\n    assert(solve((\"ab\")).equals((\"AB\")));\n    assert(solve((\"#a@C\")).equals((\"#A@c\")));\n    assert(solve((\"#AsdfW^45\")).equals((\"#aSDFw^45\")));\n    assert(solve((\"#6@2\")).equals((\"2@6#\")));\n    assert(solve((\"#$a^D\")).equals((\"#$A^d\")));\n    assert(solve((\"#ccc\")).equals((\"#CCC\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_161_solve"}
{"name": "HumanEval_130_tri", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n    // the last couple centuries. However, what people don't know is Tribonacci sequence.\n    // Tribonacci sequence is defined by the recurrence:\n    // tri(1) = 3\n    // tri(n) = 1 + n / 2, if n is even.\n    // tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n    // For example:\n    // tri(2) = 1 + (2 / 2) = 2\n    // tri(4) = 3\n    // tri(3) = tri(2) + tri(1) + tri(4)\n    // = 2 + 3 + 3 = 8 \n    // You are given a non-negative integer number n, you have to a return an array array list of the \n    // first n + 1 numbers of the Tribonacci sequence.\n    // Examples:\n    // >>> tri((3l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l)))\n    public static ArrayList<Long> tri(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(tri((3l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l)))));\n    assert(tri((4l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l)))));\n    assert(tri((5l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l)))));\n    assert(tri((6l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l, (long)4l)))));\n    assert(tri((7l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l, (long)4l, (long)24l)))));\n    assert(tri((8l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l, (long)4l, (long)24l, (long)5l)))));\n    assert(tri((9l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l, (long)4l, (long)24l, (long)5l, (long)35l)))));\n    assert(tri((20l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)8l, (long)3l, (long)15l, (long)4l, (long)24l, (long)5l, (long)35l, (long)6l, (long)48l, (long)7l, (long)63l, (long)8l, (long)80l, (long)9l, (long)99l, (long)10l, (long)120l, (long)11l)))));\n    assert(tri((0l)).equals((new ArrayList<Long>(Arrays.asList((long)1l)))));\n    assert(tri((1l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_130_tri"}
{"name": "HumanEval_36_fizz_buzz", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n    // >>> fizzBuzz((50l))\n    // (0l)\n    // >>> fizzBuzz((78l))\n    // (2l)\n    // >>> fizzBuzz((79l))\n    // (3l)\n    public static long fizzBuzz(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fizzBuzz((50l)) == (0l));\n    assert(fizzBuzz((78l)) == (2l));\n    assert(fizzBuzz((79l)) == (3l));\n    assert(fizzBuzz((100l)) == (3l));\n    assert(fizzBuzz((200l)) == (6l));\n    assert(fizzBuzz((4000l)) == (192l));\n    assert(fizzBuzz((10000l)) == (639l));\n    assert(fizzBuzz((100000l)) == (8026l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_36_fizz_buzz"}
{"name": "HumanEval_29_filter_by_prefix", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Filter an input array list of strings only for ones that start with a given prefix.\n    // >>> filterByPrefix((new ArrayList<String>(Arrays.asList())), (\"a\"))\n    // (new ArrayList<String>(Arrays.asList()))\n    // >>> filterByPrefix((new ArrayList<String>(Arrays.asList((String)\"abc\", (String)\"bcd\", (String)\"cde\", (String)\"array\"))), (\"a\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"abc\", (String)\"array\")))\n    public static ArrayList<String> filterByPrefix(ArrayList<String> strings, String prefix) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(filterByPrefix((new ArrayList<String>(Arrays.asList())), (\"john\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(filterByPrefix((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"asd\", (String)\"xxy\", (String)\"john doe\", (String)\"xxxAAA\", (String)\"xxx\"))), (\"xxx\")).equals((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"xxxAAA\", (String)\"xxx\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_29_filter_by_prefix"}
{"name": "HumanEval_84_solve", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer N, return the total sum of its digits in binary.\n    // Example\n    // >>> solve((1000l))\n    // (\"1\")\n    // >>> solve((150l))\n    // (\"110\")\n    // >>> solve((147l))\n    // (\"1100\")\n    // Variables:\n    // @N integer\n    // Constraints: 0 \u2264 N \u2264 10000.\n    // Output:\n    // a string of binary number\n    public static String solve(long N) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(solve((1000l)).equals((\"1\")));\n    assert(solve((150l)).equals((\"110\")));\n    assert(solve((147l)).equals((\"1100\")));\n    assert(solve((333l)).equals((\"1001\")));\n    assert(solve((963l)).equals((\"10010\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_84_solve"}
{"name": "HumanEval_129_minPath", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n    // each cell of the grid contains a value. Every integer in the range [1, N * N]\n    // inclusive appears exactly once on the cells of the grid.\n    // You have to find the minimum path of length k in the grid. You can start\n    // from any cell, and in each step you can move to any of the neighbor cells,\n    // in other words, you can go to cells which share an edge with you current\n    // cell.\n    // Please note that a path of length k means visiting exactly k cells (not\n    // necessarily distinct).\n    // You CANNOT go off the grid.\n    // A path A (of length k) is considered less than a path B (of length k) if\n    // after making the ordered array lists of the values on the cells that A and B go\n    // through (let's call them lst_A and lst_B), lst_A is lexicographically less\n    // than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n    // such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n    // lst_A[j] = lst_B[j].\n    // It is guaranteed that the answer is unique.\n    // Return an ordered array list of the values on the cells that the minimum path go through.\n    // Examples:    \n    // >>> minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)7l, (long)8l, (long)9l))))), (3l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)1l)))\n    // >>> minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)9l, (long)3l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)7l, (long)8l, (long)2l))))), (1l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l)))\n    public static ArrayList<Long> minPath(ArrayList<ArrayList<Long>> grid, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)7l, (long)8l, (long)9l))))), (3l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)1l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)9l, (long)3l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)7l, (long)8l, (long)2l))))), (1l)).equals((new ArrayList<Long>(Arrays.asList((long)1l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)7l, (long)8l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)9l, (long)10l, (long)11l, (long)12l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)13l, (long)14l, (long)15l, (long)16l))))), (4l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)1l, (long)2l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)6l, (long)4l, (long)13l, (long)10l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)12l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)16l, (long)11l, (long)15l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)8l, (long)14l, (long)9l, (long)2l))))), (7l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)10l, (long)1l, (long)10l, (long)1l, (long)10l, (long)1l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)8l, (long)14l, (long)9l, (long)2l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)6l, (long)4l, (long)13l, (long)15l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)1l, (long)12l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)10l, (long)11l, (long)16l))))), (5l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)7l, (long)1l, (long)7l, (long)1l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)11l, (long)8l, (long)7l, (long)2l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)16l, (long)14l, (long)4l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)9l, (long)3l, (long)15l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)12l, (long)13l, (long)10l, (long)1l))))), (9l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l, (long)1l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)12l, (long)13l, (long)10l, (long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)9l, (long)3l, (long)15l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)5l, (long)16l, (long)14l, (long)4l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)11l, (long)8l, (long)7l, (long)2l))))), (12l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l, (long)1l, (long)6l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)2l, (long)7l, (long)4l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)5l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)6l, (long)8l, (long)9l))))), (8l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)1l, (long)3l, (long)1l, (long)3l, (long)1l, (long)3l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)6l, (long)1l, (long)5l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)8l, (long)9l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)2l, (long)7l, (long)4l))))), (8l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)1l, (long)5l, (long)1l, (long)5l, (long)1l, (long)5l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)4l))))), (10l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)1l, (long)2l, (long)1l, (long)2l, (long)1l, (long)2l, (long)1l, (long)2l)))));\n    assert(minPath((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)3l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)3l, (long)2l))))), (10l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)1l, (long)3l, (long)1l, (long)3l, (long)1l, (long)3l, (long)1l, (long)3l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_129_minPath"}
{"name": "HumanEval_98_count_upper", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string s, count the number of uppercase vowels in even indices.\n    // For example:\n    // >>> countUpper((\"aBCdEf\"))\n    // (1l)\n    // >>> countUpper((\"abcdefg\"))\n    // (0l)\n    // >>> countUpper((\"dBBE\"))\n    // (0l)\n    public static long countUpper(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(countUpper((\"aBCdEf\")) == (1l));\n    assert(countUpper((\"abcdefg\")) == (0l));\n    assert(countUpper((\"dBBE\")) == (0l));\n    assert(countUpper((\"B\")) == (0l));\n    assert(countUpper((\"U\")) == (1l));\n    assert(countUpper((\"\")) == (0l));\n    assert(countUpper((\"EEEE\")) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_98_count_upper"}
{"name": "HumanEval_120_maximum", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list arr of integers and a positive integer k, return a sorted array list \n    // of length k with the maximum k numbers in arr.\n    // Example 1:\n    // >>> maximum((new ArrayList<Long>(Arrays.asList((long)-3l, (long)-4l, (long)5l))), (3l))\n    // (new ArrayList<Long>(Arrays.asList((long)-4l, (long)-3l, (long)5l)))\n    // Example 2:\n    // >>> maximum((new ArrayList<Long>(Arrays.asList((long)4l, (long)-4l, (long)4l))), (2l))\n    // (new ArrayList<Long>(Arrays.asList((long)4l, (long)4l)))\n    // Example 3:\n    // >>> maximum((new ArrayList<Long>(Arrays.asList((long)-3l, (long)2l, (long)1l, (long)2l, (long)-1l, (long)-2l, (long)1l))), (1l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l)))\n    // Note:\n    // 1. The length of the array array list will be in the range of [1, 1000].\n    // 2. The elements in the array array list will be in the range of [-1000, 1000].\n    // 3. 0 <= k <= len(arr)\n    public static ArrayList<Long> maximum(ArrayList<Long> arr, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)-3l, (long)-4l, (long)5l))), (3l)).equals((new ArrayList<Long>(Arrays.asList((long)-4l, (long)-3l, (long)5l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)4l, (long)-4l, (long)4l))), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)4l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)-3l, (long)2l, (long)1l, (long)2l, (long)-1l, (long)-2l, (long)1l))), (1l)).equals((new ArrayList<Long>(Arrays.asList((long)2l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)123l, (long)-123l, (long)20l, (long)0l, (long)1l, (long)2l, (long)-3l))), (3l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)20l, (long)123l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)-123l, (long)20l, (long)0l, (long)1l, (long)2l, (long)-3l))), (4l)).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)2l, (long)20l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)5l, (long)15l, (long)0l, (long)3l, (long)-13l, (long)-8l, (long)0l))), (7l)).equals((new ArrayList<Long>(Arrays.asList((long)-13l, (long)-8l, (long)0l, (long)0l, (long)3l, (long)5l, (long)15l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)-1l, (long)0l, (long)2l, (long)5l, (long)3l, (long)-10l))), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)1l, (long)0l, (long)5l, (long)-7l))), (1l)).equals((new ArrayList<Long>(Arrays.asList((long)5l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)4l, (long)-4l))), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)-4l, (long)4l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)-10l, (long)10l))), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)-10l, (long)10l)))));\n    assert(maximum((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)-23l, (long)243l, (long)-400l, (long)0l))), (0l)).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_120_maximum"}
{"name": "HumanEval_24_largest_divisor", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // For a given number n, find the largest number that divides n evenly, smaller than n\n    // >>> largestDivisor((15l))\n    // (5l)\n    public static long largestDivisor(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(largestDivisor((3l)) == (1l));\n    assert(largestDivisor((7l)) == (1l));\n    assert(largestDivisor((10l)) == (5l));\n    assert(largestDivisor((100l)) == (50l));\n    assert(largestDivisor((49l)) == (7l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_24_largest_divisor"}
{"name": "HumanEval_88_sort_array", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of non-negative integers, return a cojava of the given array array list after sorting,\n    // you will sort the given array array list in ascending order if the sum( first index value, last index value) is odd,\n    // or sort it in descending order if the sum( first index value, last index value) is even.\n    // Note:\n    // * don't change the given array array list.\n    // Examples:\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList())))\n    // (new ArrayList<Long>(Arrays.asList()))\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)5l))))\n    // (new ArrayList<Long>(Arrays.asList((long)5l)))\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)3l, (long)0l, (long)1l, (long)5l))))\n    // (new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)3l, (long)0l, (long)1l, (long)5l, (long)6l))))\n    // (new ArrayList<Long>(Arrays.asList((long)6l, (long)5l, (long)4l, (long)3l, (long)2l, (long)1l, (long)0l)))\n    public static ArrayList<Long> sortArray(ArrayList<Long> array) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortArray((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)5l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)3l, (long)0l, (long)1l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)3l, (long)0l, (long)1l, (long)5l, (long)6l)))).equals((new ArrayList<Long>(Arrays.asList((long)6l, (long)5l, (long)4l, (long)3l, (long)2l, (long)1l, (long)0l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)15l, (long)42l, (long)87l, (long)32l, (long)11l, (long)0l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)11l, (long)15l, (long)32l, (long)42l, (long)87l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)21l, (long)14l, (long)23l, (long)11l)))).equals((new ArrayList<Long>(Arrays.asList((long)23l, (long)21l, (long)14l, (long)11l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_88_sort_array"}
{"name": "HumanEval_106_f", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Implement the function f that takes n as a parameter,\n    // and returns an array array list of size n, such that the value of the element at index i is the factorial of i if i is even\n    // or the sum of numbers from 1 to i otherwise.\n    // i starts from 1.\n    // the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n    // Example:\n    // >>> f((5l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)6l, (long)24l, (long)15l)))\n    public static ArrayList<Long> f(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(f((5l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)6l, (long)24l, (long)15l)))));\n    assert(f((7l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)6l, (long)24l, (long)15l, (long)720l, (long)28l)))));\n    assert(f((1l)).equals((new ArrayList<Long>(Arrays.asList((long)1l)))));\n    assert(f((3l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)6l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_106_f"}
{"name": "HumanEval_77_iscube", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that takes an integer a and returns true \n    // if this ingeger is a cube of some integer number.\n    // Note: you may assume the input is always valid.\n    // Examples:\n    // >>> iscube((1l))\n    // (true)\n    // >>> iscube((2l))\n    // (false)\n    // >>> iscube((-1l))\n    // (true)\n    // >>> iscube((64l))\n    // (true)\n    // >>> iscube((0l))\n    // (true)\n    // >>> iscube((180l))\n    // (false)\n    public static boolean iscube(long a) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(iscube((1l)) == (true));\n    assert(iscube((2l)) == (false));\n    assert(iscube((-1l)) == (true));\n    assert(iscube((64l)) == (true));\n    assert(iscube((180l)) == (false));\n    assert(iscube((1000l)) == (true));\n    assert(iscube((0l)) == (true));\n    assert(iscube((1729l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_77_iscube"}
{"name": "HumanEval_93_encode", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that takes a message, and encodes in such a \n    // way that it swaps case of all letters, replaces all vowels in \n    // the message with the letter that appears 2 places ahead of that \n    // vowel in the english alphabet. \n    // Assume only letters. \n    // Examples:\n    // >>> encode((\"test\"))\n    // (\"TGST\")\n    // >>> encode((\"This is a message\"))\n    // (\"tHKS KS C MGSSCGG\")\n    public static String encode(String message) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(encode((\"TEST\")).equals((\"tgst\")));\n    assert(encode((\"Mudasir\")).equals((\"mWDCSKR\")));\n    assert(encode((\"YES\")).equals((\"ygs\")));\n    assert(encode((\"This is a message\")).equals((\"tHKS KS C MGSSCGG\")));\n    assert(encode((\"I DoNt KnOw WhAt tO WrItE\")).equals((\"k dQnT kNqW wHcT Tq wRkTg\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_93_encode"}
{"name": "HumanEval_91_is_bored", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You'll be given a string of words, and your task is to count the number\n    // of boredoms. A boredom is a sentence that starts with the word \"I\".\n    // Sentences are delimited by '.', '?' or '!'.\n    // For example:\n    // >>> isBored((\"Hello world\"))\n    // (0l)\n    // >>> isBored((\"The sky is blue. The sun is shining. I love this weather\"))\n    // (1l)\n    public static long isBored(String S) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isBored((\"Hello world\")) == (0l));\n    assert(isBored((\"Is the sky blue?\")) == (0l));\n    assert(isBored((\"I love It !\")) == (1l));\n    assert(isBored((\"bIt\")) == (0l));\n    assert(isBored((\"I feel good today. I will be productive. will kill It\")) == (2l));\n    assert(isBored((\"You and I are going for a walk\")) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_91_is_bored"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // pairs_sum_to_zero takes an array array list of integers as an input.\n    // it returns true if there are two distinct elements in the array list that\n    // sum to zero, and false otherwise.\n    // >>> pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)0l))))\n    // (false)\n    // >>> pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)-2l, (long)1l))))\n    // (false)\n    // >>> pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)7l))))\n    // (false)\n    // >>> pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)-5l, (long)3l, (long)5l, (long)7l))))\n    // (true)\n    // >>> pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l))))\n    // (false)\n    public static boolean pairsSumToZero(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)0l)))) == (false));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)-2l, (long)1l)))) == (false));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)7l)))) == (false));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)-5l, (long)3l, (long)5l, (long)7l)))) == (true));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)1l)))) == (false));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)-3l, (long)9l, (long)-1l, (long)3l, (long)2l, (long)30l)))) == (true));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)-3l, (long)9l, (long)-1l, (long)3l, (long)2l, (long)31l)))) == (true));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)-3l, (long)9l, (long)-1l, (long)4l, (long)2l, (long)30l)))) == (false));\n    assert(pairsSumToZero((new ArrayList<Long>(Arrays.asList((long)-3l, (long)9l, (long)-1l, (long)4l, (long)2l, (long)31l)))) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_43_pairs_sum_to_zero"}
{"name": "HumanEval_71_triangle_area", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given the lengths of the three sides of a triangle. Return the area of\n    // the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n    // Otherwise return -1\n    // Three sides make a valid triangle when the sum of any two sides is greater \n    // than the third side.\n    // Example:\n    // >>> triangleArea((3l), (4l), (5l))\n    // (6.0f)\n    // >>> triangleArea((1l), (2l), (10l))\n    // (float)-1l\n    public static float triangleArea(long a, long b, long c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(triangleArea((3l), (4l), (5l)) == (6.0f));\n    assert(triangleArea((1l), (2l), (10l)) == (float)-1l);\n    assert(triangleArea((4l), (8l), (5l)) == (8.18f));\n    assert(triangleArea((2l), (2l), (2l)) == (1.73f));\n    assert(triangleArea((1l), (2l), (3l)) == (float)-1l);\n    assert(triangleArea((10l), (5l), (7l)) == (16.25f));\n    assert(triangleArea((2l), (6l), (3l)) == (float)-1l);\n    assert(triangleArea((1l), (1l), (1l)) == (0.43f));\n    assert(triangleArea((2l), (2l), (10l)) == (float)-1l);\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_71_triangle_area"}
{"name": "HumanEval_148_bf", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // There are eight planets in our solar system: the closerst to the Sun \n    // is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n    // Uranus, Neptune.\n    // Write a function that takes two planet names as strings planet1 and planet2. \n    // The function should return a pair containing all planets whose orbits are \n    // located between the orbit of planet1 and the orbit of planet2, sorted by \n    // the proximity to the sun. \n    // The function should return an empty pair if planet1 or planet2\n    // are not correct planet names. \n    // Examples\n    // >>> bf((\"Jupiter\"), (\"Neptune\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"Saturn\", (String)\"Uranus\")))\n    // >>> bf((\"Earth\"), (\"Mercury\"))\n    // (ArrayList<String>(\"Venus\"))\n    // >>> bf((\"Mercury\"), (\"Uranus\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"Venus\", (String)\"Earth\", (String)\"Mars\", (String)\"Jupiter\", (String)\"Saturn\")))\n    public static ArrayList<String> bf(String planet1, String planet2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(bf((\"Jupiter\"), (\"Neptune\")).equals((new ArrayList<String>(Arrays.asList((String)\"Saturn\", (String)\"Uranus\")))));\n    assert(bf((\"Earth\"), (\"Mercury\")).equals((new ArrayList<String>(Arrays.asList((String)\"Venus\")))));\n    assert(bf((\"Mercury\"), (\"Uranus\")).equals((new ArrayList<String>(Arrays.asList((String)\"Venus\", (String)\"Earth\", (String)\"Mars\", (String)\"Jupiter\", (String)\"Saturn\")))));\n    assert(bf((\"Neptune\"), (\"Venus\")).equals((new ArrayList<String>(Arrays.asList((String)\"Earth\", (String)\"Mars\", (String)\"Jupiter\", (String)\"Saturn\", (String)\"Uranus\")))));\n    assert(bf((\"Earth\"), (\"Earth\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(bf((\"Mars\"), (\"Earth\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(bf((\"Jupiter\"), (\"Makemake\")).equals((new ArrayList<String>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_148_bf"}
{"name": "HumanEval_131_digits", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer n, return the product of the odd digits.\n    // Return 0 if all digits are even.\n    // For example:\n    // >>> digits((1l))\n    // (1l)\n    // >>> digits((4l))\n    // (0l)\n    // >>> digits((235l))\n    // (15l)\n    public static long digits(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(digits((5l)) == (5l));\n    assert(digits((54l)) == (5l));\n    assert(digits((120l)) == (1l));\n    assert(digits((5014l)) == (5l));\n    assert(digits((98765l)) == (315l));\n    assert(digits((5576543l)) == (2625l));\n    assert(digits((2468l)) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_131_digits"}
{"name": "HumanEval_101_words_string", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You will be given a string of words separated by commas or spaces. Your task is\n    // to split the string into words and return an array array list of the words.\n    // For example:\n    // >>> wordsString((\"Hi, my name is John\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"Hi\", (String)\"my\", (String)\"name\", (String)\"is\", (String)\"John\")))\n    // >>> wordsString((\"One, two, three, four, five, six\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"One\", (String)\"two\", (String)\"three\", (String)\"four\", (String)\"five\", (String)\"six\")))\n    public static ArrayList<String> wordsString(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(wordsString((\"Hi, my name is John\")).equals((new ArrayList<String>(Arrays.asList((String)\"Hi\", (String)\"my\", (String)\"name\", (String)\"is\", (String)\"John\")))));\n    assert(wordsString((\"One, two, three, four, five, six\")).equals((new ArrayList<String>(Arrays.asList((String)\"One\", (String)\"two\", (String)\"three\", (String)\"four\", (String)\"five\", (String)\"six\")))));\n    assert(wordsString((\"Hi, my name\")).equals((new ArrayList<String>(Arrays.asList((String)\"Hi\", (String)\"my\", (String)\"name\")))));\n    assert(wordsString((\"One,, two, three, four, five, six,\")).equals((new ArrayList<String>(Arrays.asList((String)\"One\", (String)\"two\", (String)\"three\", (String)\"four\", (String)\"five\", (String)\"six\")))));\n    assert(wordsString((\"\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(wordsString((\"ahmed     , gamal\")).equals((new ArrayList<String>(Arrays.asList((String)\"ahmed\", (String)\"gamal\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_101_words_string"}
{"name": "HumanEval_18_how_many_times", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Find how many times a given substring can be found in the original string. Count overlaping cases.\n    // >>> howManyTimes((\"\"), (\"a\"))\n    // (0l)\n    // >>> howManyTimes((\"aaa\"), (\"a\"))\n    // (3l)\n    // >>> howManyTimes((\"aaaa\"), (\"aa\"))\n    // (3l)\n    public static long howManyTimes(String string, String substring) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(howManyTimes((\"\"), (\"x\")) == (0l));\n    assert(howManyTimes((\"xyxyxyx\"), (\"x\")) == (4l));\n    assert(howManyTimes((\"cacacacac\"), (\"cac\")) == (4l));\n    assert(howManyTimes((\"john doe\"), (\"john\")) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_18_how_many_times"}
{"name": "HumanEval_51_remove_vowels", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // remove_vowels is a function that takes string and returns string without vowels.\n    // >>> removeVowels((\"\"))\n    // (\"\")\n    // >>> removeVowels((\"abcdef\"))\n    // (\"bcdf\")\n    // >>> removeVowels((\"aaaaa\"))\n    // (\"\")\n    // >>> removeVowels((\"aaBAA\"))\n    // (\"B\")\n    // >>> removeVowels((\"zbcd\"))\n    // (\"zbcd\")\n    public static String removeVowels(String text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(removeVowels((\"\")).equals((\"\")));\n    assert(removeVowels((\"abcdef\\nghijklm\")).equals((\"bcdf\\nghjklm\")));\n    assert(removeVowels((\"fedcba\")).equals((\"fdcb\")));\n    assert(removeVowels((\"eeeee\")).equals((\"\")));\n    assert(removeVowels((\"acBAA\")).equals((\"cB\")));\n    assert(removeVowels((\"EcBOO\")).equals((\"cB\")));\n    assert(removeVowels((\"ybcd\")).equals((\"ybcd\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_51_remove_vowels"}
{"name": "HumanEval_70_strange_sort_list", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given array list of integers, return array list in strange order.\n    // Strange sorting, is when you start with the minimum value,\n    // then maximum of the remaining integers, then minimum and so on.\n    // Examples:\n    // >>> strangeSortList((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)2l, (long)3l)))\n    // >>> strangeSortList((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)5l, (long)5l))))\n    // (new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)5l, (long)5l)))\n    // >>> strangeSortList((new ArrayList<Long>(Arrays.asList())))\n    // (new ArrayList<Long>(Arrays.asList()))\n    public static ArrayList<Long> strangeSortList(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)2l, (long)3l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)7l, (long)8l, (long)9l)))).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)9l, (long)6l, (long)8l, (long)7l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)2l, (long)4l, (long)3l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)7l, (long)8l, (long)9l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)9l, (long)5l, (long)8l, (long)6l, (long)7l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)5l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)5l, (long)5l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l, (long)7l, (long)8l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)8l, (long)2l, (long)7l, (long)3l, (long)6l, (long)4l, (long)5l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)0l, (long)2l, (long)2l, (long)2l, (long)5l, (long)5l, (long)-5l, (long)-5l)))).equals((new ArrayList<Long>(Arrays.asList((long)-5l, (long)5l, (long)-5l, (long)5l, (long)0l, (long)2l, (long)2l, (long)2l)))));\n    assert(strangeSortList((new ArrayList<Long>(Arrays.asList((long)111111l)))).equals((new ArrayList<Long>(Arrays.asList((long)111111l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_70_strange_sort_list"}
{"name": "HumanEval_20_find_closest_elements", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // From a supplied array list of numbers (of length at least two) select and return two that are the closest to each\n    // other and return them in order (smaller number, larger number).\n    // >>> findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.2f))))\n    // (Pair.with(2.0f, 2.2f))\n    // >>> findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f))))\n    // (Pair.with(2.0f, 2.0f))\n    public static Pair<Float, Float> findClosestElements(ArrayList<Float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f)))).equals((Pair.with(3.9f, 4.0f))));\n    assert(findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f)))).equals((Pair.with(5.0f, 5.9f))));\n    assert(findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.2f)))).equals((Pair.with(2.0f, 2.2f))));\n    assert(findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f)))).equals((Pair.with(2.0f, 2.0f))));\n    assert(findClosestElements((new ArrayList<Float>(Arrays.asList((float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f)))).equals((Pair.with(2.2f, 3.1f))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_20_find_closest_elements"}
{"name": "HumanEval_76_is_simple_power", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Your task is to write a function that returns true if a number x is a simple\n    // power of n and false in other cases.\n    // x is a simple power of n if n**int=x\n    // For example:\n    // >>> isSimplePower((1l), (4l))\n    // (true)\n    // >>> isSimplePower((2l), (2l))\n    // (true)\n    // >>> isSimplePower((8l), (2l))\n    // (true)\n    // >>> isSimplePower((3l), (2l))\n    // (false)\n    // >>> isSimplePower((3l), (1l))\n    // (false)\n    // >>> isSimplePower((5l), (3l))\n    // (false)\n    public static boolean isSimplePower(long x, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isSimplePower((16l), (2l)) == (true));\n    assert(isSimplePower((143214l), (16l)) == (false));\n    assert(isSimplePower((4l), (2l)) == (true));\n    assert(isSimplePower((9l), (3l)) == (true));\n    assert(isSimplePower((16l), (4l)) == (true));\n    assert(isSimplePower((24l), (2l)) == (false));\n    assert(isSimplePower((128l), (4l)) == (false));\n    assert(isSimplePower((12l), (6l)) == (false));\n    assert(isSimplePower((1l), (1l)) == (true));\n    assert(isSimplePower((1l), (12l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_76_is_simple_power"}
{"name": "HumanEval_39_prime_fib", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n    // >>> primeFib((1l))\n    // (2l)\n    // >>> primeFib((2l))\n    // (3l)\n    // >>> primeFib((3l))\n    // (5l)\n    // >>> primeFib((4l))\n    // (13l)\n    // >>> primeFib((5l))\n    // (89l)\n    public static long primeFib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(primeFib((1l)) == (2l));\n    assert(primeFib((2l)) == (3l));\n    assert(primeFib((3l)) == (5l));\n    assert(primeFib((4l)) == (13l));\n    assert(primeFib((5l)) == (89l));\n    assert(primeFib((6l)) == (233l));\n    assert(primeFib((7l)) == (1597l));\n    assert(primeFib((8l)) == (28657l));\n    assert(primeFib((9l)) == (514229l));\n    assert(primeFib((10l)) == (433494437l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_39_prime_fib"}
{"name": "HumanEval_145_order_by_points", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function which sorts the given array list of integers\n    // in ascending order according to the sum of their digits.\n    // Note: if there are several items with similar sum of their digits,\n    // order them based on their index in original array list.\n    // For example:\n    // >>> orderByPoints((new ArrayList<Long>(Arrays.asList((long)1l, (long)11l, (long)-1l, (long)-11l, (long)-12l))))\n    // (new ArrayList<Long>(Arrays.asList((long)-1l, (long)-11l, (long)1l, (long)-12l, (long)11l)))\n    // >>> orderByPoints((new ArrayList<Long>(Arrays.asList())))\n    // (new ArrayList<Long>(Arrays.asList()))\n    public static ArrayList<Long> orderByPoints(ArrayList<Long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList((long)1l, (long)11l, (long)-1l, (long)-11l, (long)-12l)))).equals((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-11l, (long)1l, (long)-12l, (long)11l)))));\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList((long)1234l, (long)423l, (long)463l, (long)145l, (long)2l, (long)423l, (long)423l, (long)53l, (long)6l, (long)37l, (long)3457l, (long)3l, (long)56l, (long)0l, (long)46l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)2l, (long)3l, (long)6l, (long)53l, (long)423l, (long)423l, (long)423l, (long)1234l, (long)145l, (long)37l, (long)46l, (long)56l, (long)463l, (long)3457l)))));\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList((long)1l, (long)-11l, (long)-32l, (long)43l, (long)54l, (long)-98l, (long)2l, (long)-3l)))).equals((new ArrayList<Long>(Arrays.asList((long)-3l, (long)-32l, (long)-98l, (long)-11l, (long)1l, (long)2l, (long)43l, (long)54l)))));\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l, (long)10l, (long)11l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)10l, (long)2l, (long)11l, (long)3l, (long)4l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l)))));\n    assert(orderByPoints((new ArrayList<Long>(Arrays.asList((long)0l, (long)6l, (long)6l, (long)-76l, (long)-21l, (long)23l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)-76l, (long)-21l, (long)0l, (long)4l, (long)23l, (long)6l, (long)6l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_145_order_by_points"}
{"name": "HumanEval_0_has_close_elements", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Check if in given array list of numbers, are any two numbers closer to each other than\n    // given threshold.\n    // >>> hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f))), (0.5f))\n    // (false)\n    // >>> hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.8f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f))), (0.3f))\n    // (true)\n    public static boolean hasCloseElements(ArrayList<Float> numbers, float threshold) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f))), (0.3f)) == (true));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.9f, (float)4.0f, (float)5.0f, (float)2.2f))), (0.05f)) == (false));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f))), (0.95f)) == (true));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)5.9f, (float)4.0f, (float)5.0f))), (0.8f)) == (false));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f, (float)2.0f))), (0.1f)) == (true));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f))), (1.0f)) == (true));\n    assert(hasCloseElements((new ArrayList<Float>(Arrays.asList((float)1.1f, (float)2.2f, (float)3.1f, (float)4.1f, (float)5.1f))), (0.5f)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_0_has_close_elements"}
{"name": "HumanEval_10_make_palindrome", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Find the shortest palindrome that begins with a supplied string.\n    // Algorithm idea is simple:\n    // - Find the longest postfix of supplied string that is a palindrome.\n    // - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n    // >>> makePalindrome((\"\"))\n    // (\"\")\n    // >>> makePalindrome((\"cat\"))\n    // (\"catac\")\n    // >>> makePalindrome((\"cata\"))\n    // (\"catac\")\n    public static String makePalindrome(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(makePalindrome((\"\")).equals((\"\")));\n    assert(makePalindrome((\"x\")).equals((\"x\")));\n    assert(makePalindrome((\"xyz\")).equals((\"xyzyx\")));\n    assert(makePalindrome((\"xyx\")).equals((\"xyx\")));\n    assert(makePalindrome((\"jerry\")).equals((\"jerryrrej\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_10_make_palindrome"}
{"name": "HumanEval_11_string_xor", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Input are two strings a and b consisting only of 1s and 0s.\n    // Perform binary XOR on these inputs and return result also as a string.\n    // >>> stringXor((\"010\"), (\"110\"))\n    // (\"100\")\n    public static String stringXor(String a, String b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(stringXor((\"111000\"), (\"101010\")).equals((\"010010\")));\n    assert(stringXor((\"1\"), (\"1\")).equals((\"0\")));\n    assert(stringXor((\"0101\"), (\"0000\")).equals((\"0101\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_11_string_xor"}
{"name": "HumanEval_139_special_factorial", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // The Brazilian factorial is defined as:\n    // brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n    // where n > 0\n    // For example:\n    // >>> specialFactorial((4l))\n    // (288l)\n    // The function will receive an integer as input and should return the special\n    // factorial of this integer.\n    public static long specialFactorial(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(specialFactorial((4l)) == (288l));\n    assert(specialFactorial((5l)) == (34560l));\n    assert(specialFactorial((7l)) == (125411328000l));\n    assert(specialFactorial((1l)) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_139_special_factorial"}
{"name": "HumanEval_122_add_elements", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a non-empty array array list of integers arr and an integer k, return\n    // the sum of the elements with at most two digits from the first k elements of arr.\n    // Example:\n    // >>> addElements((new ArrayList<Long>(Arrays.asList((long)111l, (long)21l, (long)3l, (long)4000l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l))), (4l))\n    // (24l)\n    // Constraints:\n    // 1. 1 <= len(arr) <= 100\n    // 2. 1 <= k <= len(arr)\n    public static long addElements(ArrayList<Long> arr, long k) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(addElements((new ArrayList<Long>(Arrays.asList((long)1l, (long)-2l, (long)-3l, (long)41l, (long)57l, (long)76l, (long)87l, (long)88l, (long)99l))), (3l)) == (-4l));\n    assert(addElements((new ArrayList<Long>(Arrays.asList((long)111l, (long)121l, (long)3l, (long)4000l, (long)5l, (long)6l))), (2l)) == (0l));\n    assert(addElements((new ArrayList<Long>(Arrays.asList((long)11l, (long)21l, (long)3l, (long)90l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l))), (4l)) == (125l));\n    assert(addElements((new ArrayList<Long>(Arrays.asList((long)111l, (long)21l, (long)3l, (long)4000l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l))), (4l)) == (24l));\n    assert(addElements((new ArrayList<Long>(Arrays.asList((long)1l))), (1l)) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_122_add_elements"}
{"name": "HumanEval_46_fib4", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n    // fib4(0) -> 0\n    // fib4(1) -> 0\n    // fib4(2) -> 2\n    // fib4(3) -> 0\n    // fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n    // Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n    // >>> fib4((5l))\n    // (4l)\n    // >>> fib4((6l))\n    // (8l)\n    // >>> fib4((7l))\n    // (14l)\n    public static long fib4(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fib4((5l)) == (4l));\n    assert(fib4((8l)) == (28l));\n    assert(fib4((10l)) == (104l));\n    assert(fib4((12l)) == (386l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_46_fib4"}
{"name": "HumanEval_104_unique_digits", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of positive integers x. return a sorted array list of all \n    // elements that hasn't any even digit.\n    // Note: Returned array list should be sorted in increasing order.\n    // For example:\n    // >>> uniqueDigits((new ArrayList<Long>(Arrays.asList((long)15l, (long)33l, (long)1422l, (long)1l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)15l, (long)33l)))\n    // >>> uniqueDigits((new ArrayList<Long>(Arrays.asList((long)152l, (long)323l, (long)1422l, (long)10l))))\n    // (new ArrayList<Long>(Arrays.asList()))\n    public static ArrayList<Long> uniqueDigits(ArrayList<Long> x) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(uniqueDigits((new ArrayList<Long>(Arrays.asList((long)15l, (long)33l, (long)1422l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)15l, (long)33l)))));\n    assert(uniqueDigits((new ArrayList<Long>(Arrays.asList((long)152l, (long)323l, (long)1422l, (long)10l)))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(uniqueDigits((new ArrayList<Long>(Arrays.asList((long)12345l, (long)2033l, (long)111l, (long)151l)))).equals((new ArrayList<Long>(Arrays.asList((long)111l, (long)151l)))));\n    assert(uniqueDigits((new ArrayList<Long>(Arrays.asList((long)135l, (long)103l, (long)31l)))).equals((new ArrayList<Long>(Arrays.asList((long)31l, (long)135l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_104_unique_digits"}
{"name": "HumanEval_117_select_words", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string s and a natural number n, you have been tasked to implement \n    // a function that returns an array array list of all words from string s that contain exactly \n    // n consonants, in order these words appear in the string s.\n    // If the string s is empty then the function should return an empty array list.\n    // Note: you may assume the input string contains only letters and spaces.\n    // Examples:\n    // >>> selectWords((\"Mary had a little lamb\"), (4l))\n    // (new ArrayList<String>(Arrays.asList((String)\"little\")))\n    // >>> selectWords((\"Mary had a little lamb\"), (3l))\n    // (new ArrayList<String>(Arrays.asList((String)\"Mary\", (String)\"lamb\")))\n    // >>> selectWords((\"simple white space\"), (2l))\n    // (new ArrayList<String>(Arrays.asList()))\n    // >>> selectWords((\"Hello world\"), (4l))\n    // (new ArrayList<String>(Arrays.asList((String)\"world\")))\n    // >>> selectWords((\"Uncle sam\"), (3l))\n    // (new ArrayList<String>(Arrays.asList((String)\"Uncle\")))\n    public static ArrayList<String> selectWords(String s, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(selectWords((\"Mary had a little lamb\"), (4l)).equals((new ArrayList<String>(Arrays.asList((String)\"little\")))));\n    assert(selectWords((\"Mary had a little lamb\"), (3l)).equals((new ArrayList<String>(Arrays.asList((String)\"Mary\", (String)\"lamb\")))));\n    assert(selectWords((\"simple white space\"), (2l)).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(selectWords((\"Hello world\"), (4l)).equals((new ArrayList<String>(Arrays.asList((String)\"world\")))));\n    assert(selectWords((\"Uncle sam\"), (3l)).equals((new ArrayList<String>(Arrays.asList((String)\"Uncle\")))));\n    assert(selectWords((\"\"), (4l)).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(selectWords((\"a b c d e f\"), (1l)).equals((new ArrayList<String>(Arrays.asList((String)\"b\", (String)\"c\", (String)\"d\", (String)\"f\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_117_select_words"}
{"name": "HumanEval_72_will_it_fly", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that returns true if the object q will fly, and false otherwise.\n    // The object q will fly if it's balanced (it is a palindromic array list) and the sum of its elements is less than or equal the maximum possible weight w.\n    // Example:\n    // >>> willItFly((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l))), (5l))\n    // (false)\n    // # 1+2 is less than the maximum possible weight, but it's unbalanced.\n    // >>> willItFly((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)3l))), (1l))\n    // (false)\n    // # it's balanced, but 3+2+3 is more than the maximum possible weight.\n    // >>> willItFly((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)3l))), (9l))\n    // (true)\n    // # 3+2+3 is less than the maximum possible weight, and it's balanced.\n    // >>> willItFly((new ArrayList<Long>(Arrays.asList((long)3l))), (5l))\n    // (true)\n    // # 3 is less than the maximum possible weight, and it's balanced.\n    public static boolean willItFly(ArrayList<Long> q, long w) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)3l))), (9l)) == (true));\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l))), (5l)) == (false));\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)3l))), (5l)) == (true));\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)3l))), (1l)) == (false));\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))), (6l)) == (false));\n    assert(willItFly((new ArrayList<Long>(Arrays.asList((long)5l))), (5l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_72_will_it_fly"}
{"name": "HumanEval_55_fib", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return n-th Fibonacci number.\n    // >>> fib((10l))\n    // (55l)\n    // >>> fib((1l))\n    // (1l)\n    // >>> fib((8l))\n    // (21l)\n    public static long fib(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fib((10l)) == (55l));\n    assert(fib((1l)) == (1l));\n    assert(fib((8l)) == (21l));\n    assert(fib((11l)) == (89l));\n    assert(fib((12l)) == (144l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_55_fib"}
{"name": "HumanEval_153_Strongest_Extension", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You will be given the name of a class (a string) and an array array list of extensions.\n    // The extensions are to be used to load additional classes to the class. The\n    // strength of the extension is as follows: Let CAP be the number of the uppercase\n    // letters in the extension's name, and let SM be the number of lowercase letters \n    // in the extension's name, the strength is given by the fraction CAP - SM. \n    // You should find the strongest extension and return a string in this \n    // format: ClassName.StrongestExtensionName.\n    // If there are two or more extensions with the same strength, you should\n    // choose the one that comes first in the array list.\n    // For example, if you are given \"Slices\" as the class and an array array list of the\n    // extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n    // return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n    // (its strength is -1).\n    // Example:\n    // >>> StrongestExtension((\"my_class\"), (new ArrayList<String>(Arrays.asList((String)\"AA\", (String)\"Be\", (String)\"CC\"))))\n    // (\"my_class.AA\")\n    public static String StrongestExtension(String class_name, ArrayList<String> extensions) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(StrongestExtension((\"Watashi\"), (new ArrayList<String>(Arrays.asList((String)\"tEN\", (String)\"niNE\", (String)\"eIGHt8OKe\")))).equals((\"Watashi.eIGHt8OKe\")));\n    assert(StrongestExtension((\"Boku123\"), (new ArrayList<String>(Arrays.asList((String)\"nani\", (String)\"NazeDa\", (String)\"YEs.WeCaNe\", (String)\"32145tggg\")))).equals((\"Boku123.YEs.WeCaNe\")));\n    assert(StrongestExtension((\"__YESIMHERE\"), (new ArrayList<String>(Arrays.asList((String)\"t\", (String)\"eMptY\", (String)\"nothing\", (String)\"zeR00\", (String)\"NuLl__\", (String)\"123NoooneB321\")))).equals((\"__YESIMHERE.NuLl__\")));\n    assert(StrongestExtension((\"K\"), (new ArrayList<String>(Arrays.asList((String)\"Ta\", (String)\"TAR\", (String)\"t234An\", (String)\"cosSo\")))).equals((\"K.TAR\")));\n    assert(StrongestExtension((\"__HAHA\"), (new ArrayList<String>(Arrays.asList((String)\"Tab\", (String)\"123\", (String)\"781345\", (String)\"-_-\")))).equals((\"__HAHA.123\")));\n    assert(StrongestExtension((\"YameRore\"), (new ArrayList<String>(Arrays.asList((String)\"HhAas\", (String)\"okIWILL123\", (String)\"WorkOut\", (String)\"Fails\", (String)\"-_-\")))).equals((\"YameRore.okIWILL123\")));\n    assert(StrongestExtension((\"finNNalLLly\"), (new ArrayList<String>(Arrays.asList((String)\"Die\", (String)\"NowW\", (String)\"Wow\", (String)\"WoW\")))).equals((\"finNNalLLly.WoW\")));\n    assert(StrongestExtension((\"_\"), (new ArrayList<String>(Arrays.asList((String)\"Bb\", (String)\"91245\")))).equals((\"_.Bb\")));\n    assert(StrongestExtension((\"Sp\"), (new ArrayList<String>(Arrays.asList((String)\"671235\", (String)\"Bb\")))).equals((\"Sp.671235\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_153_Strongest_Extension"}
{"name": "HumanEval_119_match_parens", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given an array array list of two strings, both strings consist of open\n    // parentheses '(' or close parentheses ')' only.\n    // Your job is to check if it is possible to concatenate the two strings in\n    // some order, that the resulting string will be good.\n    // A string S is considered to be good if and only if all parentheses in S\n    // are balanced. For example: the string '(())()' is good, while the string\n    // '())' is not.\n    // Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n    // Examples:\n    // >>> matchParens((new ArrayList<String>(Arrays.asList((String)\"()(\", (String)\")\"))))\n    // (\"Yes\")\n    // >>> matchParens((new ArrayList<String>(Arrays.asList((String)\")\", (String)\")\"))))\n    // (\"No\")\n    public static String matchParens(ArrayList<String> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"()(\", (String)\")\")))).equals((\"Yes\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\")\", (String)\")\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"(()(())\", (String)\"())())\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\")())\", (String)\"(()()(\")))).equals((\"Yes\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"(())))\", (String)\"(()())((\")))).equals((\"Yes\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"()\", (String)\"())\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"(()(\", (String)\"()))()\")))).equals((\"Yes\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"((((\", (String)\"((())\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\")(()\", (String)\"(()(\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\")(\", (String)\")(\")))).equals((\"No\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\"(\", (String)\")\")))).equals((\"Yes\")));\n    assert(matchParens((new ArrayList<String>(Arrays.asList((String)\")\", (String)\"(\")))).equals((\"Yes\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_119_match_parens"}
{"name": "HumanEval_90_next_smallest", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given an array array list of integers.\n    // Write a function next_smallest() that returns the 2nd smallest element of the array list.\n    // Return null if there is no such element.\n    // >>> nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l))))\n    // Optional.of(2l)\n    // >>> nextSmallest((new ArrayList<Long>(Arrays.asList((long)5l, (long)1l, (long)4l, (long)3l, (long)2l))))\n    // Optional.of(2l)\n    // >>> nextSmallest((new ArrayList<Long>(Arrays.asList())))\n    // Optional.empty()\n    // >>> nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l))))\n    // Optional.empty()\n    public static Optional<Long> nextSmallest(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))).equals(Optional.of(2l)));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)5l, (long)1l, (long)4l, (long)3l, (long)2l)))).equals(Optional.of(2l)));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList()))).equals(Optional.empty()));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l)))).equals(Optional.empty()));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l, (long)0l)))).equals(Optional.of(1l)));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l)))).equals(Optional.empty()));\n    assert(nextSmallest((new ArrayList<Long>(Arrays.asList((long)-35l, (long)34l, (long)12l, (long)-45l)))).equals(Optional.of(-35l)));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_90_next_smallest"}
{"name": "HumanEval_92_any_int", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function that takes 3 numbers.\n    // Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n    // Returns false in any other cases.\n    // Examples\n    // >>> anyInt((float)5l, (float)2l, (float)7l)\n    // (true)\n    // >>> anyInt((float)3l, (float)2l, (float)2l)\n    // (false)\n    // >>> anyInt((float)3l, (float)-2l, (float)1l)\n    // (true)\n    // >>> anyInt((3.6f), (-2.2f), (float)2l)\n    // (false)\n    public static boolean anyInt(float x, float y, float z) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(anyInt((float)2l, (float)3l, (float)1l) == (true));\n    assert(anyInt((2.5f), (float)2l, (float)3l) == (false));\n    assert(anyInt((1.5f), (float)5l, (3.5f)) == (false));\n    assert(anyInt((float)2l, (float)6l, (float)2l) == (false));\n    assert(anyInt((float)4l, (float)2l, (float)2l) == (true));\n    assert(anyInt((2.2f), (2.2f), (2.2f)) == (false));\n    assert(anyInt((float)-4l, (float)6l, (float)2l) == (true));\n    assert(anyInt((float)2l, (float)1l, (float)1l) == (true));\n    assert(anyInt((float)3l, (float)4l, (float)7l) == (true));\n    assert(anyInt((3.0f), (float)4l, (float)7l) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_92_any_int"}
{"name": "HumanEval_2_truncate_number", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive floating point number, it can be decomposed into\n    // and integer part (largest integer smaller than given number) and decimals\n    // (leftover part always smaller than 1).\n    // Return the decimal part of the number.\n    // >>> truncateNumber((3.5f))\n    // (0.5f)\n    public static float truncateNumber(float number) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(truncateNumber((3.5f)) == (0.5f));\n    assert(truncateNumber((1.25f)) == (0.25f));\n    assert(truncateNumber((123.0f)) == (0.0f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_2_truncate_number"}
{"name": "HumanEval_42_incr_list", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return array list with elements incremented by 1.\n    // >>> incrList((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l)))\n    // >>> incrList((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)5l, (long)2l, (long)3l, (long)3l, (long)9l, (long)0l, (long)123l))))\n    // (new ArrayList<Long>(Arrays.asList((long)6l, (long)4l, (long)6l, (long)3l, (long)4l, (long)4l, (long)10l, (long)1l, (long)124l)))\n    public static ArrayList<Long> incrList(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(incrList((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(incrList((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)3l, (long)2l)))));\n    assert(incrList((new ArrayList<Long>(Arrays.asList((long)5l, (long)2l, (long)5l, (long)2l, (long)3l, (long)3l, (long)9l, (long)0l, (long)123l)))).equals((new ArrayList<Long>(Arrays.asList((long)6l, (long)3l, (long)6l, (long)3l, (long)4l, (long)4l, (long)10l, (long)1l, (long)124l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_42_incr_list"}
{"name": "HumanEval_150_x_or_y", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // A simple program which should return the value of x if n is \n    // a prime number and should return the value of y otherwise.\n    // Examples:\n    // >>> xOrY((7l), (34l), (12l))\n    // (34l)\n    // >>> xOrY((15l), (8l), (5l))\n    // (5l)\n    public static long xOrY(long n, long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(xOrY((7l), (34l), (12l)) == (34l));\n    assert(xOrY((15l), (8l), (5l)) == (5l));\n    assert(xOrY((3l), (33l), (5212l)) == (33l));\n    assert(xOrY((1259l), (3l), (52l)) == (3l));\n    assert(xOrY((7919l), (-1l), (12l)) == (-1l));\n    assert(xOrY((3609l), (1245l), (583l)) == (583l));\n    assert(xOrY((91l), (56l), (129l)) == (129l));\n    assert(xOrY((6l), (34l), (1234l)) == (1234l));\n    assert(xOrY((1l), (2l), (0l)) == (0l));\n    assert(xOrY((2l), (2l), (0l)) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_150_x_or_y"}
{"name": "HumanEval_49_modp", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return 2^n modulo p (be aware of numerics).\n    // >>> modp((3l), (5l))\n    // (3l)\n    // >>> modp((1101l), (101l))\n    // (2l)\n    // >>> modp((0l), (101l))\n    // (1l)\n    // >>> modp((3l), (11l))\n    // (8l)\n    // >>> modp((100l), (101l))\n    // (1l)\n    public static long modp(long n, long p) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(modp((3l), (5l)) == (3l));\n    assert(modp((1101l), (101l)) == (2l));\n    assert(modp((0l), (101l)) == (1l));\n    assert(modp((3l), (11l)) == (8l));\n    assert(modp((100l), (101l)) == (1l));\n    assert(modp((30l), (5l)) == (4l));\n    assert(modp((31l), (5l)) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_49_modp"}
{"name": "HumanEval_155_even_odd_count", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an integer. return a pair that has the number of even and odd digits respectively.\n    // Example:\n    // >>> evenOddCount((-12l))\n    // (Pair.with(1l, 1l))\n    // >>> evenOddCount((123l))\n    // (Pair.with(1l, 2l))\n    public static Pair<Long, Long> evenOddCount(long num) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(evenOddCount((7l)).equals((Pair.with(0l, 1l))));\n    assert(evenOddCount((-78l)).equals((Pair.with(1l, 1l))));\n    assert(evenOddCount((3452l)).equals((Pair.with(2l, 2l))));\n    assert(evenOddCount((346211l)).equals((Pair.with(3l, 3l))));\n    assert(evenOddCount((-345821l)).equals((Pair.with(3l, 3l))));\n    assert(evenOddCount((-2l)).equals((Pair.with(1l, 0l))));\n    assert(evenOddCount((-45347l)).equals((Pair.with(2l, 3l))));\n    assert(evenOddCount((0l)).equals((Pair.with(1l, 0l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_155_even_odd_count"}
{"name": "HumanEval_80_is_happy", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a string s.\n    // Your task is to check if the string is hapjava or not.\n    // A string is hapjava if its length is at least 3 and every 3 consecutive letters are distinct\n    // For example:\n    // >>> isHappy((\"a\"))\n    // (false)\n    // >>> isHappy((\"aa\"))\n    // (false)\n    // >>> isHappy((\"abcd\"))\n    // (true)\n    // >>> isHappy((\"aabb\"))\n    // (false)\n    // >>> isHappy((\"adb\"))\n    // (true)\n    // >>> isHappy((\"xyy\"))\n    // (false)\n    public static boolean isHappy(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isHappy((\"a\")) == (false));\n    assert(isHappy((\"aa\")) == (false));\n    assert(isHappy((\"abcd\")) == (true));\n    assert(isHappy((\"aabb\")) == (false));\n    assert(isHappy((\"adb\")) == (true));\n    assert(isHappy((\"xyy\")) == (false));\n    assert(isHappy((\"iopaxpoi\")) == (true));\n    assert(isHappy((\"iopaxioi\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_80_is_happy"}
{"name": "HumanEval_59_largest_prime_factor", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return the largest prime factor of n. Assume n > 1 and is not a prime.\n    // >>> largestPrimeFactor((13195l))\n    // (29l)\n    // >>> largestPrimeFactor((2048l))\n    // (2l)\n    public static long largestPrimeFactor(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(largestPrimeFactor((15l)) == (5l));\n    assert(largestPrimeFactor((27l)) == (3l));\n    assert(largestPrimeFactor((63l)) == (7l));\n    assert(largestPrimeFactor((330l)) == (11l));\n    assert(largestPrimeFactor((13195l)) == (29l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_59_largest_prime_factor"}
{"name": "HumanEval_66_digitSum", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Task\n    // Write a function that takes a string as input and returns the sum of the upper characters only'\n    // ASCII codes.\n    // Examples:\n    // >>> digitSum((\"\"))\n    // (0l)\n    // >>> digitSum((\"abAB\"))\n    // (131l)\n    // >>> digitSum((\"abcCd\"))\n    // (67l)\n    // >>> digitSum((\"helloE\"))\n    // (69l)\n    // >>> digitSum((\"woArBld\"))\n    // (131l)\n    // >>> digitSum((\"aAaaaXa\"))\n    // (153l)\n    public static long digitSum(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(digitSum((\"\")) == (0l));\n    assert(digitSum((\"abAB\")) == (131l));\n    assert(digitSum((\"abcCd\")) == (67l));\n    assert(digitSum((\"helloE\")) == (69l));\n    assert(digitSum((\"woArBld\")) == (131l));\n    assert(digitSum((\"aAaaaXa\")) == (153l));\n    assert(digitSum((\" How are yOu?\")) == (151l));\n    assert(digitSum((\"You arE Very Smart\")) == (327l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_66_digitSum"}
{"name": "HumanEval_21_rescale_to_unit", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given array list of numbers (of at least two elements), apply a linear transform to that array list,\n    // such that the smallest number will become 0 and the largest will become 1\n    // >>> rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f))))\n    // (new ArrayList<Float>(Arrays.asList((float)0.0f, (float)0.25f, (float)0.5f, (float)0.75f, (float)1.0f)))\n    public static ArrayList<Float> rescaleToUnit(ArrayList<Float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)2.0f, (float)49.9f)))).equals((new ArrayList<Float>(Arrays.asList((float)0.0f, (float)1.0f)))));\n    assert(rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)100.0f, (float)49.9f)))).equals((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)0.0f)))));\n    assert(rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f)))).equals((new ArrayList<Float>(Arrays.asList((float)0.0f, (float)0.25f, (float)0.5f, (float)0.75f, (float)1.0f)))));\n    assert(rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)2.0f, (float)1.0f, (float)5.0f, (float)3.0f, (float)4.0f)))).equals((new ArrayList<Float>(Arrays.asList((float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f)))));\n    assert(rescaleToUnit((new ArrayList<Float>(Arrays.asList((float)12.0f, (float)11.0f, (float)15.0f, (float)13.0f, (float)14.0f)))).equals((new ArrayList<Float>(Arrays.asList((float)0.25f, (float)0.0f, (float)1.0f, (float)0.5f, (float)0.75f)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_21_rescale_to_unit"}
{"name": "HumanEval_121_solution", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a non-empty array list of integers, return the sum of all of the odd elements that are in even positions.\n    // Examples\n    // >>> solution((new ArrayList<Long>(Arrays.asList((long)5l, (long)8l, (long)7l, (long)1l))))\n    // (12l)\n    // >>> solution((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)3l, (long)3l, (long)3l))))\n    // (9l)\n    // >>> solution((new ArrayList<Long>(Arrays.asList((long)30l, (long)13l, (long)24l, (long)321l))))\n    // (0l)\n    public static long solution(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)5l, (long)8l, (long)7l, (long)1l)))) == (12l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)3l, (long)3l, (long)3l)))) == (9l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)30l, (long)13l, (long)24l, (long)321l)))) == (0l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)5l, (long)9l)))) == (5l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)8l)))) == (0l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)30l, (long)13l, (long)23l, (long)32l)))) == (23l));\n    assert(solution((new ArrayList<Long>(Arrays.asList((long)3l, (long)13l, (long)2l, (long)9l)))) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_121_solution"}
{"name": "HumanEval_68_pluck", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // \"Given an array array list representing a branch of a tree that has non-negative integer nodes\n    // your task is to pluck one of the nodes and return it.\n    // The plucked node should be the node with the smallest even value.\n    // If multiple nodes with the same smallest even value are found return the node that has smallest index.\n    // The plucked node should be returned in an array array list, [ smalest_value, its index ],\n    // If there are no even values or the given array array list is empty, return [].\n    // Example 1:\n    // >>> pluck((new ArrayList<Long>(Arrays.asList((long)4l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)1l)))\n    // Explanation: 2 has the smallest even value, and 2 has the smallest index.\n    // Example 2:\n    // >>> pluck((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)1l)))\n    // Explanation: 2 has the smallest even value, and 2 has the smallest index.\n    // Example 3:\n    // >>> pluck((new ArrayList<Long>(Arrays.asList())))\n    // (new ArrayList<Long>(Arrays.asList()))\n    // Example 4:\n    // >>> pluck((new ArrayList<Long>(Arrays.asList((long)5l, (long)0l, (long)3l, (long)0l, (long)4l, (long)2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)0l, (long)1l)))\n    // Explanation: 0 is the smallest value, but  there are two zeros,\n    // so we will choose the first zero, which has the smallest index.\n    // Constraints:\n    // * 1 <= nodes.length <= 10000\n    // * 0 <= node.value\n    public static ArrayList<Long> pluck(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)4l, (long)2l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)1l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)5l, (long)0l, (long)3l, (long)0l, (long)4l, (long)2l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)0l, (long)5l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)3l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)5l, (long)4l, (long)8l, (long)4l, (long)8l)))).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)7l, (long)6l, (long)7l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)6l, (long)1l)))));\n    assert(pluck((new ArrayList<Long>(Arrays.asList((long)7l, (long)9l, (long)7l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_68_pluck"}
{"name": "HumanEval_147_get_max_triples", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a positive integer n. You have to create an integer array array list a of length n.\n    // For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n    // Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n    // and a[i] + a[j] + a[k] is a multiple of 3.\n    // Example :\n    // >>> getMaxTriples((5l))\n    // (1l)\n    // Explanation: \n    // a = [1, 3, 7, 13, 21]\n    // The only valid triple is (1, 7, 13).\n    public static long getMaxTriples(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(getMaxTriples((5l)) == (1l));\n    assert(getMaxTriples((6l)) == (4l));\n    assert(getMaxTriples((10l)) == (36l));\n    assert(getMaxTriples((100l)) == (53361l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_147_get_max_triples"}
{"name": "HumanEval_110_exchange", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // In this problem, you will implement a function that takes two array lists of numbers,\n    // and determines whether it is possible to perform an exchange of elements\n    // between them to make lst1 an array array list of only even numbers.\n    // There is no limit on the number of exchanged elements between lst1 and lst2.\n    // If it is possible to exchange elements between the lst1 and lst2 to make\n    // all the elements of lst1 to be even, return \"YES\".\n    // Otherwise, return \"NO\".\n    // For example:\n    // >>> exchange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))))\n    // (\"YES\")\n    // >>> exchange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)3l, (long)4l))))\n    // (\"NO\")\n    // It is assumed that the input array lists will be non-empty.\n    public static String exchange(ArrayList<Long> lst1, ArrayList<Long> lst2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))).equals((\"YES\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)3l, (long)4l)))).equals((\"NO\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)2l, (long)1l, (long)4l, (long)3l)))).equals((\"YES\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)3l))), (new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)4l)))).equals((\"YES\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)3l))), (new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)3l)))).equals((\"NO\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)6l, (long)1l, (long)8l, (long)9l))), (new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)5l, (long)1l, (long)1l, (long)1l)))).equals((\"NO\")));\n    assert(exchange((new ArrayList<Long>(Arrays.asList((long)100l, (long)200l))), (new ArrayList<Long>(Arrays.asList((long)200l, (long)200l)))).equals((\"YES\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_110_exchange"}
{"name": "HumanEval_47_median", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return median of elements in the array list l.\n    // >>> median((new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)2l, (long)4l, (long)5l))))\n    // (float)3l\n    // >>> median((new ArrayList<Long>(Arrays.asList((long)-10l, (long)4l, (long)6l, (long)1000l, (long)10l, (long)20l))))\n    // (15.0f)\n    public static float median(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(median((new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)2l, (long)4l, (long)5l)))) == (float)3l);\n    assert(median((new ArrayList<Long>(Arrays.asList((long)-10l, (long)4l, (long)6l, (long)1000l, (long)10l, (long)20l)))) == (8.0f));\n    assert(median((new ArrayList<Long>(Arrays.asList((long)5l)))) == (float)5l);\n    assert(median((new ArrayList<Long>(Arrays.asList((long)6l, (long)5l)))) == (5.5f));\n    assert(median((new ArrayList<Long>(Arrays.asList((long)8l, (long)1l, (long)3l, (long)9l, (long)9l, (long)2l, (long)7l)))) == (float)7l);\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_47_median"}
{"name": "HumanEval_82_prime_length", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that takes a string and returns true if the string\n    // length is a prime number or false otherwise\n    // Examples\n    // >>> primeLength((\"Hello\"))\n    // (true)\n    // >>> primeLength((\"abcdcba\"))\n    // (true)\n    // >>> primeLength((\"kittens\"))\n    // (true)\n    // >>> primeLength((\"orange\"))\n    // (false)\n    public static boolean primeLength(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(primeLength((\"Hello\")) == (true));\n    assert(primeLength((\"abcdcba\")) == (true));\n    assert(primeLength((\"kittens\")) == (true));\n    assert(primeLength((\"orange\")) == (false));\n    assert(primeLength((\"wow\")) == (true));\n    assert(primeLength((\"world\")) == (true));\n    assert(primeLength((\"MadaM\")) == (true));\n    assert(primeLength((\"Wow\")) == (true));\n    assert(primeLength((\"\")) == (false));\n    assert(primeLength((\"HI\")) == (true));\n    assert(primeLength((\"go\")) == (true));\n    assert(primeLength((\"gogo\")) == (false));\n    assert(primeLength((\"aaaaaaaaaaaaaaa\")) == (false));\n    assert(primeLength((\"Madam\")) == (true));\n    assert(primeLength((\"M\")) == (false));\n    assert(primeLength((\"0\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_82_prime_length"}
{"name": "HumanEval_73_smallest_change", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list arr of integers, find the minimum number of elements that\n    // need to be changed to make the array array list palindromic. A palindromic array array list is an array array list that\n    // is read the same backwards and forwards. In one change, you can change one element to any other element.\n    // For example:\n    // >>> smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)5l, (long)4l, (long)7l, (long)9l, (long)6l))))\n    // (4l)\n    // >>> smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)3l, (long)2l, (long)2l))))\n    // (1l)\n    // >>> smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)1l))))\n    // (0l)\n    public static long smallestChange(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)5l, (long)4l, (long)7l, (long)9l, (long)6l)))) == (4l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)3l, (long)2l, (long)2l)))) == (1l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)2l)))) == (1l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)4l, (long)2l)))) == (1l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)1l)))) == (0l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)3l, (long)1l, (long)1l, (long)3l)))) == (0l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)1l)))) == (0l));\n    assert(smallestChange((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l)))) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_73_smallest_change"}
{"name": "HumanEval_133_sum_squares", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given an array array list of numbers.\n    // You need to return the sum of squared numbers in the given array list,\n    // round each element in the array list to the upper int(Ceiling) first.\n    // Examples:\n    // >>> lst((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f))))\n    // (14l)\n    // >>> lst((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)4.0f, (float)9.0f))))\n    // (98l)\n    // >>> lst((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f))))\n    // (84l)\n    // >>> lst((new ArrayList<Float>(Arrays.asList((float)1.4f, (float)4.2f, (float)0.0f))))\n    // (29l)\n    // >>> lst((new ArrayList<Float>(Arrays.asList((float)-2.4f, (float)1.0f, (float)1.0f))))\n    // (6l)\n    public static long sumSquares(ArrayList<Float> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f)))) == (14l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f)))) == (14l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)3.0f, (float)5.0f, (float)7.0f)))) == (84l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)1.4f, (float)4.2f, (float)0.0f)))) == (29l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)-2.4f, (float)1.0f, (float)1.0f)))) == (6l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)100.0f, (float)1.0f, (float)15.0f, (float)2.0f)))) == (10230l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)10000.0f, (float)10000.0f)))) == (200000000l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)-1.4f, (float)4.6f, (float)6.3f)))) == (75l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)-1.4f, (float)17.9f, (float)18.9f, (float)19.9f)))) == (1086l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)0.0f)))) == (0l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)-1.0f)))) == (1l));\n    assert(sumSquares((new ArrayList<Float>(Arrays.asList((float)-1.0f, (float)1.0f, (float)0.0f)))) == (2l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_133_sum_squares"}
{"name": "HumanEval_141_file_name_check", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function which takes a string representing a file's name, and returns\n    // 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n    // A file's name is considered to be valid if and only if all the following conditions \n    // are met:\n    // - There should not be more than three digits ('0'-'9') in the file's name.\n    // - The file's name contains exactly one dot '.'\n    // - The substring before the dot should not be empty, and it starts with a letter from \n    // the latin alphapet ('a'-'z' and 'A'-'Z').\n    // - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n    // Examples:\n    // >>> fileNameCheck((\"example.txt\"))\n    // (\"Yes\")\n    // >>> fileNameCheck((\"1example.dll\"))\n    // (\"No\")\n    public static String fileNameCheck(String file_name) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fileNameCheck((\"example.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1example.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"s1sdf3.asd\")).equals((\"No\")));\n    assert(fileNameCheck((\"K.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"MY16FILE3.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"His12FILE94.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"_Y.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"?aREYA.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"/this_is_valid.dll\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.wow\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_valid.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"this_is_valid.txtexe\")).equals((\"No\")));\n    assert(fileNameCheck((\"#this2_i4s_5valid.ten\")).equals((\"No\")));\n    assert(fileNameCheck((\"@this1_is6_valid.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"this_is_12valid.6exe4.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"all.exe.txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_No.exe\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"Is3youfault.txt\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"no_one#knows.dll\")).equals((\"Yes\")));\n    assert(fileNameCheck((\"1I563_Yes3.exe\")).equals((\"No\")));\n    assert(fileNameCheck((\"I563_Yes3.txtt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final..txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"final132\")).equals((\"No\")));\n    assert(fileNameCheck((\"_f4indsartal132.\")).equals((\"No\")));\n    assert(fileNameCheck((\".txt\")).equals((\"No\")));\n    assert(fileNameCheck((\"s.\")).equals((\"No\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_141_file_name_check"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // triples_sum_to_zero takes an array array list of integers as an input.\n    // it returns true if there are three distinct elements in the array list that\n    // sum to zero, and false otherwise.\n    // >>> triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)0l))))\n    // (false)\n    // >>> triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)-2l, (long)1l))))\n    // (true)\n    // >>> triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)7l))))\n    // (false)\n    // >>> triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)-5l, (long)3l, (long)9l, (long)7l))))\n    // (true)\n    // >>> triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l))))\n    // (false)\n    public static boolean triplesSumToZero(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)0l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)-1l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)-2l, (long)1l)))) == (true));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)7l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)5l, (long)7l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)-5l, (long)3l, (long)9l, (long)7l)))) == (true));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l, (long)-100l)))) == (false));\n    assert(triplesSumToZero((new ArrayList<Long>(Arrays.asList((long)100l, (long)3l, (long)5l, (long)-100l)))) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_40_triples_sum_to_zero"}
{"name": "HumanEval_127_intersection", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given two intervals,\n    // where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n    // The given intervals are closed which means that the interval (start, end)\n    // includes both start and end.\n    // For each given interval, it is assumed that its start is less or equal its end.\n    // Your task is to determine whether the length of intersection of these two \n    // intervals is a prime number.\n    // Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n    // which its length is 1, which not a prime number.\n    // If the length of the intersection is a prime number, return \"YES\",\n    // otherwise, return \"NO\".\n    // If the two intervals don't intersect, return \"NO\".\n    // [input/output] samples:\n    // >>> intersection((Pair.with(1l, 2l)), (Pair.with(2l, 3l)))\n    // (\"NO\")\n    // >>> intersection((Pair.with(-1l, 1l)), (Pair.with(0l, 4l)))\n    // (\"NO\")\n    // >>> intersection((Pair.with(-3l, -1l)), (Pair.with(-5l, 5l)))\n    // (\"YES\")\n    public static String intersection(Pair<Long, Long> interval1, Pair<Long, Long> interval2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(intersection((Pair.with(1l, 2l)), (Pair.with(2l, 3l))).equals((\"NO\")));\n    assert(intersection((Pair.with(-1l, 1l)), (Pair.with(0l, 4l))).equals((\"NO\")));\n    assert(intersection((Pair.with(-3l, -1l)), (Pair.with(-5l, 5l))).equals((\"YES\")));\n    assert(intersection((Pair.with(-2l, 2l)), (Pair.with(-4l, 0l))).equals((\"YES\")));\n    assert(intersection((Pair.with(-11l, 2l)), (Pair.with(-1l, -1l))).equals((\"NO\")));\n    assert(intersection((Pair.with(1l, 2l)), (Pair.with(3l, 5l))).equals((\"NO\")));\n    assert(intersection((Pair.with(1l, 2l)), (Pair.with(1l, 2l))).equals((\"NO\")));\n    assert(intersection((Pair.with(-2l, -2l)), (Pair.with(-3l, -2l))).equals((\"NO\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_127_intersection"}
{"name": "HumanEval_1_separate_paren_groups", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n    // separate those group into separate strings and return the array list of those.\n    // Separate groups are balanced (each open brace is properly closed) and not nested within each other\n    // Ignore any spaces in the input string.\n    // >>> separateParenGroups((\"( ) (( )) (( )( ))\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"()\", (String)\"(())\", (String)\"(()())\")))\n    public static ArrayList<String> separateParenGroups(String paren_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(separateParenGroups((\"(()()) ((())) () ((())()())\")).equals((new ArrayList<String>(Arrays.asList((String)\"(()())\", (String)\"((()))\", (String)\"()\", (String)\"((())()())\")))));\n    assert(separateParenGroups((\"() (()) ((())) (((())))\")).equals((new ArrayList<String>(Arrays.asList((String)\"()\", (String)\"(())\", (String)\"((()))\", (String)\"(((())))\")))));\n    assert(separateParenGroups((\"(()(())((())))\")).equals((new ArrayList<String>(Arrays.asList((String)\"(()(())((())))\")))));\n    assert(separateParenGroups((\"( ) (( )) (( )( ))\")).equals((new ArrayList<String>(Arrays.asList((String)\"()\", (String)\"(())\", (String)\"(()())\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_1_separate_paren_groups"}
{"name": "HumanEval_152_compare", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // I think we all remember that feeling when the result of some long-awaited\n    // event is finally known. The feelings and thoughts you have at that moment are\n    // definitely worth noting down and comparing.\n    // Your task is to determine if a person correctly guessed the results of a number of matches.\n    // You are given two array array lists of scores and guesses of equal length, where each index shows a match. \n    // Return an array array list of the same length denoting how far off each guess was. If they have guessed correctly,\n    // the value is 0, and if not, the value is the absolute difference between the guess and the score.\n    // example:\n    // >>> compare((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)1l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)2l, (long)-2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l, (long)3l, (long)3l)))\n    // >>> compare((new ArrayList<Long>(Arrays.asList((long)0l, (long)5l, (long)0l, (long)0l, (long)0l, (long)4l))), (new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)1l, (long)0l, (long)0l, (long)-2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)4l, (long)4l, (long)1l, (long)0l, (long)0l, (long)6l)))\n    public static ArrayList<Long> compare(ArrayList<Long> game, ArrayList<Long> guess) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(compare((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)1l))), (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)2l, (long)-2l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l, (long)3l, (long)3l)))));\n    assert(compare((new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l))), (new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l)))));\n    assert(compare((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))), (new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)-3l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l)))));\n    assert(compare((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)5l))), (new ArrayList<Long>(Arrays.asList((long)-1l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)0l, (long)0l, (long)1l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_152_compare"}
{"name": "HumanEval_83_starts_one_ends", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer n, return the count of the numbers of n-digit\n    // positive integers that start or end with 1.\n    public static long startsOneEnds(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(startsOneEnds((1l)) == (1l));\n    assert(startsOneEnds((2l)) == (18l));\n    assert(startsOneEnds((3l)) == (180l));\n    assert(startsOneEnds((4l)) == (1800l));\n    assert(startsOneEnds((5l)) == (18000l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_83_starts_one_ends"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function that returns true if the last character\n    // of a given string is an alphabetical character and is not\n    // a part of a word, and false otherwise.\n    // Note: \"word\" is a group of characters separated by space.\n    // Examples:\n    // >>> checkIfLastCharIsALetter((\"apple pie\"))\n    // (false)\n    // >>> checkIfLastCharIsALetter((\"apple pi e\"))\n    // (true)\n    // >>> checkIfLastCharIsALetter((\"apple pi e \"))\n    // (false)\n    // >>> checkIfLastCharIsALetter((\"\"))\n    // (false)\n    public static boolean checkIfLastCharIsALetter(String txt) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(checkIfLastCharIsALetter((\"apple\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e\")) == (true));\n    assert(checkIfLastCharIsALetter((\"eeeee\")) == (false));\n    assert(checkIfLastCharIsALetter((\"A\")) == (true));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie \")) == (false));\n    assert(checkIfLastCharIsALetter((\"Pumpkin pie 1\")) == (false));\n    assert(checkIfLastCharIsALetter((\"\")) == (false));\n    assert(checkIfLastCharIsALetter((\"eeeee e \")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pie\")) == (false));\n    assert(checkIfLastCharIsALetter((\"apple pi e \")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter"}
{"name": "HumanEval_124_valid_date", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You have to write a function which validates a given date string and\n    // returns true if the date is valid otherwise false.\n    // The date is valid if all of the following rules are satisfied:\n    // 1. The date string is not empty.\n    // 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n    // 3. The months should not be less than 1 or higher than 12.\n    // 4. The date should be in the format: mm-dd-yyyy\n    // >>> validDate((\"03-11-2000\"))\n    // (true)\n    // >>> validDate((\"15-01-2012\"))\n    // (false)\n    // >>> validDate((\"04-0-2040\"))\n    // (false)\n    // >>> validDate((\"06-04-2020\"))\n    // (true)\n    // >>> validDate((\"06/04/2020\"))\n    // (false)\n    public static boolean validDate(String date) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(validDate((\"03-11-2000\")) == (true));\n    assert(validDate((\"15-01-2012\")) == (false));\n    assert(validDate((\"04-0-2040\")) == (false));\n    assert(validDate((\"06-04-2020\")) == (true));\n    assert(validDate((\"01-01-2007\")) == (true));\n    assert(validDate((\"03-32-2011\")) == (false));\n    assert(validDate((\"\")) == (false));\n    assert(validDate((\"04-31-3000\")) == (false));\n    assert(validDate((\"06-06-2005\")) == (true));\n    assert(validDate((\"21-31-2000\")) == (false));\n    assert(validDate((\"04-12-2003\")) == (true));\n    assert(validDate((\"04122003\")) == (false));\n    assert(validDate((\"20030412\")) == (false));\n    assert(validDate((\"2003-04\")) == (false));\n    assert(validDate((\"2003-04-12\")) == (false));\n    assert(validDate((\"04-2003\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_124_valid_date"}
{"name": "HumanEval_108_count_nums", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function count_nums which takes an array array list of integers and returns\n    // the number of elements which has a sum of digits > 0.\n    // If a number is negative, then its first signed digit will be negative:\n    // e.g. -123 has signed digits -1, 2, and 3.\n    // >>> countNums((new ArrayList<Long>(Arrays.asList())))\n    // (0l)\n    // >>> countNums((new ArrayList<Long>(Arrays.asList((long)-1l, (long)11l, (long)-11l))))\n    // (1l)\n    // >>> countNums((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)2l))))\n    // (3l)\n    public static long countNums(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(countNums((new ArrayList<Long>(Arrays.asList()))) == (0l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)0l)))) == (0l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)2l, (long)-2l, (long)3l, (long)4l, (long)5l)))) == (6l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)1l, (long)6l, (long)9l, (long)-6l, (long)0l, (long)1l, (long)5l)))) == (5l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)1l, (long)100l, (long)98l, (long)-7l, (long)1l, (long)-1l)))) == (4l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)12l, (long)23l, (long)34l, (long)-45l, (long)-56l, (long)0l)))) == (5l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l)))) == (1l));\n    assert(countNums((new ArrayList<Long>(Arrays.asList((long)1l)))) == (1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_108_count_nums"}
{"name": "HumanEval_86_anti_shuffle", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that takes a string and returns an ordered version of it.\n    // Ordered version of string, is a string where all words (separated by space)\n    // are replaced by a new word where all the characters arranged in\n    // ascending order based on ascii value.\n    // Note: You should keep the order of words and blank spaces in the sentence.\n    // For example:\n    // >>> antiShuffle((\"Hi\"))\n    // (\"Hi\")\n    // >>> antiShuffle((\"hello\"))\n    // (\"ehllo\")\n    // >>> antiShuffle((\"Hello World!!!\"))\n    // (\"Hello !!!Wdlor\")\n    public static String antiShuffle(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(antiShuffle((\"Hi\")).equals((\"Hi\")));\n    assert(antiShuffle((\"hello\")).equals((\"ehllo\")));\n    assert(antiShuffle((\"number\")).equals((\"bemnru\")));\n    assert(antiShuffle((\"abcd\")).equals((\"abcd\")));\n    assert(antiShuffle((\"Hello World!!!\")).equals((\"Hello !!!Wdlor\")));\n    assert(antiShuffle((\"\")).equals((\"\")));\n    assert(antiShuffle((\"Hi. My name is Mister Robot. How are you?\")).equals((\".Hi My aemn is Meirst .Rboot How aer ?ouy\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_86_anti_shuffle"}
{"name": "HumanEval_48_is_palindrome", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Checks if given string is a palindrome\n    // >>> isPalindrome((\"\"))\n    // (true)\n    // >>> isPalindrome((\"aba\"))\n    // (true)\n    // >>> isPalindrome((\"aaaaa\"))\n    // (true)\n    // >>> isPalindrome((\"zbcd\"))\n    // (false)\n    public static boolean isPalindrome(String text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isPalindrome((\"\")) == (true));\n    assert(isPalindrome((\"aba\")) == (true));\n    assert(isPalindrome((\"aaaaa\")) == (true));\n    assert(isPalindrome((\"zbcd\")) == (false));\n    assert(isPalindrome((\"xywyx\")) == (true));\n    assert(isPalindrome((\"xywyz\")) == (false));\n    assert(isPalindrome((\"xywzx\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_48_is_palindrome"}
{"name": "HumanEval_118_get_closest_vowel", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a word. Your task is to find the closest vowel that stands between \n    // two consonants from the right side of the word (case sensitive).\n    // Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n    // find any vowel met the above condition. \n    // You may assume that the given string contains English letter only.\n    // Example:\n    // >>> getClosestVowel((\"yogurt\"))\n    // (\"u\")\n    // >>> getClosestVowel((\"FULL\"))\n    // (\"U\")\n    // >>> getClosestVowel((\"quick\"))\n    // (\"\")\n    // >>> getClosestVowel((\"ab\"))\n    // (\"\")\n    public static String getClosestVowel(String word) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(getClosestVowel((\"yogurt\")).equals((\"u\")));\n    assert(getClosestVowel((\"full\")).equals((\"u\")));\n    assert(getClosestVowel((\"easy\")).equals((\"\")));\n    assert(getClosestVowel((\"eAsy\")).equals((\"\")));\n    assert(getClosestVowel((\"ali\")).equals((\"\")));\n    assert(getClosestVowel((\"bad\")).equals((\"a\")));\n    assert(getClosestVowel((\"most\")).equals((\"o\")));\n    assert(getClosestVowel((\"ab\")).equals((\"\")));\n    assert(getClosestVowel((\"ba\")).equals((\"\")));\n    assert(getClosestVowel((\"quick\")).equals((\"\")));\n    assert(getClosestVowel((\"anime\")).equals((\"i\")));\n    assert(getClosestVowel((\"Asia\")).equals((\"\")));\n    assert(getClosestVowel((\"Above\")).equals((\"o\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_118_get_closest_vowel"}
{"name": "HumanEval_31_is_prime", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return true if a given number is prime, and false otherwise.\n    // >>> isPrime((6l))\n    // (false)\n    // >>> isPrime((101l))\n    // (true)\n    // >>> isPrime((11l))\n    // (true)\n    // >>> isPrime((13441l))\n    // (true)\n    // >>> isPrime((61l))\n    // (true)\n    // >>> isPrime((4l))\n    // (false)\n    // >>> isPrime((1l))\n    // (false)\n    public static boolean isPrime(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isPrime((6l)) == (false));\n    assert(isPrime((101l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((13441l)) == (true));\n    assert(isPrime((61l)) == (true));\n    assert(isPrime((4l)) == (false));\n    assert(isPrime((1l)) == (false));\n    assert(isPrime((5l)) == (true));\n    assert(isPrime((11l)) == (true));\n    assert(isPrime((17l)) == (true));\n    assert(isPrime((85l)) == (false));\n    assert(isPrime((77l)) == (false));\n    assert(isPrime((255379l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_31_is_prime"}
{"name": "HumanEval_144_simplify", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Your task is to implement a function that will simplify the expression\n    // x * n. The function returns true if x * n evaluates to a whole number and false\n    // otherwise. Both x and n, are string representation of a fraction, and have the following format,\n    // <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n    // You can assume that x, and n are valid fractions, and do not have zero as denominator.\n    // >>> simplify((\"1/5\"), (\"5/1\"))\n    // (true)\n    // >>> simplify((\"1/6\"), (\"2/1\"))\n    // (false)\n    // >>> simplify((\"7/10\"), (\"10/2\"))\n    // (false)\n    public static boolean simplify(String x, String n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/6\"), (\"2/1\")) == (false));\n    assert(simplify((\"5/1\"), (\"3/1\")) == (true));\n    assert(simplify((\"7/10\"), (\"10/2\")) == (false));\n    assert(simplify((\"2/10\"), (\"50/10\")) == (true));\n    assert(simplify((\"7/2\"), (\"4/2\")) == (true));\n    assert(simplify((\"11/6\"), (\"6/1\")) == (true));\n    assert(simplify((\"2/3\"), (\"5/2\")) == (false));\n    assert(simplify((\"5/2\"), (\"3/5\")) == (false));\n    assert(simplify((\"2/4\"), (\"8/4\")) == (true));\n    assert(simplify((\"2/4\"), (\"4/2\")) == (true));\n    assert(simplify((\"1/5\"), (\"5/1\")) == (true));\n    assert(simplify((\"1/5\"), (\"1/5\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_144_simplify"}
{"name": "HumanEval_78_hex_key", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You have been tasked to write a function that receives \n    // a hexadecimal number as a string and counts the number of hexadecimal \n    // digits that are primes (prime number, or a prime, is a natural number \n    // greater than 1 that is not a product of two smaller natural numbers).\n    // Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n    // Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n    // So you have to determine a number of the following digits: 2, 3, 5, 7, \n    // B (=decimal 11), D (=decimal 13).\n    // Note: you may assume the input is always correct or empty string, \n    // and symbols A,B,C,D,E,F are always uppercase.\n    // Examples:\n    // >>> hexKey((\"AB\"))\n    // (1l)\n    // >>> hexKey((\"1077E\"))\n    // (2l)\n    // >>> hexKey((\"ABED1A33\"))\n    // (4l)\n    // >>> hexKey((\"123456789ABCDEF0\"))\n    // (6l)\n    // >>> hexKey((\"2020\"))\n    // (2l)\n    public static long hexKey(String num) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(hexKey((\"AB\")) == (1l));\n    assert(hexKey((\"1077E\")) == (2l));\n    assert(hexKey((\"ABED1A33\")) == (4l));\n    assert(hexKey((\"2020\")) == (2l));\n    assert(hexKey((\"123456789ABCDEF0\")) == (6l));\n    assert(hexKey((\"112233445566778899AABBCCDDEEFF00\")) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_78_hex_key"}
{"name": "HumanEval_143_words_in_sentence", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a string representing a sentence,\n    // the sentence contains some words separated by a space,\n    // and you have to return a string that contains the words from the original sentence,\n    // whose lengths are prime numbers,\n    // the order of the words in the new string should be the same as the original one.\n    // Example 1:\n    // >>> wordsInSentence((\"This is a test\"))\n    // (\"is\")\n    // Example 2:\n    // >>> wordsInSentence((\"lets go for swimming\"))\n    // (\"go for\")\n    // Constraints:\n    // * 1 <= len(sentence) <= 100\n    // * sentence contains only letters\n    public static String wordsInSentence(String sentence) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(wordsInSentence((\"This is a test\")).equals((\"is\")));\n    assert(wordsInSentence((\"lets go for swimming\")).equals((\"go for\")));\n    assert(wordsInSentence((\"there is no place available here\")).equals((\"there is no place\")));\n    assert(wordsInSentence((\"Hi I am Hussein\")).equals((\"Hi am Hussein\")));\n    assert(wordsInSentence((\"go for it\")).equals((\"go for it\")));\n    assert(wordsInSentence((\"here\")).equals((\"\")));\n    assert(wordsInSentence((\"here is\")).equals((\"is\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_143_words_in_sentence"}
{"name": "HumanEval_111_histogram", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string representing a space separated lowercase letters, return a hash map\n    // of the letter with the most repetition and containing the corresponding count.\n    // If several letters have the same occurrence, return all of them.\n    // Example:\n    // >>> histogram((\"a b c\"))\n    // (new HashMap<String,Long>(Map.of(\"a\", 1l, \"b\", 1l, \"c\", 1l)))\n    // >>> histogram((\"a b b a\"))\n    // (new HashMap<String,Long>(Map.of(\"a\", 2l, \"b\", 2l)))\n    // >>> histogram((\"a b c a b\"))\n    // (new HashMap<String,Long>(Map.of(\"a\", 2l, \"b\", 2l)))\n    // >>> histogram((\"b b b b a\"))\n    // (new HashMap<String,Long>(Map.of(\"b\", 4l)))\n    // >>> histogram((\"\"))\n    // (new HashMap<String,Long>())\n    public static HashMap<String,Long> histogram(String test) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(histogram((\"a b b a\")).equals((new HashMap<String,Long>(Map.of(\"a\", 2l, \"b\", 2l)))));\n    assert(histogram((\"a b c a b\")).equals((new HashMap<String,Long>(Map.of(\"a\", 2l, \"b\", 2l)))));\n    assert(histogram((\"a b c d g\")).equals((new HashMap<String,Long>(Map.of(\"a\", 1l, \"b\", 1l, \"c\", 1l, \"d\", 1l, \"g\", 1l)))));\n    assert(histogram((\"r t g\")).equals((new HashMap<String,Long>(Map.of(\"r\", 1l, \"t\", 1l, \"g\", 1l)))));\n    assert(histogram((\"b b b b a\")).equals((new HashMap<String,Long>(Map.of(\"b\", 4l)))));\n    assert(histogram((\"r t g\")).equals((new HashMap<String,Long>(Map.of(\"r\", 1l, \"t\", 1l, \"g\", 1l)))));\n    assert(histogram((\"\")).equals((new HashMap<String,Long>())));\n    assert(histogram((\"a\")).equals((new HashMap<String,Long>(Map.of(\"a\", 1l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_111_histogram"}
{"name": "HumanEval_87_get_row", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a 2 dimensional data, as a nested array lists,\n    // which is similar to matrix, however, unlike matrices,\n    // each row may contain a different number of columns.\n    // Given lst, and integer x, find integers x in the array list,\n    // and return array list of pairs, [(x1, y1), (x2, y2) ...] such that\n    // each pair is a coordinate - (row, columns), starting with 0.\n    // Sort coordinates initially by rows in ascending order.\n    // Also, sort coordinates of the row by columns in descending order.\n    // Examples:\n    // >>> getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)1l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)1l))))), (1l))\n    // (new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(0l, 0l), (Pair<Long, Long>)Pair.with(1l, 4l), (Pair<Long, Long>)Pair.with(1l, 0l), (Pair<Long, Long>)Pair.with(2l, 5l), (Pair<Long, Long>)Pair.with(2l, 0l))))\n    // >>> getRow((new ArrayList<ArrayList<Long>>(Arrays.asList())), (1l))\n    // (new ArrayList<Pair<Long, Long>>(Arrays.asList()))\n    // >>> getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList()), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))), (3l))\n    // (new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(2l, 2l))))\n    public static ArrayList<Pair<Long, Long>> getRow(ArrayList<ArrayList<Long>> lst, long x) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)1l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)1l))))), (1l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(0l, 0l), (Pair<Long, Long>)Pair.with(1l, 4l), (Pair<Long, Long>)Pair.with(1l, 0l), (Pair<Long, Long>)Pair.with(2l, 5l), (Pair<Long, Long>)Pair.with(2l, 0l))))));\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l))))), (2l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(0l, 1l), (Pair<Long, Long>)Pair.with(1l, 1l), (Pair<Long, Long>)Pair.with(2l, 1l), (Pair<Long, Long>)Pair.with(3l, 1l), (Pair<Long, Long>)Pair.with(4l, 1l), (Pair<Long, Long>)Pair.with(5l, 1l))))));\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)3l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)1l, (long)4l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)1l, (long)5l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)1l, (long)6l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)1l))))), (1l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(0l, 0l), (Pair<Long, Long>)Pair.with(1l, 0l), (Pair<Long, Long>)Pair.with(2l, 1l), (Pair<Long, Long>)Pair.with(2l, 0l), (Pair<Long, Long>)Pair.with(3l, 2l), (Pair<Long, Long>)Pair.with(3l, 0l), (Pair<Long, Long>)Pair.with(4l, 3l), (Pair<Long, Long>)Pair.with(4l, 0l), (Pair<Long, Long>)Pair.with(5l, 4l), (Pair<Long, Long>)Pair.with(5l, 0l), (Pair<Long, Long>)Pair.with(6l, 5l), (Pair<Long, Long>)Pair.with(6l, 0l))))));\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList())), (1l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList()))));\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l))))), (2l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList()))));\n    assert(getRow((new ArrayList<ArrayList<Long>>(Arrays.asList((ArrayList<Long>)new ArrayList<Long>(Arrays.asList()), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l)), (ArrayList<Long>)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))), (3l)).equals((new ArrayList<Pair<Long, Long>>(Arrays.asList((Pair<Long, Long>)Pair.with(2l, 2l))))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_87_get_row"}
{"name": "HumanEval_123_get_odd_collatz", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer n, return a sorted array list that has the odd numbers in collatz sequence.\n    // The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n    // as follows: start with any positive integer n. Then each term is obtained from the \n    // previous term as follows: if the previous term is even, the next term is one half of \n    // the previous term. If the previous term is odd, the next term is 3 times the previous\n    // term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n    // Note: \n    // 1. Collatz(1) is [1].\n    // 2. returned array list sorted in increasing order.\n    // For example:\n    // get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n    // >>> getOddCollatz((5l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)5l)))\n    public static ArrayList<Long> getOddCollatz(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(getOddCollatz((14l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)7l, (long)11l, (long)13l, (long)17l)))));\n    assert(getOddCollatz((5l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l)))));\n    assert(getOddCollatz((12l)).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)5l)))));\n    assert(getOddCollatz((1l)).equals((new ArrayList<Long>(Arrays.asList((long)1l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_123_get_odd_collatz"}
{"name": "HumanEval_135_can_arrange", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function which returns the largest index of an element which\n    // is not greater than or equal to the element immediately preceding it. If\n    // no such element exists then return -1. The given array array list will not contain\n    // duplicate values.\n    // Examples:\n    // >>> canArrange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)3l, (long)5l))))\n    // (3l)\n    // >>> canArrange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (-1l)\n    public static long canArrange(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(canArrange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)3l, (long)5l)))) == (3l));\n    assert(canArrange((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)5l)))) == (-1l));\n    assert(canArrange((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)2l, (long)5l, (long)6l, (long)7l, (long)8l, (long)9l, (long)10l)))) == (2l));\n    assert(canArrange((new ArrayList<Long>(Arrays.asList((long)4l, (long)8l, (long)5l, (long)7l, (long)3l)))) == (4l));\n    assert(canArrange((new ArrayList<Long>(Arrays.asList()))) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_135_can_arrange"}
{"name": "HumanEval_19_sort_numbers", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Input is a space-delimited string of numberals from 'zero' to 'nine'.\n    // Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n    // Return the string with numbers sorted from smallest to largest\n    // >>> sortNumbers((\"three one five\"))\n    // (\"one three five\")\n    public static String sortNumbers(String numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortNumbers((\"\")).equals((\"\")));\n    assert(sortNumbers((\"three\")).equals((\"three\")));\n    assert(sortNumbers((\"three five nine\")).equals((\"three five nine\")));\n    assert(sortNumbers((\"five zero four seven nine eight\")).equals((\"zero four five seven eight nine\")));\n    assert(sortNumbers((\"six five four three two one zero\")).equals((\"zero one two three four five six\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_19_sort_numbers"}
{"name": "HumanEval_65_circular_shift", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Circular shift the digits of the integer x, shift the digits right by shift\n    // and return the result as a string.\n    // If shift > number of digits, return digits reversed.\n    // >>> circularShift((12l), (1l))\n    // (\"21\")\n    // >>> circularShift((12l), (2l))\n    // (\"12\")\n    public static String circularShift(long x, long shift) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(circularShift((100l), (2l)).equals((\"001\")));\n    assert(circularShift((12l), (2l)).equals((\"12\")));\n    assert(circularShift((97l), (8l)).equals((\"79\")));\n    assert(circularShift((12l), (1l)).equals((\"21\")));\n    assert(circularShift((11l), (101l)).equals((\"11\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_65_circular_shift"}
{"name": "HumanEval_142_sum_squares", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // \"\n    // This function will take an array array list of integers. For all entries in the array list, the function shall square the integer entry if its index is a \n    // multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n    // change the entries in the array list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n    // Examples:\n    // >>> lst\n    // (long)new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))\n    // >>> lst\n    // (long)new ArrayList<Long>(Arrays.asList())\n    // >>> lst\n    // (long)new ArrayList<Long>(Arrays.asList((long)-1l, (long)-5l, (long)2l, (long)-1l, (long)-5l))\n    public static long sumSquares(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))) == (6l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)9l)))) == (14l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList()))) == (0l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)1l, (long)1l, (long)1l, (long)1l, (long)1l, (long)1l)))) == (9l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-1l, (long)-1l, (long)-1l, (long)-1l, (long)-1l, (long)-1l, (long)-1l, (long)-1l)))) == (-3l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)0l)))) == (0l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-5l, (long)2l, (long)-1l, (long)-5l)))) == (-126l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-56l, (long)-99l, (long)1l, (long)0l, (long)-2l)))) == (3030l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-1l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)0l, (long)-1l)))) == (0l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-16l, (long)-9l, (long)-2l, (long)36l, (long)36l, (long)26l, (long)-20l, (long)25l, (long)-40l, (long)20l, (long)-4l, (long)12l, (long)-26l, (long)35l, (long)37l)))) == (-14196l));\n    assert(sumSquares((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-3l, (long)17l, (long)-1l, (long)-15l, (long)13l, (long)-1l, (long)14l, (long)-14l, (long)-12l, (long)-5l, (long)14l, (long)-14l, (long)6l, (long)13l, (long)11l, (long)16l, (long)16l, (long)4l, (long)10l)))) == (-1448l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_142_sum_squares"}
{"name": "HumanEval_94_skjkasdkd", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given an array array list of integers.\n    // You need to find the largest prime value and return the sum of its digits.\n    // Examples:\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)3l, (long)2l, (long)1l, (long)3l, (long)5l, (long)7l, (long)4l, (long)5l, (long)5l, (long)5l, (long)2l, (long)181l, (long)32l, (long)4l, (long)32l, (long)3l, (long)2l, (long)32l, (long)324l, (long)4l, (long)3l))))\n    // (10l)\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)1l, (long)0l, (long)1l, (long)8l, (long)2l, (long)4597l, (long)2l, (long)1l, (long)3l, (long)40l, (long)1l, (long)2l, (long)1l, (long)2l, (long)4l, (long)2l, (long)5l, (long)1l))))\n    // (25l)\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)1l, (long)32l, (long)5107l, (long)34l, (long)83278l, (long)109l, (long)163l, (long)23l, (long)2323l, (long)32l, (long)30l, (long)1l, (long)9l, (long)3l))))\n    // (13l)\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)724l, (long)32l, (long)71l, (long)99l, (long)32l, (long)6l, (long)0l, (long)5l, (long)91l, (long)83l, (long)0l, (long)5l, (long)6l))))\n    // (11l)\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)81l, (long)12l, (long)3l, (long)1l, (long)21l))))\n    // (3l)\n    // >>> skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)8l, (long)1l, (long)2l, (long)1l, (long)7l))))\n    // (7l)\n    public static long skjkasdkd(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)3l, (long)2l, (long)1l, (long)3l, (long)5l, (long)7l, (long)4l, (long)5l, (long)5l, (long)5l, (long)2l, (long)181l, (long)32l, (long)4l, (long)32l, (long)3l, (long)2l, (long)32l, (long)324l, (long)4l, (long)3l)))) == (10l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)1l, (long)0l, (long)1l, (long)8l, (long)2l, (long)4597l, (long)2l, (long)1l, (long)3l, (long)40l, (long)1l, (long)2l, (long)1l, (long)2l, (long)4l, (long)2l, (long)5l, (long)1l)))) == (25l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)1l, (long)32l, (long)5107l, (long)34l, (long)83278l, (long)109l, (long)163l, (long)23l, (long)2323l, (long)32l, (long)30l, (long)1l, (long)9l, (long)3l)))) == (13l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)724l, (long)32l, (long)71l, (long)99l, (long)32l, (long)6l, (long)0l, (long)5l, (long)91l, (long)83l, (long)0l, (long)5l, (long)6l)))) == (11l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)81l, (long)12l, (long)3l, (long)1l, (long)21l)))) == (3l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)0l, (long)8l, (long)1l, (long)2l, (long)1l, (long)7l)))) == (7l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)8191l)))) == (19l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)8191l, (long)123456l, (long)127l, (long)7l)))) == (19l));\n    assert(skjkasdkd((new ArrayList<Long>(Arrays.asList((long)127l, (long)97l, (long)8192l)))) == (10l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_94_skjkasdkd"}
{"name": "HumanEval_8_sum_product", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // For a given array list of integers, return a pair consisting of a sum and a product of all the integers in an array array list.\n    // Empty sum should be equal to 0 and empty product should be equal to 1.\n    // >>> sumProduct((new ArrayList<Long>(Arrays.asList())))\n    // (Pair.with(0l, 1l))\n    // >>> sumProduct((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l))))\n    // (Pair.with(10l, 24l))\n    public static Pair<Long, Long> sumProduct(ArrayList<Long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sumProduct((new ArrayList<Long>(Arrays.asList()))).equals((Pair.with(0l, 1l))));\n    assert(sumProduct((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l)))).equals((Pair.with(3l, 1l))));\n    assert(sumProduct((new ArrayList<Long>(Arrays.asList((long)100l, (long)0l)))).equals((Pair.with(100l, 0l))));\n    assert(sumProduct((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)7l)))).equals((Pair.with(15l, 105l))));\n    assert(sumProduct((new ArrayList<Long>(Arrays.asList((long)10l)))).equals((Pair.with(10l, 10l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_8_sum_product"}
{"name": "HumanEval_102_choose_num", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // This function takes two positive numbers x and y and returns the\n    // biggest even integer number that is in the range [x, y] inclusive. If \n    // there's no such number, then the function should return -1.\n    // For example:\n    // >>> chooseNum((12l), (15l))\n    // (14l)\n    // >>> chooseNum((13l), (12l))\n    // (-1l)\n    public static long chooseNum(long x, long y) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(chooseNum((12l), (15l)) == (14l));\n    assert(chooseNum((13l), (12l)) == (-1l));\n    assert(chooseNum((33l), (12354l)) == (12354l));\n    assert(chooseNum((5234l), (5233l)) == (-1l));\n    assert(chooseNum((6l), (29l)) == (28l));\n    assert(chooseNum((27l), (10l)) == (-1l));\n    assert(chooseNum((7l), (7l)) == (-1l));\n    assert(chooseNum((546l), (546l)) == (546l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_102_choose_num"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function that returns a pair (a, b), where 'a' is\n    // the largest of negative integers, and 'b' is the smallest\n    // of positive integers in an array array list.\n    // If there is no negative or positive integers, return them as null.\n    // Examples:\n    // >>> largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)1l, (long)3l, (long)5l, (long)7l))))\n    // Pair.with(Optional.of(Optional.empty()), Optional.of(1l))\n    // >>> largestSmallestIntegers((new ArrayList<Long>(Arrays.asList())))\n    // Pair.with(Optional.of(Optional.empty()), Optional.of(Optional.empty()))\n    // >>> largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)0l))))\n    // Pair.with(Optional.of(Optional.empty()), Optional.of(Optional.empty()))\n    public static Pair<Optional<Long>, Optional<Long>> largestSmallestIntegers(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)1l, (long)3l, (long)5l, (long)7l)))).equals(Pair.with(Optional.of(Optional.empty()), Optional.of(1l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)1l, (long)3l, (long)5l, (long)7l, (long)0l)))).equals(Pair.with(Optional.of(Optional.empty()), Optional.of(1l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)2l, (long)4l, (long)5l, (long)6l, (long)-2l)))).equals(Optional.of(Pair.with(-2l, 1l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)4l, (long)5l, (long)3l, (long)6l, (long)2l, (long)7l, (long)-7l)))).equals(Optional.of(Pair.with(-7l, 2l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)7l, (long)3l, (long)8l, (long)4l, (long)9l, (long)2l, (long)5l, (long)-9l)))).equals(Optional.of(Pair.with(-9l, 2l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList()))).equals(Pair.with(Optional.of(Optional.empty()), Optional.of(Optional.empty()))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)0l)))).equals(Pair.with(Optional.of(Optional.empty()), Optional.of(Optional.empty()))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-3l, (long)-5l, (long)-6l)))).equals(Pair.with(Optional.of(-1l), Optional.of(Optional.empty()))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-3l, (long)-5l, (long)-6l, (long)0l)))).equals(Pair.with(Optional.of(-1l), Optional.of(Optional.empty()))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)-6l, (long)-4l, (long)-4l, (long)-3l, (long)1l)))).equals(Optional.of(Pair.with(-3l, 1l))));\n    assert(largestSmallestIntegers((new ArrayList<Long>(Arrays.asList((long)-6l, (long)-4l, (long)-4l, (long)-3l, (long)-100l, (long)1l)))).equals(Optional.of(Pair.with(-3l, 1l))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_136_largest_smallest_integers"}
{"name": "HumanEval_16_count_distinct_characters", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string, find out how many distinct characters (regardless of case) does it consist of\n    // >>> countDistinctCharacters((\"xyzXYZ\"))\n    // (3l)\n    // >>> countDistinctCharacters((\"Jerry\"))\n    // (4l)\n    public static long countDistinctCharacters(String string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(countDistinctCharacters((\"\")) == (0l));\n    assert(countDistinctCharacters((\"abcde\")) == (5l));\n    assert(countDistinctCharacters((\"abcdecadeCADE\")) == (5l));\n    assert(countDistinctCharacters((\"aaaaAAAAaaaa\")) == (1l));\n    assert(countDistinctCharacters((\"Jerry jERRY JeRRRY\")) == (5l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_16_count_distinct_characters"}
{"name": "HumanEval_100_make_a_pile", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer n, you have to make a pile of n levels of stones.\n    // The first level has n stones.\n    // The number of stones in the next level is:\n    // - the next odd number if n is odd.\n    // - the next even number if n is even.\n    // Return the number of stones in each level in an array array list, where element at index\n    // i represents the number of stones in the level (i+1).\n    // Examples:\n    // >>> makeAPile((3l))\n    // (new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)7l)))\n    public static ArrayList<Long> makeAPile(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(makeAPile((3l)).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)5l, (long)7l)))));\n    assert(makeAPile((4l)).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)6l, (long)8l, (long)10l)))));\n    assert(makeAPile((5l)).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)9l, (long)11l, (long)13l)))));\n    assert(makeAPile((6l)).equals((new ArrayList<Long>(Arrays.asList((long)6l, (long)8l, (long)10l, (long)12l, (long)14l, (long)16l)))));\n    assert(makeAPile((8l)).equals((new ArrayList<Long>(Arrays.asList((long)8l, (long)10l, (long)12l, (long)14l, (long)16l, (long)18l, (long)20l, (long)22l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_100_make_a_pile"}
{"name": "HumanEval_128_prod_signs", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given an array array list arr of integers and you need to return\n    // sum of magnitudes of integers multiplied by product of all signs\n    // of each number in the array array list, represented by 1, -1 or 0.\n    // Note: return null for empty arr.\n    // Example:\n    // >>> prodSigns((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)-4l))))\n    // Optional.of(9l)\n    // >>> prodSigns((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l))))\n    // Optional.of(0l)\n    // >>> prodSigns((new ArrayList<Long>(Arrays.asList())))\n    // Optional.empty()\n    public static Optional<Long> prodSigns(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)-4l)))).equals(Optional.of(-9l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l)))).equals(Optional.of(0l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)1l, (long)1l, (long)1l, (long)2l, (long)3l, (long)-1l, (long)1l)))).equals(Optional.of(-10l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList()))).equals(Optional.empty()));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)1l, (long)2l, (long)-1l, (long)-1l, (long)9l)))).equals(Optional.of(20l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)-1l, (long)1l, (long)-1l, (long)1l)))).equals(Optional.of(4l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)-1l, (long)1l, (long)1l, (long)1l)))).equals(Optional.of(-4l)));\n    assert(prodSigns((new ArrayList<Long>(Arrays.asList((long)-1l, (long)1l, (long)1l, (long)0l)))).equals(Optional.of(0l)));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_128_prod_signs"}
{"name": "HumanEval_114_minSubArraySum", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given an array array list of integers nums, find the minimum sum of any non-empty sub-array array list\n    // of nums.\n    // Example\n    // >>> minSubArraySum((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l, (long)1l, (long)2l, (long)4l))))\n    // (1l)\n    // >>> minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)-3l))))\n    // (-6l)\n    public static long minSubArraySum(ArrayList<Long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l, (long)1l, (long)2l, (long)4l)))) == (1l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)-3l)))) == (-6l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)-3l, (long)2l, (long)-10l)))) == (-14l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-9999999999999999l)))) == (-9999999999999999l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)0l, (long)10l, (long)20l, (long)1000000l)))) == (0l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)-3l, (long)10l, (long)-5l)))) == (-6l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)100l, (long)-1l, (long)-2l, (long)-3l, (long)10l, (long)-5l)))) == (-6l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)10l, (long)11l, (long)13l, (long)8l, (long)3l, (long)4l)))) == (3l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)100l, (long)-33l, (long)32l, (long)-1l, (long)0l, (long)-2l)))) == (-33l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)-10l)))) == (-10l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)7l)))) == (7l));\n    assert(minSubArraySum((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l)))) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_114_minSubArraySum"}
{"name": "HumanEval_15_string_sequence", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n    // >>> stringSequence((0l))\n    // (\"0\")\n    // >>> stringSequence((5l))\n    // (\"0 1 2 3 4 5\")\n    public static String stringSequence(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(stringSequence((0l)).equals((\"0\")));\n    assert(stringSequence((3l)).equals((\"0 1 2 3\")));\n    assert(stringSequence((10l)).equals((\"0 1 2 3 4 5 6 7 8 9 10\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_15_string_sequence"}
{"name": "HumanEval_154_cycpattern_check", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n    // >>> cycpatternCheck((\"abcd\"), (\"abd\"))\n    // (false)\n    // >>> cycpatternCheck((\"hello\"), (\"ell\"))\n    // (true)\n    // >>> cycpatternCheck((\"whassup\"), (\"psus\"))\n    // (false)\n    // >>> cycpatternCheck((\"abab\"), (\"baa\"))\n    // (true)\n    // >>> cycpatternCheck((\"efef\"), (\"eeff\"))\n    // (false)\n    // >>> cycpatternCheck((\"himenss\"), (\"simen\"))\n    // (true)\n    public static boolean cycpatternCheck(String a, String b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(cycpatternCheck((\"xyzw\"), (\"xyw\")) == (false));\n    assert(cycpatternCheck((\"yello\"), (\"ell\")) == (true));\n    assert(cycpatternCheck((\"whattup\"), (\"ptut\")) == (false));\n    assert(cycpatternCheck((\"efef\"), (\"fee\")) == (true));\n    assert(cycpatternCheck((\"abab\"), (\"aabb\")) == (false));\n    assert(cycpatternCheck((\"winemtt\"), (\"tinem\")) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_154_cycpattern_check"}
{"name": "HumanEval_57_monotonic", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return true is array list elements are monotonically increasing or decreasing.\n    // >>> monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)20l))))\n    // (true)\n    // >>> monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l))))\n    // (false)\n    // >>> monotonic((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)0l, (long)-10l))))\n    // (true)\n    public static boolean monotonic(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)10l)))) == (true));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)20l)))) == (true));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l)))) == (false));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)0l, (long)-10l)))) == (true));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)1l, (long)0l)))) == (true));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)5l, (long)60l)))) == (false));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l, (long)60l)))) == (true));\n    assert(monotonic((new ArrayList<Long>(Arrays.asList((long)9l, (long)9l, (long)9l, (long)9l)))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_57_monotonic"}
{"name": "HumanEval_12_longest", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Out of array list of strings, return the longest one. Return the first one in case of multiple\n    // strings of the same length. Return null in case the input array list is empty.\n    // >>> longest((new ArrayList<String>(Arrays.asList())))\n    // Optional.empty()\n    // >>> longest((new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"b\", (String)\"c\"))))\n    // Optional.of(\"a\")\n    // >>> longest((new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"bb\", (String)\"ccc\"))))\n    // Optional.of(\"ccc\")\n    public static Optional<String> longest(ArrayList<String> strings) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(longest((new ArrayList<String>(Arrays.asList()))).equals(Optional.empty()));\n    assert(longest((new ArrayList<String>(Arrays.asList((String)\"x\", (String)\"y\", (String)\"z\")))).equals(Optional.of(\"x\")));\n    assert(longest((new ArrayList<String>(Arrays.asList((String)\"x\", (String)\"yyy\", (String)\"zzzz\", (String)\"www\", (String)\"kkkk\", (String)\"abc\")))).equals(Optional.of(\"zzzz\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_12_longest"}
{"name": "HumanEval_52_below_threshold", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return true if all numbers in the array list l are below threshold t.\n    // >>> belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)10l))), (100l))\n    // (true)\n    // >>> belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l))), (5l))\n    // (false)\n    public static boolean belowThreshold(ArrayList<Long> l, long t) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)10l))), (100l)) == (true));\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l))), (5l)) == (false));\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l))), (21l)) == (true));\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)20l, (long)4l, (long)10l))), (22l)) == (true));\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)8l, (long)4l, (long)10l))), (11l)) == (true));\n    assert(belowThreshold((new ArrayList<Long>(Arrays.asList((long)1l, (long)8l, (long)4l, (long)10l))), (10l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_52_below_threshold"}
{"name": "HumanEval_75_is_multiply_prime", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that returns true if the given number is the multiplication of 3 prime numbers\n    // and false otherwise.\n    // Knowing that (a) is less then 100. \n    // Example:\n    // >>> isMultiplyPrime((30l))\n    // (true)\n    // 30 = 2 * 3 * 5\n    public static boolean isMultiplyPrime(long a) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(isMultiplyPrime((5l)) == (false));\n    assert(isMultiplyPrime((30l)) == (true));\n    assert(isMultiplyPrime((8l)) == (true));\n    assert(isMultiplyPrime((10l)) == (false));\n    assert(isMultiplyPrime((125l)) == (true));\n    assert(isMultiplyPrime((105l)) == (true));\n    assert(isMultiplyPrime((126l)) == (false));\n    assert(isMultiplyPrime((729l)) == (false));\n    assert(isMultiplyPrime((891l)) == (false));\n    assert(isMultiplyPrime((1001l)) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_75_is_multiply_prime"}
{"name": "HumanEval_30_get_positive", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return only positive numbers in the array list.\n    // >>> getPositive((new ArrayList<Long>(Arrays.asList((long)-1l, (long)2l, (long)-4l, (long)5l, (long)6l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)5l, (long)6l)))\n    // >>> getPositive((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)-5l, (long)2l, (long)-3l, (long)3l, (long)9l, (long)0l, (long)123l, (long)1l, (long)-10l))))\n    // (new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)2l, (long)3l, (long)9l, (long)123l, (long)1l)))\n    public static ArrayList<Long> getPositive(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(getPositive((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l, (long)4l, (long)5l, (long)6l)))).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)5l, (long)6l)))));\n    assert(getPositive((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)-5l, (long)2l, (long)3l, (long)3l, (long)9l, (long)0l, (long)123l, (long)1l, (long)-10l)))).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)2l, (long)3l, (long)3l, (long)9l, (long)123l, (long)1l)))));\n    assert(getPositive((new ArrayList<Long>(Arrays.asList((long)-1l, (long)-2l)))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(getPositive((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_30_get_positive"}
{"name": "HumanEval_33_sort_third", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // This function takes an array array list l and returns an array array list l' such that\n    // l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n    // to the values of the corresponding indicies of l, but sorted.\n    // >>> sortThird((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))\n    // >>> sortThird((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)5l)))\n    public static ArrayList<Long> sortThird(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortThird((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)2l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)5l)))));\n    assert(sortThird((new ArrayList<Long>(Arrays.asList((long)5l, (long)8l, (long)3l, (long)4l, (long)6l, (long)9l, (long)2l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)8l, (long)3l, (long)4l, (long)6l, (long)9l, (long)5l)))));\n    assert(sortThird((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)9l, (long)4l, (long)8l, (long)3l, (long)2l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)9l, (long)4l, (long)8l, (long)3l, (long)5l)))));\n    assert(sortThird((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)2l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)3l, (long)4l, (long)8l, (long)9l, (long)5l, (long)1l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_33_sort_third"}
{"name": "HumanEval_6_parse_nested_parens", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n    // For each of the group, output the deepest level of nesting of parentheses.\n    // E.g. (()()) has maximum two levels of nesting while ((())) has three.\n    // >>> parseNestedParens((\"(()()) ((())) () ((())()())\"))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)1l, (long)3l)))\n    public static ArrayList<Long> parseNestedParens(String paren_string) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(parseNestedParens((\"(()()) ((())) () ((())()())\")).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)1l, (long)3l)))));\n    assert(parseNestedParens((\"() (()) ((())) (((())))\")).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))));\n    assert(parseNestedParens((\"(()(())((())))\")).equals((new ArrayList<Long>(Arrays.asList((long)4l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_6_parse_nested_parens"}
{"name": "HumanEval_45_triangle_area", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given length of a side and high return area for a triangle.\n    // >>> triangleArea((5l), (3l))\n    // (7.5f)\n    public static float triangleArea(long a, long h) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(triangleArea((5l), (3l)) == (7.5f));\n    assert(triangleArea((2l), (2l)) == (2.0f));\n    assert(triangleArea((10l), (8l)) == (40.0f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_45_triangle_area"}
{"name": "HumanEval_97_multiply", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Complete the function that takes two integers and returns \n    // the product of their unit digits.\n    // Assume the input is always valid.\n    // Examples:\n    // >>> multiply((148l), (412l))\n    // (16l)\n    // >>> multiply((19l), (28l))\n    // (72l)\n    // >>> multiply((2020l), (1851l))\n    // (0l)\n    // >>> multiply((14l), (-15l))\n    // (20l)\n    public static long multiply(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(multiply((148l), (412l)) == (16l));\n    assert(multiply((19l), (28l)) == (72l));\n    assert(multiply((2020l), (1851l)) == (0l));\n    assert(multiply((14l), (-15l)) == (20l));\n    assert(multiply((76l), (67l)) == (42l));\n    assert(multiply((17l), (27l)) == (49l));\n    assert(multiply((0l), (1l)) == (0l));\n    assert(multiply((0l), (0l)) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_97_multiply"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // For a given array list of input numbers, calculate Mean Absolute Deviation\n    // around the mean of this dataset.\n    // Mean Absolute Deviation is the average absolute difference between each\n    // element and a centerpoint (mean in this case):\n    // MAD = average | x - x_mean |\n    // >>> meanAbsoluteDeviation((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f))))\n    // (1.0f)\n    public static float meanAbsoluteDeviation(ArrayList<Float> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(meanAbsoluteDeviation((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f)))) == (0.5f));\n    assert(meanAbsoluteDeviation((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f)))) == (1.0f));\n    assert(meanAbsoluteDeviation((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)2.0f, (float)3.0f, (float)4.0f, (float)5.0f)))) == (1.2f));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_4_mean_absolute_deviation"}
{"name": "HumanEval_58_common", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return sorted unique common elements for two array lists.\n    // >>> common((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)3l, (long)34l, (long)653l, (long)2l, (long)5l))), (new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)1l, (long)5l, (long)9l, (long)653l, (long)121l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)653l)))\n    // >>> common((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)2l, (long)8l))), (new ArrayList<Long>(Arrays.asList((long)3l, (long)2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)3l)))\n    public static ArrayList<Long> common(ArrayList<Long> l1, ArrayList<Long> l2) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(common((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)3l, (long)34l, (long)653l, (long)2l, (long)5l))), (new ArrayList<Long>(Arrays.asList((long)5l, (long)7l, (long)1l, (long)5l, (long)9l, (long)653l, (long)121l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)653l)))));\n    assert(common((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)2l, (long)8l))), (new ArrayList<Long>(Arrays.asList((long)3l, (long)2l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l)))));\n    assert(common((new ArrayList<Long>(Arrays.asList((long)4l, (long)3l, (long)2l, (long)8l))), (new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)4l)))));\n    assert(common((new ArrayList<Long>(Arrays.asList((long)4l, (long)3l, (long)2l, (long)8l))), (new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_58_common"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a positive integer, obtain its roman numeral equivalent as a string,\n    // and return it in lowercase.\n    // Restrictions: 1 <= num <= 1000\n    // Examples:\n    // >>> intToMiniRoman((19l))\n    // (\"xix\")\n    // >>> intToMiniRoman((152l))\n    // (\"clii\")\n    // >>> intToMiniRoman((426l))\n    // (\"cdxxvi\")\n    public static String intToMiniRoman(long number) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(intToMiniRoman((19l)).equals((\"xix\")));\n    assert(intToMiniRoman((152l)).equals((\"clii\")));\n    assert(intToMiniRoman((251l)).equals((\"ccli\")));\n    assert(intToMiniRoman((426l)).equals((\"cdxxvi\")));\n    assert(intToMiniRoman((500l)).equals((\"d\")));\n    assert(intToMiniRoman((1l)).equals((\"i\")));\n    assert(intToMiniRoman((4l)).equals((\"iv\")));\n    assert(intToMiniRoman((43l)).equals((\"xliii\")));\n    assert(intToMiniRoman((90l)).equals((\"xc\")));\n    assert(intToMiniRoman((94l)).equals((\"xciv\")));\n    assert(intToMiniRoman((532l)).equals((\"dxxxii\")));\n    assert(intToMiniRoman((900l)).equals((\"cm\")));\n    assert(intToMiniRoman((994l)).equals((\"cmxciv\")));\n    assert(intToMiniRoman((1000l)).equals((\"m\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_156_int_to_mini_roman"}
{"name": "HumanEval_67_fruit_distribution", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // In this task, you will be given a string that represents a number of apples and oranges \n    // that are distributed in a basket of fruit this basket contains \n    // apples, oranges, and mango fruits. Given the string that represents the total number of \n    // the oranges and apples and an integer that represent the total number of the fruits \n    // in the basket return the number of the mango fruits in the basket.\n    // for examble:\n    // >>> fruitDistribution((\"5 apples and 6 oranges\"), (19l))\n    // (8l)\n    // >>> fruitDistribution((\"0 apples and 1 oranges\"), (3l))\n    // (2l)\n    // >>> fruitDistribution((\"2 apples and 3 oranges\"), (100l))\n    // (95l)\n    // >>> fruitDistribution((\"100 apples and 1 oranges\"), (120l))\n    // (19l)\n    public static long fruitDistribution(String s, long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (19l)) == (8l));\n    assert(fruitDistribution((\"5 apples and 6 oranges\"), (21l)) == (10l));\n    assert(fruitDistribution((\"0 apples and 1 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"1 apples and 0 oranges\"), (3l)) == (2l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (100l)) == (95l));\n    assert(fruitDistribution((\"2 apples and 3 oranges\"), (5l)) == (0l));\n    assert(fruitDistribution((\"1 apples and 100 oranges\"), (120l)) == (19l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_67_fruit_distribution"}
{"name": "HumanEval_112_reverse_delete", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Task\n    // We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n    // then check if the result string is palindrome.\n    // A string is called palindrome if it reads the same backward as forward.\n    // You should return a pair containing the result string and true/false for the check.\n    // Example\n    // >>> reverseDelete((\"abcde\"), (\"ae\"))\n    // (Pair.with(\"bcd\", false))\n    // >>> reverseDelete((\"abcdef\"), (\"b\"))\n    // (Pair.with(\"acdef\", false))\n    // >>> reverseDelete((\"abcdedcba\"), (\"ab\"))\n    // (Pair.with(\"cdedc\", true))\n    public static Pair<String, Boolean> reverseDelete(String s, String c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(reverseDelete((\"abcde\"), (\"ae\")).equals((Pair.with(\"bcd\", false))));\n    assert(reverseDelete((\"abcdef\"), (\"b\")).equals((Pair.with(\"acdef\", false))));\n    assert(reverseDelete((\"abcdedcba\"), (\"ab\")).equals((Pair.with(\"cdedc\", true))));\n    assert(reverseDelete((\"dwik\"), (\"w\")).equals((Pair.with(\"dik\", false))));\n    assert(reverseDelete((\"a\"), (\"a\")).equals((Pair.with(\"\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"\")).equals((Pair.with(\"abcdedcba\", true))));\n    assert(reverseDelete((\"abcdedcba\"), (\"v\")).equals((Pair.with(\"abcdedcba\", true))));\n    assert(reverseDelete((\"vabba\"), (\"v\")).equals((Pair.with(\"abba\", true))));\n    assert(reverseDelete((\"mamma\"), (\"mia\")).equals((Pair.with(\"\", true))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_112_reverse_delete"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Return a greatest common divisor of two integers a and b\n    // >>> greatestCommonDivisor((3l), (5l))\n    // (1l)\n    // >>> greatestCommonDivisor((25l), (15l))\n    // (5l)\n    public static long greatestCommonDivisor(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(greatestCommonDivisor((3l), (7l)) == (1l));\n    assert(greatestCommonDivisor((10l), (15l)) == (5l));\n    assert(greatestCommonDivisor((49l), (14l)) == (7l));\n    assert(greatestCommonDivisor((144l), (60l)) == (12l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_13_greatest_common_divisor"}
{"name": "HumanEval_116_sort_array", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // In this Kata, you have to sort an array array list of non-negative integers according to\n    // number of ones in their binary representation in ascending order.\n    // For similar number of ones, sort based on decimal value.\n    // It must be implemented like this:\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)2l, (long)3l, (long)4l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l, (long)5l)))\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)-2l, (long)-3l, (long)-4l, (long)-5l, (long)-6l))))\n    // (new ArrayList<Long>(Arrays.asList((long)-6l, (long)-5l, (long)-4l, (long)-3l, (long)-2l)))\n    // >>> sortArray((new ArrayList<Long>(Arrays.asList((long)1l, (long)0l, (long)2l, (long)3l, (long)4l))))\n    // (new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)2l, (long)3l, (long)4l)))\n    public static ArrayList<Long> sortArray(ArrayList<Long> arr) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)1l, (long)5l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)4l, (long)3l, (long)5l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)-2l, (long)-3l, (long)-4l, (long)-5l, (long)-6l)))).equals((new ArrayList<Long>(Arrays.asList((long)-4l, (long)-2l, (long)-6l, (long)-5l, (long)-3l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)1l, (long)0l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)0l, (long)1l, (long)2l, (long)4l, (long)3l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)5l, (long)77l, (long)4l, (long)5l, (long)3l, (long)5l, (long)7l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)4l, (long)4l, (long)3l, (long)3l, (long)5l, (long)5l, (long)5l, (long)7l, (long)77l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)3l, (long)6l, (long)44l, (long)12l, (long)32l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)32l, (long)3l, (long)5l, (long)6l, (long)12l, (long)44l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)8l, (long)16l, (long)32l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)8l, (long)16l, (long)32l)))));\n    assert(sortArray((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)8l, (long)16l, (long)32l)))).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)8l, (long)16l, (long)32l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_116_sort_array"}
{"name": "HumanEval_28_concatenate", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Concatenate array list of strings into a single string\n    // >>> concatenate((new ArrayList<String>(Arrays.asList())))\n    // (\"\")\n    // >>> concatenate((new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"b\", (String)\"c\"))))\n    // (\"abc\")\n    public static String concatenate(ArrayList<String> strings) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(concatenate((new ArrayList<String>(Arrays.asList()))).equals((\"\")));\n    assert(concatenate((new ArrayList<String>(Arrays.asList((String)\"x\", (String)\"y\", (String)\"z\")))).equals((\"xyz\")));\n    assert(concatenate((new ArrayList<String>(Arrays.asList((String)\"x\", (String)\"y\", (String)\"z\", (String)\"w\", (String)\"k\")))).equals((\"xyzwk\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_28_concatenate"}
{"name": "HumanEval_149_sorted_list_sum", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that accepts an array array list of strings as a parameter,\n    // deletes the strings that have odd lengths from it,\n    // and returns the resulted array list with a sorted order,\n    // The array list is always an array array list of strings and never an array array list of numbers,\n    // and it may contain duplicates.\n    // The order of the array list should be ascending by length of each word, and you\n    // should return the array list sorted by that rule.\n    // If two words have the same length, sort the array list alphabetically.\n    // The function should return an array array list of strings in sorted order.\n    // You may assume that all words will have the same length.\n    // For example:\n    // >>> listSort((new ArrayList<String>(Arrays.asList((String)\"aa\", (String)\"a\", (String)\"aaa\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"aa\")))\n    // >>> listSort((new ArrayList<String>(Arrays.asList((String)\"ab\", (String)\"a\", (String)\"aaa\", (String)\"cd\"))))\n    // (new ArrayList<String>(Arrays.asList((String)\"ab\", (String)\"cd\")))\n    public static ArrayList<String> sortedListSum(ArrayList<String> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"aa\", (String)\"a\", (String)\"aaa\")))).equals((new ArrayList<String>(Arrays.asList((String)\"aa\")))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"school\", (String)\"AI\", (String)\"asdf\", (String)\"b\")))).equals((new ArrayList<String>(Arrays.asList((String)\"AI\", (String)\"asdf\", (String)\"school\")))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"d\", (String)\"b\", (String)\"c\", (String)\"a\")))).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"d\", (String)\"dcba\", (String)\"abcd\", (String)\"a\")))).equals((new ArrayList<String>(Arrays.asList((String)\"abcd\", (String)\"dcba\")))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"AI\", (String)\"ai\", (String)\"au\")))).equals((new ArrayList<String>(Arrays.asList((String)\"AI\", (String)\"ai\", (String)\"au\")))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"a\", (String)\"b\", (String)\"b\", (String)\"c\", (String)\"c\", (String)\"a\")))).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(sortedListSum((new ArrayList<String>(Arrays.asList((String)\"aaaa\", (String)\"bbbb\", (String)\"dd\", (String)\"cc\")))).equals((new ArrayList<String>(Arrays.asList((String)\"cc\", (String)\"dd\", (String)\"aaaa\", (String)\"bbbb\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_149_sorted_list_sum"}
{"name": "HumanEval_7_filter_by_substring", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Filter an input array list of strings only for ones that contain given substring\n    // >>> filterBySubstring((new ArrayList<String>(Arrays.asList())), (\"a\"))\n    // (new ArrayList<String>(Arrays.asList()))\n    // >>> filterBySubstring((new ArrayList<String>(Arrays.asList((String)\"abc\", (String)\"bacd\", (String)\"cde\", (String)\"array\"))), (\"a\"))\n    // (new ArrayList<String>(Arrays.asList((String)\"abc\", (String)\"bacd\", (String)\"array\")))\n    public static ArrayList<String> filterBySubstring(ArrayList<String> strings, String substring) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(filterBySubstring((new ArrayList<String>(Arrays.asList())), (\"john\")).equals((new ArrayList<String>(Arrays.asList()))));\n    assert(filterBySubstring((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"asd\", (String)\"xxy\", (String)\"john doe\", (String)\"xxxAAA\", (String)\"xxx\"))), (\"xxx\")).equals((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"xxxAAA\", (String)\"xxx\")))));\n    assert(filterBySubstring((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"asd\", (String)\"aaaxxy\", (String)\"john doe\", (String)\"xxxAAA\", (String)\"xxx\"))), (\"xx\")).equals((new ArrayList<String>(Arrays.asList((String)\"xxx\", (String)\"aaaxxy\", (String)\"xxxAAA\", (String)\"xxx\")))));\n    assert(filterBySubstring((new ArrayList<String>(Arrays.asList((String)\"grunt\", (String)\"trumpet\", (String)\"prune\", (String)\"gruesome\"))), (\"run\")).equals((new ArrayList<String>(Arrays.asList((String)\"grunt\", (String)\"prune\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_7_filter_by_substring"}
{"name": "HumanEval_99_closest_integer", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Create a function that takes a value (string) representing a number\n    // and returns the closest integer to it. If the number is equidistant\n    // from two integers, round it away from zero.\n    // Examples\n    // >>> closestInteger((\"10\"))\n    // (10l)\n    // >>> closestInteger((\"15.3\"))\n    // (15l)\n    // Note:\n    // Rounding away from zero means that if the given number is equidistant\n    // from two integers, the one you should return is the one that is the\n    // farthest from zero. For example closest_integer(\"14.5\") should\n    // return 15 and closest_integer(\"-14.5\") should return -15.\n    public static long closestInteger(String value) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(closestInteger((\"10\")) == (10l));\n    assert(closestInteger((\"14.5\")) == (15l));\n    assert(closestInteger((\"-15.5\")) == (-16l));\n    assert(closestInteger((\"15.3\")) == (15l));\n    assert(closestInteger((\"0\")) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_99_closest_integer"}
{"name": "HumanEval_64_vowels_count", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function vowels_count which takes a string representing\n    // a word as input and returns the number of vowels in the string.\n    // Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n    // vowel, but only when it is at the end of the given word.\n    // Example:\n    // >>> vowelsCount((\"abcde\"))\n    // (2l)\n    // >>> vowelsCount((\"ACEDY\"))\n    // (3l)\n    public static long vowelsCount(String s) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(vowelsCount((\"abcde\")) == (2l));\n    assert(vowelsCount((\"Alone\")) == (3l));\n    assert(vowelsCount((\"key\")) == (2l));\n    assert(vowelsCount((\"bye\")) == (1l));\n    assert(vowelsCount((\"keY\")) == (2l));\n    assert(vowelsCount((\"bYe\")) == (1l));\n    assert(vowelsCount((\"ACEDY\")) == (3l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_64_vowels_count"}
{"name": "HumanEval_158_find_max", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that accepts an array array list of strings.\n    // The array list contains different words. Return the word with maximum number\n    // of unique characters. If multiple strings have maximum number of unique\n    // characters, return the one which comes first in lexicographical order.\n    // >>> findMax((new ArrayList<String>(Arrays.asList((String)\"name\", (String)\"of\", (String)\"string\"))))\n    // (\"string\")\n    // >>> findMax((new ArrayList<String>(Arrays.asList((String)\"name\", (String)\"enam\", (String)\"game\"))))\n    // (\"enam\")\n    // >>> findMax((new ArrayList<String>(Arrays.asList((String)\"aaaaaaa\", (String)\"bb\", (String)\"cc\"))))\n    // (\"aaaaaaa\")\n    public static String findMax(ArrayList<String> words) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"name\", (String)\"of\", (String)\"string\")))).equals((\"string\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"name\", (String)\"enam\", (String)\"game\")))).equals((\"enam\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"aaaaaaa\", (String)\"bb\", (String)\"cc\")))).equals((\"aaaaaaa\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"abc\", (String)\"cba\")))).equals((\"abc\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"play\", (String)\"this\", (String)\"game\", (String)\"of\", (String)\"footbott\")))).equals((\"footbott\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"we\", (String)\"are\", (String)\"gonna\", (String)\"rock\")))).equals((\"gonna\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"we\", (String)\"are\", (String)\"a\", (String)\"mad\", (String)\"nation\")))).equals((\"nation\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"this\", (String)\"is\", (String)\"a\", (String)\"prrk\")))).equals((\"this\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"b\")))).equals((\"b\")));\n    assert(findMax((new ArrayList<String>(Arrays.asList((String)\"play\", (String)\"play\", (String)\"play\")))).equals((\"play\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_158_find_max"}
{"name": "HumanEval_162_string_to_md5", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given a string 'text', return its md5 hash equivalent string.\n    // If 'text' is an empty string, return null.\n    // >>> stringToMd5((\"Hello world\"))\n    // Optional.of(\"3e25960a79dbc69b674cd4ec67a72c62\")\n    public static Optional<String> stringToMd5(String text) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(stringToMd5((\"Hello world\")).equals(Optional.of(\"3e25960a79dbc69b674cd4ec67a72c62\")));\n    assert(stringToMd5((\"\")).equals(Optional.empty()));\n    assert(stringToMd5((\"A B C\")).equals(Optional.of(\"0ef78513b0cb8cef12743f5aeb35f888\")));\n    assert(stringToMd5((\"password\")).equals(Optional.of(\"5f4dcc3b5aa765d61d8327deb882cf99\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_162_string_to_md5"}
{"name": "HumanEval_44_change_base", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Change numerical base of input number x to base.\n    // return string representation after the conversion.\n    // base numbers are less than 10.\n    // >>> changeBase((8l), (3l))\n    // (\"22\")\n    // >>> changeBase((8l), (2l))\n    // (\"1000\")\n    // >>> changeBase((7l), (2l))\n    // (\"111\")\n    public static String changeBase(long x, long base) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(changeBase((8l), (3l)).equals((\"22\")));\n    assert(changeBase((9l), (3l)).equals((\"100\")));\n    assert(changeBase((234l), (2l)).equals((\"11101010\")));\n    assert(changeBase((16l), (2l)).equals((\"10000\")));\n    assert(changeBase((8l), (2l)).equals((\"1000\")));\n    assert(changeBase((7l), (2l)).equals((\"111\")));\n    assert(changeBase((2l), (3l)).equals((\"2\")));\n    assert(changeBase((3l), (4l)).equals((\"3\")));\n    assert(changeBase((4l), (5l)).equals((\"4\")));\n    assert(changeBase((5l), (6l)).equals((\"5\")));\n    assert(changeBase((6l), (7l)).equals((\"6\")));\n    assert(changeBase((7l), (8l)).equals((\"7\")));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_44_change_base"}
{"name": "HumanEval_157_right_angle_triangle", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given the lengths of the three sides of a triangle. Return true if the three\n    // sides form a right-angled triangle, false otherwise.\n    // A right-angled triangle is a triangle in which one angle is right angle or \n    // 90 degree.\n    // Example:\n    // >>> rightAngleTriangle((3l), (4l), (5l))\n    // (true)\n    // >>> rightAngleTriangle((1l), (2l), (3l))\n    // (false)\n    public static boolean rightAngleTriangle(long a, long b, long c) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(rightAngleTriangle((3l), (4l), (5l)) == (true));\n    assert(rightAngleTriangle((1l), (2l), (3l)) == (false));\n    assert(rightAngleTriangle((10l), (6l), (8l)) == (true));\n    assert(rightAngleTriangle((2l), (2l), (2l)) == (false));\n    assert(rightAngleTriangle((7l), (24l), (25l)) == (true));\n    assert(rightAngleTriangle((10l), (5l), (7l)) == (false));\n    assert(rightAngleTriangle((5l), (12l), (13l)) == (true));\n    assert(rightAngleTriangle((15l), (8l), (17l)) == (true));\n    assert(rightAngleTriangle((48l), (55l), (73l)) == (true));\n    assert(rightAngleTriangle((1l), (1l), (1l)) == (false));\n    assert(rightAngleTriangle((2l), (2l), (10l)) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_157_right_angle_triangle"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // It is the last week of the semester and the teacher has to give the grades\n    // to students. The teacher has been making her own algorithm for grading.\n    // The only problem is, she has lost the code she used for grading.\n    // She has given you an array array list of GPAs for some students and you have to write \n    // a function that can output an array array list of letter grades using the following table:\n    // GPA       |    Letter grade\n    // 4.0                A+\n    // > 3.7                A \n    // > 3.3                A- \n    // > 3.0                B+\n    // > 2.7                B \n    // > 2.3                B-\n    // > 2.0                C+\n    // > 1.7                C\n    // > 1.3                C-\n    // > 1.0                D+ \n    // > 0.7                D \n    // > 0.0                D-\n    // 0.0                E\n    // Example:\n    // >>> gradeEquation((new ArrayList<Float>(Arrays.asList((float)4.0f, (float)3l, (float)1.7f, (float)2l, (float)3.5f))))\n    // (new ArrayList<String>(Arrays.asList((String)\"A+\", (String)\"B\", (String)\"C-\", (String)\"C\", (String)\"A-\")))\n    public static ArrayList<String> numericalLetterGrade(ArrayList<Float> grades) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)4.0f, (float)3l, (float)1.7f, (float)2l, (float)3.5f)))).equals((new ArrayList<String>(Arrays.asList((String)\"A+\", (String)\"B\", (String)\"C-\", (String)\"C\", (String)\"A-\")))));\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)1.2f)))).equals((new ArrayList<String>(Arrays.asList((String)\"D+\")))));\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)0.5f)))).equals((new ArrayList<String>(Arrays.asList((String)\"D-\")))));\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)0.0f)))).equals((new ArrayList<String>(Arrays.asList((String)\"E\")))));\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)1.0f, (float)0.3f, (float)1.5f, (float)2.8f, (float)3.3f)))).equals((new ArrayList<String>(Arrays.asList((String)\"D\", (String)\"D-\", (String)\"C-\", (String)\"B\", (String)\"B+\")))));\n    assert(numericalLetterGrade((new ArrayList<Float>(Arrays.asList((float)0.0f, (float)0.7f)))).equals((new ArrayList<String>(Arrays.asList((String)\"E\", (String)\"D-\")))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_81_numerical_letter_grade"}
{"name": "HumanEval_5_intersperse", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Insert a number 'delimeter' between every two consecutive elements of input array list `numbers'\n    // >>> intersperse((new ArrayList<Long>(Arrays.asList())), (4l))\n    // (new ArrayList<Long>(Arrays.asList()))\n    // >>> intersperse((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))), (4l))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)2l, (long)4l, (long)3l)))\n    public static ArrayList<Long> intersperse(ArrayList<Long> numbers, long delimeter) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(intersperse((new ArrayList<Long>(Arrays.asList())), (7l)).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(intersperse((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)3l, (long)2l))), (8l)).equals((new ArrayList<Long>(Arrays.asList((long)5l, (long)8l, (long)6l, (long)8l, (long)3l, (long)8l, (long)2l)))));\n    assert(intersperse((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)2l))), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)2l, (long)2l, (long)2l, (long)2l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_5_intersperse"}
{"name": "HumanEval_146_specialFilter", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Write a function that takes an array array list of numbers as input and returns \n    // the number of elements in the array array list that are greater than 10 and both \n    // first and last digits of a number are odd (1, 3, 5, 7, 9).\n    // For example:\n    // >>> specialFilter((new ArrayList<Long>(Arrays.asList((long)15l, (long)-73l, (long)14l, (long)-15l))))\n    // (1l)\n    // >>> specialFilter((new ArrayList<Long>(Arrays.asList((long)33l, (long)-2l, (long)-3l, (long)45l, (long)21l, (long)109l))))\n    // (2l)\n    public static long specialFilter(ArrayList<Long> nums) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)5l, (long)-2l, (long)1l, (long)-5l)))) == (0l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)15l, (long)-73l, (long)14l, (long)-15l)))) == (1l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)33l, (long)-2l, (long)-3l, (long)45l, (long)21l, (long)109l)))) == (2l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)43l, (long)-12l, (long)93l, (long)125l, (long)121l, (long)109l)))) == (4l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)71l, (long)-2l, (long)-33l, (long)75l, (long)21l, (long)19l)))) == (3l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList((long)1l)))) == (0l));\n    assert(specialFilter((new ArrayList<Long>(Arrays.asList()))) == (0l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_146_specialFilter"}
{"name": "HumanEval_60_sum_to_n", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // sum_to_n is a function that sums numbers from 1 to n.\n    // >>> sumToN((30l))\n    // (465l)\n    // >>> sumToN((100l))\n    // (5050l)\n    // >>> sumToN((5l))\n    // (15l)\n    // >>> sumToN((10l))\n    // (55l)\n    // >>> sumToN((1l))\n    // (1l)\n    public static long sumToN(long n) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sumToN((1l)) == (1l));\n    assert(sumToN((6l)) == (21l));\n    assert(sumToN((11l)) == (66l));\n    assert(sumToN((30l)) == (465l));\n    assert(sumToN((100l)) == (5050l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_60_sum_to_n"}
{"name": "HumanEval_26_remove_duplicates", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // From an array array list of integers, remove all elements that occur more than once.\n    // Keep order of elements left the same as in the input.\n    // >>> removeDuplicates((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)4l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)3l, (long)4l)))\n    public static ArrayList<Long> removeDuplicates(ArrayList<Long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(removeDuplicates((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(removeDuplicates((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))));\n    assert(removeDuplicates((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)4l, (long)3l, (long)5l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)4l, (long)5l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_26_remove_duplicates"}
{"name": "HumanEval_163_generate_integers", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Given two positive integers a and b, return the even digits between a\n    // and b, in ascending order.\n    // For example:\n    // >>> generateIntegers((2l), (8l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l, (long)8l)))\n    // >>> generateIntegers((8l), (2l))\n    // (new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l, (long)8l)))\n    // >>> generateIntegers((10l), (14l))\n    // (new ArrayList<Long>(Arrays.asList()))\n    public static ArrayList<Long> generateIntegers(long a, long b) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(generateIntegers((2l), (10l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l, (long)8l)))));\n    assert(generateIntegers((10l), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l, (long)8l)))));\n    assert(generateIntegers((132l), (2l)).equals((new ArrayList<Long>(Arrays.asList((long)2l, (long)4l, (long)6l, (long)8l)))));\n    assert(generateIntegers((17l), (89l)).equals((new ArrayList<Long>(Arrays.asList()))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_163_generate_integers"}
{"name": "HumanEval_9_rolling_max", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // From a given array list of integers, generate an array array list of rolling maximum element found until given moment\n    // in the sequence.\n    // >>> rollingMax((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)2l, (long)3l, (long)4l, (long)2l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)3l, (long)3l, (long)4l, (long)4l)))\n    public static ArrayList<Long> rollingMax(ArrayList<Long> numbers) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(rollingMax((new ArrayList<Long>(Arrays.asList()))).equals((new ArrayList<Long>(Arrays.asList()))));\n    assert(rollingMax((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l, (long)4l)))));\n    assert(rollingMax((new ArrayList<Long>(Arrays.asList((long)4l, (long)3l, (long)2l, (long)1l)))).equals((new ArrayList<Long>(Arrays.asList((long)4l, (long)4l, (long)4l, (long)4l)))));\n    assert(rollingMax((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)3l, (long)100l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l, (long)3l, (long)100l, (long)100l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_9_rolling_max"}
{"name": "HumanEval_3_below_zero", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You're given an array array list of deposit and withdrawal operations on a bank account that starts with\n    // zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n    // at that point function should return true. Otherwise it should return false.\n    // >>> belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (false)\n    // >>> belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)-4l, (long)5l))))\n    // (true)\n    public static boolean belowZero(ArrayList<Long> operations) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(belowZero((new ArrayList<Long>(Arrays.asList()))) == (false));\n    assert(belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)-3l, (long)1l, (long)2l, (long)-3l)))) == (false));\n    assert(belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)-4l, (long)5l, (long)6l)))) == (true));\n    assert(belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l, (long)2l, (long)-2l, (long)5l, (long)-5l, (long)4l, (long)-4l)))) == (false));\n    assert(belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)-1l, (long)2l, (long)-2l, (long)5l, (long)-5l, (long)4l, (long)-5l)))) == (true));\n    assert(belowZero((new ArrayList<Long>(Arrays.asList((long)1l, (long)-2l, (long)2l, (long)-2l, (long)5l, (long)-5l, (long)4l, (long)-4l)))) == (true));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_3_below_zero"}
{"name": "HumanEval_69_search", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // You are given a non-empty array list of positive integers. Return the greatest integer that is greater than \n    // zero, and has a frequency greater than or equal to the value of the integer itself. \n    // The frequency of an integer is the number of times it appears in the array list.\n    // If no such a value exist, return -1.\n    // Examples:\n    // >>> search((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)2l, (long)2l, (long)3l, (long)1l))))\n    // (2l)\n    // >>> search((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)2l, (long)3l, (long)3l, (long)3l, (long)4l, (long)4l, (long)4l))))\n    // (3l)\n    // >>> search((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)4l, (long)4l, (long)4l))))\n    // (-1l)\n    public static long search(ArrayList<Long> lst) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(search((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)5l, (long)5l, (long)1l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)4l, (long)1l, (long)4l, (long)1l, (long)4l, (long)4l)))) == (4l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)3l, (long)3l)))) == (-1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)8l, (long)8l, (long)8l, (long)8l, (long)8l, (long)8l, (long)8l, (long)8l)))) == (8l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)2l, (long)3l, (long)3l, (long)2l, (long)2l)))) == (2l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)2l, (long)7l, (long)8l, (long)8l, (long)4l, (long)8l, (long)7l, (long)3l, (long)9l, (long)6l, (long)5l, (long)10l, (long)4l, (long)3l, (long)6l, (long)7l, (long)1l, (long)7l, (long)4l, (long)10l, (long)8l, (long)1l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)3l, (long)2l, (long)8l, (long)2l)))) == (2l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)6l, (long)7l, (long)1l, (long)8l, (long)8l, (long)10l, (long)5l, (long)8l, (long)5l, (long)3l, (long)10l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)8l, (long)8l, (long)3l, (long)6l, (long)5l, (long)6l, (long)4l)))) == (-1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)6l, (long)9l, (long)6l, (long)7l, (long)1l, (long)4l, (long)7l, (long)1l, (long)8l, (long)8l, (long)9l, (long)8l, (long)10l, (long)10l, (long)8l, (long)4l, (long)10l, (long)4l, (long)10l, (long)1l, (long)2l, (long)9l, (long)5l, (long)7l, (long)9l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)1l, (long)9l, (long)10l, (long)1l, (long)3l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)6l, (long)9l, (long)7l, (long)5l, (long)8l, (long)7l, (long)5l, (long)3l, (long)7l, (long)5l, (long)10l, (long)10l, (long)3l, (long)6l, (long)10l, (long)2l, (long)8l, (long)6l, (long)5l, (long)4l, (long)9l, (long)5l, (long)3l, (long)10l)))) == (5l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)1l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)8l, (long)8l, (long)10l, (long)6l, (long)4l, (long)3l, (long)5l, (long)8l, (long)2l, (long)4l, (long)2l, (long)8l, (long)4l, (long)6l, (long)10l, (long)4l, (long)2l, (long)1l, (long)10l, (long)2l, (long)1l, (long)1l, (long)5l)))) == (4l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)2l, (long)10l, (long)4l, (long)8l, (long)2l, (long)10l, (long)5l, (long)1l, (long)2l, (long)9l, (long)5l, (long)5l, (long)6l, (long)3l, (long)8l, (long)6l, (long)4l, (long)10l)))) == (2l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)1l, (long)6l, (long)10l, (long)1l, (long)6l, (long)9l, (long)10l, (long)8l, (long)6l, (long)8l, (long)7l, (long)3l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)9l, (long)2l, (long)4l, (long)1l, (long)5l, (long)1l, (long)5l, (long)2l, (long)5l, (long)7l, (long)7l, (long)7l, (long)3l, (long)10l, (long)1l, (long)5l, (long)4l, (long)2l, (long)8l, (long)4l, (long)1l, (long)9l, (long)10l, (long)7l, (long)10l, (long)2l, (long)8l, (long)10l, (long)9l, (long)4l)))) == (4l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)2l, (long)6l, (long)4l, (long)2l, (long)8l, (long)7l, (long)5l, (long)6l, (long)4l, (long)10l, (long)4l, (long)6l, (long)3l, (long)7l, (long)8l, (long)8l, (long)3l, (long)1l, (long)4l, (long)2l, (long)2l, (long)10l, (long)7l)))) == (4l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)9l, (long)8l, (long)6l, (long)10l, (long)2l, (long)6l, (long)10l, (long)2l, (long)7l, (long)8l, (long)10l, (long)3l, (long)8l, (long)2l, (long)6l, (long)2l, (long)3l, (long)1l)))) == (2l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)5l, (long)5l, (long)3l, (long)9l, (long)5l, (long)6l, (long)3l, (long)2l, (long)8l, (long)5l, (long)6l, (long)10l, (long)10l, (long)6l, (long)8l, (long)4l, (long)10l, (long)7l, (long)7l, (long)10l, (long)8l)))) == (-1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)10l)))) == (-1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)9l, (long)7l, (long)7l, (long)2l, (long)4l, (long)7l, (long)2l, (long)10l, (long)9l, (long)7l, (long)5l, (long)7l, (long)2l)))) == (2l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)5l, (long)4l, (long)10l, (long)2l, (long)1l, (long)1l, (long)10l, (long)3l, (long)6l, (long)1l, (long)8l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)7l, (long)9l, (long)9l, (long)9l, (long)3l, (long)4l, (long)1l, (long)5l, (long)9l, (long)1l, (long)2l, (long)1l, (long)1l, (long)10l, (long)7l, (long)5l, (long)6l, (long)7l, (long)6l, (long)7l, (long)7l, (long)6l)))) == (1l));\n    assert(search((new ArrayList<Long>(Arrays.asList((long)3l, (long)10l, (long)10l, (long)9l, (long)2l)))) == (-1l));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_69_search"}
{"name": "HumanEval_61_correct_bracketing", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // brackets is a string of \"(\" and \")\".\n    // return true if every opening bracket has a corresponding closing bracket.\n    // >>> correctBracketing((\"(\"))\n    // (false)\n    // >>> correctBracketing((\"()\"))\n    // (true)\n    // >>> correctBracketing((\"(()())\"))\n    // (true)\n    // >>> correctBracketing((\")(()\"))\n    // (false)\n    public static boolean correctBracketing(String brackets) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(correctBracketing((\"()\")) == (true));\n    assert(correctBracketing((\"(()())\")) == (true));\n    assert(correctBracketing((\"()()(()())()\")) == (true));\n    assert(correctBracketing((\"()()((()()())())(()()(()))\")) == (true));\n    assert(correctBracketing((\"((()())))\")) == (false));\n    assert(correctBracketing((\")(()\")) == (false));\n    assert(correctBracketing((\"(\")) == (false));\n    assert(correctBracketing((\"((((\")) == (false));\n    assert(correctBracketing((\")\")) == (false));\n    assert(correctBracketing((\"(()\")) == (false));\n    assert(correctBracketing((\"()()(()())())(()\")) == (false));\n    assert(correctBracketing((\"()()(()())()))()\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_61_correct_bracketing"}
{"name": "HumanEval_37_sort_even", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // This function takes an array array list l and returns an array array list l' such that\n    // l' is identical to l in the odd indicies, while its values at the even indicies are equal\n    // to the values of the even indicies of l, but sorted.\n    // >>> sortEven((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l))))\n    // (new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))\n    // >>> sortEven((new ArrayList<Long>(Arrays.asList((long)5l, (long)6l, (long)3l, (long)4l))))\n    // (new ArrayList<Long>(Arrays.asList((long)3l, (long)6l, (long)5l, (long)4l)))\n    public static ArrayList<Long> sortEven(ArrayList<Long> l) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sortEven((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))).equals((new ArrayList<Long>(Arrays.asList((long)1l, (long)2l, (long)3l)))));\n    assert(sortEven((new ArrayList<Long>(Arrays.asList((long)5l, (long)3l, (long)-5l, (long)2l, (long)-3l, (long)3l, (long)9l, (long)0l, (long)123l, (long)1l, (long)-10l)))).equals((new ArrayList<Long>(Arrays.asList((long)-10l, (long)3l, (long)-5l, (long)2l, (long)-3l, (long)3l, (long)5l, (long)0l, (long)9l, (long)1l, (long)123l)))));\n    assert(sortEven((new ArrayList<Long>(Arrays.asList((long)5l, (long)8l, (long)-12l, (long)4l, (long)23l, (long)2l, (long)3l, (long)11l, (long)12l, (long)-10l)))).equals((new ArrayList<Long>(Arrays.asList((long)-12l, (long)8l, (long)3l, (long)4l, (long)5l, (long)2l, (long)12l, (long)11l, (long)23l, (long)-10l)))));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_37_sort_even"}
{"name": "HumanEval_54_same_chars", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // Check if two words have the same characters.\n    // >>> sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\"))\n    // (true)\n    // >>> sameChars((\"abcd\"), (\"dddddddabc\"))\n    // (true)\n    // >>> sameChars((\"dddddddabc\"), (\"abcd\"))\n    // (true)\n    // >>> sameChars((\"eabcd\"), (\"dddddddabc\"))\n    // (false)\n    // >>> sameChars((\"abcd\"), (\"dddddddabce\"))\n    // (false)\n    // >>> sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\"))\n    // (false)\n    public static boolean sameChars(String s0, String s1) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddeddabc\")) == (true));\n    assert(sameChars((\"abcd\"), (\"dddddddabc\")) == (true));\n    assert(sameChars((\"dddddddabc\"), (\"abcd\")) == (true));\n    assert(sameChars((\"eabcd\"), (\"dddddddabc\")) == (false));\n    assert(sameChars((\"abcd\"), (\"dddddddabcf\")) == (false));\n    assert(sameChars((\"eabcdzzzz\"), (\"dddzzzzzzzddddabc\")) == (false));\n    assert(sameChars((\"aabb\"), (\"aaccc\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_54_same_chars"}
{"name": "HumanEval_56_correct_bracketing", "language": "java", "prompt": "import java.util.*;\nimport java.lang.reflect.*;\nimport org.javatuples.*;\nimport java.security.*;\nimport java.math.*;\nimport java.io.*;\nimport java.util.stream.*;\nclass Problem {\n    // brackets is a string of \"<\" and \">\".\n    // return true if every opening bracket has a corresponding closing bracket.\n    // >>> correctBracketing((\"<\"))\n    // (false)\n    // >>> correctBracketing((\"<>\"))\n    // (true)\n    // >>> correctBracketing((\"<<><>>\"))\n    // (true)\n    // >>> correctBracketing((\"><<>\"))\n    // (false)\n    public static boolean correctBracketing(String brackets) {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "    }\n    public static void main(String[] args) {\n    assert(correctBracketing((\"<>\")) == (true));\n    assert(correctBracketing((\"<<><>>\")) == (true));\n    assert(correctBracketing((\"<><><<><>><>\")) == (true));\n    assert(correctBracketing((\"<><><<<><><>><>><<><><<>>>\")) == (true));\n    assert(correctBracketing((\"<<<><>>>>\")) == (false));\n    assert(correctBracketing((\"><<>\")) == (false));\n    assert(correctBracketing((\"<\")) == (false));\n    assert(correctBracketing((\"<<<<\")) == (false));\n    assert(correctBracketing((\">\")) == (false));\n    assert(correctBracketing((\"<<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>><<>\")) == (false));\n    assert(correctBracketing((\"<><><<><>><>>><>\")) == (false));\n    }\n\n}\n", "stop_tokens": ["\n    }\n"], "task_id": "HumanEval_56_correct_bracketing"}
