{"name": "HumanEval_23_strlen", "language": "go_test.go", "prompt": "package strlen_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return length of given string\n// >>> strlen(\"\")\n// 0\n// >>> strlen(\"abc\")\n// 3\nfunc strlen(myString string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "func TestStrlen(t *testing.T) {\n  candidate := strlen\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"x\"), expected: 1 },\n     { actual: candidate(\"asdasnakj\"), expected: 9 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_23_strlen", "test": "func TestStrlen(t *testing.T) {\n  candidate := strlen\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"x\"), expected: 1 },\n     { actual: candidate(\"asdasnakj\"), expected: 9 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_89_encrypt", "language": "go_test.go", "prompt": "package encrypt_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function encrypt that takes a string as an argument and\n// returns a string encrypted with the alphabet being rotated. \n// The alphabet should be rotated in a manner such that the letters \n// shift down by two multiplied to two places.\n// For example:\n// >>> encrypt(\"hi\")\n// \"lm\"\n// >>> encrypt(\"asdfghjkl\")\n// \"ewhjklnop\"\n// >>> encrypt(\"gf\")\n// \"kj\"\n// >>> encrypt(\"et\")\n// \"ix\"\nfunc encrypt(s string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "func TestEncrypt(t *testing.T) {\n  candidate := encrypt\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"hi\"), expected: \"lm\" },\n     { actual: candidate(\"asdfghjkl\"), expected: \"ewhjklnop\" },\n     { actual: candidate(\"gf\"), expected: \"kj\" },\n     { actual: candidate(\"et\"), expected: \"ix\" },\n     { actual: candidate(\"faewfawefaewg\"), expected: \"jeiajeaijeiak\" },\n     { actual: candidate(\"hellomyfriend\"), expected: \"lippsqcjvmirh\" },\n     { actual: candidate(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\"), expected: \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" },\n     { actual: candidate(\"a\"), expected: \"e\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_89_encrypt", "test": "func TestEncrypt(t *testing.T) {\n  candidate := encrypt\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"hi\"), expected: \"lm\" },\n     { actual: candidate(\"asdfghjkl\"), expected: \"ewhjklnop\" },\n     { actual: candidate(\"gf\"), expected: \"kj\" },\n     { actual: candidate(\"et\"), expected: \"ix\" },\n     { actual: candidate(\"faewfawefaewg\"), expected: \"jeiajeaijeiak\" },\n     { actual: candidate(\"hellomyfriend\"), expected: \"lippsqcjvmirh\" },\n     { actual: candidate(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\"), expected: \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" },\n     { actual: candidate(\"a\"), expected: \"e\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_95_check_dict_case", "language": "go_test.go", "prompt": "package check_dict_case_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a map, return true if all keys are strings in lower \n// case or all keys are strings in upper case, else return false.\n// The function should return false is the given map is empty.\n// Examples:\n// >>> check_dict_case(map[string]string{\"a\": \"apple\", \"b\": \"banana\"})\n// true\n// >>> check_dict_case(map[string]string{\"a\": \"apple\", \"A\": \"banana\", \"B\": \"banana\"})\n// false\n// >>> check_dict_case(map[interface{}]string{\"a\": \"apple\", 8: \"banana\", \"a\": \"apple\"})\n// false\n// >>> check_dict_case(map[string]string{\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"})\n// false\n// >>> check_dict_case(map[string]string{\"STATE\": \"NC\", \"ZIP\": \"12345\"})\n// true\nfunc check_dict_case(dict map[string]string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "func TestCheck_Dict_Case(t *testing.T) {\n  candidate := check_dict_case\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"b\": \"banana\"}), expected: true },\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"A\": \"banana\", \"B\": \"banana\"}), expected: false },\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"5\": \"banana\", \"a\": \"apple\"}), expected: false },\n     { actual: candidate(map[string]string{\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"}), expected: false },\n     { actual: candidate(map[string]string{\"STATE\": \"NC\", \"ZIP\": \"12345\"}), expected: true },\n     { actual: candidate(map[string]string{\"fruit\": \"Orange\", \"taste\": \"Sweet\"}), expected: true },\n     { actual: candidate(map[string]string{}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_95_check_dict_case", "test": "func TestCheck_Dict_Case(t *testing.T) {\n  candidate := check_dict_case\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"b\": \"banana\"}), expected: true },\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"A\": \"banana\", \"B\": \"banana\"}), expected: false },\n     { actual: candidate(map[string]string{\"p\": \"pineapple\", \"5\": \"banana\", \"a\": \"apple\"}), expected: false },\n     { actual: candidate(map[string]string{\"Name\": \"John\", \"Age\": \"36\", \"City\": \"Houston\"}), expected: false },\n     { actual: candidate(map[string]string{\"STATE\": \"NC\", \"ZIP\": \"12345\"}), expected: true },\n     { actual: candidate(map[string]string{\"fruit\": \"Orange\", \"taste\": \"Sweet\"}), expected: true },\n     { actual: candidate(map[string]string{}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_85_add", "language": "go_test.go", "prompt": "package add_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a non-empty list of integers lst. add the even elements that are at odd indices..\n// Examples:\n// >>> add([]int{4, 2, 6, 7})\n// 2\nfunc add(lst []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "func TestAdd(t *testing.T) {\n  candidate := add\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{4, 88}), expected: 88 },\n     { actual: candidate([]int{4, 5, 6, 7, 2, 122}), expected: 122 },\n     { actual: candidate([]int{4, 0, 6, 7}), expected: 0 },\n     { actual: candidate([]int{4, 4, 6, 8}), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_85_add", "test": "func TestAdd(t *testing.T) {\n  candidate := add\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{4, 88}), expected: 88 },\n     { actual: candidate([]int{4, 5, 6, 7, 2, 122}), expected: 122 },\n     { actual: candidate([]int{4, 0, 6, 7}), expected: 0 },\n     { actual: candidate([]int{4, 4, 6, 8}), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_140_fix_spaces", "language": "go_test.go", "prompt": "package fix_spaces_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a string text, replace all spaces in it with underscores, \n// and if a string has more than 2 consecutive spaces, \n// then replace all consecutive spaces with - \n// >>> fix_spaces(\" Example\")\n// \"Example\"\n// >>> fix_spaces(\" Example 1\")\n// \"Example_1\"\n// >>> fix_spaces(\" Example 2\")\n// \"_Example_2\"\n// >>> fix_spaces(\" Example 3\")\n// \"_Example-3\"\nfunc fix_spaces(text string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "func TestFix_Spaces(t *testing.T) {\n  candidate := fix_spaces\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Example\"), expected: \"Example\" },\n     { actual: candidate(\"Mudasir Hanif \"), expected: \"Mudasir_Hanif_\" },\n     { actual: candidate(\"Yellow Yellow  Dirty  Fellow\"), expected: \"Yellow_Yellow__Dirty__Fellow\" },\n     { actual: candidate(\"Exa   mple\"), expected: \"Exa-mple\" },\n     { actual: candidate(\"   Exa 1 2 2 mple\"), expected: \"-Exa_1_2_2_mple\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_140_fix_spaces", "test": "func TestFix_Spaces(t *testing.T) {\n  candidate := fix_spaces\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Example\"), expected: \"Example\" },\n     { actual: candidate(\"Mudasir Hanif \"), expected: \"Mudasir_Hanif_\" },\n     { actual: candidate(\"Yellow Yellow  Dirty  Fellow\"), expected: \"Yellow_Yellow__Dirty__Fellow\" },\n     { actual: candidate(\"Exa   mple\"), expected: \"Exa-mple\" },\n     { actual: candidate(\"   Exa 1 2 2 mple\"), expected: \"-Exa_1_2_2_mple\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_63_fibfib", "language": "go_test.go", "prompt": "package fibfib_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fibfib(0) == 0\n// fibfib(1) == 0\n// fibfib(2) == 1\n// fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n// Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n// >>> fibfib(1)\n// 0\n// >>> fibfib(5)\n// 4\n// >>> fibfib(8)\n// 24\nfunc fibfib(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "func TestFibfib(t *testing.T) {\n  candidate := fibfib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: 1 },\n     { actual: candidate(1), expected: 0 },\n     { actual: candidate(5), expected: 4 },\n     { actual: candidate(8), expected: 24 },\n     { actual: candidate(10), expected: 81 },\n     { actual: candidate(12), expected: 274 },\n     { actual: candidate(14), expected: 927 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_63_fibfib", "test": "func TestFibfib(t *testing.T) {\n  candidate := fibfib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: 1 },\n     { actual: candidate(1), expected: 0 },\n     { actual: candidate(5), expected: 4 },\n     { actual: candidate(8), expected: 24 },\n     { actual: candidate(10), expected: 81 },\n     { actual: candidate(12), expected: 274 },\n     { actual: candidate(14), expected: 927 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_151_double_the_difference", "language": "go_test.go", "prompt": "package double_the_difference_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of numbers, return the sum of squares of the numbers\n// in the list that are odd. Ignore numbers that are negative or not integers.\n// >>> double_the_difference([]int{1, 3, 2, 0})\n// 10\n// >>> double_the_difference([]int{-1, -2, 0})\n// 0\n// >>> double_the_difference([]int{9, -2})\n// 81\n// >>> double_the_difference([]int{0})\n// 0\n// If the input list is empty, return 0.\nfunc double_the_difference(lst []float64) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "func TestDouble_The_Difference(t *testing.T) {\n  candidate := double_the_difference\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{}), expected: 0 },\n     { actual: candidate([]float64{5.0, 4.0}), expected: 25 },\n     { actual: candidate([]float64{0.1, 0.2, 0.3}), expected: 0 },\n     { actual: candidate([]float64{-10.0, -20.0, -30.0}), expected: 0 },\n     { actual: candidate([]float64{-1.0, -2.0, 8.0}), expected: 0 },\n     { actual: candidate([]float64{0.2, 3.0, 5.0}), expected: 34 },\n     { actual: candidate([]float64{-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0}), expected: 165 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_151_double_the_difference", "test": "func TestDouble_The_Difference(t *testing.T) {\n  candidate := double_the_difference\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{}), expected: 0 },\n     { actual: candidate([]float64{5.0, 4.0}), expected: 25 },\n     { actual: candidate([]float64{0.1, 0.2, 0.3}), expected: 0 },\n     { actual: candidate([]float64{-10.0, -20.0, -30.0}), expected: 0 },\n     { actual: candidate([]float64{-1.0, -2.0, 8.0}), expected: 0 },\n     { actual: candidate([]float64{0.2, 3.0, 5.0}), expected: 34 },\n     { actual: candidate([]float64{-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0}), expected: 165 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_22_filter_integers", "language": "go_test.go", "prompt": "package filter_integers_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Filter given list of any gothon values only for integers\n// >>> filter_integers([]float64{\"a\", 3.14, 5})\n// []int{5}\n// >>> filter_integers([]interface{}{1, 2, 3, \"abc\", map[interface{}]interface{}{}, []interface{}{}})\n// []int{1, 2, 3}\nfunc filter_integers(values []interface{}) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "func TestFilter_Integers(t *testing.T) {\n  candidate := filter_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]interface{}{}), expected: []int{} },\n     { actual: candidate([]interface{}{4, map[interface{}]interface{}{}, []interface{}{}, 23.2, 9, \"adasd\"}), expected: []int{4, 9} },\n     { actual: candidate([]interface{}{3, \"c\", 3, 3, \"a\", \"b\"}), expected: []int{3, 3, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_22_filter_integers", "test": "func TestFilter_Integers(t *testing.T) {\n  candidate := filter_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]interface{}{}), expected: []int{} },\n     { actual: candidate([]interface{}{4, map[interface{}]interface{}{}, []interface{}{}, 23.2, 9, \"adasd\"}), expected: []int{4, 9} },\n     { actual: candidate([]interface{}{3, \"c\", 3, 3, \"a\", \"b\"}), expected: []int{3, 3, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_41_car_race_collision", "language": "go_test.go", "prompt": "package car_race_collision_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Imagine a road that's a perfectly straight infinitely long line.\n// n cars are driving left to right;  simultaneously, a different set of n cars\n// are driving right to left.   The two sets of cars start out being very far from\n// each other.  All cars move in the same speed.  Two cars are said to collide\n// when a car that's moving left to right hits a car that's moving right to left.\n// However, the cars are infinitely sturdy and strong; as a result, they continue moving\n// in their trajectory as if they did not collide.\n// This function outputs the number of such collisions.\nfunc car_race_collision(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "func TestCar_Race_Collision(t *testing.T) {\n  candidate := car_race_collision\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: 4 },\n     { actual: candidate(3), expected: 9 },\n     { actual: candidate(4), expected: 16 },\n     { actual: candidate(8), expected: 64 },\n     { actual: candidate(10), expected: 100 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_41_car_race_collision", "test": "func TestCar_Race_Collision(t *testing.T) {\n  candidate := car_race_collision\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: 4 },\n     { actual: candidate(3), expected: 9 },\n     { actual: candidate(4), expected: 16 },\n     { actual: candidate(8), expected: 64 },\n     { actual: candidate(10), expected: 100 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_17_parse_music", "language": "go_test.go", "prompt": "package parse_music_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Input to this function is a string representing musical notes in a special ASCII format.\n// Your task is to parse this string and return list of integers corresponding to how many beats does each\n// not last.\n// Here is a legend:\n// 'o' - whole note, lasts four beats\n// 'o|' - half note, lasts two beats\n// '.|' - quater note, lasts one beat\n// >>> parse_music(\"o o| .| o| o| .| .| .| .| o o\")\n// []int{4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4}\nfunc parse_music(music_string string) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "func TestParse_Music(t *testing.T) {\n  candidate := parse_music\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: []int{} },\n     { actual: candidate(\"o o o o\"), expected: []int{4, 4, 4, 4} },\n     { actual: candidate(\".| .| .| .|\"), expected: []int{1, 1, 1, 1} },\n     { actual: candidate(\"o| o| .| .| o o o o\"), expected: []int{2, 2, 1, 1, 4, 4, 4, 4} },\n     { actual: candidate(\"o| .| o| .| o o| o o|\"), expected: []int{2, 1, 2, 1, 4, 2, 4, 2} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_17_parse_music", "test": "func TestParse_Music(t *testing.T) {\n  candidate := parse_music\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: []int{} },\n     { actual: candidate(\"o o o o\"), expected: []int{4, 4, 4, 4} },\n     { actual: candidate(\".| .| .| .|\"), expected: []int{1, 1, 1, 1} },\n     { actual: candidate(\"o| o| .| .| o o o o\"), expected: []int{2, 2, 1, 1, 4, 4, 4, 4} },\n     { actual: candidate(\"o| .| o| .| o o| o o|\"), expected: []int{2, 1, 2, 1, 4, 2, 4, 2} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_79_decimal_to_binary", "language": "go_test.go", "prompt": "package decimal_to_binary_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You will be given a number in decimal form and your task is to convert it to\n// binary format. The function should return a string, with each character representing a binary\n// number. Each character in the string will be '0' or '1'.\n// There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n// The extra characters are there to help with the format.\n// Examples:\n// >>> decimal_to_binary(15)\n// \"db1111db\"\n// >>> decimal_to_binary(32)\n// \"db100000db\"\nfunc decimal_to_binary(decimal int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "func TestDecimal_To_Binary(t *testing.T) {\n  candidate := decimal_to_binary\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0), expected: \"db0db\" },\n     { actual: candidate(32), expected: \"db100000db\" },\n     { actual: candidate(103), expected: \"db1100111db\" },\n     { actual: candidate(15), expected: \"db1111db\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_79_decimal_to_binary", "test": "func TestDecimal_To_Binary(t *testing.T) {\n  candidate := decimal_to_binary\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0), expected: \"db0db\" },\n     { actual: candidate(32), expected: \"db100000db\" },\n     { actual: candidate(103), expected: \"db1100111db\" },\n     { actual: candidate(15), expected: \"db1111db\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_14_all_prefixes", "language": "go_test.go", "prompt": "package all_prefixes_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return list of all prefixes from shortest to longest of the input string\n// >>> all_prefixes(\"abc\")\n// []string{\"a\", \"ab\", \"abc\"}\nfunc all_prefixes(myString string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "func TestAll_Prefixes(t *testing.T) {\n  candidate := all_prefixes\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: []string{} },\n     { actual: candidate(\"asdfgh\"), expected: []string{\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"} },\n     { actual: candidate(\"WWW\"), expected: []string{\"W\", \"WW\", \"WWW\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_14_all_prefixes", "test": "func TestAll_Prefixes(t *testing.T) {\n  candidate := all_prefixes\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: []string{} },\n     { actual: candidate(\"asdfgh\"), expected: []string{\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"} },\n     { actual: candidate(\"WWW\"), expected: []string{\"W\", \"WW\", \"WWW\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_53_add", "language": "go_test.go", "prompt": "package add_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Add two numbers x and y\n// >>> add(2, 3)\n// 5\n// >>> add(5, 7)\n// 12\nfunc add(x int, y int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "func TestAdd(t *testing.T) {\n  candidate := add\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0, 1), expected: 1 },\n     { actual: candidate(1, 0), expected: 1 },\n     { actual: candidate(2, 3), expected: 5 },\n     { actual: candidate(5, 7), expected: 12 },\n     { actual: candidate(7, 5), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_53_add", "test": "func TestAdd(t *testing.T) {\n  candidate := add\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0, 1), expected: 1 },\n     { actual: candidate(1, 0), expected: 1 },\n     { actual: candidate(2, 3), expected: 5 },\n     { actual: candidate(5, 7), expected: 12 },\n     { actual: candidate(7, 5), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_159_eat", "language": "go_test.go", "prompt": "package eat_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You're a hungry rabbit, and you already have eaten a certain number of carrots,\n// but now you need to eat more carrots to complete the day's meals.\n// you should return a list of [ total number of eaten carrots after your meals,\n// the number of carrots left after your meals ]\n// if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n// Example:\n// >>> eat(5, 6, 10)\n// []int{11, 4}\n// >>> eat(4, 8, 9)\n// []int{12, 1}\n// >>> eat(1, 10, 10)\n// []int{11, 0}\n// >>> eat(2, 11, 5)\n// []int{7, 0}\n// Variables:\n// @number : integer\n// the number of carrots that you have eaten.\n// @need : integer\n// the number of carrots that you need to eat.\n// @remaining : integer\n// the number of remaining carrots thet exist in stock\n// Constrain:\n// * 0 <= number <= 1000\n// * 0 <= need <= 1000\n// * 0 <= remaining <= 1000\n// Have fun :)\nfunc eat(number int, need int, remaining int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "func TestEat(t *testing.T) {\n  candidate := eat\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5, 6, 10), expected: []int{11, 4} },\n     { actual: candidate(4, 8, 9), expected: []int{12, 1} },\n     { actual: candidate(1, 10, 10), expected: []int{11, 0} },\n     { actual: candidate(2, 11, 5), expected: []int{7, 0} },\n     { actual: candidate(4, 5, 7), expected: []int{9, 2} },\n     { actual: candidate(4, 5, 1), expected: []int{5, 0} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_159_eat", "test": "func TestEat(t *testing.T) {\n  candidate := eat\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5, 6, 10), expected: []int{11, 4} },\n     { actual: candidate(4, 8, 9), expected: []int{12, 1} },\n     { actual: candidate(1, 10, 10), expected: []int{11, 0} },\n     { actual: candidate(2, 11, 5), expected: []int{7, 0} },\n     { actual: candidate(4, 5, 7), expected: []int{9, 2} },\n     { actual: candidate(4, 5, 1), expected: []int{5, 0} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_115_max_fill", "language": "go_test.go", "prompt": "package max_fill_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a rectangular grid of wells. Each row represents a single well,\n// and each 1 in a row represents a single unit of water.\n// Each well has a corresponding bucket that can be used to extract water from it, \n// and all buckets have the same capacity.\n// Your task is to use the buckets to empty the wells.\n// Output the number of times you need to lower the buckets.\n// Example 1:\n// >>> max_fill([][]int{[]int{0, 0, 1, 0}, []int{0, 1, 0, 0}, []int{1, 1, 1, 1}}, 1)\n// 6\n// Example 2:\n// >>> max_fill([][]int{[]int{0, 0, 1, 1}, []int{0, 0, 0, 0}, []int{1, 1, 1, 1}, []int{0, 1, 1, 1}}, 2)\n// 5\n// Example 3:\n// >>> max_fill([][]int{[]int{0, 0, 0}, []int{0, 0, 0}}, 5)\n// 0\n// Constraints:\n// * all wells have the same length\n// * 1 <= grid.length <= 10^2\n// * 1 <= grid[:,1].length <= 10^2\n// * grid[i][j] -> 0 | 1\n// * 1 <= capacity <= 10\nfunc max_fill(grid [][]int, capacity int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "func TestMax_Fill(t *testing.T) {\n  candidate := max_fill\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{0, 0, 1, 0}, []int{0, 1, 0, 0}, []int{1, 1, 1, 1}}, 1), expected: 6 },\n     { actual: candidate([][]int{[]int{0, 0, 1, 1}, []int{0, 0, 0, 0}, []int{1, 1, 1, 1}, []int{0, 1, 1, 1}}, 2), expected: 5 },\n     { actual: candidate([][]int{[]int{0, 0, 0}, []int{0, 0, 0}}, 5), expected: 0 },\n     { actual: candidate([][]int{[]int{1, 1, 1, 1}, []int{1, 1, 1, 1}}, 2), expected: 4 },\n     { actual: candidate([][]int{[]int{1, 1, 1, 1}, []int{1, 1, 1, 1}}, 9), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_115_max_fill", "test": "func TestMax_Fill(t *testing.T) {\n  candidate := max_fill\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{0, 0, 1, 0}, []int{0, 1, 0, 0}, []int{1, 1, 1, 1}}, 1), expected: 6 },\n     { actual: candidate([][]int{[]int{0, 0, 1, 1}, []int{0, 0, 0, 0}, []int{1, 1, 1, 1}, []int{0, 1, 1, 1}}, 2), expected: 5 },\n     { actual: candidate([][]int{[]int{0, 0, 0}, []int{0, 0, 0}}, 5), expected: 0 },\n     { actual: candidate([][]int{[]int{1, 1, 1, 1}, []int{1, 1, 1, 1}}, 2), expected: 4 },\n     { actual: candidate([][]int{[]int{1, 1, 1, 1}, []int{1, 1, 1, 1}}, 9), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_160_do_algebra", "language": "go_test.go", "prompt": "package do_algebra_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given two lists operator, and operand. The first list has basic algebra operations, and \n// the second list is a list of integers. Use the two given lists to build the algebric \n// expression and return the evaluation of this expression.\n// The basic algebra operations:\n// Addition ( + ) \n// Subtraction ( - ) \n// Multiplication ( * ) \n// Floor division ( // ) \n// Exponentiation ( ** ) \n// Example:\n// operator['+', '*', '-']\n// list = [2, 3, 4, 5]\n// result = 2 + 3 * 4 - 5\n// => result = 9\n// Note:\n// The length of operator list is equal to the length of operand list minus one.\n// Operand is a list of of non-negative integers.\n// Operator list has at least one operator, and operand list has at least two operands.\nfunc do_algebra(operator []string, operand []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "func TestDo_Algebra(t *testing.T) {\n  candidate := do_algebra\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"**\", \"*\", \"+\"}, []int{2, 3, 4, 5}), expected: 37 },\n     { actual: candidate([]string{\"+\", \"*\", \"-\"}, []int{2, 3, 4, 5}), expected: 9 },\n     { actual: candidate([]string{\"//\", \"*\"}, []int{7, 3, 4}), expected: 8 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_160_do_algebra", "test": "func TestDo_Algebra(t *testing.T) {\n  candidate := do_algebra\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"**\", \"*\", \"+\"}, []int{2, 3, 4, 5}), expected: 37 },\n     { actual: candidate([]string{\"+\", \"*\", \"-\"}, []int{2, 3, 4, 5}), expected: 9 },\n     { actual: candidate([]string{\"//\", \"*\"}, []int{7, 3, 4}), expected: 8 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_27_flip_case", "language": "go_test.go", "prompt": "package flip_case_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n// >>> flip_case(\"Hello\")\n// \"hELLO\"\nfunc flip_case(myString string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "func TestFlip_Case(t *testing.T) {\n  candidate := flip_case\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"Hello!\"), expected: \"hELLO!\" },\n     { actual: candidate(\"These violent delights have violent ends\"), expected: \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_27_flip_case", "test": "func TestFlip_Case(t *testing.T) {\n  candidate := flip_case\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"Hello!\"), expected: \"hELLO!\" },\n     { actual: candidate(\"These violent delights have violent ends\"), expected: \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_105_by_length", "language": "go_test.go", "prompt": "package by_length_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of integers, sort the integers that are between 1 and 9 inclusive,\n// reverse the resulting list, and then replace each digit by its corresponding name from\n// \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n// For example:\n// >>> by_length([]int{2, 1, 1, 4, 5, 8, 2, 3})\n// []string{\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"}\n// If the list is empty, return an empty list:\n// >>> by_length([]int{})\n// []string{}\n// If the list has any strange number ignore it:\n// >>> by_length([]int{1, -1, 55})\n// []string{\"One\"}\nfunc by_length(arr []int) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "func TestBy_Length(t *testing.T) {\n  candidate := by_length\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 1, 1, 4, 5, 8, 2, 3}), expected: []string{\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"} },\n     { actual: candidate([]int{}), expected: []string{} },\n     { actual: candidate([]int{1, -1, 55}), expected: []string{\"One\"} },\n     { actual: candidate([]int{1, -1, 3, 2}), expected: []string{\"Three\", \"Two\", \"One\"} },\n     { actual: candidate([]int{9, 4, 8}), expected: []string{\"Nine\", \"Eight\", \"Four\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_105_by_length", "test": "func TestBy_Length(t *testing.T) {\n  candidate := by_length\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 1, 1, 4, 5, 8, 2, 3}), expected: []string{\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"} },\n     { actual: candidate([]int{}), expected: []string{} },\n     { actual: candidate([]int{1, -1, 55}), expected: []string{\"One\"} },\n     { actual: candidate([]int{1, -1, 3, 2}), expected: []string{\"Three\", \"Two\", \"One\"} },\n     { actual: candidate([]int{9, 4, 8}), expected: []string{\"Nine\", \"Eight\", \"Four\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_25_factorize", "language": "go_test.go", "prompt": "package factorize_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return list of prime factors of given integer in the order from smallest to largest.\n// Each of the factors should be listed number of times corresponding to how many times it appeares in factorization.\n// Input number should be equal to the product of all factors\n// >>> factorize(8)\n// []int{2, 2, 2}\n// >>> factorize(25)\n// []int{5, 5}\n// >>> factorize(70)\n// []int{2, 5, 7}\nfunc factorize(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "func TestFactorize(t *testing.T) {\n  candidate := factorize\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: []int{2} },\n     { actual: candidate(4), expected: []int{2, 2} },\n     { actual: candidate(8), expected: []int{2, 2, 2} },\n     { actual: candidate(57), expected: []int{3, 19} },\n     { actual: candidate(3249), expected: []int{3, 3, 19, 19} },\n     { actual: candidate(185193), expected: []int{3, 3, 3, 19, 19, 19} },\n     { actual: candidate(20577), expected: []int{3, 19, 19, 19} },\n     { actual: candidate(18), expected: []int{2, 3, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_25_factorize", "test": "func TestFactorize(t *testing.T) {\n  candidate := factorize\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2), expected: []int{2} },\n     { actual: candidate(4), expected: []int{2, 2} },\n     { actual: candidate(8), expected: []int{2, 2, 2} },\n     { actual: candidate(57), expected: []int{3, 19} },\n     { actual: candidate(3249), expected: []int{3, 3, 19, 19} },\n     { actual: candidate(185193), expected: []int{3, 3, 3, 19, 19, 19} },\n     { actual: candidate(20577), expected: []int{3, 19, 19, 19} },\n     { actual: candidate(18), expected: []int{2, 3, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_96_count_up_to", "language": "go_test.go", "prompt": "package count_up_to_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Implement a function that takes an non-negative integer and returns a list of the first n\n// integers that are prime numbers and less than n.\n// for example:\n// >>> count_up_to(5)\n// []int{2, 3}\n// >>> count_up_to(11)\n// []int{2, 3, 5, 7}\n// >>> count_up_to(0)\n// []int{}\n// >>> count_up_to(20)\n// []int{2, 3, 5, 7, 11, 13, 17, 19}\n// >>> count_up_to(1)\n// []int{}\n// >>> count_up_to(18)\n// []int{2, 3, 5, 7, 11, 13, 17}\nfunc count_up_to(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "func TestCount_Up_To(t *testing.T) {\n  candidate := count_up_to\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: []int{2, 3} },\n     { actual: candidate(6), expected: []int{2, 3, 5} },\n     { actual: candidate(7), expected: []int{2, 3, 5} },\n     { actual: candidate(10), expected: []int{2, 3, 5, 7} },\n     { actual: candidate(0), expected: []int{} },\n     { actual: candidate(22), expected: []int{2, 3, 5, 7, 11, 13, 17, 19} },\n     { actual: candidate(1), expected: []int{} },\n     { actual: candidate(18), expected: []int{2, 3, 5, 7, 11, 13, 17} },\n     { actual: candidate(47), expected: []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43} },\n     { actual: candidate(101), expected: []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_96_count_up_to", "test": "func TestCount_Up_To(t *testing.T) {\n  candidate := count_up_to\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: []int{2, 3} },\n     { actual: candidate(6), expected: []int{2, 3, 5} },\n     { actual: candidate(7), expected: []int{2, 3, 5} },\n     { actual: candidate(10), expected: []int{2, 3, 5, 7} },\n     { actual: candidate(0), expected: []int{} },\n     { actual: candidate(22), expected: []int{2, 3, 5, 7, 11, 13, 17, 19} },\n     { actual: candidate(1), expected: []int{} },\n     { actual: candidate(18), expected: []int{2, 3, 5, 7, 11, 13, 17} },\n     { actual: candidate(47), expected: []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43} },\n     { actual: candidate(101), expected: []int{2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_34_unique", "language": "go_test.go", "prompt": "package unique_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return sorted unique elements in a list\n// >>> unique([]int{5, 3, 5, 2, 3, 3, 9, 0, 123})\n// []int{0, 2, 3, 5, 9, 123}\nfunc unique(l []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "func TestUnique(t *testing.T) {\n  candidate := unique\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 3, 5, 2, 3, 3, 9, 0, 123}), expected: []int{0, 2, 3, 5, 9, 123} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_34_unique", "test": "func TestUnique(t *testing.T) {\n  candidate := unique\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 3, 5, 2, 3, 3, 9, 0, 123}), expected: []int{0, 2, 3, 5, 9, 123} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_74_total_match", "language": "go_test.go", "prompt": "package total_match_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that accepts two lists of strings and returns the list that has \n// total number of chars in the all strings of the list less than the other list.\n// if the two lists have the same number of chars, return the first list.\n// Examples\n// >>> total_match([]string{}, []string{})\n// []string{}\n// >>> total_match([]string{\"hi\", \"admin\"}, []string{\"hI\", \"Hi\"})\n// []string{\"hI\", \"Hi\"}\n// >>> total_match([]string{\"hi\", \"admin\"}, []string{\"hi\", \"hi\", \"admin\", \"project\"})\n// []string{\"hi\", \"admin\"}\n// >>> total_match([]string{\"hi\", \"admin\"}, []string{\"hI\", \"hi\", \"hi\"})\n// []string{\"hI\", \"hi\", \"hi\"}\n// >>> total_match([]string{\"4\"}, []string{\"1\", \"2\", \"3\", \"4\", \"5\"})\n// []string{\"4\"}\nfunc total_match(lst1 []string, lst2 []string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "func TestTotal_Match(t *testing.T) {\n  candidate := total_match\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, []string{}), expected: []string{} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hi\", \"hi\"}), expected: []string{\"hi\", \"hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hi\", \"hi\", \"admin\", \"project\"}), expected: []string{\"hi\", \"admin\"} },\n     { actual: candidate([]string{\"4\"}, []string{\"1\", \"2\", \"3\", \"4\", \"5\"}), expected: []string{\"4\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"Hi\"}), expected: []string{\"hI\", \"Hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"hi\", \"hi\"}), expected: []string{\"hI\", \"hi\", \"hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"hi\", \"hii\"}), expected: []string{\"hi\", \"admin\"} },\n     { actual: candidate([]string{}, []string{\"this\"}), expected: []string{} },\n     { actual: candidate([]string{\"this\"}, []string{}), expected: []string{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_74_total_match", "test": "func TestTotal_Match(t *testing.T) {\n  candidate := total_match\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, []string{}), expected: []string{} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hi\", \"hi\"}), expected: []string{\"hi\", \"hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hi\", \"hi\", \"admin\", \"project\"}), expected: []string{\"hi\", \"admin\"} },\n     { actual: candidate([]string{\"4\"}, []string{\"1\", \"2\", \"3\", \"4\", \"5\"}), expected: []string{\"4\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"Hi\"}), expected: []string{\"hI\", \"Hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"hi\", \"hi\"}), expected: []string{\"hI\", \"hi\", \"hi\"} },\n     { actual: candidate([]string{\"hi\", \"admin\"}, []string{\"hI\", \"hi\", \"hii\"}), expected: []string{\"hi\", \"admin\"} },\n     { actual: candidate([]string{}, []string{\"this\"}), expected: []string{} },\n     { actual: candidate([]string{\"this\"}, []string{}), expected: []string{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_35_max_element", "language": "go_test.go", "prompt": "package max_element_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return maximum element in the list.\n// >>> max_element([]int{1, 2, 3})\n// 3\n// >>> max_element([]int{5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10})\n// 123\nfunc max_element(l []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "func TestMax_Element(t *testing.T) {\n  candidate := max_element\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: 3 },\n     { actual: candidate([]int{5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10}), expected: 124 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_35_max_element", "test": "func TestMax_Element(t *testing.T) {\n  candidate := max_element\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: 3 },\n     { actual: candidate([]int{5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10}), expected: 124 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_132_is_nested", "language": "go_test.go", "prompt": "package is_nested_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function that takes a string as input which contains only square brackets.\n// The function should return true if and only if there is a valid subsequence of brackets \n// where at least one bracket in the subsequence is nested.\n// >>> is_nested(\"[[]]\")\n// true\n// >>> is_nested(\"[]]]]]]][[[[[]\")\n// false\n// >>> is_nested(\"[][]\")\n// false\n// >>> is_nested(\"[]\")\n// false\n// >>> is_nested(\"[[][]]\")\n// true\n// >>> is_nested(\"[[]][[\")\n// true\nfunc is_nested(myString string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "func TestIs_Nested(t *testing.T) {\n  candidate := is_nested\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"[[]]\"), expected: true },\n     { actual: candidate(\"[]]]]]]][[[[[]\"), expected: false },\n     { actual: candidate(\"[][]\"), expected: false },\n     { actual: candidate(\"[]\"), expected: false },\n     { actual: candidate(\"[[[[]]]]\"), expected: true },\n     { actual: candidate(\"[]]]]]]]]]]\"), expected: false },\n     { actual: candidate(\"[][][[]]\"), expected: true },\n     { actual: candidate(\"[[]\"), expected: false },\n     { actual: candidate(\"[]]\"), expected: false },\n     { actual: candidate(\"[[]][[\"), expected: true },\n     { actual: candidate(\"[[][]]\"), expected: true },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"[[[[[[[[\"), expected: false },\n     { actual: candidate(\"]]]]]]]]\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_132_is_nested", "test": "func TestIs_Nested(t *testing.T) {\n  candidate := is_nested\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"[[]]\"), expected: true },\n     { actual: candidate(\"[]]]]]]][[[[[]\"), expected: false },\n     { actual: candidate(\"[][]\"), expected: false },\n     { actual: candidate(\"[]\"), expected: false },\n     { actual: candidate(\"[[[[]]]]\"), expected: true },\n     { actual: candidate(\"[]]]]]]]]]]\"), expected: false },\n     { actual: candidate(\"[][][[]]\"), expected: true },\n     { actual: candidate(\"[[]\"), expected: false },\n     { actual: candidate(\"[]]\"), expected: false },\n     { actual: candidate(\"[[]][[\"), expected: true },\n     { actual: candidate(\"[[][]]\"), expected: true },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"[[[[[[[[\"), expected: false },\n     { actual: candidate(\"]]]]]]]]\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_113_odd_count", "language": "go_test.go", "prompt": "package odd_count_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of strings, where each string consists of only digits, return a list.\n// Each element i of the output should be \"the number of odd elements in the\n// string i of the input.\" where all the i's should be replaced by the number\n// of odd digits in the i'th string of the input.\n// >>> odd_count([]string{\"1234567\"})\n// []string{\"the number of odd elements 4n the str4ng 4 of the 4nput.\"}\n// >>> odd_count([]string{\"3\", \"11111111\"})\n// []string{\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"}\nfunc odd_count(lst []string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "func TestOdd_Count(t *testing.T) {\n  candidate := odd_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"1234567\"}), expected: []string{\"the number of odd elements 4n the str4ng 4 of the 4nput.\"} },\n     { actual: candidate([]string{\"3\", \"11111111\"}), expected: []string{\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"} },\n     { actual: candidate([]string{\"271\", \"137\", \"314\"}), expected: []string{\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_113_odd_count", "test": "func TestOdd_Count(t *testing.T) {\n  candidate := odd_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"1234567\"}), expected: []string{\"the number of odd elements 4n the str4ng 4 of the 4nput.\"} },\n     { actual: candidate([]string{\"3\", \"11111111\"}), expected: []string{\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"} },\n     { actual: candidate([]string{\"271\", \"137\", \"314\"}), expected: []string{\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_109_move_one_ball", "language": "go_test.go", "prompt": "package move_one_ball_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// We have a list 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n// numbers in the list will be randomly ordered. Your task is to determine if\n// it is possible to get a list sorted in non-decreasing order by performing \n// the following operation on the given list:\n// You are allowed to perform right shift operation any number of times.\n// One right shift operation means shifting all elements of the list by one\n// position in the right direction. The last element of the list will be moved to\n// the starting position in the list i.e. 0th index. \n// If it is possible to obtain the sorted list by performing the above operation\n// then return true else return false.\n// If the given list is empty then return true.\n// Note: The given list is guaranteed to have unique elements.\n// For Example:\n// >>> move_one_ball([]int{3, 4, 5, 1, 2})\n// true\n// Explanation: By performin 2 right shift operations, non-decreasing order can\n// be achieved for the given list.\n// >>> move_one_ball([]int{3, 5, 4, 1, 2})\n// false\n// Explanation:It is not possible to get non-decreasing order for the given\n// list by performing any number of right shift operations.\nfunc move_one_ball(arr []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "func TestMove_One_Ball(t *testing.T) {\n  candidate := move_one_ball\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 4, 5, 1, 2}), expected: true },\n     { actual: candidate([]int{3, 5, 10, 1, 2}), expected: true },\n     { actual: candidate([]int{4, 3, 1, 2}), expected: false },\n     { actual: candidate([]int{3, 5, 4, 1, 2}), expected: false },\n     { actual: candidate([]int{}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_109_move_one_ball", "test": "func TestMove_One_Ball(t *testing.T) {\n  candidate := move_one_ball\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 4, 5, 1, 2}), expected: true },\n     { actual: candidate([]int{3, 5, 10, 1, 2}), expected: true },\n     { actual: candidate([]int{4, 3, 1, 2}), expected: false },\n     { actual: candidate([]int{3, 5, 4, 1, 2}), expected: false },\n     { actual: candidate([]int{}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "go_test.go", "prompt": "package even_odd_palindrome_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer n, return a list that has the number of even and odd\n// integer palindromes that fall within the range(1, n), inclusive.\n// Example 1:\n// >>> even_odd_palindrome(3)\n// []interface{}{1, 2}\n// Explanation:\n// Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n// Example 2:\n// >>> even_odd_palindrome(12)\n// []interface{}{4, 6}\n// Explanation:\n// Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n// Note:\n// 1. 1 <= n <= 10^3\n// 2. returned list has the number of even and odd integer palindromes respectively.\nfunc even_odd_palindrome(n int) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "func TestEven_Odd_Palindrome(t *testing.T) {\n  candidate := even_odd_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(123), expected: []interface{}{8, 13} },\n     { actual: candidate(12), expected: []interface{}{4, 6} },\n     { actual: candidate(3), expected: []interface{}{1, 2} },\n     { actual: candidate(63), expected: []interface{}{6, 8} },\n     { actual: candidate(25), expected: []interface{}{5, 6} },\n     { actual: candidate(19), expected: []interface{}{4, 6} },\n     { actual: candidate(9), expected: []interface{}{4, 5} },\n     { actual: candidate(1), expected: []interface{}{0, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_107_even_odd_palindrome", "test": "func TestEven_Odd_Palindrome(t *testing.T) {\n  candidate := even_odd_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(123), expected: []interface{}{8, 13} },\n     { actual: candidate(12), expected: []interface{}{4, 6} },\n     { actual: candidate(3), expected: []interface{}{1, 2} },\n     { actual: candidate(63), expected: []interface{}{6, 8} },\n     { actual: candidate(25), expected: []interface{}{5, 6} },\n     { actual: candidate(19), expected: []interface{}{4, 6} },\n     { actual: candidate(9), expected: []interface{}{4, 5} },\n     { actual: candidate(1), expected: []interface{}{0, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "go_test.go", "prompt": "package is_equal_to_sum_even_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n// Example\n// >>> is_equal_to_sum_even(4)\n// false\n// >>> is_equal_to_sum_even(6)\n// false\n// >>> is_equal_to_sum_even(8)\n// true\nfunc is_equal_to_sum_even(n int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "func TestIs_Equal_To_Sum_Even(t *testing.T) {\n  candidate := is_equal_to_sum_even\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(4), expected: false },\n     { actual: candidate(6), expected: false },\n     { actual: candidate(8), expected: true },\n     { actual: candidate(10), expected: true },\n     { actual: candidate(11), expected: false },\n     { actual: candidate(12), expected: true },\n     { actual: candidate(13), expected: false },\n     { actual: candidate(16), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "func TestIs_Equal_To_Sum_Even(t *testing.T) {\n  candidate := is_equal_to_sum_even\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(4), expected: false },\n     { actual: candidate(6), expected: false },\n     { actual: candidate(8), expected: true },\n     { actual: candidate(10), expected: true },\n     { actual: candidate(11), expected: false },\n     { actual: candidate(12), expected: true },\n     { actual: candidate(13), expected: false },\n     { actual: candidate(16), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_62_derivative", "language": "go_test.go", "prompt": "package derivative_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// xs represent coefficients of a polynomial.\n// xs[0] + xs[1] * x + xs[2] * x^2 + ....\n// Return derivative of this polynomial in the same form.\n// >>> derivative([]int{3, 1, 2, 4, 5})\n// []int{1, 4, 12, 20}\n// >>> derivative([]int{1, 2, 3})\n// []int{2, 6}\nfunc derivative(xs []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "func TestDerivative(t *testing.T) {\n  candidate := derivative\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 1, 2, 4, 5}), expected: []int{1, 4, 12, 20} },\n     { actual: candidate([]int{1, 2, 3}), expected: []int{2, 6} },\n     { actual: candidate([]int{3, 2, 1}), expected: []int{2, 2} },\n     { actual: candidate([]int{3, 2, 1, 0, 4}), expected: []int{2, 2, 0, 16} },\n     { actual: candidate([]int{1}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_62_derivative", "test": "func TestDerivative(t *testing.T) {\n  candidate := derivative\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 1, 2, 4, 5}), expected: []int{1, 4, 12, 20} },\n     { actual: candidate([]int{1, 2, 3}), expected: []int{2, 6} },\n     { actual: candidate([]int{3, 2, 1}), expected: []int{2, 2} },\n     { actual: candidate([]int{3, 2, 1, 0, 4}), expected: []int{2, 2, 0, 16} },\n     { actual: candidate([]int{1}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_126_is_sorted", "language": "go_test.go", "prompt": "package is_sorted_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of numbers, return whether or not they are sorted\n// in ascending order. If list has more than 1 duplicate of the same\n// number, return false. Assume no negative numbers and only integers.\n// Examples\n// >>> is_sorted([]int{5})\n// true\n// >>> is_sorted([]int{1, 2, 3, 4, 5})\n// true\n// >>> is_sorted([]int{1, 3, 2, 4, 5})\n// false\n// >>> is_sorted([]int{1, 2, 3, 4, 5, 6})\n// true\n// >>> is_sorted([]int{1, 2, 3, 4, 5, 6, 7})\n// true\n// >>> is_sorted([]int{1, 3, 2, 4, 5, 6, 7})\n// false\n// >>> is_sorted([]int{1, 2, 2, 3, 3, 4})\n// true\n// >>> is_sorted([]int{1, 2, 2, 2, 3, 4})\n// false\nfunc is_sorted(lst []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "func TestIs_Sorted(t *testing.T) {\n  candidate := is_sorted\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4, 5}), expected: true },\n     { actual: candidate([]int{1, 3, 2, 4, 5}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7}), expected: true },\n     { actual: candidate([]int{1, 3, 2, 4, 5, 6, 7}), expected: false },\n     { actual: candidate([]int{}), expected: true },\n     { actual: candidate([]int{1}), expected: true },\n     { actual: candidate([]int{3, 2, 1}), expected: false },\n     { actual: candidate([]int{1, 2, 2, 2, 3, 4}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 3, 3, 4}), expected: false },\n     { actual: candidate([]int{1, 2, 2, 3, 3, 4}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_126_is_sorted", "test": "func TestIs_Sorted(t *testing.T) {\n  candidate := is_sorted\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4, 5}), expected: true },\n     { actual: candidate([]int{1, 3, 2, 4, 5}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7}), expected: true },\n     { actual: candidate([]int{1, 3, 2, 4, 5, 6, 7}), expected: false },\n     { actual: candidate([]int{}), expected: true },\n     { actual: candidate([]int{1}), expected: true },\n     { actual: candidate([]int{3, 2, 1}), expected: false },\n     { actual: candidate([]int{1, 2, 2, 2, 3, 4}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 3, 3, 4}), expected: false },\n     { actual: candidate([]int{1, 2, 2, 3, 3, 4}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_161_solve", "language": "go_test.go", "prompt": "package solve_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a string s.\n// if s[i] is a letter, reverse its case from lower to upper or vise versa, \n// otherwise keep it as it is.\n// If the string contains no letters, reverse the string.\n// The function should return the resulted string.\n// Examples\n// >>> solve(\"1234\")\n// \"4321\"\n// >>> solve(\"ab\")\n// \"AB\"\n// >>> solve(\"#a@C\")\n// \"#A@c\"\nfunc solve(s string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "func TestSolve(t *testing.T) {\n  candidate := solve\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"AsDf\"), expected: \"aSdF\" },\n     { actual: candidate(\"1234\"), expected: \"4321\" },\n     { actual: candidate(\"ab\"), expected: \"AB\" },\n     { actual: candidate(\"#a@C\"), expected: \"#A@c\" },\n     { actual: candidate(\"#AsdfW^45\"), expected: \"#aSDFw^45\" },\n     { actual: candidate(\"#6@2\"), expected: \"2@6#\" },\n     { actual: candidate(\"#$a^D\"), expected: \"#$A^d\" },\n     { actual: candidate(\"#ccc\"), expected: \"#CCC\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_161_solve", "test": "func TestSolve(t *testing.T) {\n  candidate := solve\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"AsDf\"), expected: \"aSdF\" },\n     { actual: candidate(\"1234\"), expected: \"4321\" },\n     { actual: candidate(\"ab\"), expected: \"AB\" },\n     { actual: candidate(\"#a@C\"), expected: \"#A@c\" },\n     { actual: candidate(\"#AsdfW^45\"), expected: \"#aSDFw^45\" },\n     { actual: candidate(\"#6@2\"), expected: \"2@6#\" },\n     { actual: candidate(\"#$a^D\"), expected: \"#$A^d\" },\n     { actual: candidate(\"#ccc\"), expected: \"#CCC\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_130_tri", "language": "go_test.go", "prompt": "package tri_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n// the last couple centuries. However, what people don't know is Tribonacci sequence.\n// Tribonacci sequence is defined by the recurrence:\n// tri(1) = 3\n// tri(n) = 1 + n / 2, if n is even.\n// tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n// For example:\n// tri(2) = 1 + (2 / 2) = 2\n// tri(4) = 3\n// tri(3) = tri(2) + tri(1) + tri(4)\n// = 2 + 3 + 3 = 8 \n// You are given a non-negative integer number n, you have to a return a list of the \n// first n + 1 numbers of the Tribonacci sequence.\n// Examples:\n// >>> tri(3)\n// []int{1, 3, 2, 8}\nfunc tri(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "func TestTri(t *testing.T) {\n  candidate := tri\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: []int{1, 3, 2, 8} },\n     { actual: candidate(4), expected: []int{1, 3, 2, 8, 3} },\n     { actual: candidate(5), expected: []int{1, 3, 2, 8, 3, 15} },\n     { actual: candidate(6), expected: []int{1, 3, 2, 8, 3, 15, 4} },\n     { actual: candidate(7), expected: []int{1, 3, 2, 8, 3, 15, 4, 24} },\n     { actual: candidate(8), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5} },\n     { actual: candidate(9), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5, 35} },\n     { actual: candidate(20), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11} },\n     { actual: candidate(0), expected: []int{1} },\n     { actual: candidate(1), expected: []int{1, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_130_tri", "test": "func TestTri(t *testing.T) {\n  candidate := tri\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: []int{1, 3, 2, 8} },\n     { actual: candidate(4), expected: []int{1, 3, 2, 8, 3} },\n     { actual: candidate(5), expected: []int{1, 3, 2, 8, 3, 15} },\n     { actual: candidate(6), expected: []int{1, 3, 2, 8, 3, 15, 4} },\n     { actual: candidate(7), expected: []int{1, 3, 2, 8, 3, 15, 4, 24} },\n     { actual: candidate(8), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5} },\n     { actual: candidate(9), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5, 35} },\n     { actual: candidate(20), expected: []int{1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11} },\n     { actual: candidate(0), expected: []int{1} },\n     { actual: candidate(1), expected: []int{1, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_36_fizz_buzz", "language": "go_test.go", "prompt": "package fizz_buzz_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n// >>> fizz_buzz(50)\n// 0\n// >>> fizz_buzz(78)\n// 2\n// >>> fizz_buzz(79)\n// 3\nfunc fizz_buzz(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "func TestFizz_Buzz(t *testing.T) {\n  candidate := fizz_buzz\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(50), expected: 0 },\n     { actual: candidate(78), expected: 2 },\n     { actual: candidate(79), expected: 3 },\n     { actual: candidate(100), expected: 3 },\n     { actual: candidate(200), expected: 6 },\n     { actual: candidate(4000), expected: 192 },\n     { actual: candidate(10000), expected: 639 },\n     { actual: candidate(100000), expected: 8026 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_36_fizz_buzz", "test": "func TestFizz_Buzz(t *testing.T) {\n  candidate := fizz_buzz\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(50), expected: 0 },\n     { actual: candidate(78), expected: 2 },\n     { actual: candidate(79), expected: 3 },\n     { actual: candidate(100), expected: 3 },\n     { actual: candidate(200), expected: 6 },\n     { actual: candidate(4000), expected: 192 },\n     { actual: candidate(10000), expected: 639 },\n     { actual: candidate(100000), expected: 8026 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_29_filter_by_prefix", "language": "go_test.go", "prompt": "package filter_by_prefix_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Filter an input list of strings only for ones that start with a given prefix.\n// >>> filter_by_prefix([]string{}, \"a\")\n// []string{}\n// >>> filter_by_prefix([]string{\"abc\", \"bcd\", \"cde\", \"array\"}, \"a\")\n// []string{\"abc\", \"array\"}\nfunc filter_by_prefix(strings []string, prefix string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "func TestFilter_By_Prefix(t *testing.T) {\n  candidate := filter_by_prefix\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, \"john\"), expected: []string{} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xxx\"), expected: []string{\"xxx\", \"xxxAAA\", \"xxx\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_29_filter_by_prefix", "test": "func TestFilter_By_Prefix(t *testing.T) {\n  candidate := filter_by_prefix\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, \"john\"), expected: []string{} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xxx\"), expected: []string{\"xxx\", \"xxxAAA\", \"xxx\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_84_solve", "language": "go_test.go", "prompt": "package solve_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer N, return the total sum of its digits in binary.\n// Example\n// >>> solve(1000)\n// \"1\"\n// >>> solve(150)\n// \"110\"\n// >>> solve(147)\n// \"1100\"\n// Variables:\n// @N integer\n// Constraints: 0 \u2264 N \u2264 10000.\n// Output:\n// a string of binary number\nfunc solve(N int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "func TestSolve(t *testing.T) {\n  candidate := solve\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1000), expected: \"1\" },\n     { actual: candidate(150), expected: \"110\" },\n     { actual: candidate(147), expected: \"1100\" },\n     { actual: candidate(333), expected: \"1001\" },\n     { actual: candidate(963), expected: \"10010\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_84_solve", "test": "func TestSolve(t *testing.T) {\n  candidate := solve\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1000), expected: \"1\" },\n     { actual: candidate(150), expected: \"110\" },\n     { actual: candidate(147), expected: \"1100\" },\n     { actual: candidate(333), expected: \"1001\" },\n     { actual: candidate(963), expected: \"10010\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_129_minPath", "language": "go_test.go", "prompt": "package minPath_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n// each cell of the grid contains a value. Every integer in the range [1, N * N]\n// inclusive appears exactly once on the cells of the grid.\n// You have to find the minimum path of length k in the grid. You can start\n// from any cell, and in each step you can move to any of the neighbor cells,\n// in other words, you can go to cells which share an edge with you current\n// cell.\n// Please note that a path of length k means visiting exactly k cells (not\n// necessarily distinct).\n// You CANNOT go off the grid.\n// A path A (of length k) is considered less than a path B (of length k) if\n// after making the ordered lists of the values on the cells that A and B go\n// through (let's call them lst_A and lst_B), lst_A is lexicographically less\n// than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n// such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n// lst_A[j] = lst_B[j].\n// It is guaranteed that the answer is unique.\n// Return an ordered list of the values on the cells that the minimum path go through.\n// Examples:    \n// >>> minPath([][]int{[]int{1, 2, 3}, []int{4, 5, 6}, []int{7, 8, 9}}, 3)\n// []int{1, 2, 1}\n// >>> minPath([][]int{[]int{5, 9, 3}, []int{4, 1, 6}, []int{7, 8, 2}}, 1)\n// []int{1}\nfunc minPath(grid [][]int, k int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "func TestMinpath(t *testing.T) {\n  candidate := minPath\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{1, 2, 3}, []int{4, 5, 6}, []int{7, 8, 9}}, 3), expected: []int{1, 2, 1} },\n     { actual: candidate([][]int{[]int{5, 9, 3}, []int{4, 1, 6}, []int{7, 8, 2}}, 1), expected: []int{1} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4}, []int{5, 6, 7, 8}, []int{9, 10, 11, 12}, []int{13, 14, 15, 16}}, 4), expected: []int{1, 2, 1, 2} },\n     { actual: candidate([][]int{[]int{6, 4, 13, 10}, []int{5, 7, 12, 1}, []int{3, 16, 11, 15}, []int{8, 14, 9, 2}}, 7), expected: []int{1, 10, 1, 10, 1, 10, 1} },\n     { actual: candidate([][]int{[]int{8, 14, 9, 2}, []int{6, 4, 13, 15}, []int{5, 7, 1, 12}, []int{3, 10, 11, 16}}, 5), expected: []int{1, 7, 1, 7, 1} },\n     { actual: candidate([][]int{[]int{11, 8, 7, 2}, []int{5, 16, 14, 4}, []int{9, 3, 15, 6}, []int{12, 13, 10, 1}}, 9), expected: []int{1, 6, 1, 6, 1, 6, 1, 6, 1} },\n     { actual: candidate([][]int{[]int{12, 13, 10, 1}, []int{9, 3, 15, 6}, []int{5, 16, 14, 4}, []int{11, 8, 7, 2}}, 12), expected: []int{1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6} },\n     { actual: candidate([][]int{[]int{2, 7, 4}, []int{3, 1, 5}, []int{6, 8, 9}}, 8), expected: []int{1, 3, 1, 3, 1, 3, 1, 3} },\n     { actual: candidate([][]int{[]int{6, 1, 5}, []int{3, 8, 9}, []int{2, 7, 4}}, 8), expected: []int{1, 5, 1, 5, 1, 5, 1, 5} },\n     { actual: candidate([][]int{[]int{1, 2}, []int{3, 4}}, 10), expected: []int{1, 2, 1, 2, 1, 2, 1, 2, 1, 2} },\n     { actual: candidate([][]int{[]int{1, 3}, []int{3, 2}}, 10), expected: []int{1, 3, 1, 3, 1, 3, 1, 3, 1, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_129_minPath", "test": "func TestMinpath(t *testing.T) {\n  candidate := minPath\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{1, 2, 3}, []int{4, 5, 6}, []int{7, 8, 9}}, 3), expected: []int{1, 2, 1} },\n     { actual: candidate([][]int{[]int{5, 9, 3}, []int{4, 1, 6}, []int{7, 8, 2}}, 1), expected: []int{1} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4}, []int{5, 6, 7, 8}, []int{9, 10, 11, 12}, []int{13, 14, 15, 16}}, 4), expected: []int{1, 2, 1, 2} },\n     { actual: candidate([][]int{[]int{6, 4, 13, 10}, []int{5, 7, 12, 1}, []int{3, 16, 11, 15}, []int{8, 14, 9, 2}}, 7), expected: []int{1, 10, 1, 10, 1, 10, 1} },\n     { actual: candidate([][]int{[]int{8, 14, 9, 2}, []int{6, 4, 13, 15}, []int{5, 7, 1, 12}, []int{3, 10, 11, 16}}, 5), expected: []int{1, 7, 1, 7, 1} },\n     { actual: candidate([][]int{[]int{11, 8, 7, 2}, []int{5, 16, 14, 4}, []int{9, 3, 15, 6}, []int{12, 13, 10, 1}}, 9), expected: []int{1, 6, 1, 6, 1, 6, 1, 6, 1} },\n     { actual: candidate([][]int{[]int{12, 13, 10, 1}, []int{9, 3, 15, 6}, []int{5, 16, 14, 4}, []int{11, 8, 7, 2}}, 12), expected: []int{1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6} },\n     { actual: candidate([][]int{[]int{2, 7, 4}, []int{3, 1, 5}, []int{6, 8, 9}}, 8), expected: []int{1, 3, 1, 3, 1, 3, 1, 3} },\n     { actual: candidate([][]int{[]int{6, 1, 5}, []int{3, 8, 9}, []int{2, 7, 4}}, 8), expected: []int{1, 5, 1, 5, 1, 5, 1, 5} },\n     { actual: candidate([][]int{[]int{1, 2}, []int{3, 4}}, 10), expected: []int{1, 2, 1, 2, 1, 2, 1, 2, 1, 2} },\n     { actual: candidate([][]int{[]int{1, 3}, []int{3, 2}}, 10), expected: []int{1, 3, 1, 3, 1, 3, 1, 3, 1, 3} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_98_count_upper", "language": "go_test.go", "prompt": "package count_upper_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a string s, count the number of uppercase vowels in even indices.\n// For example:\n// >>> count_upper(\"aBCdEf\")\n// 1\n// >>> count_upper(\"abcdefg\")\n// 0\n// >>> count_upper(\"dBBE\")\n// 0\nfunc count_upper(s string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "func TestCount_Upper(t *testing.T) {\n  candidate := count_upper\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"aBCdEf\"), expected: 1 },\n     { actual: candidate(\"abcdefg\"), expected: 0 },\n     { actual: candidate(\"dBBE\"), expected: 0 },\n     { actual: candidate(\"B\"), expected: 0 },\n     { actual: candidate(\"U\"), expected: 1 },\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"EEEE\"), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_98_count_upper", "test": "func TestCount_Upper(t *testing.T) {\n  candidate := count_upper\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"aBCdEf\"), expected: 1 },\n     { actual: candidate(\"abcdefg\"), expected: 0 },\n     { actual: candidate(\"dBBE\"), expected: 0 },\n     { actual: candidate(\"B\"), expected: 0 },\n     { actual: candidate(\"U\"), expected: 1 },\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"EEEE\"), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_120_maximum", "language": "go_test.go", "prompt": "package maximum_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list arr of integers and a positive integer k, return a sorted list \n// of length k with the maximum k numbers in arr.\n// Example 1:\n// >>> maximum([]int{-3, -4, 5}, 3)\n// []int{-4, -3, 5}\n// Example 2:\n// >>> maximum([]int{4, -4, 4}, 2)\n// []int{4, 4}\n// Example 3:\n// >>> maximum([]int{-3, 2, 1, 2, -1, -2, 1}, 1)\n// []int{2}\n// Note:\n// 1. The length of the list will be in the range of [1, 1000].\n// 2. The elements in the list will be in the range of [-1000, 1000].\n// 3. 0 <= k <= len(arr)\nfunc maximum(arr []int, k int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "func TestMaximum(t *testing.T) {\n  candidate := maximum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{-3, -4, 5}, 3), expected: []int{-4, -3, 5} },\n     { actual: candidate([]int{4, -4, 4}, 2), expected: []int{4, 4} },\n     { actual: candidate([]int{-3, 2, 1, 2, -1, -2, 1}, 1), expected: []int{2} },\n     { actual: candidate([]int{123, -123, 20, 0, 1, 2, -3}, 3), expected: []int{2, 20, 123} },\n     { actual: candidate([]int{-123, 20, 0, 1, 2, -3}, 4), expected: []int{0, 1, 2, 20} },\n     { actual: candidate([]int{5, 15, 0, 3, -13, -8, 0}, 7), expected: []int{-13, -8, 0, 0, 3, 5, 15} },\n     { actual: candidate([]int{-1, 0, 2, 5, 3, -10}, 2), expected: []int{3, 5} },\n     { actual: candidate([]int{1, 0, 5, -7}, 1), expected: []int{5} },\n     { actual: candidate([]int{4, -4}, 2), expected: []int{-4, 4} },\n     { actual: candidate([]int{-10, 10}, 2), expected: []int{-10, 10} },\n     { actual: candidate([]int{1, 2, 3, -23, 243, -400, 0}, 0), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_120_maximum", "test": "func TestMaximum(t *testing.T) {\n  candidate := maximum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{-3, -4, 5}, 3), expected: []int{-4, -3, 5} },\n     { actual: candidate([]int{4, -4, 4}, 2), expected: []int{4, 4} },\n     { actual: candidate([]int{-3, 2, 1, 2, -1, -2, 1}, 1), expected: []int{2} },\n     { actual: candidate([]int{123, -123, 20, 0, 1, 2, -3}, 3), expected: []int{2, 20, 123} },\n     { actual: candidate([]int{-123, 20, 0, 1, 2, -3}, 4), expected: []int{0, 1, 2, 20} },\n     { actual: candidate([]int{5, 15, 0, 3, -13, -8, 0}, 7), expected: []int{-13, -8, 0, 0, 3, 5, 15} },\n     { actual: candidate([]int{-1, 0, 2, 5, 3, -10}, 2), expected: []int{3, 5} },\n     { actual: candidate([]int{1, 0, 5, -7}, 1), expected: []int{5} },\n     { actual: candidate([]int{4, -4}, 2), expected: []int{-4, 4} },\n     { actual: candidate([]int{-10, 10}, 2), expected: []int{-10, 10} },\n     { actual: candidate([]int{1, 2, 3, -23, 243, -400, 0}, 0), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_24_largest_divisor", "language": "go_test.go", "prompt": "package largest_divisor_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// For a given number n, find the largest number that divides n evenly, smaller than n\n// >>> largest_divisor(15)\n// 5\nfunc largest_divisor(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "func TestLargest_Divisor(t *testing.T) {\n  candidate := largest_divisor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: 1 },\n     { actual: candidate(7), expected: 1 },\n     { actual: candidate(10), expected: 5 },\n     { actual: candidate(100), expected: 50 },\n     { actual: candidate(49), expected: 7 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_24_largest_divisor", "test": "func TestLargest_Divisor(t *testing.T) {\n  candidate := largest_divisor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: 1 },\n     { actual: candidate(7), expected: 1 },\n     { actual: candidate(10), expected: 5 },\n     { actual: candidate(100), expected: 50 },\n     { actual: candidate(49), expected: 7 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_88_sort_array", "language": "go_test.go", "prompt": "package sort_array_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of non-negative integers, return a cogo of the given list after sorting,\n// you will sort the given list in ascending order if the sum( first index value, last index value) is odd,\n// or sort it in descending order if the sum( first index value, last index value) is even.\n// Note:\n// * don't change the given list.\n// Examples:\n// >>> sort_array([]int{})\n// []int{}\n// >>> sort_array([]int{5})\n// []int{5}\n// >>> sort_array([]int{2, 4, 3, 0, 1, 5})\n// []int{0, 1, 2, 3, 4, 5}\n// >>> sort_array([]int{2, 4, 3, 0, 1, 5, 6})\n// []int{6, 5, 4, 3, 2, 1, 0}\nfunc sort_array(array []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "func TestSort_Array(t *testing.T) {\n  candidate := sort_array\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{5}), expected: []int{5} },\n     { actual: candidate([]int{2, 4, 3, 0, 1, 5}), expected: []int{0, 1, 2, 3, 4, 5} },\n     { actual: candidate([]int{2, 4, 3, 0, 1, 5, 6}), expected: []int{6, 5, 4, 3, 2, 1, 0} },\n     { actual: candidate([]int{2, 1}), expected: []int{1, 2} },\n     { actual: candidate([]int{15, 42, 87, 32, 11, 0}), expected: []int{0, 11, 15, 32, 42, 87} },\n     { actual: candidate([]int{21, 14, 23, 11}), expected: []int{23, 21, 14, 11} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_88_sort_array", "test": "func TestSort_Array(t *testing.T) {\n  candidate := sort_array\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{5}), expected: []int{5} },\n     { actual: candidate([]int{2, 4, 3, 0, 1, 5}), expected: []int{0, 1, 2, 3, 4, 5} },\n     { actual: candidate([]int{2, 4, 3, 0, 1, 5, 6}), expected: []int{6, 5, 4, 3, 2, 1, 0} },\n     { actual: candidate([]int{2, 1}), expected: []int{1, 2} },\n     { actual: candidate([]int{15, 42, 87, 32, 11, 0}), expected: []int{0, 11, 15, 32, 42, 87} },\n     { actual: candidate([]int{21, 14, 23, 11}), expected: []int{23, 21, 14, 11} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_106_f", "language": "go_test.go", "prompt": "package f_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Implement the function f that takes n as a parameter,\n// and returns a list of size n, such that the value of the element at index i is the factorial of i if i is even\n// or the sum of numbers from 1 to i otherwise.\n// i starts from 1.\n// the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n// Example:\n// >>> f(5)\n// []int{1, 2, 6, 24, 15}\nfunc f(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "func TestF(t *testing.T) {\n  candidate := f\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: []int{1, 2, 6, 24, 15} },\n     { actual: candidate(7), expected: []int{1, 2, 6, 24, 15, 720, 28} },\n     { actual: candidate(1), expected: []int{1} },\n     { actual: candidate(3), expected: []int{1, 2, 6} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_106_f", "test": "func TestF(t *testing.T) {\n  candidate := f\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: []int{1, 2, 6, 24, 15} },\n     { actual: candidate(7), expected: []int{1, 2, 6, 24, 15, 720, 28} },\n     { actual: candidate(1), expected: []int{1} },\n     { actual: candidate(3), expected: []int{1, 2, 6} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_77_iscube", "language": "go_test.go", "prompt": "package iscube_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that takes an integer a and returns true \n// if this ingeger is a cube of some integer number.\n// Note: you may assume the input is always valid.\n// Examples:\n// >>> iscube(1)\n// true\n// >>> iscube(2)\n// false\n// >>> iscube(-1)\n// true\n// >>> iscube(64)\n// true\n// >>> iscube(0)\n// true\n// >>> iscube(180)\n// false\nfunc iscube(a int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "func TestIscube(t *testing.T) {\n  candidate := iscube\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: true },\n     { actual: candidate(2), expected: false },\n     { actual: candidate(-1), expected: true },\n     { actual: candidate(64), expected: true },\n     { actual: candidate(180), expected: false },\n     { actual: candidate(1000), expected: true },\n     { actual: candidate(0), expected: true },\n     { actual: candidate(1729), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_77_iscube", "test": "func TestIscube(t *testing.T) {\n  candidate := iscube\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: true },\n     { actual: candidate(2), expected: false },\n     { actual: candidate(-1), expected: true },\n     { actual: candidate(64), expected: true },\n     { actual: candidate(180), expected: false },\n     { actual: candidate(1000), expected: true },\n     { actual: candidate(0), expected: true },\n     { actual: candidate(1729), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_93_encode", "language": "go_test.go", "prompt": "package encode_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that takes a message, and encodes in such a \n// way that it swaps case of all letters, replaces all vowels in \n// the message with the letter that appears 2 places ahead of that \n// vowel in the english alphabet. \n// Assume only letters. \n// Examples:\n// >>> encode(\"test\")\n// \"TGST\"\n// >>> encode(\"This is a message\")\n// \"tHKS KS C MGSSCGG\"\nfunc encode(message string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "func TestEncode(t *testing.T) {\n  candidate := encode\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"TEST\"), expected: \"tgst\" },\n     { actual: candidate(\"Mudasir\"), expected: \"mWDCSKR\" },\n     { actual: candidate(\"YES\"), expected: \"ygs\" },\n     { actual: candidate(\"This is a message\"), expected: \"tHKS KS C MGSSCGG\" },\n     { actual: candidate(\"I DoNt KnOw WhAt tO WrItE\"), expected: \"k dQnT kNqW wHcT Tq wRkTg\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_93_encode", "test": "func TestEncode(t *testing.T) {\n  candidate := encode\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"TEST\"), expected: \"tgst\" },\n     { actual: candidate(\"Mudasir\"), expected: \"mWDCSKR\" },\n     { actual: candidate(\"YES\"), expected: \"ygs\" },\n     { actual: candidate(\"This is a message\"), expected: \"tHKS KS C MGSSCGG\" },\n     { actual: candidate(\"I DoNt KnOw WhAt tO WrItE\"), expected: \"k dQnT kNqW wHcT Tq wRkTg\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_91_is_bored", "language": "go_test.go", "prompt": "package is_bored_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You'll be given a string of words, and your task is to count the number\n// of boredoms. A boredom is a sentence that starts with the word \"I\".\n// Sentences are delimited by '.', '?' or '!'.\n// For example:\n// >>> is_bored(\"Hello world\")\n// 0\n// >>> is_bored(\"The sky is blue. The sun is shining. I love this weather\")\n// 1\nfunc is_bored(S string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "func TestIs_Bored(t *testing.T) {\n  candidate := is_bored\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hello world\"), expected: 0 },\n     { actual: candidate(\"Is the sky blue?\"), expected: 0 },\n     { actual: candidate(\"I love It !\"), expected: 1 },\n     { actual: candidate(\"bIt\"), expected: 0 },\n     { actual: candidate(\"I feel good today. I will be productive. will kill It\"), expected: 2 },\n     { actual: candidate(\"You and I are going for a walk\"), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_91_is_bored", "test": "func TestIs_Bored(t *testing.T) {\n  candidate := is_bored\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hello world\"), expected: 0 },\n     { actual: candidate(\"Is the sky blue?\"), expected: 0 },\n     { actual: candidate(\"I love It !\"), expected: 1 },\n     { actual: candidate(\"bIt\"), expected: 0 },\n     { actual: candidate(\"I feel good today. I will be productive. will kill It\"), expected: 2 },\n     { actual: candidate(\"You and I are going for a walk\"), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "go_test.go", "prompt": "package pairs_sum_to_zero_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// pairs_sum_to_zero takes a list of integers as an input.\n// it returns true if there are two distinct elements in the list that\n// sum to zero, and false otherwise.\n// >>> pairs_sum_to_zero([]int{1, 3, 5, 0})\n// false\n// >>> pairs_sum_to_zero([]int{1, 3, -2, 1})\n// false\n// >>> pairs_sum_to_zero([]int{1, 2, 3, 7})\n// false\n// >>> pairs_sum_to_zero([]int{2, 4, -5, 3, 5, 7})\n// true\n// >>> pairs_sum_to_zero([]int{1})\n// false\nfunc pairs_sum_to_zero(l []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "func TestPairs_Sum_To_Zero(t *testing.T) {\n  candidate := pairs_sum_to_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 3, 5, 0}), expected: false },\n     { actual: candidate([]int{1, 3, -2, 1}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 7}), expected: false },\n     { actual: candidate([]int{2, 4, -5, 3, 5, 7}), expected: true },\n     { actual: candidate([]int{1}), expected: false },\n     { actual: candidate([]int{-3, 9, -1, 3, 2, 30}), expected: true },\n     { actual: candidate([]int{-3, 9, -1, 3, 2, 31}), expected: true },\n     { actual: candidate([]int{-3, 9, -1, 4, 2, 30}), expected: false },\n     { actual: candidate([]int{-3, 9, -1, 4, 2, 31}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "func TestPairs_Sum_To_Zero(t *testing.T) {\n  candidate := pairs_sum_to_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 3, 5, 0}), expected: false },\n     { actual: candidate([]int{1, 3, -2, 1}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 7}), expected: false },\n     { actual: candidate([]int{2, 4, -5, 3, 5, 7}), expected: true },\n     { actual: candidate([]int{1}), expected: false },\n     { actual: candidate([]int{-3, 9, -1, 3, 2, 30}), expected: true },\n     { actual: candidate([]int{-3, 9, -1, 3, 2, 31}), expected: true },\n     { actual: candidate([]int{-3, 9, -1, 4, 2, 30}), expected: false },\n     { actual: candidate([]int{-3, 9, -1, 4, 2, 31}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_71_triangle_area", "language": "go_test.go", "prompt": "package triangle_area_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given the lengths of the three sides of a triangle. Return the area of\n// the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n// Otherwise return -1\n// Three sides make a valid triangle when the sum of any two sides is greater \n// than the third side.\n// Example:\n// >>> triangle_area(3, 4, 5)\n// 6.0\n// >>> triangle_area(1, 2, 10)\n// -1\nfunc triangle_area(a int, b int, c int) float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "func TestTriangle_Area(t *testing.T) {\n  candidate := triangle_area\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 4, 5), expected: 6.0 },\n     { actual: candidate(1, 2, 10), expected: -1 },\n     { actual: candidate(4, 8, 5), expected: 8.18 },\n     { actual: candidate(2, 2, 2), expected: 1.73 },\n     { actual: candidate(1, 2, 3), expected: -1 },\n     { actual: candidate(10, 5, 7), expected: 16.25 },\n     { actual: candidate(2, 6, 3), expected: -1 },\n     { actual: candidate(1, 1, 1), expected: 0.43 },\n     { actual: candidate(2, 2, 10), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_71_triangle_area", "test": "func TestTriangle_Area(t *testing.T) {\n  candidate := triangle_area\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 4, 5), expected: 6.0 },\n     { actual: candidate(1, 2, 10), expected: -1 },\n     { actual: candidate(4, 8, 5), expected: 8.18 },\n     { actual: candidate(2, 2, 2), expected: 1.73 },\n     { actual: candidate(1, 2, 3), expected: -1 },\n     { actual: candidate(10, 5, 7), expected: 16.25 },\n     { actual: candidate(2, 6, 3), expected: -1 },\n     { actual: candidate(1, 1, 1), expected: 0.43 },\n     { actual: candidate(2, 2, 10), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_148_bf", "language": "go_test.go", "prompt": "package bf_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// There are eight planets in our solar system: the closerst to the Sun \n// is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n// Uranus, Neptune.\n// Write a function that takes two planet names as strings planet1 and planet2. \n// The function should return a list containing all planets whose orbits are \n// located between the orbit of planet1 and the orbit of planet2, sorted by \n// the proximity to the sun. \n// The function should return an empty list if planet1 or planet2\n// are not correct planet names. \n// Examples\n// >>> bf(\"Jupiter\", \"Neptune\")\n// []interface{}{\"Saturn\", \"Uranus\"}\n// >>> bf(\"Earth\", \"Mercury\")\n// \"Venus\"\n// >>> bf(\"Mercury\", \"Uranus\")\n// []interface{}{\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"}\nfunc bf(planet1 string, planet2 string) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "func TestBf(t *testing.T) {\n  candidate := bf\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Jupiter\", \"Neptune\"), expected: []interface{}{\"Saturn\", \"Uranus\"} },\n     { actual: candidate(\"Earth\", \"Mercury\"), expected: []interface{}{\"Venus\"} },\n     { actual: candidate(\"Mercury\", \"Uranus\"), expected: []interface{}{\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"} },\n     { actual: candidate(\"Neptune\", \"Venus\"), expected: []interface{}{\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"} },\n     { actual: candidate(\"Earth\", \"Earth\"), expected: []interface{}{} },\n     { actual: candidate(\"Mars\", \"Earth\"), expected: []interface{}{} },\n     { actual: candidate(\"Jupiter\", \"Makemake\"), expected: []interface{}{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_148_bf", "test": "func TestBf(t *testing.T) {\n  candidate := bf\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Jupiter\", \"Neptune\"), expected: []interface{}{\"Saturn\", \"Uranus\"} },\n     { actual: candidate(\"Earth\", \"Mercury\"), expected: []interface{}{\"Venus\"} },\n     { actual: candidate(\"Mercury\", \"Uranus\"), expected: []interface{}{\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"} },\n     { actual: candidate(\"Neptune\", \"Venus\"), expected: []interface{}{\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"} },\n     { actual: candidate(\"Earth\", \"Earth\"), expected: []interface{}{} },\n     { actual: candidate(\"Mars\", \"Earth\"), expected: []interface{}{} },\n     { actual: candidate(\"Jupiter\", \"Makemake\"), expected: []interface{}{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_131_digits", "language": "go_test.go", "prompt": "package digits_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer n, return the product of the odd digits.\n// Return 0 if all digits are even.\n// For example:\n// >>> digits(1)\n// 1\n// >>> digits(4)\n// 0\n// >>> digits(235)\n// 15\nfunc digits(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "func TestDigits(t *testing.T) {\n  candidate := digits\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 5 },\n     { actual: candidate(54), expected: 5 },\n     { actual: candidate(120), expected: 1 },\n     { actual: candidate(5014), expected: 5 },\n     { actual: candidate(98765), expected: 315 },\n     { actual: candidate(5576543), expected: 2625 },\n     { actual: candidate(2468), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_131_digits", "test": "func TestDigits(t *testing.T) {\n  candidate := digits\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 5 },\n     { actual: candidate(54), expected: 5 },\n     { actual: candidate(120), expected: 1 },\n     { actual: candidate(5014), expected: 5 },\n     { actual: candidate(98765), expected: 315 },\n     { actual: candidate(5576543), expected: 2625 },\n     { actual: candidate(2468), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_101_words_string", "language": "go_test.go", "prompt": "package words_string_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You will be given a string of words separated by commas or spaces. Your task is\n// to split the string into words and return a list of the words.\n// For example:\n// >>> words_string(\"Hi, my name is John\")\n// []string{\"Hi\", \"my\", \"name\", \"is\", \"John\"}\n// >>> words_string(\"One, two, three, four, five, six\")\n// []string{\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"}\nfunc words_string(s string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "func TestWords_String(t *testing.T) {\n  candidate := words_string\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hi, my name is John\"), expected: []string{\"Hi\", \"my\", \"name\", \"is\", \"John\"} },\n     { actual: candidate(\"One, two, three, four, five, six\"), expected: []string{\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"} },\n     { actual: candidate(\"Hi, my name\"), expected: []string{\"Hi\", \"my\", \"name\"} },\n     { actual: candidate(\"One,, two, three, four, five, six,\"), expected: []string{\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"} },\n     { actual: candidate(\"\"), expected: []string{} },\n     { actual: candidate(\"ahmed     , gamal\"), expected: []string{\"ahmed\", \"gamal\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_101_words_string", "test": "func TestWords_String(t *testing.T) {\n  candidate := words_string\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hi, my name is John\"), expected: []string{\"Hi\", \"my\", \"name\", \"is\", \"John\"} },\n     { actual: candidate(\"One, two, three, four, five, six\"), expected: []string{\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"} },\n     { actual: candidate(\"Hi, my name\"), expected: []string{\"Hi\", \"my\", \"name\"} },\n     { actual: candidate(\"One,, two, three, four, five, six,\"), expected: []string{\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"} },\n     { actual: candidate(\"\"), expected: []string{} },\n     { actual: candidate(\"ahmed     , gamal\"), expected: []string{\"ahmed\", \"gamal\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_18_how_many_times", "language": "go_test.go", "prompt": "package how_many_times_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Find how many times a given substring can be found in the original string. Count overlaping cases.\n// >>> how_many_times(\"\", \"a\")\n// 0\n// >>> how_many_times(\"aaa\", \"a\")\n// 3\n// >>> how_many_times(\"aaaa\", \"aa\")\n// 3\nfunc how_many_times(myString string, substring string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "func TestHow_Many_Times(t *testing.T) {\n  candidate := how_many_times\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\", \"x\"), expected: 0 },\n     { actual: candidate(\"xyxyxyx\", \"x\"), expected: 4 },\n     { actual: candidate(\"cacacacac\", \"cac\"), expected: 4 },\n     { actual: candidate(\"john doe\", \"john\"), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_18_how_many_times", "test": "func TestHow_Many_Times(t *testing.T) {\n  candidate := how_many_times\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\", \"x\"), expected: 0 },\n     { actual: candidate(\"xyxyxyx\", \"x\"), expected: 4 },\n     { actual: candidate(\"cacacacac\", \"cac\"), expected: 4 },\n     { actual: candidate(\"john doe\", \"john\"), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_51_remove_vowels", "language": "go_test.go", "prompt": "package remove_vowels_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// remove_vowels is a function that takes string and returns string without vowels.\n// >>> remove_vowels(\"\")\n// \"\"\n// >>> remove_vowels(\"abcdef\")\n// \"bcdf\"\n// >>> remove_vowels(\"aaaaa\")\n// \"\"\n// >>> remove_vowels(\"aaBAA\")\n// \"B\"\n// >>> remove_vowels(\"zbcd\")\n// \"zbcd\"\nfunc remove_vowels(text string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "func TestRemove_Vowels(t *testing.T) {\n  candidate := remove_vowels\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"abcdef\\nghijklm\"), expected: \"bcdf\\nghjklm\" },\n     { actual: candidate(\"fedcba\"), expected: \"fdcb\" },\n     { actual: candidate(\"eeeee\"), expected: \"\" },\n     { actual: candidate(\"acBAA\"), expected: \"cB\" },\n     { actual: candidate(\"EcBOO\"), expected: \"cB\" },\n     { actual: candidate(\"ybcd\"), expected: \"ybcd\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_51_remove_vowels", "test": "func TestRemove_Vowels(t *testing.T) {\n  candidate := remove_vowels\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"abcdef\\nghijklm\"), expected: \"bcdf\\nghjklm\" },\n     { actual: candidate(\"fedcba\"), expected: \"fdcb\" },\n     { actual: candidate(\"eeeee\"), expected: \"\" },\n     { actual: candidate(\"acBAA\"), expected: \"cB\" },\n     { actual: candidate(\"EcBOO\"), expected: \"cB\" },\n     { actual: candidate(\"ybcd\"), expected: \"ybcd\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_70_strange_sort_list", "language": "go_test.go", "prompt": "package strange_sort_list_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given list of integers, return list in strange order.\n// Strange sorting, is when you start with the minimum value,\n// then maximum of the remaining integers, then minimum and so on.\n// Examples:\n// >>> strange_sort_list([]int{1, 2, 3, 4})\n// []int{1, 4, 2, 3}\n// >>> strange_sort_list([]int{5, 5, 5, 5})\n// []int{5, 5, 5, 5}\n// >>> strange_sort_list([]int{})\n// []int{}\nfunc strange_sort_list(lst []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "func TestStrange_Sort_List(t *testing.T) {\n  candidate := strange_sort_list\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 4, 2, 3} },\n     { actual: candidate([]int{5, 6, 7, 8, 9}), expected: []int{5, 9, 6, 8, 7} },\n     { actual: candidate([]int{1, 2, 3, 4, 5}), expected: []int{1, 5, 2, 4, 3} },\n     { actual: candidate([]int{5, 6, 7, 8, 9, 1}), expected: []int{1, 9, 5, 8, 6, 7} },\n     { actual: candidate([]int{5, 5, 5, 5}), expected: []int{5, 5, 5, 5} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7, 8}), expected: []int{1, 8, 2, 7, 3, 6, 4, 5} },\n     { actual: candidate([]int{0, 2, 2, 2, 5, 5, -5, -5}), expected: []int{-5, 5, -5, 5, 0, 2, 2, 2} },\n     { actual: candidate([]int{111111}), expected: []int{111111} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_70_strange_sort_list", "test": "func TestStrange_Sort_List(t *testing.T) {\n  candidate := strange_sort_list\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 4, 2, 3} },\n     { actual: candidate([]int{5, 6, 7, 8, 9}), expected: []int{5, 9, 6, 8, 7} },\n     { actual: candidate([]int{1, 2, 3, 4, 5}), expected: []int{1, 5, 2, 4, 3} },\n     { actual: candidate([]int{5, 6, 7, 8, 9, 1}), expected: []int{1, 9, 5, 8, 6, 7} },\n     { actual: candidate([]int{5, 5, 5, 5}), expected: []int{5, 5, 5, 5} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7, 8}), expected: []int{1, 8, 2, 7, 3, 6, 4, 5} },\n     { actual: candidate([]int{0, 2, 2, 2, 5, 5, -5, -5}), expected: []int{-5, 5, -5, 5, 0, 2, 2, 2} },\n     { actual: candidate([]int{111111}), expected: []int{111111} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_20_find_closest_elements", "language": "go_test.go", "prompt": "package find_closest_elements_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// From a supplied list of numbers (of length at least two) select and return two that are the closest to each\n// other and return them in order (smaller number, larger number).\n// >>> find_closest_elements([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.2})\n// []interface{}{2.0, 2.2}\n// >>> find_closest_elements([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.0})\n// []interface{}{2.0, 2.0}\nfunc find_closest_elements(numbers []float64) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "func TestFind_Closest_Elements(t *testing.T) {\n  candidate := find_closest_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}), expected: []interface{}{3.9, 4.0} },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}), expected: []interface{}{5.0, 5.9} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.2}), expected: []interface{}{2.0, 2.2} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.0}), expected: []interface{}{2.0, 2.0} },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}), expected: []interface{}{2.2, 3.1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_20_find_closest_elements", "test": "func TestFind_Closest_Elements(t *testing.T) {\n  candidate := find_closest_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}), expected: []interface{}{3.9, 4.0} },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}), expected: []interface{}{5.0, 5.9} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.2}), expected: []interface{}{2.0, 2.2} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.0}), expected: []interface{}{2.0, 2.0} },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}), expected: []interface{}{2.2, 3.1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_76_is_simple_power", "language": "go_test.go", "prompt": "package is_simple_power_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Your task is to write a function that returns true if a number x is a simple\n// power of n and false in other cases.\n// x is a simple power of n if n**int=x\n// For example:\n// >>> is_simple_power(1, 4)\n// true\n// >>> is_simple_power(2, 2)\n// true\n// >>> is_simple_power(8, 2)\n// true\n// >>> is_simple_power(3, 2)\n// false\n// >>> is_simple_power(3, 1)\n// false\n// >>> is_simple_power(5, 3)\n// false\nfunc is_simple_power(x int, n int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "func TestIs_Simple_Power(t *testing.T) {\n  candidate := is_simple_power\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(16, 2), expected: true },\n     { actual: candidate(143214, 16), expected: false },\n     { actual: candidate(4, 2), expected: true },\n     { actual: candidate(9, 3), expected: true },\n     { actual: candidate(16, 4), expected: true },\n     { actual: candidate(24, 2), expected: false },\n     { actual: candidate(128, 4), expected: false },\n     { actual: candidate(12, 6), expected: false },\n     { actual: candidate(1, 1), expected: true },\n     { actual: candidate(1, 12), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_76_is_simple_power", "test": "func TestIs_Simple_Power(t *testing.T) {\n  candidate := is_simple_power\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(16, 2), expected: true },\n     { actual: candidate(143214, 16), expected: false },\n     { actual: candidate(4, 2), expected: true },\n     { actual: candidate(9, 3), expected: true },\n     { actual: candidate(16, 4), expected: true },\n     { actual: candidate(24, 2), expected: false },\n     { actual: candidate(128, 4), expected: false },\n     { actual: candidate(12, 6), expected: false },\n     { actual: candidate(1, 1), expected: true },\n     { actual: candidate(1, 12), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_39_prime_fib", "language": "go_test.go", "prompt": "package prime_fib_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n// >>> prime_fib(1)\n// 2\n// >>> prime_fib(2)\n// 3\n// >>> prime_fib(3)\n// 5\n// >>> prime_fib(4)\n// 13\n// >>> prime_fib(5)\n// 89\nfunc prime_fib(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "func TestPrime_Fib(t *testing.T) {\n  candidate := prime_fib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 2 },\n     { actual: candidate(2), expected: 3 },\n     { actual: candidate(3), expected: 5 },\n     { actual: candidate(4), expected: 13 },\n     { actual: candidate(5), expected: 89 },\n     { actual: candidate(6), expected: 233 },\n     { actual: candidate(7), expected: 1597 },\n     { actual: candidate(8), expected: 28657 },\n     { actual: candidate(9), expected: 514229 },\n     { actual: candidate(10), expected: 433494437 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_39_prime_fib", "test": "func TestPrime_Fib(t *testing.T) {\n  candidate := prime_fib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 2 },\n     { actual: candidate(2), expected: 3 },\n     { actual: candidate(3), expected: 5 },\n     { actual: candidate(4), expected: 13 },\n     { actual: candidate(5), expected: 89 },\n     { actual: candidate(6), expected: 233 },\n     { actual: candidate(7), expected: 1597 },\n     { actual: candidate(8), expected: 28657 },\n     { actual: candidate(9), expected: 514229 },\n     { actual: candidate(10), expected: 433494437 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_145_order_by_points", "language": "go_test.go", "prompt": "package order_by_points_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function which sorts the given list of integers\n// in ascending order according to the sum of their digits.\n// Note: if there are several items with similar sum of their digits,\n// order them based on their index in original list.\n// For example:\n// >>> order_by_points([]int{1, 11, -1, -11, -12})\n// []int{-1, -11, 1, -12, 11}\n// >>> order_by_points([]int{})\n// []int{}\nfunc order_by_points(nums []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "func TestOrder_By_Points(t *testing.T) {\n  candidate := order_by_points\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 11, -1, -11, -12}), expected: []int{-1, -11, 1, -12, 11} },\n     { actual: candidate([]int{1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46}), expected: []int{0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, -11, -32, 43, 54, -98, 2, -3}), expected: []int{-3, -32, -98, -11, 1, 2, 43, 54} },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}), expected: []int{1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9} },\n     { actual: candidate([]int{0, 6, 6, -76, -21, 23, 4}), expected: []int{-76, -21, 0, 4, 23, 6, 6} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_145_order_by_points", "test": "func TestOrder_By_Points(t *testing.T) {\n  candidate := order_by_points\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 11, -1, -11, -12}), expected: []int{-1, -11, 1, -12, 11} },\n     { actual: candidate([]int{1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46}), expected: []int{0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, -11, -32, 43, 54, -98, 2, -3}), expected: []int{-3, -32, -98, -11, 1, 2, 43, 54} },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11}), expected: []int{1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9} },\n     { actual: candidate([]int{0, 6, 6, -76, -21, 23, 4}), expected: []int{-76, -21, 0, 4, 23, 6, 6} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_0_has_close_elements", "language": "go_test.go", "prompt": "package has_close_elements_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Check if in given list of numbers, are any two numbers closer to each other than\n// given threshold.\n// >>> has_close_elements([]float64{1.0, 2.0, 3.0}, 0.5)\n// false\n// >>> has_close_elements([]float64{1.0, 2.8, 3.0, 4.0, 5.0, 2.0}, 0.3)\n// true\nfunc has_close_elements(numbers []float64, threshold float64) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "func TestHas_Close_Elements(t *testing.T) {\n  candidate := has_close_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}, 0.3), expected: true },\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}, 0.05), expected: false },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}, 0.95), expected: true },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}, 0.8), expected: false },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.0}, 0.1), expected: true },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}, 1.0), expected: true },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}, 0.5), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_0_has_close_elements", "test": "func TestHas_Close_Elements(t *testing.T) {\n  candidate := has_close_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}, 0.3), expected: true },\n     { actual: candidate([]float64{1.0, 2.0, 3.9, 4.0, 5.0, 2.2}, 0.05), expected: false },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}, 0.95), expected: true },\n     { actual: candidate([]float64{1.0, 2.0, 5.9, 4.0, 5.0}, 0.8), expected: false },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0, 2.0}, 0.1), expected: true },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}, 1.0), expected: true },\n     { actual: candidate([]float64{1.1, 2.2, 3.1, 4.1, 5.1}, 0.5), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_10_make_palindrome", "language": "go_test.go", "prompt": "package make_palindrome_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Find the shortest palindrome that begins with a supplied string.\n// Algorithm idea is simple:\n// - Find the longest postfix of supplied string that is a palindrome.\n// - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n// >>> make_palindrome(\"\")\n// \"\"\n// >>> make_palindrome(\"cat\")\n// \"catac\"\n// >>> make_palindrome(\"cata\")\n// \"catac\"\nfunc make_palindrome(myString string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "func TestMake_Palindrome(t *testing.T) {\n  candidate := make_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"x\"), expected: \"x\" },\n     { actual: candidate(\"xyz\"), expected: \"xyzyx\" },\n     { actual: candidate(\"xyx\"), expected: \"xyx\" },\n     { actual: candidate(\"jerry\"), expected: \"jerryrrej\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_10_make_palindrome", "test": "func TestMake_Palindrome(t *testing.T) {\n  candidate := make_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"x\"), expected: \"x\" },\n     { actual: candidate(\"xyz\"), expected: \"xyzyx\" },\n     { actual: candidate(\"xyx\"), expected: \"xyx\" },\n     { actual: candidate(\"jerry\"), expected: \"jerryrrej\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_11_string_xor", "language": "go_test.go", "prompt": "package string_xor_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Input are two strings a and b consisting only of 1s and 0s.\n// Perform binary XOR on these inputs and return result also as a string.\n// >>> string_xor(\"010\", \"110\")\n// \"100\"\nfunc string_xor(a string, b string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "func TestString_Xor(t *testing.T) {\n  candidate := string_xor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"111000\", \"101010\"), expected: \"010010\" },\n     { actual: candidate(\"1\", \"1\"), expected: \"0\" },\n     { actual: candidate(\"0101\", \"0000\"), expected: \"0101\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_11_string_xor", "test": "func TestString_Xor(t *testing.T) {\n  candidate := string_xor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"111000\", \"101010\"), expected: \"010010\" },\n     { actual: candidate(\"1\", \"1\"), expected: \"0\" },\n     { actual: candidate(\"0101\", \"0000\"), expected: \"0101\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_139_special_factorial", "language": "go_test.go", "prompt": "package special_factorial_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// The Brazilian factorial is defined as:\n// brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n// where n > 0\n// For example:\n// >>> special_factorial(4)\n// 288\n// The function will receive an integer as input and should return the special\n// factorial of this integer.\nfunc special_factorial(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "func TestSpecial_Factorial(t *testing.T) {\n  candidate := special_factorial\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(4), expected: 288 },\n     { actual: candidate(5), expected: 34560 },\n     { actual: candidate(7), expected: 125411328000 },\n     { actual: candidate(1), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_139_special_factorial", "test": "func TestSpecial_Factorial(t *testing.T) {\n  candidate := special_factorial\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(4), expected: 288 },\n     { actual: candidate(5), expected: 34560 },\n     { actual: candidate(7), expected: 125411328000 },\n     { actual: candidate(1), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_122_add_elements", "language": "go_test.go", "prompt": "package add_elements_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a non-empty list of integers arr and an integer k, return\n// the sum of the elements with at most two digits from the first k elements of arr.\n// Example:\n// >>> add_elements([]int{111, 21, 3, 4000, 5, 6, 7, 8, 9}, 4)\n// 24\n// Constraints:\n// 1. 1 <= len(arr) <= 100\n// 2. 1 <= k <= len(arr)\nfunc add_elements(arr []int, k int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "func TestAdd_Elements(t *testing.T) {\n  candidate := add_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, -2, -3, 41, 57, 76, 87, 88, 99}, 3), expected: -4 },\n     { actual: candidate([]int{111, 121, 3, 4000, 5, 6}, 2), expected: 0 },\n     { actual: candidate([]int{11, 21, 3, 90, 5, 6, 7, 8, 9}, 4), expected: 125 },\n     { actual: candidate([]int{111, 21, 3, 4000, 5, 6, 7, 8, 9}, 4), expected: 24 },\n     { actual: candidate([]int{1}, 1), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_122_add_elements", "test": "func TestAdd_Elements(t *testing.T) {\n  candidate := add_elements\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, -2, -3, 41, 57, 76, 87, 88, 99}, 3), expected: -4 },\n     { actual: candidate([]int{111, 121, 3, 4000, 5, 6}, 2), expected: 0 },\n     { actual: candidate([]int{11, 21, 3, 90, 5, 6, 7, 8, 9}, 4), expected: 125 },\n     { actual: candidate([]int{111, 21, 3, 4000, 5, 6, 7, 8, 9}, 4), expected: 24 },\n     { actual: candidate([]int{1}, 1), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_46_fib4", "language": "go_test.go", "prompt": "package fib4_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n// fib4(0) -> 0\n// fib4(1) -> 0\n// fib4(2) -> 2\n// fib4(3) -> 0\n// fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n// Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n// >>> fib4(5)\n// 4\n// >>> fib4(6)\n// 8\n// >>> fib4(7)\n// 14\nfunc fib4(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "func TestFib4(t *testing.T) {\n  candidate := fib4\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 4 },\n     { actual: candidate(8), expected: 28 },\n     { actual: candidate(10), expected: 104 },\n     { actual: candidate(12), expected: 386 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_46_fib4", "test": "func TestFib4(t *testing.T) {\n  candidate := fib4\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 4 },\n     { actual: candidate(8), expected: 28 },\n     { actual: candidate(10), expected: 104 },\n     { actual: candidate(12), expected: 386 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_104_unique_digits", "language": "go_test.go", "prompt": "package unique_digits_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of positive integers x. return a sorted list of all \n// elements that hasn't any even digit.\n// Note: Returned list should be sorted in increasing order.\n// For example:\n// >>> unique_digits([]int{15, 33, 1422, 1})\n// []int{1, 15, 33}\n// >>> unique_digits([]int{152, 323, 1422, 10})\n// []int{}\nfunc unique_digits(x []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "func TestUnique_Digits(t *testing.T) {\n  candidate := unique_digits\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{15, 33, 1422, 1}), expected: []int{1, 15, 33} },\n     { actual: candidate([]int{152, 323, 1422, 10}), expected: []int{} },\n     { actual: candidate([]int{12345, 2033, 111, 151}), expected: []int{111, 151} },\n     { actual: candidate([]int{135, 103, 31}), expected: []int{31, 135} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_104_unique_digits", "test": "func TestUnique_Digits(t *testing.T) {\n  candidate := unique_digits\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{15, 33, 1422, 1}), expected: []int{1, 15, 33} },\n     { actual: candidate([]int{152, 323, 1422, 10}), expected: []int{} },\n     { actual: candidate([]int{12345, 2033, 111, 151}), expected: []int{111, 151} },\n     { actual: candidate([]int{135, 103, 31}), expected: []int{31, 135} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_117_select_words", "language": "go_test.go", "prompt": "package select_words_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a string s and a natural number n, you have been tasked to implement \n// a function that returns a list of all words from string s that contain exactly \n// n consonants, in order these words appear in the string s.\n// If the string s is empty then the function should return an empty list.\n// Note: you may assume the input string contains only letters and spaces.\n// Examples:\n// >>> select_words(\"Mary had a little lamb\", 4)\n// []string{\"little\"}\n// >>> select_words(\"Mary had a little lamb\", 3)\n// []string{\"Mary\", \"lamb\"}\n// >>> select_words(\"simple white space\", 2)\n// []string{}\n// >>> select_words(\"Hello world\", 4)\n// []string{\"world\"}\n// >>> select_words(\"Uncle sam\", 3)\n// []string{\"Uncle\"}\nfunc select_words(s string, n int) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "func TestSelect_Words(t *testing.T) {\n  candidate := select_words\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Mary had a little lamb\", 4), expected: []string{\"little\"} },\n     { actual: candidate(\"Mary had a little lamb\", 3), expected: []string{\"Mary\", \"lamb\"} },\n     { actual: candidate(\"simple white space\", 2), expected: []string{} },\n     { actual: candidate(\"Hello world\", 4), expected: []string{\"world\"} },\n     { actual: candidate(\"Uncle sam\", 3), expected: []string{\"Uncle\"} },\n     { actual: candidate(\"\", 4), expected: []string{} },\n     { actual: candidate(\"a b c d e f\", 1), expected: []string{\"b\", \"c\", \"d\", \"f\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_117_select_words", "test": "func TestSelect_Words(t *testing.T) {\n  candidate := select_words\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Mary had a little lamb\", 4), expected: []string{\"little\"} },\n     { actual: candidate(\"Mary had a little lamb\", 3), expected: []string{\"Mary\", \"lamb\"} },\n     { actual: candidate(\"simple white space\", 2), expected: []string{} },\n     { actual: candidate(\"Hello world\", 4), expected: []string{\"world\"} },\n     { actual: candidate(\"Uncle sam\", 3), expected: []string{\"Uncle\"} },\n     { actual: candidate(\"\", 4), expected: []string{} },\n     { actual: candidate(\"a b c d e f\", 1), expected: []string{\"b\", \"c\", \"d\", \"f\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_72_will_it_fly", "language": "go_test.go", "prompt": "package will_it_fly_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that returns true if the object q will fly, and false otherwise.\n// The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n// Example:\n// >>> will_it_fly([]int{1, 2}, 5)\n// false\n// # 1+2 is less than the maximum possible weight, but it's unbalanced.\n// >>> will_it_fly([]int{3, 2, 3}, 1)\n// false\n// # it's balanced, but 3+2+3 is more than the maximum possible weight.\n// >>> will_it_fly([]int{3, 2, 3}, 9)\n// true\n// # 3+2+3 is less than the maximum possible weight, and it's balanced.\n// >>> will_it_fly([]int{3}, 5)\n// true\n// # 3 is less than the maximum possible weight, and it's balanced.\nfunc will_it_fly(q []int, w int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "func TestWill_It_Fly(t *testing.T) {\n  candidate := will_it_fly\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 2, 3}, 9), expected: true },\n     { actual: candidate([]int{1, 2}, 5), expected: false },\n     { actual: candidate([]int{3}, 5), expected: true },\n     { actual: candidate([]int{3, 2, 3}, 1), expected: false },\n     { actual: candidate([]int{1, 2, 3}, 6), expected: false },\n     { actual: candidate([]int{5}, 5), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_72_will_it_fly", "test": "func TestWill_It_Fly(t *testing.T) {\n  candidate := will_it_fly\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 2, 3}, 9), expected: true },\n     { actual: candidate([]int{1, 2}, 5), expected: false },\n     { actual: candidate([]int{3}, 5), expected: true },\n     { actual: candidate([]int{3, 2, 3}, 1), expected: false },\n     { actual: candidate([]int{1, 2, 3}, 6), expected: false },\n     { actual: candidate([]int{5}, 5), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_55_fib", "language": "go_test.go", "prompt": "package fib_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return n-th Fibonacci number.\n// >>> fib(10)\n// 55\n// >>> fib(1)\n// 1\n// >>> fib(8)\n// 21\nfunc fib(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "func TestFib(t *testing.T) {\n  candidate := fib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(10), expected: 55 },\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(8), expected: 21 },\n     { actual: candidate(11), expected: 89 },\n     { actual: candidate(12), expected: 144 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_55_fib", "test": "func TestFib(t *testing.T) {\n  candidate := fib\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(10), expected: 55 },\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(8), expected: 21 },\n     { actual: candidate(11), expected: 89 },\n     { actual: candidate(12), expected: 144 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_153_Strongest_Extension", "language": "go_test.go", "prompt": "package Strongest_Extension_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You will be given the name of a class (a string) and a list of extensions.\n// The extensions are to be used to load additional classes to the class. The\n// strength of the extension is as follows: Let CAP be the number of the uppercase\n// letters in the extension's name, and let SM be the number of lowercase letters \n// in the extension's name, the strength is given by the fraction CAP - SM. \n// You should find the strongest extension and return a string in this \n// format: ClassName.StrongestExtensionName.\n// If there are two or more extensions with the same strength, you should\n// choose the one that comes first in the list.\n// For example, if you are given \"Slices\" as the class and a list of the\n// extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n// return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n// (its strength is -1).\n// Example:\n// >>> Strongest_Extension(\"my_class\", []string{\"AA\", \"Be\", \"CC\"})\n// \"my_class.AA\"\nfunc Strongest_Extension(class_name string, extensions []string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "func TestStrongest_Extension(t *testing.T) {\n  candidate := Strongest_Extension\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Watashi\", []string{\"tEN\", \"niNE\", \"eIGHt8OKe\"}), expected: \"Watashi.eIGHt8OKe\" },\n     { actual: candidate(\"Boku123\", []string{\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"}), expected: \"Boku123.YEs.WeCaNe\" },\n     { actual: candidate(\"__YESIMHERE\", []string{\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"}), expected: \"__YESIMHERE.NuLl__\" },\n     { actual: candidate(\"K\", []string{\"Ta\", \"TAR\", \"t234An\", \"cosSo\"}), expected: \"K.TAR\" },\n     { actual: candidate(\"__HAHA\", []string{\"Tab\", \"123\", \"781345\", \"-_-\"}), expected: \"__HAHA.123\" },\n     { actual: candidate(\"YameRore\", []string{\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"}), expected: \"YameRore.okIWILL123\" },\n     { actual: candidate(\"finNNalLLly\", []string{\"Die\", \"NowW\", \"Wow\", \"WoW\"}), expected: \"finNNalLLly.WoW\" },\n     { actual: candidate(\"_\", []string{\"Bb\", \"91245\"}), expected: \"_.Bb\" },\n     { actual: candidate(\"Sp\", []string{\"671235\", \"Bb\"}), expected: \"Sp.671235\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_153_Strongest_Extension", "test": "func TestStrongest_Extension(t *testing.T) {\n  candidate := Strongest_Extension\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Watashi\", []string{\"tEN\", \"niNE\", \"eIGHt8OKe\"}), expected: \"Watashi.eIGHt8OKe\" },\n     { actual: candidate(\"Boku123\", []string{\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"}), expected: \"Boku123.YEs.WeCaNe\" },\n     { actual: candidate(\"__YESIMHERE\", []string{\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"}), expected: \"__YESIMHERE.NuLl__\" },\n     { actual: candidate(\"K\", []string{\"Ta\", \"TAR\", \"t234An\", \"cosSo\"}), expected: \"K.TAR\" },\n     { actual: candidate(\"__HAHA\", []string{\"Tab\", \"123\", \"781345\", \"-_-\"}), expected: \"__HAHA.123\" },\n     { actual: candidate(\"YameRore\", []string{\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"}), expected: \"YameRore.okIWILL123\" },\n     { actual: candidate(\"finNNalLLly\", []string{\"Die\", \"NowW\", \"Wow\", \"WoW\"}), expected: \"finNNalLLly.WoW\" },\n     { actual: candidate(\"_\", []string{\"Bb\", \"91245\"}), expected: \"_.Bb\" },\n     { actual: candidate(\"Sp\", []string{\"671235\", \"Bb\"}), expected: \"Sp.671235\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_119_match_parens", "language": "go_test.go", "prompt": "package match_parens_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a list of two strings, both strings consist of open\n// parentheses '(' or close parentheses ')' only.\n// Your job is to check if it is possible to concatenate the two strings in\n// some order, that the resulting string will be good.\n// A string S is considered to be good if and only if all parentheses in S\n// are balanced. For example: the string '(())()' is good, while the string\n// '())' is not.\n// Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n// Examples:\n// >>> match_parens([]string{\"()(\", \")\"})\n// \"Yes\"\n// >>> match_parens([]string{\")\", \")\"})\n// \"No\"\nfunc match_parens(lst []string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "func TestMatch_Parens(t *testing.T) {\n  candidate := match_parens\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"()(\", \")\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\")\", \")\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(()(())\", \"())())\"}), expected: \"No\" },\n     { actual: candidate([]string{\")())\", \"(()()(\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"(())))\", \"(()())((\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"()\", \"())\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(()(\", \"()))()\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"((((\", \"((())\"}), expected: \"No\" },\n     { actual: candidate([]string{\")(()\", \"(()(\"}), expected: \"No\" },\n     { actual: candidate([]string{\")(\", \")(\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(\", \")\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\")\", \"(\"}), expected: \"Yes\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_119_match_parens", "test": "func TestMatch_Parens(t *testing.T) {\n  candidate := match_parens\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"()(\", \")\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\")\", \")\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(()(())\", \"())())\"}), expected: \"No\" },\n     { actual: candidate([]string{\")())\", \"(()()(\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"(())))\", \"(()())((\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"()\", \"())\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(()(\", \"()))()\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\"((((\", \"((())\"}), expected: \"No\" },\n     { actual: candidate([]string{\")(()\", \"(()(\"}), expected: \"No\" },\n     { actual: candidate([]string{\")(\", \")(\"}), expected: \"No\" },\n     { actual: candidate([]string{\"(\", \")\"}), expected: \"Yes\" },\n     { actual: candidate([]string{\")\", \"(\"}), expected: \"Yes\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_92_any_int", "language": "go_test.go", "prompt": "package any_int_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function that takes 3 numbers.\n// Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n// Returns false in any other cases.\n// Examples\n// >>> any_int(5, 2, 7)\n// true\n// >>> any_int(3, 2, 2)\n// false\n// >>> any_int(3, -2, 1)\n// true\n// >>> any_int(3.6, -2.2, 2)\n// false\nfunc any_int(x float64, y float64, z float64) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "func TestAny_Int(t *testing.T) {\n  candidate := any_int\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2, 3, 1), expected: true },\n     { actual: candidate(2.5, 2, 3), expected: false },\n     { actual: candidate(1.5, 5, 3.5), expected: false },\n     { actual: candidate(2, 6, 2), expected: false },\n     { actual: candidate(4, 2, 2), expected: true },\n     { actual: candidate(2.2, 2.2, 2.2), expected: false },\n     { actual: candidate(-4, 6, 2), expected: true },\n     { actual: candidate(2, 1, 1), expected: true },\n     { actual: candidate(3, 4, 7), expected: true },\n     { actual: candidate(3.0, 4, 7), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_92_any_int", "test": "func TestAny_Int(t *testing.T) {\n  candidate := any_int\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2, 3, 1), expected: true },\n     { actual: candidate(2.5, 2, 3), expected: false },\n     { actual: candidate(1.5, 5, 3.5), expected: false },\n     { actual: candidate(2, 6, 2), expected: false },\n     { actual: candidate(4, 2, 2), expected: true },\n     { actual: candidate(2.2, 2.2, 2.2), expected: false },\n     { actual: candidate(-4, 6, 2), expected: true },\n     { actual: candidate(2, 1, 1), expected: true },\n     { actual: candidate(3, 4, 7), expected: true },\n     { actual: candidate(3.0, 4, 7), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_2_truncate_number", "language": "go_test.go", "prompt": "package truncate_number_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive floating point number, it can be decomposed into\n// and integer part (largest integer smaller than given number) and decimals\n// (leftover part always smaller than 1).\n// Return the decimal part of the number.\n// >>> truncate_number(3.5)\n// 0.5\nfunc truncate_number(number float64) float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "func TestTruncate_Number(t *testing.T) {\n  candidate := truncate_number\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3.5), expected: 0.5 },\n     { actual: candidate(1.25), expected: 0.25 },\n     { actual: candidate(123.0), expected: 0.0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_2_truncate_number", "test": "func TestTruncate_Number(t *testing.T) {\n  candidate := truncate_number\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3.5), expected: 0.5 },\n     { actual: candidate(1.25), expected: 0.25 },\n     { actual: candidate(123.0), expected: 0.0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_42_incr_list", "language": "go_test.go", "prompt": "package incr_list_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return list with elements incremented by 1.\n// >>> incr_list([]int{1, 2, 3})\n// []int{2, 3, 4}\n// >>> incr_list([]int{5, 3, 5, 2, 3, 3, 9, 0, 123})\n// []int{6, 4, 6, 3, 4, 4, 10, 1, 124}\nfunc incr_list(l []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "func TestIncr_List(t *testing.T) {\n  candidate := incr_list\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{3, 2, 1}), expected: []int{4, 3, 2} },\n     { actual: candidate([]int{5, 2, 5, 2, 3, 3, 9, 0, 123}), expected: []int{6, 3, 6, 3, 4, 4, 10, 1, 124} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_42_incr_list", "test": "func TestIncr_List(t *testing.T) {\n  candidate := incr_list\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{3, 2, 1}), expected: []int{4, 3, 2} },\n     { actual: candidate([]int{5, 2, 5, 2, 3, 3, 9, 0, 123}), expected: []int{6, 3, 6, 3, 4, 4, 10, 1, 124} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_150_x_or_y", "language": "go_test.go", "prompt": "package x_or_y_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// A simple program which should return the value of x if n is \n// a prime number and should return the value of y otherwise.\n// Examples:\n// >>> x_or_y(7, 34, 12)\n// 34\n// >>> x_or_y(15, 8, 5)\n// 5\nfunc x_or_y(n int, x int, y int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "func TestX_Or_Y(t *testing.T) {\n  candidate := x_or_y\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(7, 34, 12), expected: 34 },\n     { actual: candidate(15, 8, 5), expected: 5 },\n     { actual: candidate(3, 33, 5212), expected: 33 },\n     { actual: candidate(1259, 3, 52), expected: 3 },\n     { actual: candidate(7919, -1, 12), expected: -1 },\n     { actual: candidate(3609, 1245, 583), expected: 583 },\n     { actual: candidate(91, 56, 129), expected: 129 },\n     { actual: candidate(6, 34, 1234), expected: 1234 },\n     { actual: candidate(1, 2, 0), expected: 0 },\n     { actual: candidate(2, 2, 0), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_150_x_or_y", "test": "func TestX_Or_Y(t *testing.T) {\n  candidate := x_or_y\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(7, 34, 12), expected: 34 },\n     { actual: candidate(15, 8, 5), expected: 5 },\n     { actual: candidate(3, 33, 5212), expected: 33 },\n     { actual: candidate(1259, 3, 52), expected: 3 },\n     { actual: candidate(7919, -1, 12), expected: -1 },\n     { actual: candidate(3609, 1245, 583), expected: 583 },\n     { actual: candidate(91, 56, 129), expected: 129 },\n     { actual: candidate(6, 34, 1234), expected: 1234 },\n     { actual: candidate(1, 2, 0), expected: 0 },\n     { actual: candidate(2, 2, 0), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_49_modp", "language": "go_test.go", "prompt": "package modp_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return 2^n modulo p (be aware of numerics).\n// >>> modp(3, 5)\n// 3\n// >>> modp(1101, 101)\n// 2\n// >>> modp(0, 101)\n// 1\n// >>> modp(3, 11)\n// 8\n// >>> modp(100, 101)\n// 1\nfunc modp(n int, p int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "func TestModp(t *testing.T) {\n  candidate := modp\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 5), expected: 3 },\n     { actual: candidate(1101, 101), expected: 2 },\n     { actual: candidate(0, 101), expected: 1 },\n     { actual: candidate(3, 11), expected: 8 },\n     { actual: candidate(100, 101), expected: 1 },\n     { actual: candidate(30, 5), expected: 4 },\n     { actual: candidate(31, 5), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_49_modp", "test": "func TestModp(t *testing.T) {\n  candidate := modp\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 5), expected: 3 },\n     { actual: candidate(1101, 101), expected: 2 },\n     { actual: candidate(0, 101), expected: 1 },\n     { actual: candidate(3, 11), expected: 8 },\n     { actual: candidate(100, 101), expected: 1 },\n     { actual: candidate(30, 5), expected: 4 },\n     { actual: candidate(31, 5), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_155_even_odd_count", "language": "go_test.go", "prompt": "package even_odd_count_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given an integer. return a list that has the number of even and odd digits respectively.\n// Example:\n// >>> even_odd_count(-12)\n// []interface{}{1, 1}\n// >>> even_odd_count(123)\n// []interface{}{1, 2}\nfunc even_odd_count(num int) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "func TestEven_Odd_Count(t *testing.T) {\n  candidate := even_odd_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(7), expected: []interface{}{0, 1} },\n     { actual: candidate(-78), expected: []interface{}{1, 1} },\n     { actual: candidate(3452), expected: []interface{}{2, 2} },\n     { actual: candidate(346211), expected: []interface{}{3, 3} },\n     { actual: candidate(-345821), expected: []interface{}{3, 3} },\n     { actual: candidate(-2), expected: []interface{}{1, 0} },\n     { actual: candidate(-45347), expected: []interface{}{2, 3} },\n     { actual: candidate(0), expected: []interface{}{1, 0} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_155_even_odd_count", "test": "func TestEven_Odd_Count(t *testing.T) {\n  candidate := even_odd_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(7), expected: []interface{}{0, 1} },\n     { actual: candidate(-78), expected: []interface{}{1, 1} },\n     { actual: candidate(3452), expected: []interface{}{2, 2} },\n     { actual: candidate(346211), expected: []interface{}{3, 3} },\n     { actual: candidate(-345821), expected: []interface{}{3, 3} },\n     { actual: candidate(-2), expected: []interface{}{1, 0} },\n     { actual: candidate(-45347), expected: []interface{}{2, 3} },\n     { actual: candidate(0), expected: []interface{}{1, 0} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_80_is_happy", "language": "go_test.go", "prompt": "package is_happy_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a string s.\n// Your task is to check if the string is hapgo or not.\n// A string is hapgo if its length is at least 3 and every 3 consecutive letters are distinct\n// For example:\n// >>> is_happy(\"a\")\n// false\n// >>> is_happy(\"aa\")\n// false\n// >>> is_happy(\"abcd\")\n// true\n// >>> is_happy(\"aabb\")\n// false\n// >>> is_happy(\"adb\")\n// true\n// >>> is_happy(\"xyy\")\n// false\nfunc is_happy(s string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "func TestIs_Happy(t *testing.T) {\n  candidate := is_happy\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"a\"), expected: false },\n     { actual: candidate(\"aa\"), expected: false },\n     { actual: candidate(\"abcd\"), expected: true },\n     { actual: candidate(\"aabb\"), expected: false },\n     { actual: candidate(\"adb\"), expected: true },\n     { actual: candidate(\"xyy\"), expected: false },\n     { actual: candidate(\"iopaxpoi\"), expected: true },\n     { actual: candidate(\"iopaxioi\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_80_is_happy", "test": "func TestIs_Happy(t *testing.T) {\n  candidate := is_happy\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"a\"), expected: false },\n     { actual: candidate(\"aa\"), expected: false },\n     { actual: candidate(\"abcd\"), expected: true },\n     { actual: candidate(\"aabb\"), expected: false },\n     { actual: candidate(\"adb\"), expected: true },\n     { actual: candidate(\"xyy\"), expected: false },\n     { actual: candidate(\"iopaxpoi\"), expected: true },\n     { actual: candidate(\"iopaxioi\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_59_largest_prime_factor", "language": "go_test.go", "prompt": "package largest_prime_factor_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return the largest prime factor of n. Assume n > 1 and is not a prime.\n// >>> largest_prime_factor(13195)\n// 29\n// >>> largest_prime_factor(2048)\n// 2\nfunc largest_prime_factor(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "func TestLargest_Prime_Factor(t *testing.T) {\n  candidate := largest_prime_factor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(15), expected: 5 },\n     { actual: candidate(27), expected: 3 },\n     { actual: candidate(63), expected: 7 },\n     { actual: candidate(330), expected: 11 },\n     { actual: candidate(13195), expected: 29 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_59_largest_prime_factor", "test": "func TestLargest_Prime_Factor(t *testing.T) {\n  candidate := largest_prime_factor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(15), expected: 5 },\n     { actual: candidate(27), expected: 3 },\n     { actual: candidate(63), expected: 7 },\n     { actual: candidate(330), expected: 11 },\n     { actual: candidate(13195), expected: 29 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_66_digitSum", "language": "go_test.go", "prompt": "package digitSum_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Task\n// Write a function that takes a string as input and returns the sum of the upper characters only'\n// ASCII codes.\n// Examples:\n// >>> digitSum(\"\")\n// 0\n// >>> digitSum(\"abAB\")\n// 131\n// >>> digitSum(\"abcCd\")\n// 67\n// >>> digitSum(\"helloE\")\n// 69\n// >>> digitSum(\"woArBld\")\n// 131\n// >>> digitSum(\"aAaaaXa\")\n// 153\nfunc digitSum(s string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "func TestDigitsum(t *testing.T) {\n  candidate := digitSum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"abAB\"), expected: 131 },\n     { actual: candidate(\"abcCd\"), expected: 67 },\n     { actual: candidate(\"helloE\"), expected: 69 },\n     { actual: candidate(\"woArBld\"), expected: 131 },\n     { actual: candidate(\"aAaaaXa\"), expected: 153 },\n     { actual: candidate(\" How are yOu?\"), expected: 151 },\n     { actual: candidate(\"You arE Very Smart\"), expected: 327 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_66_digitSum", "test": "func TestDigitsum(t *testing.T) {\n  candidate := digitSum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"abAB\"), expected: 131 },\n     { actual: candidate(\"abcCd\"), expected: 67 },\n     { actual: candidate(\"helloE\"), expected: 69 },\n     { actual: candidate(\"woArBld\"), expected: 131 },\n     { actual: candidate(\"aAaaaXa\"), expected: 153 },\n     { actual: candidate(\" How are yOu?\"), expected: 151 },\n     { actual: candidate(\"You arE Very Smart\"), expected: 327 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_21_rescale_to_unit", "language": "go_test.go", "prompt": "package rescale_to_unit_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given list of numbers (of at least two elements), apply a linear transform to that list,\n// such that the smallest number will become 0 and the largest will become 1\n// >>> rescale_to_unit([]float64{1.0, 2.0, 3.0, 4.0, 5.0})\n// []float64{0.0, 0.25, 0.5, 0.75, 1.0}\nfunc rescale_to_unit(numbers []float64) []float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "func TestRescale_To_Unit(t *testing.T) {\n  candidate := rescale_to_unit\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{2.0, 49.9}), expected: []float64{0.0, 1.0} },\n     { actual: candidate([]float64{100.0, 49.9}), expected: []float64{1.0, 0.0} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0}), expected: []float64{0.0, 0.25, 0.5, 0.75, 1.0} },\n     { actual: candidate([]float64{2.0, 1.0, 5.0, 3.0, 4.0}), expected: []float64{0.25, 0.0, 1.0, 0.5, 0.75} },\n     { actual: candidate([]float64{12.0, 11.0, 15.0, 13.0, 14.0}), expected: []float64{0.25, 0.0, 1.0, 0.5, 0.75} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_21_rescale_to_unit", "test": "func TestRescale_To_Unit(t *testing.T) {\n  candidate := rescale_to_unit\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{2.0, 49.9}), expected: []float64{0.0, 1.0} },\n     { actual: candidate([]float64{100.0, 49.9}), expected: []float64{1.0, 0.0} },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0}), expected: []float64{0.0, 0.25, 0.5, 0.75, 1.0} },\n     { actual: candidate([]float64{2.0, 1.0, 5.0, 3.0, 4.0}), expected: []float64{0.25, 0.0, 1.0, 0.5, 0.75} },\n     { actual: candidate([]float64{12.0, 11.0, 15.0, 13.0, 14.0}), expected: []float64{0.25, 0.0, 1.0, 0.5, 0.75} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_121_solution", "language": "go_test.go", "prompt": "package solution_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a non-empty list of integers, return the sum of all of the odd elements that are in even positions.\n// Examples\n// >>> solution([]int{5, 8, 7, 1})\n// 12\n// >>> solution([]int{3, 3, 3, 3, 3})\n// 9\n// >>> solution([]int{30, 13, 24, 321})\n// 0\nfunc solution(lst []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "func TestSolution(t *testing.T) {\n  candidate := solution\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 8, 7, 1}), expected: 12 },\n     { actual: candidate([]int{3, 3, 3, 3, 3}), expected: 9 },\n     { actual: candidate([]int{30, 13, 24, 321}), expected: 0 },\n     { actual: candidate([]int{5, 9}), expected: 5 },\n     { actual: candidate([]int{2, 4, 8}), expected: 0 },\n     { actual: candidate([]int{30, 13, 23, 32}), expected: 23 },\n     { actual: candidate([]int{3, 13, 2, 9}), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_121_solution", "test": "func TestSolution(t *testing.T) {\n  candidate := solution\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 8, 7, 1}), expected: 12 },\n     { actual: candidate([]int{3, 3, 3, 3, 3}), expected: 9 },\n     { actual: candidate([]int{30, 13, 24, 321}), expected: 0 },\n     { actual: candidate([]int{5, 9}), expected: 5 },\n     { actual: candidate([]int{2, 4, 8}), expected: 0 },\n     { actual: candidate([]int{30, 13, 23, 32}), expected: 23 },\n     { actual: candidate([]int{3, 13, 2, 9}), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_68_pluck", "language": "go_test.go", "prompt": "package pluck_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// \"Given a list representing a branch of a tree that has non-negative integer nodes\n// your task is to pluck one of the nodes and return it.\n// The plucked node should be the node with the smallest even value.\n// If multiple nodes with the same smallest even value are found return the node that has smallest index.\n// The plucked node should be returned in a list, [ smalest_value, its index ],\n// If there are no even values or the given list is empty, return [].\n// Example 1:\n// >>> pluck([]int{4, 2, 3})\n// []int{2, 1}\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 2:\n// >>> pluck([]int{1, 2, 3})\n// []int{2, 1}\n// Explanation: 2 has the smallest even value, and 2 has the smallest index.\n// Example 3:\n// >>> pluck([]int{})\n// []int{}\n// Example 4:\n// >>> pluck([]int{5, 0, 3, 0, 4, 2})\n// []int{0, 1}\n// Explanation: 0 is the smallest value, but  there are two zeros,\n// so we will choose the first zero, which has the smallest index.\n// Constraints:\n// * 1 <= nodes.length <= 10000\n// * 0 <= node.value\nfunc pluck(arr []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "func TestPluck(t *testing.T) {\n  candidate := pluck\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{4, 2, 3}), expected: []int{2, 1} },\n     { actual: candidate([]int{1, 2, 3}), expected: []int{2, 1} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{5, 0, 3, 0, 4, 2}), expected: []int{0, 1} },\n     { actual: candidate([]int{1, 2, 3, 0, 5, 3}), expected: []int{0, 3} },\n     { actual: candidate([]int{5, 4, 8, 4, 8}), expected: []int{4, 1} },\n     { actual: candidate([]int{7, 6, 7, 1}), expected: []int{6, 1} },\n     { actual: candidate([]int{7, 9, 7, 1}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_68_pluck", "test": "func TestPluck(t *testing.T) {\n  candidate := pluck\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{4, 2, 3}), expected: []int{2, 1} },\n     { actual: candidate([]int{1, 2, 3}), expected: []int{2, 1} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{5, 0, 3, 0, 4, 2}), expected: []int{0, 1} },\n     { actual: candidate([]int{1, 2, 3, 0, 5, 3}), expected: []int{0, 3} },\n     { actual: candidate([]int{5, 4, 8, 4, 8}), expected: []int{4, 1} },\n     { actual: candidate([]int{7, 6, 7, 1}), expected: []int{6, 1} },\n     { actual: candidate([]int{7, 9, 7, 1}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_147_get_max_triples", "language": "go_test.go", "prompt": "package get_max_triples_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a positive integer n. You have to create an integer list a of length n.\n// For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n// Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n// and a[i] + a[j] + a[k] is a multiple of 3.\n// Example :\n// >>> get_max_triples(5)\n// 1\n// Explanation: \n// a = [1, 3, 7, 13, 21]\n// The only valid triple is (1, 7, 13).\nfunc get_max_triples(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "func TestGet_Max_Triples(t *testing.T) {\n  candidate := get_max_triples\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 1 },\n     { actual: candidate(6), expected: 4 },\n     { actual: candidate(10), expected: 36 },\n     { actual: candidate(100), expected: 53361 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_147_get_max_triples", "test": "func TestGet_Max_Triples(t *testing.T) {\n  candidate := get_max_triples\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: 1 },\n     { actual: candidate(6), expected: 4 },\n     { actual: candidate(10), expected: 36 },\n     { actual: candidate(100), expected: 53361 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_110_exchange", "language": "go_test.go", "prompt": "package exchange_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// In this problem, you will implement a function that takes two lists of numbers,\n// and determines whether it is possible to perform an exchange of elements\n// between them to make lst1 a list of only even numbers.\n// There is no limit on the number of exchanged elements between lst1 and lst2.\n// If it is possible to exchange elements between the lst1 and lst2 to make\n// all the elements of lst1 to be even, return \"YES\".\n// Otherwise, return \"NO\".\n// For example:\n// >>> exchange([]int{1, 2, 3, 4}, []int{1, 2, 3, 4})\n// \"YES\"\n// >>> exchange([]int{1, 2, 3, 4}, []int{1, 5, 3, 4})\n// \"NO\"\n// It is assumed that the input lists will be non-empty.\nfunc exchange(lst1 []int, lst2 []int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "func TestExchange(t *testing.T) {\n  candidate := exchange\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4}, []int{1, 2, 3, 4}), expected: \"YES\" },\n     { actual: candidate([]int{1, 2, 3, 4}, []int{1, 5, 3, 4}), expected: \"NO\" },\n     { actual: candidate([]int{1, 2, 3, 4}, []int{2, 1, 4, 3}), expected: \"YES\" },\n     { actual: candidate([]int{5, 7, 3}, []int{2, 6, 4}), expected: \"YES\" },\n     { actual: candidate([]int{5, 7, 3}, []int{2, 6, 3}), expected: \"NO\" },\n     { actual: candidate([]int{3, 2, 6, 1, 8, 9}, []int{3, 5, 5, 1, 1, 1}), expected: \"NO\" },\n     { actual: candidate([]int{100, 200}, []int{200, 200}), expected: \"YES\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_110_exchange", "test": "func TestExchange(t *testing.T) {\n  candidate := exchange\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4}, []int{1, 2, 3, 4}), expected: \"YES\" },\n     { actual: candidate([]int{1, 2, 3, 4}, []int{1, 5, 3, 4}), expected: \"NO\" },\n     { actual: candidate([]int{1, 2, 3, 4}, []int{2, 1, 4, 3}), expected: \"YES\" },\n     { actual: candidate([]int{5, 7, 3}, []int{2, 6, 4}), expected: \"YES\" },\n     { actual: candidate([]int{5, 7, 3}, []int{2, 6, 3}), expected: \"NO\" },\n     { actual: candidate([]int{3, 2, 6, 1, 8, 9}, []int{3, 5, 5, 1, 1, 1}), expected: \"NO\" },\n     { actual: candidate([]int{100, 200}, []int{200, 200}), expected: \"YES\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_47_median", "language": "go_test.go", "prompt": "package median_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return median of elements in the list l.\n// >>> median([]int{3, 1, 2, 4, 5})\n// 3\n// >>> median([]int{-10, 4, 6, 1000, 10, 20})\n// 15.0\nfunc median(l []int) float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "func TestMedian(t *testing.T) {\n  candidate := median\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 1, 2, 4, 5}), expected: 3 },\n     { actual: candidate([]int{-10, 4, 6, 1000, 10, 20}), expected: 8.0 },\n     { actual: candidate([]int{5}), expected: 5 },\n     { actual: candidate([]int{6, 5}), expected: 5.5 },\n     { actual: candidate([]int{8, 1, 3, 9, 9, 2, 7}), expected: 7 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_47_median", "test": "func TestMedian(t *testing.T) {\n  candidate := median\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{3, 1, 2, 4, 5}), expected: 3 },\n     { actual: candidate([]int{-10, 4, 6, 1000, 10, 20}), expected: 8.0 },\n     { actual: candidate([]int{5}), expected: 5 },\n     { actual: candidate([]int{6, 5}), expected: 5.5 },\n     { actual: candidate([]int{8, 1, 3, 9, 9, 2, 7}), expected: 7 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_82_prime_length", "language": "go_test.go", "prompt": "package prime_length_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that takes a string and returns true if the string\n// length is a prime number or false otherwise\n// Examples\n// >>> prime_length(\"Hello\")\n// true\n// >>> prime_length(\"abcdcba\")\n// true\n// >>> prime_length(\"kittens\")\n// true\n// >>> prime_length(\"orange\")\n// false\nfunc prime_length(myString string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "func TestPrime_Length(t *testing.T) {\n  candidate := prime_length\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hello\"), expected: true },\n     { actual: candidate(\"abcdcba\"), expected: true },\n     { actual: candidate(\"kittens\"), expected: true },\n     { actual: candidate(\"orange\"), expected: false },\n     { actual: candidate(\"wow\"), expected: true },\n     { actual: candidate(\"world\"), expected: true },\n     { actual: candidate(\"MadaM\"), expected: true },\n     { actual: candidate(\"Wow\"), expected: true },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"HI\"), expected: true },\n     { actual: candidate(\"go\"), expected: true },\n     { actual: candidate(\"gogo\"), expected: false },\n     { actual: candidate(\"aaaaaaaaaaaaaaa\"), expected: false },\n     { actual: candidate(\"Madam\"), expected: true },\n     { actual: candidate(\"M\"), expected: false },\n     { actual: candidate(\"0\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_82_prime_length", "test": "func TestPrime_Length(t *testing.T) {\n  candidate := prime_length\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hello\"), expected: true },\n     { actual: candidate(\"abcdcba\"), expected: true },\n     { actual: candidate(\"kittens\"), expected: true },\n     { actual: candidate(\"orange\"), expected: false },\n     { actual: candidate(\"wow\"), expected: true },\n     { actual: candidate(\"world\"), expected: true },\n     { actual: candidate(\"MadaM\"), expected: true },\n     { actual: candidate(\"Wow\"), expected: true },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"HI\"), expected: true },\n     { actual: candidate(\"go\"), expected: true },\n     { actual: candidate(\"gogo\"), expected: false },\n     { actual: candidate(\"aaaaaaaaaaaaaaa\"), expected: false },\n     { actual: candidate(\"Madam\"), expected: true },\n     { actual: candidate(\"M\"), expected: false },\n     { actual: candidate(\"0\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_73_smallest_change", "language": "go_test.go", "prompt": "package smallest_change_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list arr of integers, find the minimum number of elements that\n// need to be changed to make the list palindromic. A palindromic list is a list that\n// is read the same backwards and forwards. In one change, you can change one element to any other element.\n// For example:\n// >>> smallest_change([]int{1, 2, 3, 5, 4, 7, 9, 6})\n// 4\n// >>> smallest_change([]int{1, 2, 3, 4, 3, 2, 2})\n// 1\n// >>> smallest_change([]int{1, 2, 3, 2, 1})\n// 0\nfunc smallest_change(arr []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "func TestSmallest_Change(t *testing.T) {\n  candidate := smallest_change\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 5, 4, 7, 9, 6}), expected: 4 },\n     { actual: candidate([]int{1, 2, 3, 4, 3, 2, 2}), expected: 1 },\n     { actual: candidate([]int{1, 4, 2}), expected: 1 },\n     { actual: candidate([]int{1, 4, 4, 2}), expected: 1 },\n     { actual: candidate([]int{1, 2, 3, 2, 1}), expected: 0 },\n     { actual: candidate([]int{3, 1, 1, 3}), expected: 0 },\n     { actual: candidate([]int{1}), expected: 0 },\n     { actual: candidate([]int{0, 1}), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_73_smallest_change", "test": "func TestSmallest_Change(t *testing.T) {\n  candidate := smallest_change\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 5, 4, 7, 9, 6}), expected: 4 },\n     { actual: candidate([]int{1, 2, 3, 4, 3, 2, 2}), expected: 1 },\n     { actual: candidate([]int{1, 4, 2}), expected: 1 },\n     { actual: candidate([]int{1, 4, 4, 2}), expected: 1 },\n     { actual: candidate([]int{1, 2, 3, 2, 1}), expected: 0 },\n     { actual: candidate([]int{3, 1, 1, 3}), expected: 0 },\n     { actual: candidate([]int{1}), expected: 0 },\n     { actual: candidate([]int{0, 1}), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_133_sum_squares", "language": "go_test.go", "prompt": "package sum_squares_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a list of numbers.\n// You need to return the sum of squared numbers in the given list,\n// round each element in the list to the upper int(Ceiling) first.\n// Examples:\n// >>> lst([]float64{1.0, 2.0, 3.0})\n// 14\n// >>> lst([]float64{1.0, 4.0, 9.0})\n// 98\n// >>> lst([]float64{1.0, 3.0, 5.0, 7.0})\n// 84\n// >>> lst([]float64{1.4, 4.2, 0.0})\n// 29\n// >>> lst([]float64{-2.4, 1.0, 1.0})\n// 6\nfunc sum_squares(lst []float64) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "func TestSum_Squares(t *testing.T) {\n  candidate := sum_squares\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.0}), expected: 14 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0}), expected: 14 },\n     { actual: candidate([]float64{1.0, 3.0, 5.0, 7.0}), expected: 84 },\n     { actual: candidate([]float64{1.4, 4.2, 0.0}), expected: 29 },\n     { actual: candidate([]float64{-2.4, 1.0, 1.0}), expected: 6 },\n     { actual: candidate([]float64{100.0, 1.0, 15.0, 2.0}), expected: 10230 },\n     { actual: candidate([]float64{10000.0, 10000.0}), expected: 200000000 },\n     { actual: candidate([]float64{-1.4, 4.6, 6.3}), expected: 75 },\n     { actual: candidate([]float64{-1.4, 17.9, 18.9, 19.9}), expected: 1086 },\n     { actual: candidate([]float64{0.0}), expected: 0 },\n     { actual: candidate([]float64{-1.0}), expected: 1 },\n     { actual: candidate([]float64{-1.0, 1.0, 0.0}), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_133_sum_squares", "test": "func TestSum_Squares(t *testing.T) {\n  candidate := sum_squares\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0, 3.0}), expected: 14 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0}), expected: 14 },\n     { actual: candidate([]float64{1.0, 3.0, 5.0, 7.0}), expected: 84 },\n     { actual: candidate([]float64{1.4, 4.2, 0.0}), expected: 29 },\n     { actual: candidate([]float64{-2.4, 1.0, 1.0}), expected: 6 },\n     { actual: candidate([]float64{100.0, 1.0, 15.0, 2.0}), expected: 10230 },\n     { actual: candidate([]float64{10000.0, 10000.0}), expected: 200000000 },\n     { actual: candidate([]float64{-1.4, 4.6, 6.3}), expected: 75 },\n     { actual: candidate([]float64{-1.4, 17.9, 18.9, 19.9}), expected: 1086 },\n     { actual: candidate([]float64{0.0}), expected: 0 },\n     { actual: candidate([]float64{-1.0}), expected: 1 },\n     { actual: candidate([]float64{-1.0, 1.0, 0.0}), expected: 2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_141_file_name_check", "language": "go_test.go", "prompt": "package file_name_check_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function which takes a string representing a file's name, and returns\n// 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n// A file's name is considered to be valid if and only if all the following conditions \n// are met:\n// - There should not be more than three digits ('0'-'9') in the file's name.\n// - The file's name contains exactly one dot '.'\n// - The substring before the dot should not be empty, and it starts with a letter from \n// the latin alphapet ('a'-'z' and 'A'-'Z').\n// - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n// Examples:\n// >>> file_name_check(\"example.txt\")\n// \"Yes\"\n// >>> file_name_check(\"1example.dll\")\n// \"No\"\nfunc file_name_check(file_name string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "func TestFile_Name_Check(t *testing.T) {\n  candidate := file_name_check\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"example.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"1example.dll\"), expected: \"No\" },\n     { actual: candidate(\"s1sdf3.asd\"), expected: \"No\" },\n     { actual: candidate(\"K.dll\"), expected: \"Yes\" },\n     { actual: candidate(\"MY16FILE3.exe\"), expected: \"Yes\" },\n     { actual: candidate(\"His12FILE94.exe\"), expected: \"No\" },\n     { actual: candidate(\"_Y.txt\"), expected: \"No\" },\n     { actual: candidate(\"?aREYA.exe\"), expected: \"No\" },\n     { actual: candidate(\"/this_is_valid.dll\"), expected: \"No\" },\n     { actual: candidate(\"this_is_valid.wow\"), expected: \"No\" },\n     { actual: candidate(\"this_is_valid.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"this_is_valid.txtexe\"), expected: \"No\" },\n     { actual: candidate(\"#this2_i4s_5valid.ten\"), expected: \"No\" },\n     { actual: candidate(\"@this1_is6_valid.exe\"), expected: \"No\" },\n     { actual: candidate(\"this_is_12valid.6exe4.txt\"), expected: \"No\" },\n     { actual: candidate(\"all.exe.txt\"), expected: \"No\" },\n     { actual: candidate(\"I563_No.exe\"), expected: \"Yes\" },\n     { actual: candidate(\"Is3youfault.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"no_one#knows.dll\"), expected: \"Yes\" },\n     { actual: candidate(\"1I563_Yes3.exe\"), expected: \"No\" },\n     { actual: candidate(\"I563_Yes3.txtt\"), expected: \"No\" },\n     { actual: candidate(\"final..txt\"), expected: \"No\" },\n     { actual: candidate(\"final132\"), expected: \"No\" },\n     { actual: candidate(\"_f4indsartal132.\"), expected: \"No\" },\n     { actual: candidate(\".txt\"), expected: \"No\" },\n     { actual: candidate(\"s.\"), expected: \"No\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_141_file_name_check", "test": "func TestFile_Name_Check(t *testing.T) {\n  candidate := file_name_check\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"example.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"1example.dll\"), expected: \"No\" },\n     { actual: candidate(\"s1sdf3.asd\"), expected: \"No\" },\n     { actual: candidate(\"K.dll\"), expected: \"Yes\" },\n     { actual: candidate(\"MY16FILE3.exe\"), expected: \"Yes\" },\n     { actual: candidate(\"His12FILE94.exe\"), expected: \"No\" },\n     { actual: candidate(\"_Y.txt\"), expected: \"No\" },\n     { actual: candidate(\"?aREYA.exe\"), expected: \"No\" },\n     { actual: candidate(\"/this_is_valid.dll\"), expected: \"No\" },\n     { actual: candidate(\"this_is_valid.wow\"), expected: \"No\" },\n     { actual: candidate(\"this_is_valid.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"this_is_valid.txtexe\"), expected: \"No\" },\n     { actual: candidate(\"#this2_i4s_5valid.ten\"), expected: \"No\" },\n     { actual: candidate(\"@this1_is6_valid.exe\"), expected: \"No\" },\n     { actual: candidate(\"this_is_12valid.6exe4.txt\"), expected: \"No\" },\n     { actual: candidate(\"all.exe.txt\"), expected: \"No\" },\n     { actual: candidate(\"I563_No.exe\"), expected: \"Yes\" },\n     { actual: candidate(\"Is3youfault.txt\"), expected: \"Yes\" },\n     { actual: candidate(\"no_one#knows.dll\"), expected: \"Yes\" },\n     { actual: candidate(\"1I563_Yes3.exe\"), expected: \"No\" },\n     { actual: candidate(\"I563_Yes3.txtt\"), expected: \"No\" },\n     { actual: candidate(\"final..txt\"), expected: \"No\" },\n     { actual: candidate(\"final132\"), expected: \"No\" },\n     { actual: candidate(\"_f4indsartal132.\"), expected: \"No\" },\n     { actual: candidate(\".txt\"), expected: \"No\" },\n     { actual: candidate(\"s.\"), expected: \"No\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "go_test.go", "prompt": "package triples_sum_to_zero_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// triples_sum_to_zero takes a list of integers as an input.\n// it returns true if there are three distinct elements in the list that\n// sum to zero, and false otherwise.\n// >>> triples_sum_to_zero([]int{1, 3, 5, 0})\n// false\n// >>> triples_sum_to_zero([]int{1, 3, -2, 1})\n// true\n// >>> triples_sum_to_zero([]int{1, 2, 3, 7})\n// false\n// >>> triples_sum_to_zero([]int{2, 4, -5, 3, 9, 7})\n// true\n// >>> triples_sum_to_zero([]int{1})\n// false\nfunc triples_sum_to_zero(l []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "func TestTriples_Sum_To_Zero(t *testing.T) {\n  candidate := triples_sum_to_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 3, 5, 0}), expected: false },\n     { actual: candidate([]int{1, 3, 5, -1}), expected: false },\n     { actual: candidate([]int{1, 3, -2, 1}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 7}), expected: false },\n     { actual: candidate([]int{1, 2, 5, 7}), expected: false },\n     { actual: candidate([]int{2, 4, -5, 3, 9, 7}), expected: true },\n     { actual: candidate([]int{1}), expected: false },\n     { actual: candidate([]int{1, 3, 5, -100}), expected: false },\n     { actual: candidate([]int{100, 3, 5, -100}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "func TestTriples_Sum_To_Zero(t *testing.T) {\n  candidate := triples_sum_to_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 3, 5, 0}), expected: false },\n     { actual: candidate([]int{1, 3, 5, -1}), expected: false },\n     { actual: candidate([]int{1, 3, -2, 1}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 7}), expected: false },\n     { actual: candidate([]int{1, 2, 5, 7}), expected: false },\n     { actual: candidate([]int{2, 4, -5, 3, 9, 7}), expected: true },\n     { actual: candidate([]int{1}), expected: false },\n     { actual: candidate([]int{1, 3, 5, -100}), expected: false },\n     { actual: candidate([]int{100, 3, 5, -100}), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_127_intersection", "language": "go_test.go", "prompt": "package intersection_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given two intervals,\n// where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n// The given intervals are closed which means that the interval (start, end)\n// includes both start and end.\n// For each given interval, it is assumed that its start is less or equal its end.\n// Your task is to determine whether the length of intersection of these two \n// intervals is a prime number.\n// Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n// which its length is 1, which not a prime number.\n// If the length of the intersection is a prime number, return \"YES\",\n// otherwise, return \"NO\".\n// If the two intervals don't intersect, return \"NO\".\n// [input/output] samples:\n// >>> intersection([]interface{}{1, 2}, []interface{}{2, 3})\n// \"NO\"\n// >>> intersection([]interface{}{-1, 1}, []interface{}{0, 4})\n// \"NO\"\n// >>> intersection([]interface{}{-3, -1}, []interface{}{-5, 5})\n// \"YES\"\nfunc intersection(interval1 []interface{}, interval2 []interface{}) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "func TestIntersection(t *testing.T) {\n  candidate := intersection\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]interface{}{1, 2}, []interface{}{2, 3}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-1, 1}, []interface{}{0, 4}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-3, -1}, []interface{}{-5, 5}), expected: \"YES\" },\n     { actual: candidate([]interface{}{-2, 2}, []interface{}{-4, 0}), expected: \"YES\" },\n     { actual: candidate([]interface{}{-11, 2}, []interface{}{-1, -1}), expected: \"NO\" },\n     { actual: candidate([]interface{}{1, 2}, []interface{}{3, 5}), expected: \"NO\" },\n     { actual: candidate([]interface{}{1, 2}, []interface{}{1, 2}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-2, -2}, []interface{}{-3, -2}), expected: \"NO\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_127_intersection", "test": "func TestIntersection(t *testing.T) {\n  candidate := intersection\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]interface{}{1, 2}, []interface{}{2, 3}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-1, 1}, []interface{}{0, 4}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-3, -1}, []interface{}{-5, 5}), expected: \"YES\" },\n     { actual: candidate([]interface{}{-2, 2}, []interface{}{-4, 0}), expected: \"YES\" },\n     { actual: candidate([]interface{}{-11, 2}, []interface{}{-1, -1}), expected: \"NO\" },\n     { actual: candidate([]interface{}{1, 2}, []interface{}{3, 5}), expected: \"NO\" },\n     { actual: candidate([]interface{}{1, 2}, []interface{}{1, 2}), expected: \"NO\" },\n     { actual: candidate([]interface{}{-2, -2}, []interface{}{-3, -2}), expected: \"NO\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_1_separate_paren_groups", "language": "go_test.go", "prompt": "package separate_paren_groups_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n// separate those group into separate strings and return the list of those.\n// Separate groups are balanced (each open brace is properly closed) and not nested within each other\n// Ignore any spaces in the input string.\n// >>> separate_paren_groups(\"( ) (( )) (( )( ))\")\n// []string{\"()\", \"(())\", \"(()())\"}\nfunc separate_paren_groups(paren_string string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "func TestSeparate_Paren_Groups(t *testing.T) {\n  candidate := separate_paren_groups\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"(()()) ((())) () ((())()())\"), expected: []string{\"(()())\", \"((()))\", \"()\", \"((())()())\"} },\n     { actual: candidate(\"() (()) ((())) (((())))\"), expected: []string{\"()\", \"(())\", \"((()))\", \"(((())))\"} },\n     { actual: candidate(\"(()(())((())))\"), expected: []string{\"(()(())((())))\"} },\n     { actual: candidate(\"( ) (( )) (( )( ))\"), expected: []string{\"()\", \"(())\", \"(()())\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_1_separate_paren_groups", "test": "func TestSeparate_Paren_Groups(t *testing.T) {\n  candidate := separate_paren_groups\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"(()()) ((())) () ((())()())\"), expected: []string{\"(()())\", \"((()))\", \"()\", \"((())()())\"} },\n     { actual: candidate(\"() (()) ((())) (((())))\"), expected: []string{\"()\", \"(())\", \"((()))\", \"(((())))\"} },\n     { actual: candidate(\"(()(())((())))\"), expected: []string{\"(()(())((())))\"} },\n     { actual: candidate(\"( ) (( )) (( )( ))\"), expected: []string{\"()\", \"(())\", \"(()())\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_152_compare", "language": "go_test.go", "prompt": "package compare_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// I think we all remember that feeling when the result of some long-awaited\n// event is finally known. The feelings and thoughts you have at that moment are\n// definitely worth noting down and comparing.\n// Your task is to determine if a person correctly guessed the results of a number of matches.\n// You are given two lists of scores and guesses of equal length, where each index shows a match. \n// Return a list of the same length denoting how far off each guess was. If they have guessed correctly,\n// the value is 0, and if not, the value is the absolute difference between the guess and the score.\n// example:\n// >>> compare([]int{1, 2, 3, 4, 5, 1}, []int{1, 2, 3, 4, 2, -2})\n// []int{0, 0, 0, 0, 3, 3}\n// >>> compare([]int{0, 5, 0, 0, 0, 4}, []int{4, 1, 1, 0, 0, -2})\n// []int{4, 4, 1, 0, 0, 6}\nfunc compare(game []int, guess []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "func TestCompare(t *testing.T) {\n  candidate := compare\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4, 5, 1}, []int{1, 2, 3, 4, 2, -2}), expected: []int{0, 0, 0, 0, 3, 3} },\n     { actual: candidate([]int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0, 0}), expected: []int{0, 0, 0, 0, 0, 0} },\n     { actual: candidate([]int{1, 2, 3}, []int{-1, -2, -3}), expected: []int{2, 4, 6} },\n     { actual: candidate([]int{1, 2, 3, 5}, []int{-1, 2, 3, 4}), expected: []int{2, 0, 0, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_152_compare", "test": "func TestCompare(t *testing.T) {\n  candidate := compare\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3, 4, 5, 1}, []int{1, 2, 3, 4, 2, -2}), expected: []int{0, 0, 0, 0, 3, 3} },\n     { actual: candidate([]int{0, 0, 0, 0, 0, 0}, []int{0, 0, 0, 0, 0, 0}), expected: []int{0, 0, 0, 0, 0, 0} },\n     { actual: candidate([]int{1, 2, 3}, []int{-1, -2, -3}), expected: []int{2, 4, 6} },\n     { actual: candidate([]int{1, 2, 3, 5}, []int{-1, 2, 3, 4}), expected: []int{2, 0, 0, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_83_starts_one_ends", "language": "go_test.go", "prompt": "package starts_one_ends_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer n, return the count of the numbers of n-digit\n// positive integers that start or end with 1.\nfunc starts_one_ends(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "func TestStarts_One_Ends(t *testing.T) {\n  candidate := starts_one_ends\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(2), expected: 18 },\n     { actual: candidate(3), expected: 180 },\n     { actual: candidate(4), expected: 1800 },\n     { actual: candidate(5), expected: 18000 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_83_starts_one_ends", "test": "func TestStarts_One_Ends(t *testing.T) {\n  candidate := starts_one_ends\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(2), expected: 18 },\n     { actual: candidate(3), expected: 180 },\n     { actual: candidate(4), expected: 1800 },\n     { actual: candidate(5), expected: 18000 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "go_test.go", "prompt": "package check_if_last_char_is_a_letter_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function that returns true if the last character\n// of a given string is an alphabetical character and is not\n// a part of a word, and false otherwise.\n// Note: \"word\" is a group of characters separated by space.\n// Examples:\n// >>> check_if_last_char_is_a_letter(\"apple pie\")\n// false\n// >>> check_if_last_char_is_a_letter(\"apple pi e\")\n// true\n// >>> check_if_last_char_is_a_letter(\"apple pi e \")\n// false\n// >>> check_if_last_char_is_a_letter(\"\")\n// false\nfunc check_if_last_char_is_a_letter(txt string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "func TestCheck_If_Last_Char_Is_A_Letter(t *testing.T) {\n  candidate := check_if_last_char_is_a_letter\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"apple\"), expected: false },\n     { actual: candidate(\"apple pi e\"), expected: true },\n     { actual: candidate(\"eeeee\"), expected: false },\n     { actual: candidate(\"A\"), expected: true },\n     { actual: candidate(\"Pumpkin pie \"), expected: false },\n     { actual: candidate(\"Pumpkin pie 1\"), expected: false },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"eeeee e \"), expected: false },\n     { actual: candidate(\"apple pie\"), expected: false },\n     { actual: candidate(\"apple pi e \"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "func TestCheck_If_Last_Char_Is_A_Letter(t *testing.T) {\n  candidate := check_if_last_char_is_a_letter\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"apple\"), expected: false },\n     { actual: candidate(\"apple pi e\"), expected: true },\n     { actual: candidate(\"eeeee\"), expected: false },\n     { actual: candidate(\"A\"), expected: true },\n     { actual: candidate(\"Pumpkin pie \"), expected: false },\n     { actual: candidate(\"Pumpkin pie 1\"), expected: false },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"eeeee e \"), expected: false },\n     { actual: candidate(\"apple pie\"), expected: false },\n     { actual: candidate(\"apple pi e \"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_124_valid_date", "language": "go_test.go", "prompt": "package valid_date_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You have to write a function which validates a given date string and\n// returns true if the date is valid otherwise false.\n// The date is valid if all of the following rules are satisfied:\n// 1. The date string is not empty.\n// 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n// 3. The months should not be less than 1 or higher than 12.\n// 4. The date should be in the format: mm-dd-yyyy\n// >>> valid_date(\"03-11-2000\")\n// true\n// >>> valid_date(\"15-01-2012\")\n// false\n// >>> valid_date(\"04-0-2040\")\n// false\n// >>> valid_date(\"06-04-2020\")\n// true\n// >>> valid_date(\"06/04/2020\")\n// false\nfunc valid_date(date string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "func TestValid_Date(t *testing.T) {\n  candidate := valid_date\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"03-11-2000\"), expected: true },\n     { actual: candidate(\"15-01-2012\"), expected: false },\n     { actual: candidate(\"04-0-2040\"), expected: false },\n     { actual: candidate(\"06-04-2020\"), expected: true },\n     { actual: candidate(\"01-01-2007\"), expected: true },\n     { actual: candidate(\"03-32-2011\"), expected: false },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"04-31-3000\"), expected: false },\n     { actual: candidate(\"06-06-2005\"), expected: true },\n     { actual: candidate(\"21-31-2000\"), expected: false },\n     { actual: candidate(\"04-12-2003\"), expected: true },\n     { actual: candidate(\"04122003\"), expected: false },\n     { actual: candidate(\"20030412\"), expected: false },\n     { actual: candidate(\"2003-04\"), expected: false },\n     { actual: candidate(\"2003-04-12\"), expected: false },\n     { actual: candidate(\"04-2003\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_124_valid_date", "test": "func TestValid_Date(t *testing.T) {\n  candidate := valid_date\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"03-11-2000\"), expected: true },\n     { actual: candidate(\"15-01-2012\"), expected: false },\n     { actual: candidate(\"04-0-2040\"), expected: false },\n     { actual: candidate(\"06-04-2020\"), expected: true },\n     { actual: candidate(\"01-01-2007\"), expected: true },\n     { actual: candidate(\"03-32-2011\"), expected: false },\n     { actual: candidate(\"\"), expected: false },\n     { actual: candidate(\"04-31-3000\"), expected: false },\n     { actual: candidate(\"06-06-2005\"), expected: true },\n     { actual: candidate(\"21-31-2000\"), expected: false },\n     { actual: candidate(\"04-12-2003\"), expected: true },\n     { actual: candidate(\"04122003\"), expected: false },\n     { actual: candidate(\"20030412\"), expected: false },\n     { actual: candidate(\"2003-04\"), expected: false },\n     { actual: candidate(\"2003-04-12\"), expected: false },\n     { actual: candidate(\"04-2003\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_108_count_nums", "language": "go_test.go", "prompt": "package count_nums_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function count_nums which takes a list of integers and returns\n// the number of elements which has a sum of digits > 0.\n// If a number is negative, then its first signed digit will be negative:\n// e.g. -123 has signed digits -1, 2, and 3.\n// >>> count_nums([]int{})\n// 0\n// >>> count_nums([]int{-1, 11, -11})\n// 1\n// >>> count_nums([]int{1, 1, 2})\n// 3\nfunc count_nums(arr []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "func TestCount_Nums(t *testing.T) {\n  candidate := count_nums\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: 0 },\n     { actual: candidate([]int{-1, -2, 0}), expected: 0 },\n     { actual: candidate([]int{1, 1, 2, -2, 3, 4, 5}), expected: 6 },\n     { actual: candidate([]int{1, 6, 9, -6, 0, 1, 5}), expected: 5 },\n     { actual: candidate([]int{1, 100, 98, -7, 1, -1}), expected: 4 },\n     { actual: candidate([]int{12, 23, 34, -45, -56, 0}), expected: 5 },\n     { actual: candidate([]int{0, 1}), expected: 1 },\n     { actual: candidate([]int{1}), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_108_count_nums", "test": "func TestCount_Nums(t *testing.T) {\n  candidate := count_nums\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: 0 },\n     { actual: candidate([]int{-1, -2, 0}), expected: 0 },\n     { actual: candidate([]int{1, 1, 2, -2, 3, 4, 5}), expected: 6 },\n     { actual: candidate([]int{1, 6, 9, -6, 0, 1, 5}), expected: 5 },\n     { actual: candidate([]int{1, 100, 98, -7, 1, -1}), expected: 4 },\n     { actual: candidate([]int{12, 23, 34, -45, -56, 0}), expected: 5 },\n     { actual: candidate([]int{0, 1}), expected: 1 },\n     { actual: candidate([]int{1}), expected: 1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_86_anti_shuffle", "language": "go_test.go", "prompt": "package anti_shuffle_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that takes a string and returns an ordered version of it.\n// Ordered version of string, is a string where all words (separated by space)\n// are replaced by a new word where all the characters arranged in\n// ascending order based on ascii value.\n// Note: You should keep the order of words and blank spaces in the sentence.\n// For example:\n// >>> anti_shuffle(\"Hi\")\n// \"Hi\"\n// >>> anti_shuffle(\"hello\")\n// \"ehllo\"\n// >>> anti_shuffle(\"Hello World!!!\")\n// \"Hello !!!Wdlor\"\nfunc anti_shuffle(s string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "func TestAnti_Shuffle(t *testing.T) {\n  candidate := anti_shuffle\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hi\"), expected: \"Hi\" },\n     { actual: candidate(\"hello\"), expected: \"ehllo\" },\n     { actual: candidate(\"number\"), expected: \"bemnru\" },\n     { actual: candidate(\"abcd\"), expected: \"abcd\" },\n     { actual: candidate(\"Hello World!!!\"), expected: \"Hello !!!Wdlor\" },\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"Hi. My name is Mister Robot. How are you?\"), expected: \".Hi My aemn is Meirst .Rboot How aer ?ouy\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_86_anti_shuffle", "test": "func TestAnti_Shuffle(t *testing.T) {\n  candidate := anti_shuffle\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"Hi\"), expected: \"Hi\" },\n     { actual: candidate(\"hello\"), expected: \"ehllo\" },\n     { actual: candidate(\"number\"), expected: \"bemnru\" },\n     { actual: candidate(\"abcd\"), expected: \"abcd\" },\n     { actual: candidate(\"Hello World!!!\"), expected: \"Hello !!!Wdlor\" },\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"Hi. My name is Mister Robot. How are you?\"), expected: \".Hi My aemn is Meirst .Rboot How aer ?ouy\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_48_is_palindrome", "language": "go_test.go", "prompt": "package is_palindrome_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Checks if given string is a palindrome\n// >>> is_palindrome(\"\")\n// true\n// >>> is_palindrome(\"aba\")\n// true\n// >>> is_palindrome(\"aaaaa\")\n// true\n// >>> is_palindrome(\"zbcd\")\n// false\nfunc is_palindrome(text string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "func TestIs_Palindrome(t *testing.T) {\n  candidate := is_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: true },\n     { actual: candidate(\"aba\"), expected: true },\n     { actual: candidate(\"aaaaa\"), expected: true },\n     { actual: candidate(\"zbcd\"), expected: false },\n     { actual: candidate(\"xywyx\"), expected: true },\n     { actual: candidate(\"xywyz\"), expected: false },\n     { actual: candidate(\"xywzx\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_48_is_palindrome", "test": "func TestIs_Palindrome(t *testing.T) {\n  candidate := is_palindrome\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: true },\n     { actual: candidate(\"aba\"), expected: true },\n     { actual: candidate(\"aaaaa\"), expected: true },\n     { actual: candidate(\"zbcd\"), expected: false },\n     { actual: candidate(\"xywyx\"), expected: true },\n     { actual: candidate(\"xywyz\"), expected: false },\n     { actual: candidate(\"xywzx\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_118_get_closest_vowel", "language": "go_test.go", "prompt": "package get_closest_vowel_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a word. Your task is to find the closest vowel that stands between \n// two consonants from the right side of the word (case sensitive).\n// Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n// find any vowel met the above condition. \n// You may assume that the given string contains English letter only.\n// Example:\n// >>> get_closest_vowel(\"yogurt\")\n// \"u\"\n// >>> get_closest_vowel(\"FULL\")\n// \"U\"\n// >>> get_closest_vowel(\"quick\")\n// \"\"\n// >>> get_closest_vowel(\"ab\")\n// \"\"\nfunc get_closest_vowel(word string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "func TestGet_Closest_Vowel(t *testing.T) {\n  candidate := get_closest_vowel\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"yogurt\"), expected: \"u\" },\n     { actual: candidate(\"full\"), expected: \"u\" },\n     { actual: candidate(\"easy\"), expected: \"\" },\n     { actual: candidate(\"eAsy\"), expected: \"\" },\n     { actual: candidate(\"ali\"), expected: \"\" },\n     { actual: candidate(\"bad\"), expected: \"a\" },\n     { actual: candidate(\"most\"), expected: \"o\" },\n     { actual: candidate(\"ab\"), expected: \"\" },\n     { actual: candidate(\"ba\"), expected: \"\" },\n     { actual: candidate(\"quick\"), expected: \"\" },\n     { actual: candidate(\"anime\"), expected: \"i\" },\n     { actual: candidate(\"Asia\"), expected: \"\" },\n     { actual: candidate(\"Above\"), expected: \"o\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_118_get_closest_vowel", "test": "func TestGet_Closest_Vowel(t *testing.T) {\n  candidate := get_closest_vowel\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"yogurt\"), expected: \"u\" },\n     { actual: candidate(\"full\"), expected: \"u\" },\n     { actual: candidate(\"easy\"), expected: \"\" },\n     { actual: candidate(\"eAsy\"), expected: \"\" },\n     { actual: candidate(\"ali\"), expected: \"\" },\n     { actual: candidate(\"bad\"), expected: \"a\" },\n     { actual: candidate(\"most\"), expected: \"o\" },\n     { actual: candidate(\"ab\"), expected: \"\" },\n     { actual: candidate(\"ba\"), expected: \"\" },\n     { actual: candidate(\"quick\"), expected: \"\" },\n     { actual: candidate(\"anime\"), expected: \"i\" },\n     { actual: candidate(\"Asia\"), expected: \"\" },\n     { actual: candidate(\"Above\"), expected: \"o\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_31_is_prime", "language": "go_test.go", "prompt": "package is_prime_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return true if a given number is prime, and false otherwise.\n// >>> is_prime(6)\n// false\n// >>> is_prime(101)\n// true\n// >>> is_prime(11)\n// true\n// >>> is_prime(13441)\n// true\n// >>> is_prime(61)\n// true\n// >>> is_prime(4)\n// false\n// >>> is_prime(1)\n// false\nfunc is_prime(n int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "func TestIs_Prime(t *testing.T) {\n  candidate := is_prime\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(6), expected: false },\n     { actual: candidate(101), expected: true },\n     { actual: candidate(11), expected: true },\n     { actual: candidate(13441), expected: true },\n     { actual: candidate(61), expected: true },\n     { actual: candidate(4), expected: false },\n     { actual: candidate(1), expected: false },\n     { actual: candidate(5), expected: true },\n     { actual: candidate(11), expected: true },\n     { actual: candidate(17), expected: true },\n     { actual: candidate(85), expected: false },\n     { actual: candidate(77), expected: false },\n     { actual: candidate(255379), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_31_is_prime", "test": "func TestIs_Prime(t *testing.T) {\n  candidate := is_prime\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(6), expected: false },\n     { actual: candidate(101), expected: true },\n     { actual: candidate(11), expected: true },\n     { actual: candidate(13441), expected: true },\n     { actual: candidate(61), expected: true },\n     { actual: candidate(4), expected: false },\n     { actual: candidate(1), expected: false },\n     { actual: candidate(5), expected: true },\n     { actual: candidate(11), expected: true },\n     { actual: candidate(17), expected: true },\n     { actual: candidate(85), expected: false },\n     { actual: candidate(77), expected: false },\n     { actual: candidate(255379), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_144_simplify", "language": "go_test.go", "prompt": "package simplify_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Your task is to implement a function that will simplify the expression\n// x * n. The function returns true if x * n evaluates to a whole number and false\n// otherwise. Both x and n, are string representation of a fraction, and have the following format,\n// <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n// You can assume that x, and n are valid fractions, and do not have zero as denominator.\n// >>> simplify(\"1/5\", \"5/1\")\n// true\n// >>> simplify(\"1/6\", \"2/1\")\n// false\n// >>> simplify(\"7/10\", \"10/2\")\n// false\nfunc simplify(x string, n string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "func TestSimplify(t *testing.T) {\n  candidate := simplify\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"1/5\", \"5/1\"), expected: true },\n     { actual: candidate(\"1/6\", \"2/1\"), expected: false },\n     { actual: candidate(\"5/1\", \"3/1\"), expected: true },\n     { actual: candidate(\"7/10\", \"10/2\"), expected: false },\n     { actual: candidate(\"2/10\", \"50/10\"), expected: true },\n     { actual: candidate(\"7/2\", \"4/2\"), expected: true },\n     { actual: candidate(\"11/6\", \"6/1\"), expected: true },\n     { actual: candidate(\"2/3\", \"5/2\"), expected: false },\n     { actual: candidate(\"5/2\", \"3/5\"), expected: false },\n     { actual: candidate(\"2/4\", \"8/4\"), expected: true },\n     { actual: candidate(\"2/4\", \"4/2\"), expected: true },\n     { actual: candidate(\"1/5\", \"5/1\"), expected: true },\n     { actual: candidate(\"1/5\", \"1/5\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_144_simplify", "test": "func TestSimplify(t *testing.T) {\n  candidate := simplify\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"1/5\", \"5/1\"), expected: true },\n     { actual: candidate(\"1/6\", \"2/1\"), expected: false },\n     { actual: candidate(\"5/1\", \"3/1\"), expected: true },\n     { actual: candidate(\"7/10\", \"10/2\"), expected: false },\n     { actual: candidate(\"2/10\", \"50/10\"), expected: true },\n     { actual: candidate(\"7/2\", \"4/2\"), expected: true },\n     { actual: candidate(\"11/6\", \"6/1\"), expected: true },\n     { actual: candidate(\"2/3\", \"5/2\"), expected: false },\n     { actual: candidate(\"5/2\", \"3/5\"), expected: false },\n     { actual: candidate(\"2/4\", \"8/4\"), expected: true },\n     { actual: candidate(\"2/4\", \"4/2\"), expected: true },\n     { actual: candidate(\"1/5\", \"5/1\"), expected: true },\n     { actual: candidate(\"1/5\", \"1/5\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_78_hex_key", "language": "go_test.go", "prompt": "package hex_key_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You have been tasked to write a function that receives \n// a hexadecimal number as a string and counts the number of hexadecimal \n// digits that are primes (prime number, or a prime, is a natural number \n// greater than 1 that is not a product of two smaller natural numbers).\n// Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n// Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n// So you have to determine a number of the following digits: 2, 3, 5, 7, \n// B (=decimal 11), D (=decimal 13).\n// Note: you may assume the input is always correct or empty string, \n// and symbols A,B,C,D,E,F are always uppercase.\n// Examples:\n// >>> hex_key(\"AB\")\n// 1\n// >>> hex_key(\"1077E\")\n// 2\n// >>> hex_key(\"ABED1A33\")\n// 4\n// >>> hex_key(\"123456789ABCDEF0\")\n// 6\n// >>> hex_key(\"2020\")\n// 2\nfunc hex_key(num string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "func TestHex_Key(t *testing.T) {\n  candidate := hex_key\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"AB\"), expected: 1 },\n     { actual: candidate(\"1077E\"), expected: 2 },\n     { actual: candidate(\"ABED1A33\"), expected: 4 },\n     { actual: candidate(\"2020\"), expected: 2 },\n     { actual: candidate(\"123456789ABCDEF0\"), expected: 6 },\n     { actual: candidate(\"112233445566778899AABBCCDDEEFF00\"), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_78_hex_key", "test": "func TestHex_Key(t *testing.T) {\n  candidate := hex_key\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"AB\"), expected: 1 },\n     { actual: candidate(\"1077E\"), expected: 2 },\n     { actual: candidate(\"ABED1A33\"), expected: 4 },\n     { actual: candidate(\"2020\"), expected: 2 },\n     { actual: candidate(\"123456789ABCDEF0\"), expected: 6 },\n     { actual: candidate(\"112233445566778899AABBCCDDEEFF00\"), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_143_words_in_sentence", "language": "go_test.go", "prompt": "package words_in_sentence_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a string representing a sentence,\n// the sentence contains some words separated by a space,\n// and you have to return a string that contains the words from the original sentence,\n// whose lengths are prime numbers,\n// the order of the words in the new string should be the same as the original one.\n// Example 1:\n// >>> words_in_sentence(\"This is a test\")\n// \"is\"\n// Example 2:\n// >>> words_in_sentence(\"lets go for swimming\")\n// \"go for\"\n// Constraints:\n// * 1 <= len(sentence) <= 100\n// * sentence contains only letters\nfunc words_in_sentence(sentence string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "func TestWords_In_Sentence(t *testing.T) {\n  candidate := words_in_sentence\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"This is a test\"), expected: \"is\" },\n     { actual: candidate(\"lets go for swimming\"), expected: \"go for\" },\n     { actual: candidate(\"there is no place available here\"), expected: \"there is no place\" },\n     { actual: candidate(\"Hi I am Hussein\"), expected: \"Hi am Hussein\" },\n     { actual: candidate(\"go for it\"), expected: \"go for it\" },\n     { actual: candidate(\"here\"), expected: \"\" },\n     { actual: candidate(\"here is\"), expected: \"is\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_143_words_in_sentence", "test": "func TestWords_In_Sentence(t *testing.T) {\n  candidate := words_in_sentence\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"This is a test\"), expected: \"is\" },\n     { actual: candidate(\"lets go for swimming\"), expected: \"go for\" },\n     { actual: candidate(\"there is no place available here\"), expected: \"there is no place\" },\n     { actual: candidate(\"Hi I am Hussein\"), expected: \"Hi am Hussein\" },\n     { actual: candidate(\"go for it\"), expected: \"go for it\" },\n     { actual: candidate(\"here\"), expected: \"\" },\n     { actual: candidate(\"here is\"), expected: \"is\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_111_histogram", "language": "go_test.go", "prompt": "package histogram_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a string representing a space separated lowercase letters, return a map\n// of the letter with the most repetition and containing the corresponding count.\n// If several letters have the same occurrence, return all of them.\n// Example:\n// >>> histogram(\"a b c\")\n// map[string]int{\"a\": 1, \"b\": 1, \"c\": 1}\n// >>> histogram(\"a b b a\")\n// map[string]int{\"a\": 2, \"b\": 2}\n// >>> histogram(\"a b c a b\")\n// map[string]int{\"a\": 2, \"b\": 2}\n// >>> histogram(\"b b b b a\")\n// map[string]int{\"b\": 4}\n// >>> histogram(\"\")\n// map[string]int{}\nfunc histogram(test string) map[string]int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "func TestHistogram(t *testing.T) {\n  candidate := histogram\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"a b b a\"), expected: map[string]int{\"a\": 2, \"b\": 2} },\n     { actual: candidate(\"a b c a b\"), expected: map[string]int{\"a\": 2, \"b\": 2} },\n     { actual: candidate(\"a b c d g\"), expected: map[string]int{\"a\": 1, \"b\": 1, \"c\": 1, \"d\": 1, \"g\": 1} },\n     { actual: candidate(\"r t g\"), expected: map[string]int{\"r\": 1, \"t\": 1, \"g\": 1} },\n     { actual: candidate(\"b b b b a\"), expected: map[string]int{\"b\": 4} },\n     { actual: candidate(\"r t g\"), expected: map[string]int{\"r\": 1, \"t\": 1, \"g\": 1} },\n     { actual: candidate(\"\"), expected: map[string]int{} },\n     { actual: candidate(\"a\"), expected: map[string]int{\"a\": 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_111_histogram", "test": "func TestHistogram(t *testing.T) {\n  candidate := histogram\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"a b b a\"), expected: map[string]int{\"a\": 2, \"b\": 2} },\n     { actual: candidate(\"a b c a b\"), expected: map[string]int{\"a\": 2, \"b\": 2} },\n     { actual: candidate(\"a b c d g\"), expected: map[string]int{\"a\": 1, \"b\": 1, \"c\": 1, \"d\": 1, \"g\": 1} },\n     { actual: candidate(\"r t g\"), expected: map[string]int{\"r\": 1, \"t\": 1, \"g\": 1} },\n     { actual: candidate(\"b b b b a\"), expected: map[string]int{\"b\": 4} },\n     { actual: candidate(\"r t g\"), expected: map[string]int{\"r\": 1, \"t\": 1, \"g\": 1} },\n     { actual: candidate(\"\"), expected: map[string]int{} },\n     { actual: candidate(\"a\"), expected: map[string]int{\"a\": 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_87_get_row", "language": "go_test.go", "prompt": "package get_row_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a 2 dimensional data, as a nested lists,\n// which is similar to matrix, however, unlike matrices,\n// each row may contain a different number of columns.\n// Given lst, and integer x, find integers x in the list,\n// and return list of lists, [(x1, y1), (x2, y2) ...] such that\n// each list is a coordinate - (row, columns), starting with 0.\n// Sort coordinates initially by rows in ascending order.\n// Also, sort coordinates of the row by columns in descending order.\n// Examples:\n// >>> get_row([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 1, 6}, []int{1, 2, 3, 4, 5, 1}}, 1)\n// [][]int{[]interface{}{0, 0}, []interface{}{1, 4}, []interface{}{1, 0}, []interface{}{2, 5}, []interface{}{2, 0}}\n// >>> get_row([][]int{}, 1)\n// [][]interface{}{}\n// >>> get_row([]interface{}{[]interface{}{}, []int{1}, []int{1, 2, 3}}, 3)\n// [][]int{[]interface{}{2, 2}}\nfunc get_row(lst [][]int, x int) [][]interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "func TestGet_Row(t *testing.T) {\n  candidate := get_row\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 1, 6}, []int{1, 2, 3, 4, 5, 1}}, 1), expected: [][]int{[]interface{}{0, 0}, []interface{}{1, 4}, []interface{}{1, 0}, []interface{}{2, 5}, []interface{}{2, 0}} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}}, 2), expected: [][]int{[]interface{}{0, 1}, []interface{}{1, 1}, []interface{}{2, 1}, []interface{}{3, 1}, []interface{}{4, 1}, []interface{}{5, 1}} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 1, 3, 4, 5, 6}, []int{1, 2, 1, 4, 5, 6}, []int{1, 2, 3, 1, 5, 6}, []int{1, 2, 3, 4, 1, 6}, []int{1, 2, 3, 4, 5, 1}}, 1), expected: [][]int{[]interface{}{0, 0}, []interface{}{1, 0}, []interface{}{2, 1}, []interface{}{2, 0}, []interface{}{3, 2}, []interface{}{3, 0}, []interface{}{4, 3}, []interface{}{4, 0}, []interface{}{5, 4}, []interface{}{5, 0}, []interface{}{6, 5}, []interface{}{6, 0}} },\n     { actual: candidate([][]int{}, 1), expected: [][]interface{}{} },\n     { actual: candidate([][]int{[]int{1}}, 2), expected: [][]interface{}{} },\n     { actual: candidate([]interface{}{[]interface{}{}, []int{1}, []int{1, 2, 3}}, 3), expected: [][]int{[]interface{}{2, 2}} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_87_get_row", "test": "func TestGet_Row(t *testing.T) {\n  candidate := get_row\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 1, 6}, []int{1, 2, 3, 4, 5, 1}}, 1), expected: [][]int{[]interface{}{0, 0}, []interface{}{1, 4}, []interface{}{1, 0}, []interface{}{2, 5}, []interface{}{2, 0}} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}}, 2), expected: [][]int{[]interface{}{0, 1}, []interface{}{1, 1}, []interface{}{2, 1}, []interface{}{3, 1}, []interface{}{4, 1}, []interface{}{5, 1}} },\n     { actual: candidate([][]int{[]int{1, 2, 3, 4, 5, 6}, []int{1, 2, 3, 4, 5, 6}, []int{1, 1, 3, 4, 5, 6}, []int{1, 2, 1, 4, 5, 6}, []int{1, 2, 3, 1, 5, 6}, []int{1, 2, 3, 4, 1, 6}, []int{1, 2, 3, 4, 5, 1}}, 1), expected: [][]int{[]interface{}{0, 0}, []interface{}{1, 0}, []interface{}{2, 1}, []interface{}{2, 0}, []interface{}{3, 2}, []interface{}{3, 0}, []interface{}{4, 3}, []interface{}{4, 0}, []interface{}{5, 4}, []interface{}{5, 0}, []interface{}{6, 5}, []interface{}{6, 0}} },\n     { actual: candidate([][]int{}, 1), expected: [][]interface{}{} },\n     { actual: candidate([][]int{[]int{1}}, 2), expected: [][]interface{}{} },\n     { actual: candidate([]interface{}{[]interface{}{}, []int{1}, []int{1, 2, 3}}, 3), expected: [][]int{[]interface{}{2, 2}} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_123_get_odd_collatz", "language": "go_test.go", "prompt": "package get_odd_collatz_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n// The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n// as follows: start with any positive integer n. Then each term is obtained from the \n// previous term as follows: if the previous term is even, the next term is one half of \n// the previous term. If the previous term is odd, the next term is 3 times the previous\n// term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n// Note: \n// 1. Collatz(1) is [1].\n// 2. returned list sorted in increasing order.\n// For example:\n// get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n// >>> get_odd_collatz(5)\n// []int{1, 5}\nfunc get_odd_collatz(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "func TestGet_Odd_Collatz(t *testing.T) {\n  candidate := get_odd_collatz\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(14), expected: []int{1, 5, 7, 11, 13, 17} },\n     { actual: candidate(5), expected: []int{1, 5} },\n     { actual: candidate(12), expected: []int{1, 3, 5} },\n     { actual: candidate(1), expected: []int{1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_123_get_odd_collatz", "test": "func TestGet_Odd_Collatz(t *testing.T) {\n  candidate := get_odd_collatz\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(14), expected: []int{1, 5, 7, 11, 13, 17} },\n     { actual: candidate(5), expected: []int{1, 5} },\n     { actual: candidate(12), expected: []int{1, 3, 5} },\n     { actual: candidate(1), expected: []int{1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_135_can_arrange", "language": "go_test.go", "prompt": "package can_arrange_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function which returns the largest index of an element which\n// is not greater than or equal to the element immediately preceding it. If\n// no such element exists then return -1. The given list will not contain\n// duplicate values.\n// Examples:\n// >>> can_arrange([]int{1, 2, 4, 3, 5})\n// 3\n// >>> can_arrange([]int{1, 2, 3})\n// -1\nfunc can_arrange(arr []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "func TestCan_Arrange(t *testing.T) {\n  candidate := can_arrange\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 3, 5}), expected: 3 },\n     { actual: candidate([]int{1, 2, 4, 5}), expected: -1 },\n     { actual: candidate([]int{1, 4, 2, 5, 6, 7, 8, 9, 10}), expected: 2 },\n     { actual: candidate([]int{4, 8, 5, 7, 3}), expected: 4 },\n     { actual: candidate([]int{}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_135_can_arrange", "test": "func TestCan_Arrange(t *testing.T) {\n  candidate := can_arrange\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 3, 5}), expected: 3 },\n     { actual: candidate([]int{1, 2, 4, 5}), expected: -1 },\n     { actual: candidate([]int{1, 4, 2, 5, 6, 7, 8, 9, 10}), expected: 2 },\n     { actual: candidate([]int{4, 8, 5, 7, 3}), expected: 4 },\n     { actual: candidate([]int{}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_19_sort_numbers", "language": "go_test.go", "prompt": "package sort_numbers_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Input is a space-delimited string of numberals from 'zero' to 'nine'.\n// Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n// Return the string with numbers sorted from smallest to largest\n// >>> sort_numbers(\"three one five\")\n// \"one three five\"\nfunc sort_numbers(numbers string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "func TestSort_Numbers(t *testing.T) {\n  candidate := sort_numbers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"three\"), expected: \"three\" },\n     { actual: candidate(\"three five nine\"), expected: \"three five nine\" },\n     { actual: candidate(\"five zero four seven nine eight\"), expected: \"zero four five seven eight nine\" },\n     { actual: candidate(\"six five four three two one zero\"), expected: \"zero one two three four five six\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_19_sort_numbers", "test": "func TestSort_Numbers(t *testing.T) {\n  candidate := sort_numbers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: \"\" },\n     { actual: candidate(\"three\"), expected: \"three\" },\n     { actual: candidate(\"three five nine\"), expected: \"three five nine\" },\n     { actual: candidate(\"five zero four seven nine eight\"), expected: \"zero four five seven eight nine\" },\n     { actual: candidate(\"six five four three two one zero\"), expected: \"zero one two three four five six\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_65_circular_shift", "language": "go_test.go", "prompt": "package circular_shift_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Circular shift the digits of the integer x, shift the digits right by shift\n// and return the result as a string.\n// If shift > number of digits, return digits reversed.\n// >>> circular_shift(12, 1)\n// \"21\"\n// >>> circular_shift(12, 2)\n// \"12\"\nfunc circular_shift(x int, shift int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "func TestCircular_Shift(t *testing.T) {\n  candidate := circular_shift\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(100, 2), expected: \"001\" },\n     { actual: candidate(12, 2), expected: \"12\" },\n     { actual: candidate(97, 8), expected: \"79\" },\n     { actual: candidate(12, 1), expected: \"21\" },\n     { actual: candidate(11, 101), expected: \"11\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_65_circular_shift", "test": "func TestCircular_Shift(t *testing.T) {\n  candidate := circular_shift\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(100, 2), expected: \"001\" },\n     { actual: candidate(12, 2), expected: \"12\" },\n     { actual: candidate(97, 8), expected: \"79\" },\n     { actual: candidate(12, 1), expected: \"21\" },\n     { actual: candidate(11, 101), expected: \"11\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_142_sum_squares", "language": "go_test.go", "prompt": "package sum_squares_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// \"\n// This function will take a list of integers. For all entries in the list, the function shall square the integer entry if its index is a \n// multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n// change the entries in the list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n// Examples:\n// >>> lst\n// []int{1, 2, 3}\n// >>> lst\n// int{}\n// >>> lst\n// []int{-1, -5, 2, -1, -5}\nfunc sum_squares(lst []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "func TestSum_Squares(t *testing.T) {\n  candidate := sum_squares\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: 6 },\n     { actual: candidate([]int{1, 4, 9}), expected: 14 },\n     { actual: candidate([]int{}), expected: 0 },\n     { actual: candidate([]int{1, 1, 1, 1, 1, 1, 1, 1, 1}), expected: 9 },\n     { actual: candidate([]int{-1, -1, -1, -1, -1, -1, -1, -1, -1}), expected: -3 },\n     { actual: candidate([]int{0}), expected: 0 },\n     { actual: candidate([]int{-1, -5, 2, -1, -5}), expected: -126 },\n     { actual: candidate([]int{-56, -99, 1, 0, -2}), expected: 3030 },\n     { actual: candidate([]int{-1, 0, 0, 0, 0, 0, 0, 0, -1}), expected: 0 },\n     { actual: candidate([]int{-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37}), expected: -14196 },\n     { actual: candidate([]int{-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10}), expected: -1448 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_142_sum_squares", "test": "func TestSum_Squares(t *testing.T) {\n  candidate := sum_squares\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: 6 },\n     { actual: candidate([]int{1, 4, 9}), expected: 14 },\n     { actual: candidate([]int{}), expected: 0 },\n     { actual: candidate([]int{1, 1, 1, 1, 1, 1, 1, 1, 1}), expected: 9 },\n     { actual: candidate([]int{-1, -1, -1, -1, -1, -1, -1, -1, -1}), expected: -3 },\n     { actual: candidate([]int{0}), expected: 0 },\n     { actual: candidate([]int{-1, -5, 2, -1, -5}), expected: -126 },\n     { actual: candidate([]int{-56, -99, 1, 0, -2}), expected: 3030 },\n     { actual: candidate([]int{-1, 0, 0, 0, 0, 0, 0, 0, -1}), expected: 0 },\n     { actual: candidate([]int{-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37}), expected: -14196 },\n     { actual: candidate([]int{-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10}), expected: -1448 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_94_skjkasdkd", "language": "go_test.go", "prompt": "package skjkasdkd_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a list of integers.\n// You need to find the largest prime value and return the sum of its digits.\n// Examples:\n// >>> skjkasdkd([]int{0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3})\n// 10\n// >>> skjkasdkd([]int{1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1})\n// 25\n// >>> skjkasdkd([]int{1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3})\n// 13\n// >>> skjkasdkd([]int{0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6})\n// 11\n// >>> skjkasdkd([]int{0, 81, 12, 3, 1, 21})\n// 3\n// >>> skjkasdkd([]int{0, 8, 1, 2, 1, 7})\n// 7\nfunc skjkasdkd(lst []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "func TestSkjkasdkd(t *testing.T) {\n  candidate := skjkasdkd\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3}), expected: 10 },\n     { actual: candidate([]int{1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1}), expected: 25 },\n     { actual: candidate([]int{1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3}), expected: 13 },\n     { actual: candidate([]int{0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6}), expected: 11 },\n     { actual: candidate([]int{0, 81, 12, 3, 1, 21}), expected: 3 },\n     { actual: candidate([]int{0, 8, 1, 2, 1, 7}), expected: 7 },\n     { actual: candidate([]int{8191}), expected: 19 },\n     { actual: candidate([]int{8191, 123456, 127, 7}), expected: 19 },\n     { actual: candidate([]int{127, 97, 8192}), expected: 10 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_94_skjkasdkd", "test": "func TestSkjkasdkd(t *testing.T) {\n  candidate := skjkasdkd\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3}), expected: 10 },\n     { actual: candidate([]int{1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1}), expected: 25 },\n     { actual: candidate([]int{1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3}), expected: 13 },\n     { actual: candidate([]int{0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6}), expected: 11 },\n     { actual: candidate([]int{0, 81, 12, 3, 1, 21}), expected: 3 },\n     { actual: candidate([]int{0, 8, 1, 2, 1, 7}), expected: 7 },\n     { actual: candidate([]int{8191}), expected: 19 },\n     { actual: candidate([]int{8191, 123456, 127, 7}), expected: 19 },\n     { actual: candidate([]int{127, 97, 8192}), expected: 10 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_8_sum_product", "language": "go_test.go", "prompt": "package sum_product_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// For a given list of integers, return a list consisting of a sum and a product of all the integers in a list.\n// Empty sum should be equal to 0 and empty product should be equal to 1.\n// >>> sum_product([]int{})\n// []interface{}{0, 1}\n// >>> sum_product([]int{1, 2, 3, 4})\n// []interface{}{10, 24}\nfunc sum_product(numbers []int) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "func TestSum_Product(t *testing.T) {\n  candidate := sum_product\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []interface{}{0, 1} },\n     { actual: candidate([]int{1, 1, 1}), expected: []interface{}{3, 1} },\n     { actual: candidate([]int{100, 0}), expected: []interface{}{100, 0} },\n     { actual: candidate([]int{3, 5, 7}), expected: []interface{}{15, 105} },\n     { actual: candidate([]int{10}), expected: []interface{}{10, 10} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_8_sum_product", "test": "func TestSum_Product(t *testing.T) {\n  candidate := sum_product\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []interface{}{0, 1} },\n     { actual: candidate([]int{1, 1, 1}), expected: []interface{}{3, 1} },\n     { actual: candidate([]int{100, 0}), expected: []interface{}{100, 0} },\n     { actual: candidate([]int{3, 5, 7}), expected: []interface{}{15, 105} },\n     { actual: candidate([]int{10}), expected: []interface{}{10, 10} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_102_choose_num", "language": "go_test.go", "prompt": "package choose_num_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// This function takes two positive numbers x and y and returns the\n// biggest even integer number that is in the range [x, y] inclusive. If \n// there's no such number, then the function should return -1.\n// For example:\n// >>> choose_num(12, 15)\n// 14\n// >>> choose_num(13, 12)\n// -1\nfunc choose_num(x int, y int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "func TestChoose_Num(t *testing.T) {\n  candidate := choose_num\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(12, 15), expected: 14 },\n     { actual: candidate(13, 12), expected: -1 },\n     { actual: candidate(33, 12354), expected: 12354 },\n     { actual: candidate(5234, 5233), expected: -1 },\n     { actual: candidate(6, 29), expected: 28 },\n     { actual: candidate(27, 10), expected: -1 },\n     { actual: candidate(7, 7), expected: -1 },\n     { actual: candidate(546, 546), expected: 546 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_102_choose_num", "test": "func TestChoose_Num(t *testing.T) {\n  candidate := choose_num\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(12, 15), expected: 14 },\n     { actual: candidate(13, 12), expected: -1 },\n     { actual: candidate(33, 12354), expected: 12354 },\n     { actual: candidate(5234, 5233), expected: -1 },\n     { actual: candidate(6, 29), expected: 28 },\n     { actual: candidate(27, 10), expected: -1 },\n     { actual: candidate(7, 7), expected: -1 },\n     { actual: candidate(546, 546), expected: 546 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "go_test.go", "prompt": "package largest_smallest_integers_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function that returns a list (a, b), where 'a' is\n// the largest of negative integers, and 'b' is the smallest\n// of positive integers in a list.\n// If there is no negative or positive integers, return them as nil.\n// Examples:\n// >>> largest_smallest_integers([]int{2, 4, 1, 3, 5, 7})\n// []interface{}{nil, 1}\n// >>> largest_smallest_integers([]int{})\n// []interface{}{nil, nil}\n// >>> largest_smallest_integers([]int{0})\n// []interface{}{nil, nil}\nfunc largest_smallest_integers(lst []int) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "func TestLargest_Smallest_Integers(t *testing.T) {\n  candidate := largest_smallest_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 4, 1, 3, 5, 7}), expected: []interface{}{nil, 1} },\n     { actual: candidate([]int{2, 4, 1, 3, 5, 7, 0}), expected: []interface{}{nil, 1} },\n     { actual: candidate([]int{1, 3, 2, 4, 5, 6, -2}), expected: []interface{}{-2, 1} },\n     { actual: candidate([]int{4, 5, 3, 6, 2, 7, -7}), expected: []interface{}{-7, 2} },\n     { actual: candidate([]int{7, 3, 8, 4, 9, 2, 5, -9}), expected: []interface{}{-9, 2} },\n     { actual: candidate([]int{}), expected: []interface{}{nil, nil} },\n     { actual: candidate([]int{0}), expected: []interface{}{nil, nil} },\n     { actual: candidate([]int{-1, -3, -5, -6}), expected: []interface{}{-1, nil} },\n     { actual: candidate([]int{-1, -3, -5, -6, 0}), expected: []interface{}{-1, nil} },\n     { actual: candidate([]int{-6, -4, -4, -3, 1}), expected: []interface{}{-3, 1} },\n     { actual: candidate([]int{-6, -4, -4, -3, -100, 1}), expected: []interface{}{-3, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_136_largest_smallest_integers", "test": "func TestLargest_Smallest_Integers(t *testing.T) {\n  candidate := largest_smallest_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 4, 1, 3, 5, 7}), expected: []interface{}{nil, 1} },\n     { actual: candidate([]int{2, 4, 1, 3, 5, 7, 0}), expected: []interface{}{nil, 1} },\n     { actual: candidate([]int{1, 3, 2, 4, 5, 6, -2}), expected: []interface{}{-2, 1} },\n     { actual: candidate([]int{4, 5, 3, 6, 2, 7, -7}), expected: []interface{}{-7, 2} },\n     { actual: candidate([]int{7, 3, 8, 4, 9, 2, 5, -9}), expected: []interface{}{-9, 2} },\n     { actual: candidate([]int{}), expected: []interface{}{nil, nil} },\n     { actual: candidate([]int{0}), expected: []interface{}{nil, nil} },\n     { actual: candidate([]int{-1, -3, -5, -6}), expected: []interface{}{-1, nil} },\n     { actual: candidate([]int{-1, -3, -5, -6, 0}), expected: []interface{}{-1, nil} },\n     { actual: candidate([]int{-6, -4, -4, -3, 1}), expected: []interface{}{-3, 1} },\n     { actual: candidate([]int{-6, -4, -4, -3, -100, 1}), expected: []interface{}{-3, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_16_count_distinct_characters", "language": "go_test.go", "prompt": "package count_distinct_characters_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a string, find out how many distinct characters (regardless of case) does it consist of\n// >>> count_distinct_characters(\"xyzXYZ\")\n// 3\n// >>> count_distinct_characters(\"Jerry\")\n// 4\nfunc count_distinct_characters(myString string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "func TestCount_Distinct_Characters(t *testing.T) {\n  candidate := count_distinct_characters\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"abcde\"), expected: 5 },\n     { actual: candidate(\"abcdecadeCADE\"), expected: 5 },\n     { actual: candidate(\"aaaaAAAAaaaa\"), expected: 1 },\n     { actual: candidate(\"Jerry jERRY JeRRRY\"), expected: 5 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_16_count_distinct_characters", "test": "func TestCount_Distinct_Characters(t *testing.T) {\n  candidate := count_distinct_characters\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"\"), expected: 0 },\n     { actual: candidate(\"abcde\"), expected: 5 },\n     { actual: candidate(\"abcdecadeCADE\"), expected: 5 },\n     { actual: candidate(\"aaaaAAAAaaaa\"), expected: 1 },\n     { actual: candidate(\"Jerry jERRY JeRRRY\"), expected: 5 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_100_make_a_pile", "language": "go_test.go", "prompt": "package make_a_pile_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer n, you have to make a pile of n levels of stones.\n// The first level has n stones.\n// The number of stones in the next level is:\n// - the next odd number if n is odd.\n// - the next even number if n is even.\n// Return the number of stones in each level in a list, where element at index\n// i represents the number of stones in the level (i+1).\n// Examples:\n// >>> make_a_pile(3)\n// []int{3, 5, 7}\nfunc make_a_pile(n int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "func TestMake_A_Pile(t *testing.T) {\n  candidate := make_a_pile\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: []int{3, 5, 7} },\n     { actual: candidate(4), expected: []int{4, 6, 8, 10} },\n     { actual: candidate(5), expected: []int{5, 7, 9, 11, 13} },\n     { actual: candidate(6), expected: []int{6, 8, 10, 12, 14, 16} },\n     { actual: candidate(8), expected: []int{8, 10, 12, 14, 16, 18, 20, 22} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_100_make_a_pile", "test": "func TestMake_A_Pile(t *testing.T) {\n  candidate := make_a_pile\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3), expected: []int{3, 5, 7} },\n     { actual: candidate(4), expected: []int{4, 6, 8, 10} },\n     { actual: candidate(5), expected: []int{5, 7, 9, 11, 13} },\n     { actual: candidate(6), expected: []int{6, 8, 10, 12, 14, 16} },\n     { actual: candidate(8), expected: []int{8, 10, 12, 14, 16, 18, 20, 22} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_114_minSubArraySum", "language": "go_test.go", "prompt": "package minSubArraySum_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a list of integers nums, find the minimum sum of any non-empty sub-list\n// of nums.\n// Example\n// >>> minSubArraySum([]int{2, 3, 4, 1, 2, 4})\n// 1\n// >>> minSubArraySum([]int{-1, -2, -3})\n// -6\nfunc minSubArraySum(nums []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "func TestMinsubarraysum(t *testing.T) {\n  candidate := minSubArraySum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 3, 4, 1, 2, 4}), expected: 1 },\n     { actual: candidate([]int{-1, -2, -3}), expected: -6 },\n     { actual: candidate([]int{-1, -2, -3, 2, -10}), expected: -14 },\n     { actual: candidate([]int{-9999999999999999}), expected: -9999999999999999 },\n     { actual: candidate([]int{0, 10, 20, 1000000}), expected: 0 },\n     { actual: candidate([]int{-1, -2, -3, 10, -5}), expected: -6 },\n     { actual: candidate([]int{100, -1, -2, -3, 10, -5}), expected: -6 },\n     { actual: candidate([]int{10, 11, 13, 8, 3, 4}), expected: 3 },\n     { actual: candidate([]int{100, -33, 32, -1, 0, -2}), expected: -33 },\n     { actual: candidate([]int{-10}), expected: -10 },\n     { actual: candidate([]int{7}), expected: 7 },\n     { actual: candidate([]int{1, -1}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_114_minSubArraySum", "test": "func TestMinsubarraysum(t *testing.T) {\n  candidate := minSubArraySum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{2, 3, 4, 1, 2, 4}), expected: 1 },\n     { actual: candidate([]int{-1, -2, -3}), expected: -6 },\n     { actual: candidate([]int{-1, -2, -3, 2, -10}), expected: -14 },\n     { actual: candidate([]int{-9999999999999999}), expected: -9999999999999999 },\n     { actual: candidate([]int{0, 10, 20, 1000000}), expected: 0 },\n     { actual: candidate([]int{-1, -2, -3, 10, -5}), expected: -6 },\n     { actual: candidate([]int{100, -1, -2, -3, 10, -5}), expected: -6 },\n     { actual: candidate([]int{10, 11, 13, 8, 3, 4}), expected: 3 },\n     { actual: candidate([]int{100, -33, 32, -1, 0, -2}), expected: -33 },\n     { actual: candidate([]int{-10}), expected: -10 },\n     { actual: candidate([]int{7}), expected: 7 },\n     { actual: candidate([]int{1, -1}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_15_string_sequence", "language": "go_test.go", "prompt": "package string_sequence_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n// >>> string_sequence(0)\n// \"0\"\n// >>> string_sequence(5)\n// \"0 1 2 3 4 5\"\nfunc string_sequence(n int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "func TestString_Sequence(t *testing.T) {\n  candidate := string_sequence\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0), expected: \"0\" },\n     { actual: candidate(3), expected: \"0 1 2 3\" },\n     { actual: candidate(10), expected: \"0 1 2 3 4 5 6 7 8 9 10\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_15_string_sequence", "test": "func TestString_Sequence(t *testing.T) {\n  candidate := string_sequence\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(0), expected: \"0\" },\n     { actual: candidate(3), expected: \"0 1 2 3\" },\n     { actual: candidate(10), expected: \"0 1 2 3 4 5 6 7 8 9 10\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_154_cycpattern_check", "language": "go_test.go", "prompt": "package cycpattern_check_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n// >>> cycpattern_check(\"abcd\", \"abd\")\n// false\n// >>> cycpattern_check(\"hello\", \"ell\")\n// true\n// >>> cycpattern_check(\"whassup\", \"psus\")\n// false\n// >>> cycpattern_check(\"abab\", \"baa\")\n// true\n// >>> cycpattern_check(\"efef\", \"eeff\")\n// false\n// >>> cycpattern_check(\"himenss\", \"simen\")\n// true\nfunc cycpattern_check(a string, b string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "func TestCycpattern_Check(t *testing.T) {\n  candidate := cycpattern_check\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"xyzw\", \"xyw\"), expected: false },\n     { actual: candidate(\"yello\", \"ell\"), expected: true },\n     { actual: candidate(\"whattup\", \"ptut\"), expected: false },\n     { actual: candidate(\"efef\", \"fee\"), expected: true },\n     { actual: candidate(\"abab\", \"aabb\"), expected: false },\n     { actual: candidate(\"winemtt\", \"tinem\"), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_154_cycpattern_check", "test": "func TestCycpattern_Check(t *testing.T) {\n  candidate := cycpattern_check\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"xyzw\", \"xyw\"), expected: false },\n     { actual: candidate(\"yello\", \"ell\"), expected: true },\n     { actual: candidate(\"whattup\", \"ptut\"), expected: false },\n     { actual: candidate(\"efef\", \"fee\"), expected: true },\n     { actual: candidate(\"abab\", \"aabb\"), expected: false },\n     { actual: candidate(\"winemtt\", \"tinem\"), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_57_monotonic", "language": "go_test.go", "prompt": "package monotonic_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return true is list elements are monotonically increasing or decreasing.\n// >>> monotonic([]int{1, 2, 4, 20})\n// true\n// >>> monotonic([]int{1, 20, 4, 10})\n// false\n// >>> monotonic([]int{4, 1, 0, -10})\n// true\nfunc monotonic(l []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "func TestMonotonic(t *testing.T) {\n  candidate := monotonic\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 10}), expected: true },\n     { actual: candidate([]int{1, 2, 4, 20}), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}), expected: false },\n     { actual: candidate([]int{4, 1, 0, -10}), expected: true },\n     { actual: candidate([]int{4, 1, 1, 0}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 2, 5, 60}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 60}), expected: true },\n     { actual: candidate([]int{9, 9, 9, 9}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_57_monotonic", "test": "func TestMonotonic(t *testing.T) {\n  candidate := monotonic\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 10}), expected: true },\n     { actual: candidate([]int{1, 2, 4, 20}), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}), expected: false },\n     { actual: candidate([]int{4, 1, 0, -10}), expected: true },\n     { actual: candidate([]int{4, 1, 1, 0}), expected: true },\n     { actual: candidate([]int{1, 2, 3, 2, 5, 60}), expected: false },\n     { actual: candidate([]int{1, 2, 3, 4, 5, 60}), expected: true },\n     { actual: candidate([]int{9, 9, 9, 9}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_52_below_threshold", "language": "go_test.go", "prompt": "package below_threshold_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return true if all numbers in the list l are below threshold t.\n// >>> below_threshold([]int{1, 2, 4, 10}, 100)\n// true\n// >>> below_threshold([]int{1, 20, 4, 10}, 5)\n// false\nfunc below_threshold(l []int, t int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "func TestBelow_Threshold(t *testing.T) {\n  candidate := below_threshold\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 10}, 100), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}, 5), expected: false },\n     { actual: candidate([]int{1, 20, 4, 10}, 21), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}, 22), expected: true },\n     { actual: candidate([]int{1, 8, 4, 10}, 11), expected: true },\n     { actual: candidate([]int{1, 8, 4, 10}, 10), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_52_below_threshold", "test": "func TestBelow_Threshold(t *testing.T) {\n  candidate := below_threshold\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 4, 10}, 100), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}, 5), expected: false },\n     { actual: candidate([]int{1, 20, 4, 10}, 21), expected: true },\n     { actual: candidate([]int{1, 20, 4, 10}, 22), expected: true },\n     { actual: candidate([]int{1, 8, 4, 10}, 11), expected: true },\n     { actual: candidate([]int{1, 8, 4, 10}, 10), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_75_is_multiply_prime", "language": "go_test.go", "prompt": "package is_multiply_prime_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that returns true if the given number is the multiplication of 3 prime numbers\n// and false otherwise.\n// Knowing that (a) is less then 100. \n// Example:\n// >>> is_multiply_prime(30)\n// true\n// 30 = 2 * 3 * 5\nfunc is_multiply_prime(a int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "func TestIs_Multiply_Prime(t *testing.T) {\n  candidate := is_multiply_prime\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: false },\n     { actual: candidate(30), expected: true },\n     { actual: candidate(8), expected: true },\n     { actual: candidate(10), expected: false },\n     { actual: candidate(125), expected: true },\n     { actual: candidate(105), expected: true },\n     { actual: candidate(126), expected: false },\n     { actual: candidate(729), expected: false },\n     { actual: candidate(891), expected: false },\n     { actual: candidate(1001), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_75_is_multiply_prime", "test": "func TestIs_Multiply_Prime(t *testing.T) {\n  candidate := is_multiply_prime\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5), expected: false },\n     { actual: candidate(30), expected: true },\n     { actual: candidate(8), expected: true },\n     { actual: candidate(10), expected: false },\n     { actual: candidate(125), expected: true },\n     { actual: candidate(105), expected: true },\n     { actual: candidate(126), expected: false },\n     { actual: candidate(729), expected: false },\n     { actual: candidate(891), expected: false },\n     { actual: candidate(1001), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_30_get_positive", "language": "go_test.go", "prompt": "package get_positive_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return only positive numbers in the list.\n// >>> get_positive([]int{-1, 2, -4, 5, 6})\n// []int{2, 5, 6}\n// >>> get_positive([]int{5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10})\n// []int{5, 3, 2, 3, 9, 123, 1}\nfunc get_positive(l []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "func TestGet_Positive(t *testing.T) {\n  candidate := get_positive\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{-1, -2, 4, 5, 6}), expected: []int{4, 5, 6} },\n     { actual: candidate([]int{5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10}), expected: []int{5, 3, 2, 3, 3, 9, 123, 1} },\n     { actual: candidate([]int{-1, -2}), expected: []int{} },\n     { actual: candidate([]int{}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_30_get_positive", "test": "func TestGet_Positive(t *testing.T) {\n  candidate := get_positive\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{-1, -2, 4, 5, 6}), expected: []int{4, 5, 6} },\n     { actual: candidate([]int{5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10}), expected: []int{5, 3, 2, 3, 3, 9, 123, 1} },\n     { actual: candidate([]int{-1, -2}), expected: []int{} },\n     { actual: candidate([]int{}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_33_sort_third", "language": "go_test.go", "prompt": "package sort_third_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// This function takes a list l and returns a list l' such that\n// l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n// to the values of the corresponding indicies of l, but sorted.\n// >>> sort_third([]int{1, 2, 3})\n// []int{1, 2, 3}\n// >>> sort_third([]int{5, 6, 3, 4, 8, 9, 2})\n// []int{2, 6, 3, 4, 8, 9, 5}\nfunc sort_third(l []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "func TestSort_Third(t *testing.T) {\n  candidate := sort_third\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 6, 3, 4, 8, 9, 2}), expected: []int{2, 6, 3, 4, 8, 9, 5} },\n     { actual: candidate([]int{5, 8, 3, 4, 6, 9, 2}), expected: []int{2, 8, 3, 4, 6, 9, 5} },\n     { actual: candidate([]int{5, 6, 9, 4, 8, 3, 2}), expected: []int{2, 6, 9, 4, 8, 3, 5} },\n     { actual: candidate([]int{5, 6, 3, 4, 8, 9, 2, 1}), expected: []int{2, 6, 3, 4, 8, 9, 5, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_33_sort_third", "test": "func TestSort_Third(t *testing.T) {\n  candidate := sort_third\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 6, 3, 4, 8, 9, 2}), expected: []int{2, 6, 3, 4, 8, 9, 5} },\n     { actual: candidate([]int{5, 8, 3, 4, 6, 9, 2}), expected: []int{2, 8, 3, 4, 6, 9, 5} },\n     { actual: candidate([]int{5, 6, 9, 4, 8, 3, 2}), expected: []int{2, 6, 9, 4, 8, 3, 5} },\n     { actual: candidate([]int{5, 6, 3, 4, 8, 9, 2, 1}), expected: []int{2, 6, 3, 4, 8, 9, 5, 1} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_6_parse_nested_parens", "language": "go_test.go", "prompt": "package parse_nested_parens_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n// For each of the group, output the deepest level of nesting of parentheses.\n// E.g. (()()) has maximum two levels of nesting while ((())) has three.\n// >>> parse_nested_parens(\"(()()) ((())) () ((())()())\")\n// []int{2, 3, 1, 3}\nfunc parse_nested_parens(paren_string string) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "func TestParse_Nested_Parens(t *testing.T) {\n  candidate := parse_nested_parens\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"(()()) ((())) () ((())()())\"), expected: []int{2, 3, 1, 3} },\n     { actual: candidate(\"() (()) ((())) (((())))\"), expected: []int{1, 2, 3, 4} },\n     { actual: candidate(\"(()(())((())))\"), expected: []int{4} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_6_parse_nested_parens", "test": "func TestParse_Nested_Parens(t *testing.T) {\n  candidate := parse_nested_parens\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"(()()) ((())) () ((())()())\"), expected: []int{2, 3, 1, 3} },\n     { actual: candidate(\"() (()) ((())) (((())))\"), expected: []int{1, 2, 3, 4} },\n     { actual: candidate(\"(()(())((())))\"), expected: []int{4} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_45_triangle_area", "language": "go_test.go", "prompt": "package triangle_area_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given length of a side and high return area for a triangle.\n// >>> triangle_area(5, 3)\n// 7.5\nfunc triangle_area(a int, h int) float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "func TestTriangle_Area(t *testing.T) {\n  candidate := triangle_area\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5, 3), expected: 7.5 },\n     { actual: candidate(2, 2), expected: 2.0 },\n     { actual: candidate(10, 8), expected: 40.0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_45_triangle_area", "test": "func TestTriangle_Area(t *testing.T) {\n  candidate := triangle_area\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(5, 3), expected: 7.5 },\n     { actual: candidate(2, 2), expected: 2.0 },\n     { actual: candidate(10, 8), expected: 40.0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_97_multiply", "language": "go_test.go", "prompt": "package multiply_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Complete the function that takes two integers and returns \n// the product of their unit digits.\n// Assume the input is always valid.\n// Examples:\n// >>> multiply(148, 412)\n// 16\n// >>> multiply(19, 28)\n// 72\n// >>> multiply(2020, 1851)\n// 0\n// >>> multiply(14, -15)\n// 20\nfunc multiply(a int, b int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "func TestMultiply(t *testing.T) {\n  candidate := multiply\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(148, 412), expected: 16 },\n     { actual: candidate(19, 28), expected: 72 },\n     { actual: candidate(2020, 1851), expected: 0 },\n     { actual: candidate(14, -15), expected: 20 },\n     { actual: candidate(76, 67), expected: 42 },\n     { actual: candidate(17, 27), expected: 49 },\n     { actual: candidate(0, 1), expected: 0 },\n     { actual: candidate(0, 0), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_97_multiply", "test": "func TestMultiply(t *testing.T) {\n  candidate := multiply\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(148, 412), expected: 16 },\n     { actual: candidate(19, 28), expected: 72 },\n     { actual: candidate(2020, 1851), expected: 0 },\n     { actual: candidate(14, -15), expected: 20 },\n     { actual: candidate(76, 67), expected: 42 },\n     { actual: candidate(17, 27), expected: 49 },\n     { actual: candidate(0, 1), expected: 0 },\n     { actual: candidate(0, 0), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "go_test.go", "prompt": "package mean_absolute_deviation_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// For a given list of input numbers, calculate Mean Absolute Deviation\n// around the mean of this dataset.\n// Mean Absolute Deviation is the average absolute difference between each\n// element and a centerpoint (mean in this case):\n// MAD = average | x - x_mean |\n// >>> mean_absolute_deviation([]float64{1.0, 2.0, 3.0, 4.0})\n// 1.0\nfunc mean_absolute_deviation(numbers []float64) float64 {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "func TestMean_Absolute_Deviation(t *testing.T) {\n  candidate := mean_absolute_deviation\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0}), expected: 0.5 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0}), expected: 1.0 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0}), expected: 1.2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "func TestMean_Absolute_Deviation(t *testing.T) {\n  candidate := mean_absolute_deviation\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{1.0, 2.0}), expected: 0.5 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0}), expected: 1.0 },\n     { actual: candidate([]float64{1.0, 2.0, 3.0, 4.0, 5.0}), expected: 1.2 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_58_common", "language": "go_test.go", "prompt": "package common_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return sorted unique common elements for two lists.\n// >>> common([]int{1, 4, 3, 34, 653, 2, 5}, []int{5, 7, 1, 5, 9, 653, 121})\n// []int{1, 5, 653}\n// >>> common([]int{5, 3, 2, 8}, []int{3, 2})\n// []int{2, 3}\nfunc common(l1 []int, l2 []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "func TestCommon(t *testing.T) {\n  candidate := common\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 4, 3, 34, 653, 2, 5}, []int{5, 7, 1, 5, 9, 653, 121}), expected: []int{1, 5, 653} },\n     { actual: candidate([]int{5, 3, 2, 8}, []int{3, 2}), expected: []int{2, 3} },\n     { actual: candidate([]int{4, 3, 2, 8}, []int{3, 2, 4}), expected: []int{2, 3, 4} },\n     { actual: candidate([]int{4, 3, 2, 8}, []int{}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_58_common", "test": "func TestCommon(t *testing.T) {\n  candidate := common\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 4, 3, 34, 653, 2, 5}, []int{5, 7, 1, 5, 9, 653, 121}), expected: []int{1, 5, 653} },\n     { actual: candidate([]int{5, 3, 2, 8}, []int{3, 2}), expected: []int{2, 3} },\n     { actual: candidate([]int{4, 3, 2, 8}, []int{3, 2, 4}), expected: []int{2, 3, 4} },\n     { actual: candidate([]int{4, 3, 2, 8}, []int{}), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "go_test.go", "prompt": "package int_to_mini_roman_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given a positive integer, obtain its roman numeral equivalent as a string,\n// and return it in lowercase.\n// Restrictions: 1 <= num <= 1000\n// Examples:\n// >>> int_to_mini_roman(19)\n// \"xix\"\n// >>> int_to_mini_roman(152)\n// \"clii\"\n// >>> int_to_mini_roman(426)\n// \"cdxxvi\"\nfunc int_to_mini_roman(number int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "func TestInt_To_Mini_Roman(t *testing.T) {\n  candidate := int_to_mini_roman\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(19), expected: \"xix\" },\n     { actual: candidate(152), expected: \"clii\" },\n     { actual: candidate(251), expected: \"ccli\" },\n     { actual: candidate(426), expected: \"cdxxvi\" },\n     { actual: candidate(500), expected: \"d\" },\n     { actual: candidate(1), expected: \"i\" },\n     { actual: candidate(4), expected: \"iv\" },\n     { actual: candidate(43), expected: \"xliii\" },\n     { actual: candidate(90), expected: \"xc\" },\n     { actual: candidate(94), expected: \"xciv\" },\n     { actual: candidate(532), expected: \"dxxxii\" },\n     { actual: candidate(900), expected: \"cm\" },\n     { actual: candidate(994), expected: \"cmxciv\" },\n     { actual: candidate(1000), expected: \"m\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_156_int_to_mini_roman", "test": "func TestInt_To_Mini_Roman(t *testing.T) {\n  candidate := int_to_mini_roman\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(19), expected: \"xix\" },\n     { actual: candidate(152), expected: \"clii\" },\n     { actual: candidate(251), expected: \"ccli\" },\n     { actual: candidate(426), expected: \"cdxxvi\" },\n     { actual: candidate(500), expected: \"d\" },\n     { actual: candidate(1), expected: \"i\" },\n     { actual: candidate(4), expected: \"iv\" },\n     { actual: candidate(43), expected: \"xliii\" },\n     { actual: candidate(90), expected: \"xc\" },\n     { actual: candidate(94), expected: \"xciv\" },\n     { actual: candidate(532), expected: \"dxxxii\" },\n     { actual: candidate(900), expected: \"cm\" },\n     { actual: candidate(994), expected: \"cmxciv\" },\n     { actual: candidate(1000), expected: \"m\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_67_fruit_distribution", "language": "go_test.go", "prompt": "package fruit_distribution_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// In this task, you will be given a string that represents a number of apples and oranges \n// that are distributed in a basket of fruit this basket contains \n// apples, oranges, and mango fruits. Given the string that represents the total number of \n// the oranges and apples and an integer that represent the total number of the fruits \n// in the basket return the number of the mango fruits in the basket.\n// for examble:\n// >>> fruit_distribution(\"5 apples and 6 oranges\", 19)\n// 8\n// >>> fruit_distribution(\"0 apples and 1 oranges\", 3)\n// 2\n// >>> fruit_distribution(\"2 apples and 3 oranges\", 100)\n// 95\n// >>> fruit_distribution(\"100 apples and 1 oranges\", 120)\n// 19\nfunc fruit_distribution(s string, n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "func TestFruit_Distribution(t *testing.T) {\n  candidate := fruit_distribution\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"5 apples and 6 oranges\", 19), expected: 8 },\n     { actual: candidate(\"5 apples and 6 oranges\", 21), expected: 10 },\n     { actual: candidate(\"0 apples and 1 oranges\", 3), expected: 2 },\n     { actual: candidate(\"1 apples and 0 oranges\", 3), expected: 2 },\n     { actual: candidate(\"2 apples and 3 oranges\", 100), expected: 95 },\n     { actual: candidate(\"2 apples and 3 oranges\", 5), expected: 0 },\n     { actual: candidate(\"1 apples and 100 oranges\", 120), expected: 19 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_67_fruit_distribution", "test": "func TestFruit_Distribution(t *testing.T) {\n  candidate := fruit_distribution\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"5 apples and 6 oranges\", 19), expected: 8 },\n     { actual: candidate(\"5 apples and 6 oranges\", 21), expected: 10 },\n     { actual: candidate(\"0 apples and 1 oranges\", 3), expected: 2 },\n     { actual: candidate(\"1 apples and 0 oranges\", 3), expected: 2 },\n     { actual: candidate(\"2 apples and 3 oranges\", 100), expected: 95 },\n     { actual: candidate(\"2 apples and 3 oranges\", 5), expected: 0 },\n     { actual: candidate(\"1 apples and 100 oranges\", 120), expected: 19 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_112_reverse_delete", "language": "go_test.go", "prompt": "package reverse_delete_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Task\n// We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n// then check if the result string is palindrome.\n// A string is called palindrome if it reads the same backward as forward.\n// You should return a list containing the result string and true/false for the check.\n// Example\n// >>> reverse_delete(\"abcde\", \"ae\")\n// []interface{}{\"bcd\", false}\n// >>> reverse_delete(\"abcdef\", \"b\")\n// []interface{}{\"acdef\", false}\n// >>> reverse_delete(\"abcdedcba\", \"ab\")\n// []interface{}{\"cdedc\", true}\nfunc reverse_delete(s string, c string) []interface{} {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "func TestReverse_Delete(t *testing.T) {\n  candidate := reverse_delete\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"abcde\", \"ae\"), expected: []interface{}{\"bcd\", false} },\n     { actual: candidate(\"abcdef\", \"b\"), expected: []interface{}{\"acdef\", false} },\n     { actual: candidate(\"abcdedcba\", \"ab\"), expected: []interface{}{\"cdedc\", true} },\n     { actual: candidate(\"dwik\", \"w\"), expected: []interface{}{\"dik\", false} },\n     { actual: candidate(\"a\", \"a\"), expected: []interface{}{\"\", true} },\n     { actual: candidate(\"abcdedcba\", \"\"), expected: []interface{}{\"abcdedcba\", true} },\n     { actual: candidate(\"abcdedcba\", \"v\"), expected: []interface{}{\"abcdedcba\", true} },\n     { actual: candidate(\"vabba\", \"v\"), expected: []interface{}{\"abba\", true} },\n     { actual: candidate(\"mamma\", \"mia\"), expected: []interface{}{\"\", true} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_112_reverse_delete", "test": "func TestReverse_Delete(t *testing.T) {\n  candidate := reverse_delete\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"abcde\", \"ae\"), expected: []interface{}{\"bcd\", false} },\n     { actual: candidate(\"abcdef\", \"b\"), expected: []interface{}{\"acdef\", false} },\n     { actual: candidate(\"abcdedcba\", \"ab\"), expected: []interface{}{\"cdedc\", true} },\n     { actual: candidate(\"dwik\", \"w\"), expected: []interface{}{\"dik\", false} },\n     { actual: candidate(\"a\", \"a\"), expected: []interface{}{\"\", true} },\n     { actual: candidate(\"abcdedcba\", \"\"), expected: []interface{}{\"abcdedcba\", true} },\n     { actual: candidate(\"abcdedcba\", \"v\"), expected: []interface{}{\"abcdedcba\", true} },\n     { actual: candidate(\"vabba\", \"v\"), expected: []interface{}{\"abba\", true} },\n     { actual: candidate(\"mamma\", \"mia\"), expected: []interface{}{\"\", true} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "go_test.go", "prompt": "package greatest_common_divisor_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Return a greatest common divisor of two integers a and b\n// >>> greatest_common_divisor(3, 5)\n// 1\n// >>> greatest_common_divisor(25, 15)\n// 5\nfunc greatest_common_divisor(a int, b int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "func TestGreatest_Common_Divisor(t *testing.T) {\n  candidate := greatest_common_divisor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 7), expected: 1 },\n     { actual: candidate(10, 15), expected: 5 },\n     { actual: candidate(49, 14), expected: 7 },\n     { actual: candidate(144, 60), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_13_greatest_common_divisor", "test": "func TestGreatest_Common_Divisor(t *testing.T) {\n  candidate := greatest_common_divisor\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 7), expected: 1 },\n     { actual: candidate(10, 15), expected: 5 },\n     { actual: candidate(49, 14), expected: 7 },\n     { actual: candidate(144, 60), expected: 12 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_116_sort_array", "language": "go_test.go", "prompt": "package sort_array_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// In this Kata, you have to sort a list of non-negative integers according to\n// number of ones in their binary representation in ascending order.\n// For similar number of ones, sort based on decimal value.\n// It must be implemented like this:\n// >>> sort_array([]int{1, 5, 2, 3, 4})\n// []int{1, 2, 3, 4, 5}\n// >>> sort_array([]int{-2, -3, -4, -5, -6})\n// []int{-6, -5, -4, -3, -2}\n// >>> sort_array([]int{1, 0, 2, 3, 4})\n// []int{0, 1, 2, 3, 4}\nfunc sort_array(arr []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "func TestSort_Array(t *testing.T) {\n  candidate := sort_array\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 5, 2, 3, 4}), expected: []int{1, 2, 4, 3, 5} },\n     { actual: candidate([]int{-2, -3, -4, -5, -6}), expected: []int{-4, -2, -6, -5, -3} },\n     { actual: candidate([]int{1, 0, 2, 3, 4}), expected: []int{0, 1, 2, 4, 3} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4}), expected: []int{2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77} },\n     { actual: candidate([]int{3, 6, 44, 12, 32, 5}), expected: []int{32, 3, 5, 6, 12, 44} },\n     { actual: candidate([]int{2, 4, 8, 16, 32}), expected: []int{2, 4, 8, 16, 32} },\n     { actual: candidate([]int{2, 4, 8, 16, 32}), expected: []int{2, 4, 8, 16, 32} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_116_sort_array", "test": "func TestSort_Array(t *testing.T) {\n  candidate := sort_array\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 5, 2, 3, 4}), expected: []int{1, 2, 4, 3, 5} },\n     { actual: candidate([]int{-2, -3, -4, -5, -6}), expected: []int{-4, -2, -6, -5, -3} },\n     { actual: candidate([]int{1, 0, 2, 3, 4}), expected: []int{0, 1, 2, 4, 3} },\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4}), expected: []int{2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77} },\n     { actual: candidate([]int{3, 6, 44, 12, 32, 5}), expected: []int{32, 3, 5, 6, 12, 44} },\n     { actual: candidate([]int{2, 4, 8, 16, 32}), expected: []int{2, 4, 8, 16, 32} },\n     { actual: candidate([]int{2, 4, 8, 16, 32}), expected: []int{2, 4, 8, 16, 32} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_28_concatenate", "language": "go_test.go", "prompt": "package concatenate_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Concatenate list of strings into a single string\n// >>> concatenate([]string{})\n// \"\"\n// >>> concatenate([]string{\"a\", \"b\", \"c\"})\n// \"abc\"\nfunc concatenate(strings []string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "func TestConcatenate(t *testing.T) {\n  candidate := concatenate\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}), expected: \"\" },\n     { actual: candidate([]string{\"x\", \"y\", \"z\"}), expected: \"xyz\" },\n     { actual: candidate([]string{\"x\", \"y\", \"z\", \"w\", \"k\"}), expected: \"xyzwk\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_28_concatenate", "test": "func TestConcatenate(t *testing.T) {\n  candidate := concatenate\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}), expected: \"\" },\n     { actual: candidate([]string{\"x\", \"y\", \"z\"}), expected: \"xyz\" },\n     { actual: candidate([]string{\"x\", \"y\", \"z\", \"w\", \"k\"}), expected: \"xyzwk\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_149_sorted_list_sum", "language": "go_test.go", "prompt": "package sorted_list_sum_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that accepts a list of strings as a parameter,\n// deletes the strings that have odd lengths from it,\n// and returns the resulted list with a sorted order,\n// The list is always a list of strings and never a list of numbers,\n// and it may contain duplicates.\n// The order of the list should be ascending by length of each word, and you\n// should return the list sorted by that rule.\n// If two words have the same length, sort the list alphabetically.\n// The function should return a list of strings in sorted order.\n// You may assume that all words will have the same length.\n// For example:\n// >>> list_sort([]string{\"aa\", \"a\", \"aaa\"})\n// []string{\"aa\"}\n// >>> list_sort([]string{\"ab\", \"a\", \"aaa\", \"cd\"})\n// []string{\"ab\", \"cd\"}\nfunc sorted_list_sum(lst []string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "func TestSorted_List_Sum(t *testing.T) {\n  candidate := sorted_list_sum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"aa\", \"a\", \"aaa\"}), expected: []string{\"aa\"} },\n     { actual: candidate([]string{\"school\", \"AI\", \"asdf\", \"b\"}), expected: []string{\"AI\", \"asdf\", \"school\"} },\n     { actual: candidate([]string{\"d\", \"b\", \"c\", \"a\"}), expected: []string{} },\n     { actual: candidate([]string{\"d\", \"dcba\", \"abcd\", \"a\"}), expected: []string{\"abcd\", \"dcba\"} },\n     { actual: candidate([]string{\"AI\", \"ai\", \"au\"}), expected: []string{\"AI\", \"ai\", \"au\"} },\n     { actual: candidate([]string{\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"}), expected: []string{} },\n     { actual: candidate([]string{\"aaaa\", \"bbbb\", \"dd\", \"cc\"}), expected: []string{\"cc\", \"dd\", \"aaaa\", \"bbbb\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_149_sorted_list_sum", "test": "func TestSorted_List_Sum(t *testing.T) {\n  candidate := sorted_list_sum\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"aa\", \"a\", \"aaa\"}), expected: []string{\"aa\"} },\n     { actual: candidate([]string{\"school\", \"AI\", \"asdf\", \"b\"}), expected: []string{\"AI\", \"asdf\", \"school\"} },\n     { actual: candidate([]string{\"d\", \"b\", \"c\", \"a\"}), expected: []string{} },\n     { actual: candidate([]string{\"d\", \"dcba\", \"abcd\", \"a\"}), expected: []string{\"abcd\", \"dcba\"} },\n     { actual: candidate([]string{\"AI\", \"ai\", \"au\"}), expected: []string{\"AI\", \"ai\", \"au\"} },\n     { actual: candidate([]string{\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"}), expected: []string{} },\n     { actual: candidate([]string{\"aaaa\", \"bbbb\", \"dd\", \"cc\"}), expected: []string{\"cc\", \"dd\", \"aaaa\", \"bbbb\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_7_filter_by_substring", "language": "go_test.go", "prompt": "package filter_by_substring_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Filter an input list of strings only for ones that contain given substring\n// >>> filter_by_substring([]string{}, \"a\")\n// []string{}\n// >>> filter_by_substring([]string{\"abc\", \"bacd\", \"cde\", \"array\"}, \"a\")\n// []string{\"abc\", \"bacd\", \"array\"}\nfunc filter_by_substring(strings []string, substring string) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "func TestFilter_By_Substring(t *testing.T) {\n  candidate := filter_by_substring\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, \"john\"), expected: []string{} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xxx\"), expected: []string{\"xxx\", \"xxxAAA\", \"xxx\"} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xx\"), expected: []string{\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"} },\n     { actual: candidate([]string{\"grunt\", \"trumpet\", \"prune\", \"gruesome\"}, \"run\"), expected: []string{\"grunt\", \"prune\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_7_filter_by_substring", "test": "func TestFilter_By_Substring(t *testing.T) {\n  candidate := filter_by_substring\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{}, \"john\"), expected: []string{} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xxx\"), expected: []string{\"xxx\", \"xxxAAA\", \"xxx\"} },\n     { actual: candidate([]string{\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"}, \"xx\"), expected: []string{\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"} },\n     { actual: candidate([]string{\"grunt\", \"trumpet\", \"prune\", \"gruesome\"}, \"run\"), expected: []string{\"grunt\", \"prune\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_99_closest_integer", "language": "go_test.go", "prompt": "package closest_integer_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Create a function that takes a value (string) representing a number\n// and returns the closest integer to it. If the number is equidistant\n// from two integers, round it away from zero.\n// Examples\n// >>> closest_integer(\"10\")\n// 10\n// >>> closest_integer(\"15.3\")\n// 15\n// Note:\n// Rounding away from zero means that if the given number is equidistant\n// from two integers, the one you should return is the one that is the\n// farthest from zero. For example closest_integer(\"14.5\") should\n// return 15 and closest_integer(\"-14.5\") should return -15.\nfunc closest_integer(value string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "func TestClosest_Integer(t *testing.T) {\n  candidate := closest_integer\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"10\"), expected: 10 },\n     { actual: candidate(\"14.5\"), expected: 15 },\n     { actual: candidate(\"-15.5\"), expected: -16 },\n     { actual: candidate(\"15.3\"), expected: 15 },\n     { actual: candidate(\"0\"), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_99_closest_integer", "test": "func TestClosest_Integer(t *testing.T) {\n  candidate := closest_integer\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"10\"), expected: 10 },\n     { actual: candidate(\"14.5\"), expected: 15 },\n     { actual: candidate(\"-15.5\"), expected: -16 },\n     { actual: candidate(\"15.3\"), expected: 15 },\n     { actual: candidate(\"0\"), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_64_vowels_count", "language": "go_test.go", "prompt": "package vowels_count_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function vowels_count which takes a string representing\n// a word as input and returns the number of vowels in the string.\n// Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n// vowel, but only when it is at the end of the given word.\n// Example:\n// >>> vowels_count(\"abcde\")\n// 2\n// >>> vowels_count(\"ACEDY\")\n// 3\nfunc vowels_count(s string) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "func TestVowels_Count(t *testing.T) {\n  candidate := vowels_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"abcde\"), expected: 2 },\n     { actual: candidate(\"Alone\"), expected: 3 },\n     { actual: candidate(\"key\"), expected: 2 },\n     { actual: candidate(\"bye\"), expected: 1 },\n     { actual: candidate(\"keY\"), expected: 2 },\n     { actual: candidate(\"bYe\"), expected: 1 },\n     { actual: candidate(\"ACEDY\"), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_64_vowels_count", "test": "func TestVowels_Count(t *testing.T) {\n  candidate := vowels_count\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"abcde\"), expected: 2 },\n     { actual: candidate(\"Alone\"), expected: 3 },\n     { actual: candidate(\"key\"), expected: 2 },\n     { actual: candidate(\"bye\"), expected: 1 },\n     { actual: candidate(\"keY\"), expected: 2 },\n     { actual: candidate(\"bYe\"), expected: 1 },\n     { actual: candidate(\"ACEDY\"), expected: 3 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_158_find_max", "language": "go_test.go", "prompt": "package find_max_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that accepts a list of strings.\n// The list contains different words. Return the word with maximum number\n// of unique characters. If multiple strings have maximum number of unique\n// characters, return the one which comes first in lexicographical order.\n// >>> find_max([]string{\"name\", \"of\", \"string\"})\n// \"string\"\n// >>> find_max([]string{\"name\", \"enam\", \"game\"})\n// \"enam\"\n// >>> find_max([]string{\"aaaaaaa\", \"bb\", \"cc\"})\n// \"aaaaaaa\"\nfunc find_max(words []string) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "func TestFind_Max(t *testing.T) {\n  candidate := find_max\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"name\", \"of\", \"string\"}), expected: \"string\" },\n     { actual: candidate([]string{\"name\", \"enam\", \"game\"}), expected: \"enam\" },\n     { actual: candidate([]string{\"aaaaaaa\", \"bb\", \"cc\"}), expected: \"aaaaaaa\" },\n     { actual: candidate([]string{\"abc\", \"cba\"}), expected: \"abc\" },\n     { actual: candidate([]string{\"play\", \"this\", \"game\", \"of\", \"footbott\"}), expected: \"footbott\" },\n     { actual: candidate([]string{\"we\", \"are\", \"gonna\", \"rock\"}), expected: \"gonna\" },\n     { actual: candidate([]string{\"we\", \"are\", \"a\", \"mad\", \"nation\"}), expected: \"nation\" },\n     { actual: candidate([]string{\"this\", \"is\", \"a\", \"prrk\"}), expected: \"this\" },\n     { actual: candidate([]string{\"b\"}), expected: \"b\" },\n     { actual: candidate([]string{\"play\", \"play\", \"play\"}), expected: \"play\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_158_find_max", "test": "func TestFind_Max(t *testing.T) {\n  candidate := find_max\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]string{\"name\", \"of\", \"string\"}), expected: \"string\" },\n     { actual: candidate([]string{\"name\", \"enam\", \"game\"}), expected: \"enam\" },\n     { actual: candidate([]string{\"aaaaaaa\", \"bb\", \"cc\"}), expected: \"aaaaaaa\" },\n     { actual: candidate([]string{\"abc\", \"cba\"}), expected: \"abc\" },\n     { actual: candidate([]string{\"play\", \"this\", \"game\", \"of\", \"footbott\"}), expected: \"footbott\" },\n     { actual: candidate([]string{\"we\", \"are\", \"gonna\", \"rock\"}), expected: \"gonna\" },\n     { actual: candidate([]string{\"we\", \"are\", \"a\", \"mad\", \"nation\"}), expected: \"nation\" },\n     { actual: candidate([]string{\"this\", \"is\", \"a\", \"prrk\"}), expected: \"this\" },\n     { actual: candidate([]string{\"b\"}), expected: \"b\" },\n     { actual: candidate([]string{\"play\", \"play\", \"play\"}), expected: \"play\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_44_change_base", "language": "go_test.go", "prompt": "package change_base_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Change numerical base of input number x to base.\n// return string representation after the conversion.\n// base numbers are less than 10.\n// >>> change_base(8, 3)\n// \"22\"\n// >>> change_base(8, 2)\n// \"1000\"\n// >>> change_base(7, 2)\n// \"111\"\nfunc change_base(x int, base int) string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "func TestChange_Base(t *testing.T) {\n  candidate := change_base\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(8, 3), expected: \"22\" },\n     { actual: candidate(9, 3), expected: \"100\" },\n     { actual: candidate(234, 2), expected: \"11101010\" },\n     { actual: candidate(16, 2), expected: \"10000\" },\n     { actual: candidate(8, 2), expected: \"1000\" },\n     { actual: candidate(7, 2), expected: \"111\" },\n     { actual: candidate(2, 3), expected: \"2\" },\n     { actual: candidate(3, 4), expected: \"3\" },\n     { actual: candidate(4, 5), expected: \"4\" },\n     { actual: candidate(5, 6), expected: \"5\" },\n     { actual: candidate(6, 7), expected: \"6\" },\n     { actual: candidate(7, 8), expected: \"7\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_44_change_base", "test": "func TestChange_Base(t *testing.T) {\n  candidate := change_base\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(8, 3), expected: \"22\" },\n     { actual: candidate(9, 3), expected: \"100\" },\n     { actual: candidate(234, 2), expected: \"11101010\" },\n     { actual: candidate(16, 2), expected: \"10000\" },\n     { actual: candidate(8, 2), expected: \"1000\" },\n     { actual: candidate(7, 2), expected: \"111\" },\n     { actual: candidate(2, 3), expected: \"2\" },\n     { actual: candidate(3, 4), expected: \"3\" },\n     { actual: candidate(4, 5), expected: \"4\" },\n     { actual: candidate(5, 6), expected: \"5\" },\n     { actual: candidate(6, 7), expected: \"6\" },\n     { actual: candidate(7, 8), expected: \"7\" },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_157_right_angle_triangle", "language": "go_test.go", "prompt": "package right_angle_triangle_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given the lengths of the three sides of a triangle. Return true if the three\n// sides form a right-angled triangle, false otherwise.\n// A right-angled triangle is a triangle in which one angle is right angle or \n// 90 degree.\n// Example:\n// >>> right_angle_triangle(3, 4, 5)\n// true\n// >>> right_angle_triangle(1, 2, 3)\n// false\nfunc right_angle_triangle(a int, b int, c int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "func TestRight_Angle_Triangle(t *testing.T) {\n  candidate := right_angle_triangle\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 4, 5), expected: true },\n     { actual: candidate(1, 2, 3), expected: false },\n     { actual: candidate(10, 6, 8), expected: true },\n     { actual: candidate(2, 2, 2), expected: false },\n     { actual: candidate(7, 24, 25), expected: true },\n     { actual: candidate(10, 5, 7), expected: false },\n     { actual: candidate(5, 12, 13), expected: true },\n     { actual: candidate(15, 8, 17), expected: true },\n     { actual: candidate(48, 55, 73), expected: true },\n     { actual: candidate(1, 1, 1), expected: false },\n     { actual: candidate(2, 2, 10), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_157_right_angle_triangle", "test": "func TestRight_Angle_Triangle(t *testing.T) {\n  candidate := right_angle_triangle\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(3, 4, 5), expected: true },\n     { actual: candidate(1, 2, 3), expected: false },\n     { actual: candidate(10, 6, 8), expected: true },\n     { actual: candidate(2, 2, 2), expected: false },\n     { actual: candidate(7, 24, 25), expected: true },\n     { actual: candidate(10, 5, 7), expected: false },\n     { actual: candidate(5, 12, 13), expected: true },\n     { actual: candidate(15, 8, 17), expected: true },\n     { actual: candidate(48, 55, 73), expected: true },\n     { actual: candidate(1, 1, 1), expected: false },\n     { actual: candidate(2, 2, 10), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "go_test.go", "prompt": "package numerical_letter_grade_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// It is the last week of the semester and the teacher has to give the grades\n// to students. The teacher has been making her own algorithm for grading.\n// The only problem is, she has lost the code she used for grading.\n// She has given you a list of GPAs for some students and you have to write \n// a function that can output a list of letter grades using the following table:\n// GPA       |    Letter grade\n// 4.0                A+\n// > 3.7                A \n// > 3.3                A- \n// > 3.0                B+\n// > 2.7                B \n// > 2.3                B-\n// > 2.0                C+\n// > 1.7                C\n// > 1.3                C-\n// > 1.0                D+ \n// > 0.7                D \n// > 0.0                D-\n// 0.0                E\n// Example:\n// >>> grade_equation([]float64{4.0, 3, 1.7, 2, 3.5})\n// []string{\"A+\", \"B\", \"C-\", \"C\", \"A-\"}\nfunc numerical_letter_grade(grades []float64) []string {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "func TestNumerical_Letter_Grade(t *testing.T) {\n  candidate := numerical_letter_grade\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{4.0, 3, 1.7, 2, 3.5}), expected: []string{\"A+\", \"B\", \"C-\", \"C\", \"A-\"} },\n     { actual: candidate([]float64{1.2}), expected: []string{\"D+\"} },\n     { actual: candidate([]float64{0.5}), expected: []string{\"D-\"} },\n     { actual: candidate([]float64{0.0}), expected: []string{\"E\"} },\n     { actual: candidate([]float64{1.0, 0.3, 1.5, 2.8, 3.3}), expected: []string{\"D\", \"D-\", \"C-\", \"B\", \"B+\"} },\n     { actual: candidate([]float64{0.0, 0.7}), expected: []string{\"E\", \"D-\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_81_numerical_letter_grade", "test": "func TestNumerical_Letter_Grade(t *testing.T) {\n  candidate := numerical_letter_grade\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]float64{4.0, 3, 1.7, 2, 3.5}), expected: []string{\"A+\", \"B\", \"C-\", \"C\", \"A-\"} },\n     { actual: candidate([]float64{1.2}), expected: []string{\"D+\"} },\n     { actual: candidate([]float64{0.5}), expected: []string{\"D-\"} },\n     { actual: candidate([]float64{0.0}), expected: []string{\"E\"} },\n     { actual: candidate([]float64{1.0, 0.3, 1.5, 2.8, 3.3}), expected: []string{\"D\", \"D-\", \"C-\", \"B\", \"B+\"} },\n     { actual: candidate([]float64{0.0, 0.7}), expected: []string{\"E\", \"D-\"} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_5_intersperse", "language": "go_test.go", "prompt": "package intersperse_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Insert a number 'delimeter' between every two consecutive elements of input list `numbers'\n// >>> intersperse([]int{}, 4)\n// []int{}\n// >>> intersperse([]int{1, 2, 3}, 4)\n// []int{1, 4, 2, 4, 3}\nfunc intersperse(numbers []int, delimeter int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "func TestIntersperse(t *testing.T) {\n  candidate := intersperse\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}, 7), expected: []int{} },\n     { actual: candidate([]int{5, 6, 3, 2}, 8), expected: []int{5, 8, 6, 8, 3, 8, 2} },\n     { actual: candidate([]int{2, 2, 2}, 2), expected: []int{2, 2, 2, 2, 2} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_5_intersperse", "test": "func TestIntersperse(t *testing.T) {\n  candidate := intersperse\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}, 7), expected: []int{} },\n     { actual: candidate([]int{5, 6, 3, 2}, 8), expected: []int{5, 8, 6, 8, 3, 8, 2} },\n     { actual: candidate([]int{2, 2, 2}, 2), expected: []int{2, 2, 2, 2, 2} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_146_specialFilter", "language": "go_test.go", "prompt": "package specialFilter_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Write a function that takes a list of numbers as input and returns \n// the number of elements in the list that are greater than 10 and both \n// first and last digits of a number are odd (1, 3, 5, 7, 9).\n// For example:\n// >>> specialFilter([]int{15, -73, 14, -15})\n// 1\n// >>> specialFilter([]int{33, -2, -3, 45, 21, 109})\n// 2\nfunc specialFilter(nums []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "func TestSpecialfilter(t *testing.T) {\n  candidate := specialFilter\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, -2, 1, -5}), expected: 0 },\n     { actual: candidate([]int{15, -73, 14, -15}), expected: 1 },\n     { actual: candidate([]int{33, -2, -3, 45, 21, 109}), expected: 2 },\n     { actual: candidate([]int{43, -12, 93, 125, 121, 109}), expected: 4 },\n     { actual: candidate([]int{71, -2, -33, 75, 21, 19}), expected: 3 },\n     { actual: candidate([]int{1}), expected: 0 },\n     { actual: candidate([]int{}), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_146_specialFilter", "test": "func TestSpecialfilter(t *testing.T) {\n  candidate := specialFilter\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, -2, 1, -5}), expected: 0 },\n     { actual: candidate([]int{15, -73, 14, -15}), expected: 1 },\n     { actual: candidate([]int{33, -2, -3, 45, 21, 109}), expected: 2 },\n     { actual: candidate([]int{43, -12, 93, 125, 121, 109}), expected: 4 },\n     { actual: candidate([]int{71, -2, -33, 75, 21, 19}), expected: 3 },\n     { actual: candidate([]int{1}), expected: 0 },\n     { actual: candidate([]int{}), expected: 0 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_60_sum_to_n", "language": "go_test.go", "prompt": "package sum_to_n_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// sum_to_n is a function that sums numbers from 1 to n.\n// >>> sum_to_n(30)\n// 465\n// >>> sum_to_n(100)\n// 5050\n// >>> sum_to_n(5)\n// 15\n// >>> sum_to_n(10)\n// 55\n// >>> sum_to_n(1)\n// 1\nfunc sum_to_n(n int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "func TestSum_To_N(t *testing.T) {\n  candidate := sum_to_n\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(6), expected: 21 },\n     { actual: candidate(11), expected: 66 },\n     { actual: candidate(30), expected: 465 },\n     { actual: candidate(100), expected: 5050 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_60_sum_to_n", "test": "func TestSum_To_N(t *testing.T) {\n  candidate := sum_to_n\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(1), expected: 1 },\n     { actual: candidate(6), expected: 21 },\n     { actual: candidate(11), expected: 66 },\n     { actual: candidate(30), expected: 465 },\n     { actual: candidate(100), expected: 5050 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_26_remove_duplicates", "language": "go_test.go", "prompt": "package remove_duplicates_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// From a list of integers, remove all elements that occur more than once.\n// Keep order of elements left the same as in the input.\n// >>> remove_duplicates([]int{1, 2, 3, 2, 4})\n// []int{1, 3, 4}\nfunc remove_duplicates(numbers []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "func TestRemove_Duplicates(t *testing.T) {\n  candidate := remove_duplicates\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 2, 3, 4} },\n     { actual: candidate([]int{1, 2, 3, 2, 4, 3, 5}), expected: []int{1, 4, 5} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_26_remove_duplicates", "test": "func TestRemove_Duplicates(t *testing.T) {\n  candidate := remove_duplicates\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 2, 3, 4} },\n     { actual: candidate([]int{1, 2, 3, 2, 4, 3, 5}), expected: []int{1, 4, 5} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_163_generate_integers", "language": "go_test.go", "prompt": "package generate_integers_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Given two positive integers a and b, return the even digits between a\n// and b, in ascending order.\n// For example:\n// >>> generate_integers(2, 8)\n// []int{2, 4, 6, 8}\n// >>> generate_integers(8, 2)\n// []int{2, 4, 6, 8}\n// >>> generate_integers(10, 14)\n// []int{}\nfunc generate_integers(a int, b int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "func TestGenerate_Integers(t *testing.T) {\n  candidate := generate_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2, 10), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(10, 2), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(132, 2), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(17, 89), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_163_generate_integers", "test": "func TestGenerate_Integers(t *testing.T) {\n  candidate := generate_integers\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(2, 10), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(10, 2), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(132, 2), expected: []int{2, 4, 6, 8} },\n     { actual: candidate(17, 89), expected: []int{} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_9_rolling_max", "language": "go_test.go", "prompt": "package rolling_max_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// From a given list of integers, generate a list of rolling maximum element found until given moment\n// in the sequence.\n// >>> rolling_max([]int{1, 2, 3, 2, 3, 4, 2})\n// []int{1, 2, 3, 3, 3, 4, 4}\nfunc rolling_max(numbers []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "func TestRolling_Max(t *testing.T) {\n  candidate := rolling_max\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 2, 3, 4} },\n     { actual: candidate([]int{4, 3, 2, 1}), expected: []int{4, 4, 4, 4} },\n     { actual: candidate([]int{3, 2, 3, 100, 3}), expected: []int{3, 3, 3, 100, 100} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_9_rolling_max", "test": "func TestRolling_Max(t *testing.T) {\n  candidate := rolling_max\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: []int{} },\n     { actual: candidate([]int{1, 2, 3, 4}), expected: []int{1, 2, 3, 4} },\n     { actual: candidate([]int{4, 3, 2, 1}), expected: []int{4, 4, 4, 4} },\n     { actual: candidate([]int{3, 2, 3, 100, 3}), expected: []int{3, 3, 3, 100, 100} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_3_below_zero", "language": "go_test.go", "prompt": "package below_zero_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You're given a list of deposit and withdrawal operations on a bank account that starts with\n// zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n// at that point function should return true. Otherwise it should return false.\n// >>> below_zero([]int{1, 2, 3})\n// false\n// >>> below_zero([]int{1, 2, -4, 5})\n// true\nfunc below_zero(operations []int) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "func TestBelow_Zero(t *testing.T) {\n  candidate := below_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: false },\n     { actual: candidate([]int{1, 2, -3, 1, 2, -3}), expected: false },\n     { actual: candidate([]int{1, 2, -4, 5, 6}), expected: true },\n     { actual: candidate([]int{1, -1, 2, -2, 5, -5, 4, -4}), expected: false },\n     { actual: candidate([]int{1, -1, 2, -2, 5, -5, 4, -5}), expected: true },\n     { actual: candidate([]int{1, -2, 2, -2, 5, -5, 4, -4}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_3_below_zero", "test": "func TestBelow_Zero(t *testing.T) {\n  candidate := below_zero\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{}), expected: false },\n     { actual: candidate([]int{1, 2, -3, 1, 2, -3}), expected: false },\n     { actual: candidate([]int{1, 2, -4, 5, 6}), expected: true },\n     { actual: candidate([]int{1, -1, 2, -2, 5, -5, 4, -4}), expected: false },\n     { actual: candidate([]int{1, -1, 2, -2, 5, -5, 4, -5}), expected: true },\n     { actual: candidate([]int{1, -2, 2, -2, 5, -5, 4, -4}), expected: true },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_69_search", "language": "go_test.go", "prompt": "package search_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n// zero, and has a frequency greater than or equal to the value of the integer itself. \n// The frequency of an integer is the number of times it appears in the list.\n// If no such a value exist, return -1.\n// Examples:\n// >>> search([]int{4, 1, 2, 2, 3, 1})\n// 2\n// >>> search([]int{1, 2, 2, 3, 3, 3, 4, 4, 4})\n// 3\n// >>> search([]int{5, 5, 4, 4, 4})\n// -1\nfunc search(lst []int) int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "func TestSearch(t *testing.T) {\n  candidate := search\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 5, 5, 5, 1}), expected: 1 },\n     { actual: candidate([]int{4, 1, 4, 1, 4, 4}), expected: 4 },\n     { actual: candidate([]int{3, 3}), expected: -1 },\n     { actual: candidate([]int{8, 8, 8, 8, 8, 8, 8, 8}), expected: 8 },\n     { actual: candidate([]int{2, 3, 3, 2, 2}), expected: 2 },\n     { actual: candidate([]int{2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1}), expected: 1 },\n     { actual: candidate([]int{3, 2, 8, 2}), expected: 2 },\n     { actual: candidate([]int{6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10}), expected: 1 },\n     { actual: candidate([]int{8, 8, 3, 6, 5, 6, 4}), expected: -1 },\n     { actual: candidate([]int{6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9}), expected: 1 },\n     { actual: candidate([]int{1, 9, 10, 1, 3}), expected: 1 },\n     { actual: candidate([]int{6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10}), expected: 5 },\n     { actual: candidate([]int{1}), expected: 1 },\n     { actual: candidate([]int{8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5}), expected: 4 },\n     { actual: candidate([]int{2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10}), expected: 2 },\n     { actual: candidate([]int{1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3}), expected: 1 },\n     { actual: candidate([]int{9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4}), expected: 4 },\n     { actual: candidate([]int{2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7}), expected: 4 },\n     { actual: candidate([]int{9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1}), expected: 2 },\n     { actual: candidate([]int{5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8}), expected: -1 },\n     { actual: candidate([]int{10}), expected: -1 },\n     { actual: candidate([]int{9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2}), expected: 2 },\n     { actual: candidate([]int{5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8}), expected: 1 },\n     { actual: candidate([]int{7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6}), expected: 1 },\n     { actual: candidate([]int{3, 10, 10, 9, 2}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_69_search", "test": "func TestSearch(t *testing.T) {\n  candidate := search\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{5, 5, 5, 5, 1}), expected: 1 },\n     { actual: candidate([]int{4, 1, 4, 1, 4, 4}), expected: 4 },\n     { actual: candidate([]int{3, 3}), expected: -1 },\n     { actual: candidate([]int{8, 8, 8, 8, 8, 8, 8, 8}), expected: 8 },\n     { actual: candidate([]int{2, 3, 3, 2, 2}), expected: 2 },\n     { actual: candidate([]int{2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1}), expected: 1 },\n     { actual: candidate([]int{3, 2, 8, 2}), expected: 2 },\n     { actual: candidate([]int{6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10}), expected: 1 },\n     { actual: candidate([]int{8, 8, 3, 6, 5, 6, 4}), expected: -1 },\n     { actual: candidate([]int{6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9}), expected: 1 },\n     { actual: candidate([]int{1, 9, 10, 1, 3}), expected: 1 },\n     { actual: candidate([]int{6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10}), expected: 5 },\n     { actual: candidate([]int{1}), expected: 1 },\n     { actual: candidate([]int{8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5}), expected: 4 },\n     { actual: candidate([]int{2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10}), expected: 2 },\n     { actual: candidate([]int{1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3}), expected: 1 },\n     { actual: candidate([]int{9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4}), expected: 4 },\n     { actual: candidate([]int{2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7}), expected: 4 },\n     { actual: candidate([]int{9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1}), expected: 2 },\n     { actual: candidate([]int{5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8}), expected: -1 },\n     { actual: candidate([]int{10}), expected: -1 },\n     { actual: candidate([]int{9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2}), expected: 2 },\n     { actual: candidate([]int{5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8}), expected: 1 },\n     { actual: candidate([]int{7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6}), expected: 1 },\n     { actual: candidate([]int{3, 10, 10, 9, 2}), expected: -1 },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_61_correct_bracketing", "language": "go_test.go", "prompt": "package correct_bracketing_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// brackets is a string of \"(\" and \")\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"(\")\n// false\n// >>> correct_bracketing(\"()\")\n// true\n// >>> correct_bracketing(\"(()())\")\n// true\n// >>> correct_bracketing(\")(()\")\n// false\nfunc correct_bracketing(brackets string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "func TestCorrect_Bracketing(t *testing.T) {\n  candidate := correct_bracketing\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"()\"), expected: true },\n     { actual: candidate(\"(()())\"), expected: true },\n     { actual: candidate(\"()()(()())()\"), expected: true },\n     { actual: candidate(\"()()((()()())())(()()(()))\"), expected: true },\n     { actual: candidate(\"((()())))\"), expected: false },\n     { actual: candidate(\")(()\"), expected: false },\n     { actual: candidate(\"(\"), expected: false },\n     { actual: candidate(\"((((\"), expected: false },\n     { actual: candidate(\")\"), expected: false },\n     { actual: candidate(\"(()\"), expected: false },\n     { actual: candidate(\"()()(()())())(()\"), expected: false },\n     { actual: candidate(\"()()(()())()))()\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_61_correct_bracketing", "test": "func TestCorrect_Bracketing(t *testing.T) {\n  candidate := correct_bracketing\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"()\"), expected: true },\n     { actual: candidate(\"(()())\"), expected: true },\n     { actual: candidate(\"()()(()())()\"), expected: true },\n     { actual: candidate(\"()()((()()())())(()()(()))\"), expected: true },\n     { actual: candidate(\"((()())))\"), expected: false },\n     { actual: candidate(\")(()\"), expected: false },\n     { actual: candidate(\"(\"), expected: false },\n     { actual: candidate(\"((((\"), expected: false },\n     { actual: candidate(\")\"), expected: false },\n     { actual: candidate(\"(()\"), expected: false },\n     { actual: candidate(\"()()(()())())(()\"), expected: false },\n     { actual: candidate(\"()()(()())()))()\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_37_sort_even", "language": "go_test.go", "prompt": "package sort_even_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// This function takes a list l and returns a list l' such that\n// l' is identical to l in the odd indicies, while its values at the even indicies are equal\n// to the values of the even indicies of l, but sorted.\n// >>> sort_even([]int{1, 2, 3})\n// []int{1, 2, 3}\n// >>> sort_even([]int{5, 6, 3, 4})\n// []int{3, 6, 5, 4}\nfunc sort_even(l []int) []int {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "func TestSort_Even(t *testing.T) {\n  candidate := sort_even\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: []int{1, 2, 3} },\n     { actual: candidate([]int{5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10}), expected: []int{-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123} },\n     { actual: candidate([]int{5, 8, -12, 4, 23, 2, 3, 11, 12, -10}), expected: []int{-12, 8, 3, 4, 5, 2, 12, 11, 23, -10} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_37_sort_even", "test": "func TestSort_Even(t *testing.T) {\n  candidate := sort_even\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate([]int{1, 2, 3}), expected: []int{1, 2, 3} },\n     { actual: candidate([]int{5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10}), expected: []int{-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123} },\n     { actual: candidate([]int{5, 8, -12, 4, 23, 2, 3, 11, 12, -10}), expected: []int{-12, 8, 3, 4, 5, 2, 12, 11, 23, -10} },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_54_same_chars", "language": "go_test.go", "prompt": "package same_chars_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// Check if two words have the same characters.\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n// true\n// >>> same_chars(\"abcd\", \"dddddddabc\")\n// true\n// >>> same_chars(\"dddddddabc\", \"abcd\")\n// true\n// >>> same_chars(\"eabcd\", \"dddddddabc\")\n// false\n// >>> same_chars(\"abcd\", \"dddddddabce\")\n// false\n// >>> same_chars(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n// false\nfunc same_chars(s0 string, s1 string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "func TestSame_Chars(t *testing.T) {\n  candidate := same_chars\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"), expected: true },\n     { actual: candidate(\"abcd\", \"dddddddabc\"), expected: true },\n     { actual: candidate(\"dddddddabc\", \"abcd\"), expected: true },\n     { actual: candidate(\"eabcd\", \"dddddddabc\"), expected: false },\n     { actual: candidate(\"abcd\", \"dddddddabcf\"), expected: false },\n     { actual: candidate(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"), expected: false },\n     { actual: candidate(\"aabb\", \"aaccc\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_54_same_chars", "test": "func TestSame_Chars(t *testing.T) {\n  candidate := same_chars\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"), expected: true },\n     { actual: candidate(\"abcd\", \"dddddddabc\"), expected: true },\n     { actual: candidate(\"dddddddabc\", \"abcd\"), expected: true },\n     { actual: candidate(\"eabcd\", \"dddddddabc\"), expected: false },\n     { actual: candidate(\"abcd\", \"dddddddabcf\"), expected: false },\n     { actual: candidate(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"), expected: false },\n     { actual: candidate(\"aabb\", \"aaccc\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
{"name": "HumanEval_56_correct_bracketing", "language": "go_test.go", "prompt": "package correct_bracketing_test\n\nimport (\n    \"testing\"\n    \"fmt\"\n)\n\n// brackets is a string of \"<\" and \">\".\n// return true if every opening bracket has a corresponding closing bracket.\n// >>> correct_bracketing(\"<\")\n// false\n// >>> correct_bracketing(\"<>\")\n// true\n// >>> correct_bracketing(\"<<><>>\")\n// true\n// >>> correct_bracketing(\"><<>\")\n// false\nfunc correct_bracketing(brackets string) bool {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "func TestCorrect_Bracketing(t *testing.T) {\n  candidate := correct_bracketing\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"<>\"), expected: true },\n     { actual: candidate(\"<<><>>\"), expected: true },\n     { actual: candidate(\"<><><<><>><>\"), expected: true },\n     { actual: candidate(\"<><><<<><><>><>><<><><<>>>\"), expected: true },\n     { actual: candidate(\"<<<><>>>>\"), expected: false },\n     { actual: candidate(\"><<>\"), expected: false },\n     { actual: candidate(\"<\"), expected: false },\n     { actual: candidate(\"<<<<\"), expected: false },\n     { actual: candidate(\">\"), expected: false },\n     { actual: candidate(\"<<>\"), expected: false },\n     { actual: candidate(\"<><><<><>><>><<>\"), expected: false },\n     { actual: candidate(\"<><><<><>><>>><>\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n", "stop_tokens": ["\nfunc", "struct", "\n// "], "task_id": "HumanEval_56_correct_bracketing", "test": "func TestCorrect_Bracketing(t *testing.T) {\n  candidate := correct_bracketing\n\ttype test struct {\n\t\tactual   interface{}\n\t\texpected interface{}\n\t}\n   tests := []test{\n     { actual: candidate(\"<>\"), expected: true },\n     { actual: candidate(\"<<><>>\"), expected: true },\n     { actual: candidate(\"<><><<><>><>\"), expected: true },\n     { actual: candidate(\"<><><<<><><>><>><<><><<>>>\"), expected: true },\n     { actual: candidate(\"<<<><>>>>\"), expected: false },\n     { actual: candidate(\"><<>\"), expected: false },\n     { actual: candidate(\"<\"), expected: false },\n     { actual: candidate(\"<<<<\"), expected: false },\n     { actual: candidate(\">\"), expected: false },\n     { actual: candidate(\"<<>\"), expected: false },\n     { actual: candidate(\"<><><<><>><>><<>\"), expected: false },\n     { actual: candidate(\"<><><<><>><>>><>\"), expected: false },\n   }\n\n\tfor i, tc := range tests {\n\t\tt.Run(fmt.Sprintf(\"test num % d\", i), func(t *testing.T) {\n\t\t\tif fmt.Sprintf(\"%v\", tc.actual) != fmt.Sprintf(\"%v\", tc.expected) {\n\t\t\t\tt.Errorf(\"expected '%s', got '%s'\", tc.expected, tc.actual)\n\t\t\t}\n\t\t})\n\t}\n}\n"}
