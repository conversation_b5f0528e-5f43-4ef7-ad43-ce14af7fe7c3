{"name": "HumanEval_23_strlen", "language": "sh", "prompt": "#!/bin/bash\n# Return length of given string\n# >>> $(strlen \"\")\n# \"0\"\n# >>> $(strlen \"abc\")\n# \"3\"\n#\n# $1 is a string\nstrlen() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    strlen \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"x\") = \"1\" ]]\n    [[ $(candidate \"asdasnakj\") = \"9\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_23_strlen", "test": "}\n\ncandidate() {\n    strlen \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"x\") = \"1\" ]]\n    [[ $(candidate \"asdasnakj\") = \"9\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_89_encrypt", "language": "sh", "prompt": "#!/bin/bash\n# Create a function encrypt that takes a string as an argument and\n# returns a string encrypted with the alphabet being rotated. \n# The alphabet should be rotated in a manner such that the letters \n# shift down by two multiplied to two places.\n# For example:\n# >>> $(encrypt \"hi\")\n# \"lm\"\n# >>> $(encrypt \"asdfghjkl\")\n# \"ewhjklnop\"\n# >>> $(encrypt \"gf\")\n# \"kj\"\n# >>> $(encrypt \"et\")\n# \"ix\"\n#\n# $1 is a string\nencrypt() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    encrypt \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"hi\") = \"lm\" ]]\n    [[ $(candidate \"asdfghjkl\") = \"ewhjklnop\" ]]\n    [[ $(candidate \"gf\") = \"kj\" ]]\n    [[ $(candidate \"et\") = \"ix\" ]]\n    [[ $(candidate \"faewfawefaewg\") = \"jeiajeaijeiak\" ]]\n    [[ $(candidate \"hellomyfriend\") = \"lippsqcjvmirh\" ]]\n    [[ $(candidate \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") = \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" ]]\n    [[ $(candidate \"a\") = \"e\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_89_encrypt", "test": "}\n\ncandidate() {\n    encrypt \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"hi\") = \"lm\" ]]\n    [[ $(candidate \"asdfghjkl\") = \"ewhjklnop\" ]]\n    [[ $(candidate \"gf\") = \"kj\" ]]\n    [[ $(candidate \"et\") = \"ix\" ]]\n    [[ $(candidate \"faewfawefaewg\") = \"jeiajeaijeiak\" ]]\n    [[ $(candidate \"hellomyfriend\") = \"lippsqcjvmirh\" ]]\n    [[ $(candidate \"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\") = \"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\" ]]\n    [[ $(candidate \"a\") = \"e\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_95_check_dict_case", "language": "sh", "prompt": "#!/bin/bash\n# Given a CSV, return true if all keys are strings in lower \n# case or all keys are strings in upper case, else return false.\n# The function should return false is the given CSV is empty.\n# Examples:\n# >>> $(check_dict_case \"a,apple\\nb,banana\")\n# \"true\"\n# >>> $(check_dict_case \"a,apple\\nA,banana\\nB,banana\")\n# \"false\"\n# >>> $(check_dict_case \"a,apple\\n8,banana\")\n# \"false\"\n# >>> $(check_dict_case \"Name,John\\nAge,36\\nCity,Houston\")\n# \"false\"\n# >>> $(check_dict_case \"STATE,NC\\nZIP,12345\")\n# \"true\"\n#\n# $1 is a two column CSV in key,value order\ncheck_dict_case() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    check_dict_case \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"p,pineapple\\nb,banana\") = \"true\" ]]\n    [[ $(candidate \"p,pineapple\\nA,banana\\nB,banana\") = \"false\" ]]\n    [[ $(candidate \"p,pineapple\\n5,banana\\na,apple\") = \"false\" ]]\n    [[ $(candidate \"Name,John\\nAge,36\\nCity,Houston\") = \"false\" ]]\n    [[ $(candidate \"STATE,NC\\nZIP,12345\") = \"true\" ]]\n    [[ $(candidate \"fruit,Orange\\ntaste,Sweet\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_95_check_dict_case", "test": "}\n\ncandidate() {\n    check_dict_case \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"p,pineapple\\nb,banana\") = \"true\" ]]\n    [[ $(candidate \"p,pineapple\\nA,banana\\nB,banana\") = \"false\" ]]\n    [[ $(candidate \"p,pineapple\\n5,banana\\na,apple\") = \"false\" ]]\n    [[ $(candidate \"Name,John\\nAge,36\\nCity,Houston\") = \"false\" ]]\n    [[ $(candidate \"STATE,NC\\nZIP,12345\") = \"true\" ]]\n    [[ $(candidate \"fruit,Orange\\ntaste,Sweet\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_85_add", "language": "sh", "prompt": "#!/bin/bash\n# Given a non-empty list of integers lst. add the even elements that are at odd indices..\n# Examples:\n# >>> $(add \"4 2 6 7\")\n# \"2\"\n#\n# $1 is a space-separated list\nadd() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    add \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4 88\") = \"88\" ]]\n    [[ $(candidate \"4 5 6 7 2 122\") = \"122\" ]]\n    [[ $(candidate \"4 0 6 7\") = \"0\" ]]\n    [[ $(candidate \"4 4 6 8\") = \"12\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_85_add", "test": "}\n\ncandidate() {\n    add \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4 88\") = \"88\" ]]\n    [[ $(candidate \"4 5 6 7 2 122\") = \"122\" ]]\n    [[ $(candidate \"4 0 6 7\") = \"0\" ]]\n    [[ $(candidate \"4 4 6 8\") = \"12\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_140_fix_spaces", "language": "sh", "prompt": "#!/bin/bash\n# Given a string text, replace all spaces in it with underscores, \n# and if a string has more than 2 consecutive spaces, \n# then replace all consecutive spaces with - \n# >>> $(fix_spaces \" Example\")\n# \"Example\"\n# >>> $(fix_spaces \" Example 1\")\n# \"Example_1\"\n# >>> $(fix_spaces \" Example 2\")\n# \"_Example_2\"\n# >>> $(fix_spaces \" Example 3\")\n# \"_Example-3\"\n#\n# $1 is a string\nfix_spaces() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fix_spaces \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Example\") = \"Example\" ]]\n    [[ $(candidate \"Mudasir Hanif \") = \"Mudasir_Hanif_\" ]]\n    [[ $(candidate \"Yellow Yellow  Dirty  Fellow\") = \"Yellow_Yellow__Dirty__Fellow\" ]]\n    [[ $(candidate \"Exa   mple\") = \"Exa-mple\" ]]\n    [[ $(candidate \"   Exa 1 2 2 mple\") = \"-Exa_1_2_2_mple\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_140_fix_spaces", "test": "}\n\ncandidate() {\n    fix_spaces \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Example\") = \"Example\" ]]\n    [[ $(candidate \"Mudasir Hanif \") = \"Mudasir_Hanif_\" ]]\n    [[ $(candidate \"Yellow Yellow  Dirty  Fellow\") = \"Yellow_Yellow__Dirty__Fellow\" ]]\n    [[ $(candidate \"Exa   mple\") = \"Exa-mple\" ]]\n    [[ $(candidate \"   Exa 1 2 2 mple\") = \"-Exa_1_2_2_mple\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_63_fibfib", "language": "sh", "prompt": "#!/bin/bash\n# The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n# fibfib(0) == 0\n# fibfib(1) == 0\n# fibfib(2) == 1\n# fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n# Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n# >>> $(fibfib \"1\")\n# \"0\"\n# >>> $(fibfib \"5\")\n# \"4\"\n# >>> $(fibfib \"8\")\n# \"24\"\n#\n# $1 is an integer\nfibfib() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fibfib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"5\") = \"4\" ]]\n    [[ $(candidate \"8\") = \"24\" ]]\n    [[ $(candidate \"10\") = \"81\" ]]\n    [[ $(candidate \"12\") = \"274\" ]]\n    [[ $(candidate \"14\") = \"927\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_63_fibfib", "test": "}\n\ncandidate() {\n    fibfib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"5\") = \"4\" ]]\n    [[ $(candidate \"8\") = \"24\" ]]\n    [[ $(candidate \"10\") = \"81\" ]]\n    [[ $(candidate \"12\") = \"274\" ]]\n    [[ $(candidate \"14\") = \"927\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_151_double_the_difference", "language": "sh", "prompt": "#!/bin/bash\n# Given a list of numbers, return the sum of squares of the numbers\n# in the list that are odd. Ignore numbers that are negative or not integers.\n# >>> $(double_the_difference \"1 3 2 0\")\n# \"10\"\n# >>> $(double_the_difference \"-1 -2 0\")\n# \"0\"\n# >>> $(double_the_difference \"9 -2\")\n# \"81\"\n# >>> $(double_the_difference \"0\")\n# \"0\"\n# If the input list is empty, return 0.\n#\n# $1 is a space-separated list\ndouble_the_difference() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    double_the_difference \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"5.0 4.0\") = \"25\" ]]\n    [[ $(candidate \"0.1 0.2 0.3\") = \"0\" ]]\n    [[ $(candidate \"-10.0 -20.0 -30.0\") = \"0\" ]]\n    [[ $(candidate \"-1.0 -2.0 8.0\") = \"0\" ]]\n    [[ $(candidate \"0.2 3.0 5.0\") = \"34\" ]]\n    [[ $(candidate \"-9.0 -7.0 -5.0 -3.0 -1.0 1.0 3.0 5.0 7.0 9.0\") = \"165\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_151_double_the_difference", "test": "}\n\ncandidate() {\n    double_the_difference \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"5.0 4.0\") = \"25\" ]]\n    [[ $(candidate \"0.1 0.2 0.3\") = \"0\" ]]\n    [[ $(candidate \"-10.0 -20.0 -30.0\") = \"0\" ]]\n    [[ $(candidate \"-1.0 -2.0 8.0\") = \"0\" ]]\n    [[ $(candidate \"0.2 3.0 5.0\") = \"34\" ]]\n    [[ $(candidate \"-9.0 -7.0 -5.0 -3.0 -1.0 1.0 3.0 5.0 7.0 9.0\") = \"165\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_22_filter_integers", "language": "sh", "prompt": "#!/bin/bash\n# Filter given list of any shthon values only for integers\n# >>> $(filter_integers \"a 3.14 5\")\n# ['\"5\"']\n# >>> $(filter_integers \"1 2 3 abc  \")\n# ['\"1\"', '\"2\"', '\"3\"']\n#\n# $1 is a space-separated list\nfilter_integers() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    filter_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"4   23.2 9 adasd\") = \"4 9\" ]]\n    [[ $(candidate \"3 c 3 3 a b\") = \"3 3 3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_22_filter_integers", "test": "}\n\ncandidate() {\n    filter_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"4   23.2 9 adasd\") = \"4 9\" ]]\n    [[ $(candidate \"3 c 3 3 a b\") = \"3 3 3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_41_car_race_collision", "language": "sh", "prompt": "#!/bin/bash\n# Imagine a road that's a perfectly straight infinitely long line.\n# n cars are driving left to right;  simultaneously, a different set of n cars\n# are driving right to left.   The two sets of cars start out being very far from\n# each other.  All cars move in the same speed.  Two cars are said to collide\n# when a car that's moving left to right hits a car that's moving right to left.\n# However, the cars are infinitely sturdy and strong; as a result, they continue moving\n# in their trajectory as if they did not collide.\n# This function outputs the number of such collisions.\n#\n# $1 is an integer\ncar_race_collision() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    car_race_collision \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"4\" ]]\n    [[ $(candidate \"3\") = \"9\" ]]\n    [[ $(candidate \"4\") = \"16\" ]]\n    [[ $(candidate \"8\") = \"64\" ]]\n    [[ $(candidate \"10\") = \"100\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_41_car_race_collision", "test": "}\n\ncandidate() {\n    car_race_collision \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"4\" ]]\n    [[ $(candidate \"3\") = \"9\" ]]\n    [[ $(candidate \"4\") = \"16\" ]]\n    [[ $(candidate \"8\") = \"64\" ]]\n    [[ $(candidate \"10\") = \"100\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_17_parse_music", "language": "sh", "prompt": "#!/bin/bash\n# Input to this function is a string representing musical notes in a special ASCII format.\n# Your task is to parse this string and return list of integers corresponding to how many beats does each\n# not last.\n# Here is a legend:\n# 'o' - whole note, lasts four beats\n# 'o|' - half note, lasts two beats\n# '.|' - quater note, lasts one beat\n# >>> $(parse_music \"o o| .| o| o| .| .| .| .| o o\")\n# ['\"4\"', '\"2\"', '\"1\"', '\"2\"', '\"2\"', '\"1\"', '\"1\"', '\"1\"', '\"1\"', '\"4\"', '\"4\"']\n#\n# $1 is a string\nparse_music() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    parse_music \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"o o o o\") = \"4 4 4 4\" ]]\n    [[ $(candidate \".| .| .| .|\") = \"1 1 1 1\" ]]\n    [[ $(candidate \"o| o| .| .| o o o o\") = \"2 2 1 1 4 4 4 4\" ]]\n    [[ $(candidate \"o| .| o| .| o o| o o|\") = \"2 1 2 1 4 2 4 2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_17_parse_music", "test": "}\n\ncandidate() {\n    parse_music \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"o o o o\") = \"4 4 4 4\" ]]\n    [[ $(candidate \".| .| .| .|\") = \"1 1 1 1\" ]]\n    [[ $(candidate \"o| o| .| .| o o o o\") = \"2 2 1 1 4 4 4 4\" ]]\n    [[ $(candidate \"o| .| o| .| o o| o o|\") = \"2 1 2 1 4 2 4 2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_79_decimal_to_binary", "language": "sh", "prompt": "#!/bin/bash\n# You will be given a number in decimal form and your task is to convert it to\n# binary format. The function should return a string, with each character representing a binary\n# number. Each character in the string will be '0' or '1'.\n# There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n# The extra characters are there to help with the format.\n# Examples:\n# >>> $(decimal_to_binary \"15\")\n# \"db1111db\"\n# >>> $(decimal_to_binary \"32\")\n# \"db100000db\"\n#\n# $1 is an integer\ndecimal_to_binary() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    decimal_to_binary \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\") = \"db0db\" ]]\n    [[ $(candidate \"32\") = \"db100000db\" ]]\n    [[ $(candidate \"103\") = \"db1100111db\" ]]\n    [[ $(candidate \"15\") = \"db1111db\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_79_decimal_to_binary", "test": "}\n\ncandidate() {\n    decimal_to_binary \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\") = \"db0db\" ]]\n    [[ $(candidate \"32\") = \"db100000db\" ]]\n    [[ $(candidate \"103\") = \"db1100111db\" ]]\n    [[ $(candidate \"15\") = \"db1111db\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_14_all_prefixes", "language": "sh", "prompt": "#!/bin/bash\n# Return list of all prefixes from shortest to longest of the input string\n# >>> $(all_prefixes \"abc\")\n# ['\"a\"', '\"ab\"', '\"abc\"']\n#\n# $1 is a string\nall_prefixes() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    all_prefixes \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"asdfgh\") = \"a as asd asdf asdfg asdfgh\" ]]\n    [[ $(candidate \"WWW\") = \"W WW WWW\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_14_all_prefixes", "test": "}\n\ncandidate() {\n    all_prefixes \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"asdfgh\") = \"a as asd asdf asdfg asdfgh\" ]]\n    [[ $(candidate \"WWW\") = \"W WW WWW\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_53_add", "language": "sh", "prompt": "#!/bin/bash\n# Add two numbers x and y\n# >>> $(add \"2\" \"3\")\n# \"5\"\n# >>> $(add \"5\" \"7\")\n# \"12\"\n#\n# $1 is an integer\n# $2 is an integer\nadd() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    add \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\" \"1\") = \"1\" ]]\n    [[ $(candidate \"1\" \"0\") = \"1\" ]]\n    [[ $(candidate \"2\" \"3\") = \"5\" ]]\n    [[ $(candidate \"5\" \"7\") = \"12\" ]]\n    [[ $(candidate \"7\" \"5\") = \"12\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_53_add", "test": "}\n\ncandidate() {\n    add \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\" \"1\") = \"1\" ]]\n    [[ $(candidate \"1\" \"0\") = \"1\" ]]\n    [[ $(candidate \"2\" \"3\") = \"5\" ]]\n    [[ $(candidate \"5\" \"7\") = \"12\" ]]\n    [[ $(candidate \"7\" \"5\") = \"12\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_159_eat", "language": "sh", "prompt": "#!/bin/bash\n# You're a hungry rabbit, and you already have eaten a certain number of carrots,\n# but now you need to eat more carrots to complete the day's meals.\n# you should return an array of [ total number of eaten carrots after your meals,\n# the number of carrots left after your meals ]\n# if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n# Example:\n# >>> $(eat \"5\" \"6\" \"10\")\n# ['\"11\"', '\"4\"']\n# >>> $(eat \"4\" \"8\" \"9\")\n# ['\"12\"', '\"1\"']\n# >>> $(eat \"1\" \"10\" \"10\")\n# ['\"11\"', '\"0\"']\n# >>> $(eat \"2\" \"11\" \"5\")\n# ['\"7\"', '\"0\"']\n# Variables:\n# @number : integer\n# the number of carrots that you have eaten.\n# @need : integer\n# the number of carrots that you need to eat.\n# @remaining : integer\n# the number of remaining carrots thet exist in stock\n# Constrain:\n# * 0 <= number <= 1000\n# * 0 <= need <= 1000\n# * 0 <= remaining <= 1000\n# Have fun :)\n#\n# $1 is an integer\n# $2 is an integer\n# $3 is an integer\neat() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    eat \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\" \"6\" \"10\") = \"11 4\" ]]\n    [[ $(candidate \"4\" \"8\" \"9\") = \"12 1\" ]]\n    [[ $(candidate \"1\" \"10\" \"10\") = \"11 0\" ]]\n    [[ $(candidate \"2\" \"11\" \"5\") = \"7 0\" ]]\n    [[ $(candidate \"4\" \"5\" \"7\") = \"9 2\" ]]\n    [[ $(candidate \"4\" \"5\" \"1\") = \"5 0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_159_eat", "test": "}\n\ncandidate() {\n    eat \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\" \"6\" \"10\") = \"11 4\" ]]\n    [[ $(candidate \"4\" \"8\" \"9\") = \"12 1\" ]]\n    [[ $(candidate \"1\" \"10\" \"10\") = \"11 0\" ]]\n    [[ $(candidate \"2\" \"11\" \"5\") = \"7 0\" ]]\n    [[ $(candidate \"4\" \"5\" \"7\") = \"9 2\" ]]\n    [[ $(candidate \"4\" \"5\" \"1\") = \"5 0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_115_max_fill", "language": "sh", "prompt": "#!/bin/bash\n# You are given a rectangular grid of wells. Each row represents a single well,\n# and each 1 in a row represents a single unit of water.\n# Each well has a corresponding bucket that can be used to extract water from it, \n# and all buckets have the same capacity.\n# Your task is to use the buckets to empty the wells.\n# Output the number of times you need to lower the buckets.\n# Example 1:\n# >>> $(max_fill \"0 0 1 0\\n0 1 0 0\\n1 1 1 1\" \"1\")\n# \"6\"\n# Example 2:\n# >>> $(max_fill \"0 0 1 1\\n0 0 0 0\\n1 1 1 1\\n0 1 1 1\" \"2\")\n# \"5\"\n# Example 3:\n# >>> $(max_fill \"0 0 0\\n0 0 0\" \"5\")\n# \"0\"\n# Constraints:\n# * all wells have the same length\n# * 1 <= grid.length <= 10^2\n# * 1 <= grid[:,1].length <= 10^2\n# * grid[i][j] -> 0 | 1\n# * 1 <= capacity <= 10\n#\n# $1 is a newline-separated, space-separated list\n# $2 is an integer\nmax_fill() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    max_fill \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0 0 1 0\\n0 1 0 0\\n1 1 1 1\" \"1\") = \"6\" ]]\n    [[ $(candidate \"0 0 1 1\\n0 0 0 0\\n1 1 1 1\\n0 1 1 1\" \"2\") = \"5\" ]]\n    [[ $(candidate \"0 0 0\\n0 0 0\" \"5\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 1\\n1 1 1 1\" \"2\") = \"4\" ]]\n    [[ $(candidate \"1 1 1 1\\n1 1 1 1\" \"9\") = \"2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_115_max_fill", "test": "}\n\ncandidate() {\n    max_fill \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0 0 1 0\\n0 1 0 0\\n1 1 1 1\" \"1\") = \"6\" ]]\n    [[ $(candidate \"0 0 1 1\\n0 0 0 0\\n1 1 1 1\\n0 1 1 1\" \"2\") = \"5\" ]]\n    [[ $(candidate \"0 0 0\\n0 0 0\" \"5\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 1\\n1 1 1 1\" \"2\") = \"4\" ]]\n    [[ $(candidate \"1 1 1 1\\n1 1 1 1\" \"9\") = \"2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_160_do_algebra", "language": "sh", "prompt": "#!/bin/bash\n# Given two lists operator, and operand. The first list has basic algebra operations, and \n# the second list is a list of integers. Use the two given lists to build the algebric \n# expression and return the evaluation of this expression.\n# The basic algebra operations:\n# Addition ( + ) \n# Subtraction ( - ) \n# Multiplication ( * ) \n# Floor division ( // ) \n# Exponentiation ( ** ) \n# Example:\n# operator['+', '*', '-']\n# array = [2, 3, 4, 5]\n# result = 2 + 3 * 4 - 5\n# => result = 9\n# Note:\n# The length of operator list is equal to the length of operand list minus one.\n# Operand is a list of of non-negative integers.\n# Operator list has at least one operator, and operand list has at least two operands.\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\ndo_algebra() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    do_algebra \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"** * +\" \"2 3 4 5\") = \"37\" ]]\n    [[ $(candidate \"+ * -\" \"2 3 4 5\") = \"9\" ]]\n    [[ $(candidate \"// *\" \"7 3 4\") = \"8\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_160_do_algebra", "test": "}\n\ncandidate() {\n    do_algebra \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"** * +\" \"2 3 4 5\") = \"37\" ]]\n    [[ $(candidate \"+ * -\" \"2 3 4 5\") = \"9\" ]]\n    [[ $(candidate \"// *\" \"7 3 4\") = \"8\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_27_flip_case", "language": "sh", "prompt": "#!/bin/bash\n# For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n# >>> $(flip_case \"Hello\")\n# \"hELLO\"\n#\n# $1 is a string\nflip_case() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    flip_case \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"Hello\\!\") = \"hELLO\\!\" ]]\n    [[ $(candidate \"These violent delights have violent ends\") = \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_27_flip_case", "test": "}\n\ncandidate() {\n    flip_case \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"Hello\\!\") = \"hELLO\\!\" ]]\n    [[ $(candidate \"These violent delights have violent ends\") = \"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_105_by_length", "language": "sh", "prompt": "#!/bin/bash\n# Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n# reverse the resulting array, and then replace each digit by its corresponding name from\n# \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n# For example:\n# >>> $(by_length \"2 1 1 4 5 8 2 3\")\n# ['\"Eight\"', '\"Five\"', '\"Four\"', '\"Three\"', '\"Two\"', '\"Two\"', '\"One\"', '\"One\"']\n# If the array is empty, return an empty array:\n# >>> $(by_length \"\")\n# []\n# If the array has any strange number ignore it:\n# >>> $(by_length \"1 -1 55\")\n# ['\"One\"']\n#\n# $1 is a space-separated list\nby_length() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    by_length \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 1 1 4 5 8 2 3\") = \"Eight Five Four Three Two Two One One\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 -1 55\") = \"One\" ]]\n    [[ $(candidate \"1 -1 3 2\") = \"Three Two One\" ]]\n    [[ $(candidate \"9 4 8\") = \"Nine Eight Four\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_105_by_length", "test": "}\n\ncandidate() {\n    by_length \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 1 1 4 5 8 2 3\") = \"Eight Five Four Three Two Two One One\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 -1 55\") = \"One\" ]]\n    [[ $(candidate \"1 -1 3 2\") = \"Three Two One\" ]]\n    [[ $(candidate \"9 4 8\") = \"Nine Eight Four\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_25_factorize", "language": "sh", "prompt": "#!/bin/bash\n# Return list of prime factors of given integer in the order from smallest to largest.\n# Each of the factors should be listed number of times corresponding to how many times it appeares in factorization.\n# Input number should be equal to the product of all factors\n# >>> $(factorize \"8\")\n# ['\"2\"', '\"2\"', '\"2\"']\n# >>> $(factorize \"25\")\n# ['\"5\"', '\"5\"']\n# >>> $(factorize \"70\")\n# ['\"2\"', '\"5\"', '\"7\"']\n#\n# $1 is an integer\nfactorize() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    factorize \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"2\" ]]\n    [[ $(candidate \"4\") = \"2 2\" ]]\n    [[ $(candidate \"8\") = \"2 2 2\" ]]\n    [[ $(candidate \"57\") = \"3 19\" ]]\n    [[ $(candidate \"3249\") = \"3 3 19 19\" ]]\n    [[ $(candidate \"185193\") = \"3 3 3 19 19 19\" ]]\n    [[ $(candidate \"20577\") = \"3 19 19 19\" ]]\n    [[ $(candidate \"18\") = \"2 3 3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_25_factorize", "test": "}\n\ncandidate() {\n    factorize \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\") = \"2\" ]]\n    [[ $(candidate \"4\") = \"2 2\" ]]\n    [[ $(candidate \"8\") = \"2 2 2\" ]]\n    [[ $(candidate \"57\") = \"3 19\" ]]\n    [[ $(candidate \"3249\") = \"3 3 19 19\" ]]\n    [[ $(candidate \"185193\") = \"3 3 3 19 19 19\" ]]\n    [[ $(candidate \"20577\") = \"3 19 19 19\" ]]\n    [[ $(candidate \"18\") = \"2 3 3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_96_count_up_to", "language": "sh", "prompt": "#!/bin/bash\n# Implement a function that takes an non-negative integer and returns an array of the first n\n# integers that are prime numbers and less than n.\n# for example:\n# >>> $(count_up_to \"5\")\n# ['\"2\"', '\"3\"']\n# >>> $(count_up_to \"11\")\n# ['\"2\"', '\"3\"', '\"5\"', '\"7\"']\n# >>> $(count_up_to \"0\")\n# []\n# >>> $(count_up_to \"20\")\n# ['\"2\"', '\"3\"', '\"5\"', '\"7\"', '\"11\"', '\"13\"', '\"17\"', '\"19\"']\n# >>> $(count_up_to \"1\")\n# []\n# >>> $(count_up_to \"18\")\n# ['\"2\"', '\"3\"', '\"5\"', '\"7\"', '\"11\"', '\"13\"', '\"17\"']\n#\n# $1 is an integer\ncount_up_to() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    count_up_to \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"2 3\" ]]\n    [[ $(candidate \"6\") = \"2 3 5\" ]]\n    [[ $(candidate \"7\") = \"2 3 5\" ]]\n    [[ $(candidate \"10\") = \"2 3 5 7\" ]]\n    [[ $(candidate \"0\") = \"\" ]]\n    [[ $(candidate \"22\") = \"2 3 5 7 11 13 17 19\" ]]\n    [[ $(candidate \"1\") = \"\" ]]\n    [[ $(candidate \"18\") = \"2 3 5 7 11 13 17\" ]]\n    [[ $(candidate \"47\") = \"2 3 5 7 11 13 17 19 23 29 31 37 41 43\" ]]\n    [[ $(candidate \"101\") = \"2 3 5 7 11 13 17 19 23 29 31 37 41 43 47 53 59 61 67 71 73 79 83 89 97\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_96_count_up_to", "test": "}\n\ncandidate() {\n    count_up_to \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"2 3\" ]]\n    [[ $(candidate \"6\") = \"2 3 5\" ]]\n    [[ $(candidate \"7\") = \"2 3 5\" ]]\n    [[ $(candidate \"10\") = \"2 3 5 7\" ]]\n    [[ $(candidate \"0\") = \"\" ]]\n    [[ $(candidate \"22\") = \"2 3 5 7 11 13 17 19\" ]]\n    [[ $(candidate \"1\") = \"\" ]]\n    [[ $(candidate \"18\") = \"2 3 5 7 11 13 17\" ]]\n    [[ $(candidate \"47\") = \"2 3 5 7 11 13 17 19 23 29 31 37 41 43\" ]]\n    [[ $(candidate \"101\") = \"2 3 5 7 11 13 17 19 23 29 31 37 41 43 47 53 59 61 67 71 73 79 83 89 97\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_34_unique", "language": "sh", "prompt": "#!/bin/bash\n# Return sorted unique elements in a list\n# >>> $(unique \"5 3 5 2 3 3 9 0 123\")\n# ['\"0\"', '\"2\"', '\"3\"', '\"5\"', '\"9\"', '\"123\"']\n#\n# $1 is a space-separated list\nunique() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    unique \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 3 5 2 3 3 9 0 123\") = \"0 2 3 5 9 123\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_34_unique", "test": "}\n\ncandidate() {\n    unique \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 3 5 2 3 3 9 0 123\") = \"0 2 3 5 9 123\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_74_total_match", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that accepts two lists of strings and returns the list that has \n# total number of chars in the all strings of the list less than the other list.\n# if the two lists have the same number of chars, return the first list.\n# Examples\n# >>> $(total_match \"\" \"\")\n# []\n# >>> $(total_match \"hi admin\" \"hI Hi\")\n# ['\"hI\"', '\"Hi\"']\n# >>> $(total_match \"hi admin\" \"hi hi admin project\")\n# ['\"hi\"', '\"admin\"']\n# >>> $(total_match \"hi admin\" \"hI hi hi\")\n# ['\"hI\"', '\"hi\"', '\"hi\"']\n# >>> $(total_match \"4\" \"1 2 3 4 5\")\n# ['\"4\"']\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\ntotal_match() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    total_match \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"\") = \"\" ]]\n    [[ $(candidate \"hi admin\" \"hi hi\") = \"hi hi\" ]]\n    [[ $(candidate \"hi admin\" \"hi hi admin project\") = \"hi admin\" ]]\n    [[ $(candidate \"4\" \"1 2 3 4 5\") = \"4\" ]]\n    [[ $(candidate \"hi admin\" \"hI Hi\") = \"hI Hi\" ]]\n    [[ $(candidate \"hi admin\" \"hI hi hi\") = \"hI hi hi\" ]]\n    [[ $(candidate \"hi admin\" \"hI hi hii\") = \"hi admin\" ]]\n    [[ $(candidate \"\" \"this\") = \"\" ]]\n    [[ $(candidate \"this\" \"\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_74_total_match", "test": "}\n\ncandidate() {\n    total_match \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"\") = \"\" ]]\n    [[ $(candidate \"hi admin\" \"hi hi\") = \"hi hi\" ]]\n    [[ $(candidate \"hi admin\" \"hi hi admin project\") = \"hi admin\" ]]\n    [[ $(candidate \"4\" \"1 2 3 4 5\") = \"4\" ]]\n    [[ $(candidate \"hi admin\" \"hI Hi\") = \"hI Hi\" ]]\n    [[ $(candidate \"hi admin\" \"hI hi hi\") = \"hI hi hi\" ]]\n    [[ $(candidate \"hi admin\" \"hI hi hii\") = \"hi admin\" ]]\n    [[ $(candidate \"\" \"this\") = \"\" ]]\n    [[ $(candidate \"this\" \"\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_35_max_element", "language": "sh", "prompt": "#!/bin/bash\n# Return maximum element in the list.\n# >>> $(max_element \"1 2 3\")\n# \"3\"\n# >>> $(max_element \"5 3 -5 2 -3 3 9 0 123 1 -10\")\n# \"123\"\n#\n# $1 is a space-separated list\nmax_element() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    max_element \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"3\" ]]\n    [[ $(candidate \"5 3 -5 2 -3 3 9 0 124 1 -10\") = \"124\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_35_max_element", "test": "}\n\ncandidate() {\n    max_element \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"3\" ]]\n    [[ $(candidate \"5 3 -5 2 -3 3 9 0 124 1 -10\") = \"124\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_132_is_nested", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that takes a string as input which contains only square brackets.\n# The function should return true if and only if there is a valid subsequence of brackets \n# where at least one bracket in the subsequence is nested.\n# >>> $(is_nested \"[[]]\")\n# \"true\"\n# >>> $(is_nested \"[]]]]]]][[[[[]\")\n# \"false\"\n# >>> $(is_nested \"[][]\")\n# \"false\"\n# >>> $(is_nested \"[]\")\n# \"false\"\n# >>> $(is_nested \"[[][]]\")\n# \"true\"\n# >>> $(is_nested \"[[]][[\")\n# \"true\"\n#\n# $1 is a string\nis_nested() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_nested \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"[[]]\") = \"true\" ]]\n    [[ $(candidate \"[]]]]]]][[[[[]\") = \"false\" ]]\n    [[ $(candidate \"[][]\") = \"false\" ]]\n    [[ $(candidate \"[]\") = \"false\" ]]\n    [[ $(candidate \"[[[[]]]]\") = \"true\" ]]\n    [[ $(candidate \"[]]]]]]]]]]\") = \"false\" ]]\n    [[ $(candidate \"[][][[]]\") = \"true\" ]]\n    [[ $(candidate \"[[]\") = \"false\" ]]\n    [[ $(candidate \"[]]\") = \"false\" ]]\n    [[ $(candidate \"[[]][[\") = \"true\" ]]\n    [[ $(candidate \"[[][]]\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"[[[[[[[[\") = \"false\" ]]\n    [[ $(candidate \"]]]]]]]]\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_132_is_nested", "test": "}\n\ncandidate() {\n    is_nested \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"[[]]\") = \"true\" ]]\n    [[ $(candidate \"[]]]]]]][[[[[]\") = \"false\" ]]\n    [[ $(candidate \"[][]\") = \"false\" ]]\n    [[ $(candidate \"[]\") = \"false\" ]]\n    [[ $(candidate \"[[[[]]]]\") = \"true\" ]]\n    [[ $(candidate \"[]]]]]]]]]]\") = \"false\" ]]\n    [[ $(candidate \"[][][[]]\") = \"true\" ]]\n    [[ $(candidate \"[[]\") = \"false\" ]]\n    [[ $(candidate \"[]]\") = \"false\" ]]\n    [[ $(candidate \"[[]][[\") = \"true\" ]]\n    [[ $(candidate \"[[][]]\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"[[[[[[[[\") = \"false\" ]]\n    [[ $(candidate \"]]]]]]]]\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_103_rounded_avg", "language": "sh", "prompt": "#!/bin/bash\n# You are given two positive integers n and m, and your task is to compute the\n# average of the integers from n through m (including n and m). \n# Round the answer to the nearest integer and convert that to binary.\n# If n is greater than m, return -1.\n# Example:\n# >>> $(rounded_avg \"1\" \"5\")\n# \"0b11\"\n# >>> $(rounded_avg \"7\" \"5\")\n# \"-1\"\n# >>> $(rounded_avg \"10\" \"20\")\n# \"0b1111\"\n# >>> $(rounded_avg \"20\" \"33\")\n# \"0b11010\"\n#\n# $1 is an integer\n# $2 is an integer\nrounded_avg() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    rounded_avg \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\" \"5\") = \"0b11\" ]]\n    [[ $(candidate \"7\" \"13\") = \"0b1010\" ]]\n    [[ $(candidate \"964\" \"977\") = \"0b1111001010\" ]]\n    [[ $(candidate \"996\" \"997\") = \"0b1111100100\" ]]\n    [[ $(candidate \"560\" \"851\") = \"0b1011000010\" ]]\n    [[ $(candidate \"185\" \"546\") = \"0b101101110\" ]]\n    [[ $(candidate \"362\" \"496\") = \"0b110101101\" ]]\n    [[ $(candidate \"350\" \"902\") = \"0b1001110010\" ]]\n    [[ $(candidate \"197\" \"233\") = \"0b11010111\" ]]\n    [[ $(candidate \"7\" \"5\") = \"-1\" ]]\n    [[ $(candidate \"5\" \"1\") = \"-1\" ]]\n    [[ $(candidate \"5\" \"5\") = \"0b101\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_103_rounded_avg", "test": "}\n\ncandidate() {\n    rounded_avg \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\" \"5\") = \"0b11\" ]]\n    [[ $(candidate \"7\" \"13\") = \"0b1010\" ]]\n    [[ $(candidate \"964\" \"977\") = \"0b1111001010\" ]]\n    [[ $(candidate \"996\" \"997\") = \"0b1111100100\" ]]\n    [[ $(candidate \"560\" \"851\") = \"0b1011000010\" ]]\n    [[ $(candidate \"185\" \"546\") = \"0b101101110\" ]]\n    [[ $(candidate \"362\" \"496\") = \"0b110101101\" ]]\n    [[ $(candidate \"350\" \"902\") = \"0b1001110010\" ]]\n    [[ $(candidate \"197\" \"233\") = \"0b11010111\" ]]\n    [[ $(candidate \"7\" \"5\") = \"-1\" ]]\n    [[ $(candidate \"5\" \"1\") = \"-1\" ]]\n    [[ $(candidate \"5\" \"5\") = \"0b101\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_109_move_one_ball", "language": "sh", "prompt": "#!/bin/bash\n# We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n# numbers in the array will be randomly ordered. Your task is to determine if\n# it is possible to get an array sorted in non-decreasing order by performing \n# the following operation on the given array:\n# You are allowed to perform right shift operation any number of times.\n# One right shift operation means shifting all elements of the array by one\n# position in the right direction. The last element of the array will be moved to\n# the starting position in the array i.e. 0th index. \n# If it is possible to obtain the sorted array by performing the above operation\n# then return true else return false.\n# If the given array is empty then return true.\n# Note: The given list is guaranteed to have unique elements.\n# For Example:\n# >>> $(move_one_ball \"3 4 5 1 2\")\n# \"true\"\n# Explanation: By performin 2 right shift operations, non-decreasing order can\n# be achieved for the given array.\n# >>> $(move_one_ball \"3 5 4 1 2\")\n# \"false\"\n# Explanation:It is not possible to get non-decreasing order for the given\n# array by performing any number of right shift operations.\n#\n# $1 is a space-separated list\nmove_one_ball() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    move_one_ball \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 4 5 1 2\") = \"true\" ]]\n    [[ $(candidate \"3 5 10 1 2\") = \"true\" ]]\n    [[ $(candidate \"4 3 1 2\") = \"false\" ]]\n    [[ $(candidate \"3 5 4 1 2\") = \"false\" ]]\n    [[ $(candidate \"\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_109_move_one_ball", "test": "}\n\ncandidate() {\n    move_one_ball \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 4 5 1 2\") = \"true\" ]]\n    [[ $(candidate \"3 5 10 1 2\") = \"true\" ]]\n    [[ $(candidate \"4 3 1 2\") = \"false\" ]]\n    [[ $(candidate \"3 5 4 1 2\") = \"false\" ]]\n    [[ $(candidate \"\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer n, return a list that has the number of even and odd\n# integer palindromes that fall within the range(1, n), inclusive.\n# Example 1:\n# >>> $(even_odd_palindrome \"3\")\n# ['\"1\"', '\"2\"']\n# Explanation:\n# Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n# Example 2:\n# >>> $(even_odd_palindrome \"12\")\n# ['\"4\"', '\"6\"']\n# Explanation:\n# Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n# Note:\n# 1. 1 <= n <= 10^3\n# 2. returned list has the number of even and odd integer palindromes respectively.\n#\n# $1 is an integer\neven_odd_palindrome() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    even_odd_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"123\") = \"8 13\" ]]\n    [[ $(candidate \"12\") = \"4 6\" ]]\n    [[ $(candidate \"3\") = \"1 2\" ]]\n    [[ $(candidate \"63\") = \"6 8\" ]]\n    [[ $(candidate \"25\") = \"5 6\" ]]\n    [[ $(candidate \"19\") = \"4 6\" ]]\n    [[ $(candidate \"9\") = \"4 5\" ]]\n    [[ $(candidate \"1\") = \"0 1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "}\n\ncandidate() {\n    even_odd_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"123\") = \"8 13\" ]]\n    [[ $(candidate \"12\") = \"4 6\" ]]\n    [[ $(candidate \"3\") = \"1 2\" ]]\n    [[ $(candidate \"63\") = \"6 8\" ]]\n    [[ $(candidate \"25\") = \"5 6\" ]]\n    [[ $(candidate \"19\") = \"4 6\" ]]\n    [[ $(candidate \"9\") = \"4 5\" ]]\n    [[ $(candidate \"1\") = \"0 1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "sh", "prompt": "#!/bin/bash\n# Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n# Example\n# >>> $(is_equal_to_sum_even \"4\")\n# \"false\"\n# >>> $(is_equal_to_sum_even \"6\")\n# \"false\"\n# >>> $(is_equal_to_sum_even \"8\")\n# \"true\"\n#\n# $1 is an integer\nis_equal_to_sum_even() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_equal_to_sum_even \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4\") = \"false\" ]]\n    [[ $(candidate \"6\") = \"false\" ]]\n    [[ $(candidate \"8\") = \"true\" ]]\n    [[ $(candidate \"10\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"false\" ]]\n    [[ $(candidate \"12\") = \"true\" ]]\n    [[ $(candidate \"13\") = \"false\" ]]\n    [[ $(candidate \"16\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "}\n\ncandidate() {\n    is_equal_to_sum_even \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4\") = \"false\" ]]\n    [[ $(candidate \"6\") = \"false\" ]]\n    [[ $(candidate \"8\") = \"true\" ]]\n    [[ $(candidate \"10\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"false\" ]]\n    [[ $(candidate \"12\") = \"true\" ]]\n    [[ $(candidate \"13\") = \"false\" ]]\n    [[ $(candidate \"16\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_62_derivative", "language": "sh", "prompt": "#!/bin/bash\n# xs represent coefficients of a polynomial.\n# xs[0] + xs[1] * x + xs[2] * x^2 + ....\n# Return derivative of this polynomial in the same form.\n# >>> $(derivative \"3 1 2 4 5\")\n# ['\"1\"', '\"4\"', '\"12\"', '\"20\"']\n# >>> $(derivative \"1 2 3\")\n# ['\"2\"', '\"6\"']\n#\n# $1 is a space-separated list\nderivative() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    derivative \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 1 2 4 5\") = \"1 4 12 20\" ]]\n    [[ $(candidate \"1 2 3\") = \"2 6\" ]]\n    [[ $(candidate \"3 2 1\") = \"2 2\" ]]\n    [[ $(candidate \"3 2 1 0 4\") = \"2 2 0 16\" ]]\n    [[ $(candidate \"1\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_62_derivative", "test": "}\n\ncandidate() {\n    derivative \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 1 2 4 5\") = \"1 4 12 20\" ]]\n    [[ $(candidate \"1 2 3\") = \"2 6\" ]]\n    [[ $(candidate \"3 2 1\") = \"2 2\" ]]\n    [[ $(candidate \"3 2 1 0 4\") = \"2 2 0 16\" ]]\n    [[ $(candidate \"1\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_126_is_sorted", "language": "sh", "prompt": "#!/bin/bash\n# Given a list of numbers, return whether or not they are sorted\n# in ascending order. If list has more than 1 duplicate of the same\n# number, return false. Assume no negative numbers and only integers.\n# Examples\n# >>> $(is_sorted \"5\")\n# \"true\"\n# >>> $(is_sorted \"1 2 3 4 5\")\n# \"true\"\n# >>> $(is_sorted \"1 3 2 4 5\")\n# \"false\"\n# >>> $(is_sorted \"1 2 3 4 5 6\")\n# \"true\"\n# >>> $(is_sorted \"1 2 3 4 5 6 7\")\n# \"true\"\n# >>> $(is_sorted \"1 3 2 4 5 6 7\")\n# \"false\"\n# >>> $(is_sorted \"1 2 2 3 3 4\")\n# \"true\"\n# >>> $(is_sorted \"1 2 2 2 3 4\")\n# \"false\"\n#\n# $1 is a space-separated list\nis_sorted() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_sorted \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4 5\") = \"true\" ]]\n    [[ $(candidate \"1 3 2 4 5\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7\") = \"true\" ]]\n    [[ $(candidate \"1 3 2 4 5 6 7\") = \"false\" ]]\n    [[ $(candidate \"\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"true\" ]]\n    [[ $(candidate \"3 2 1\") = \"false\" ]]\n    [[ $(candidate \"1 2 2 2 3 4\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 3 3 4\") = \"false\" ]]\n    [[ $(candidate \"1 2 2 3 3 4\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_126_is_sorted", "test": "}\n\ncandidate() {\n    is_sorted \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4 5\") = \"true\" ]]\n    [[ $(candidate \"1 3 2 4 5\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7\") = \"true\" ]]\n    [[ $(candidate \"1 3 2 4 5 6 7\") = \"false\" ]]\n    [[ $(candidate \"\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"true\" ]]\n    [[ $(candidate \"3 2 1\") = \"false\" ]]\n    [[ $(candidate \"1 2 2 2 3 4\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 3 3 4\") = \"false\" ]]\n    [[ $(candidate \"1 2 2 3 3 4\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_161_solve", "language": "sh", "prompt": "#!/bin/bash\n# You are given a string s.\n# if s[i] is a letter, reverse its case from lower to upper or vise versa, \n# otherwise keep it as it is.\n# If the string contains no letters, reverse the string.\n# The function should return the resulted string.\n# Examples\n# >>> $(solve \"1234\")\n# \"4321\"\n# >>> $(solve \"ab\")\n# \"AB\"\n# >>> $(solve \"#a@C\")\n# \"#A@c\"\n#\n# $1 is a string\nsolve() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    solve \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"AsDf\") = \"aSdF\" ]]\n    [[ $(candidate \"1234\") = \"4321\" ]]\n    [[ $(candidate \"ab\") = \"AB\" ]]\n    [[ $(candidate \"#a@C\") = \"#A@c\" ]]\n    [[ $(candidate \"#AsdfW^45\") = \"#aSDFw^45\" ]]\n    [[ $(candidate \"#6@2\") = \"2@6#\" ]]\n    [[ $(candidate \"#\\$a^D\") = \"#\\$A^d\" ]]\n    [[ $(candidate \"#ccc\") = \"#CCC\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_161_solve", "test": "}\n\ncandidate() {\n    solve \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"AsDf\") = \"aSdF\" ]]\n    [[ $(candidate \"1234\") = \"4321\" ]]\n    [[ $(candidate \"ab\") = \"AB\" ]]\n    [[ $(candidate \"#a@C\") = \"#A@c\" ]]\n    [[ $(candidate \"#AsdfW^45\") = \"#aSDFw^45\" ]]\n    [[ $(candidate \"#6@2\") = \"2@6#\" ]]\n    [[ $(candidate \"#\\$a^D\") = \"#\\$A^d\" ]]\n    [[ $(candidate \"#ccc\") = \"#CCC\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_130_tri", "language": "sh", "prompt": "#!/bin/bash\n# Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n# the last couple centuries. However, what people don't know is Tribonacci sequence.\n# Tribonacci sequence is defined by the recurrence:\n# tri(1) = 3\n# tri(n) = 1 + n / 2, if n is even.\n# tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n# For example:\n# tri(2) = 1 + (2 / 2) = 2\n# tri(4) = 3\n# tri(3) = tri(2) + tri(1) + tri(4)\n# = 2 + 3 + 3 = 8 \n# You are given a non-negative integer number n, you have to a return a list of the \n# first n + 1 numbers of the Tribonacci sequence.\n# Examples:\n# >>> $(tri \"3\")\n# ['\"1\"', '\"3\"', '\"2\"', '\"8\"']\n#\n# $1 is an integer\ntri() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    tri \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"1 3 2 8\" ]]\n    [[ $(candidate \"4\") = \"1 3 2 8 3\" ]]\n    [[ $(candidate \"5\") = \"1 3 2 8 3 15\" ]]\n    [[ $(candidate \"6\") = \"1 3 2 8 3 15 4\" ]]\n    [[ $(candidate \"7\") = \"1 3 2 8 3 15 4 24\" ]]\n    [[ $(candidate \"8\") = \"1 3 2 8 3 15 4 24 5\" ]]\n    [[ $(candidate \"9\") = \"1 3 2 8 3 15 4 24 5 35\" ]]\n    [[ $(candidate \"20\") = \"1 3 2 8 3 15 4 24 5 35 6 48 7 63 8 80 9 99 10 120 11\" ]]\n    [[ $(candidate \"0\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"1 3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_130_tri", "test": "}\n\ncandidate() {\n    tri \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"1 3 2 8\" ]]\n    [[ $(candidate \"4\") = \"1 3 2 8 3\" ]]\n    [[ $(candidate \"5\") = \"1 3 2 8 3 15\" ]]\n    [[ $(candidate \"6\") = \"1 3 2 8 3 15 4\" ]]\n    [[ $(candidate \"7\") = \"1 3 2 8 3 15 4 24\" ]]\n    [[ $(candidate \"8\") = \"1 3 2 8 3 15 4 24 5\" ]]\n    [[ $(candidate \"9\") = \"1 3 2 8 3 15 4 24 5 35\" ]]\n    [[ $(candidate \"20\") = \"1 3 2 8 3 15 4 24 5 35 6 48 7 63 8 80 9 99 10 120 11\" ]]\n    [[ $(candidate \"0\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"1 3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_36_fizz_buzz", "language": "sh", "prompt": "#!/bin/bash\n# Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n# >>> $(fizz_buzz \"50\")\n# \"0\"\n# >>> $(fizz_buzz \"78\")\n# \"2\"\n# >>> $(fizz_buzz \"79\")\n# \"3\"\n#\n# $1 is an integer\nfizz_buzz() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fizz_buzz \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"50\") = \"0\" ]]\n    [[ $(candidate \"78\") = \"2\" ]]\n    [[ $(candidate \"79\") = \"3\" ]]\n    [[ $(candidate \"100\") = \"3\" ]]\n    [[ $(candidate \"200\") = \"6\" ]]\n    [[ $(candidate \"4000\") = \"192\" ]]\n    [[ $(candidate \"10000\") = \"639\" ]]\n    [[ $(candidate \"100000\") = \"8026\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_36_fizz_buzz", "test": "}\n\ncandidate() {\n    fizz_buzz \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"50\") = \"0\" ]]\n    [[ $(candidate \"78\") = \"2\" ]]\n    [[ $(candidate \"79\") = \"3\" ]]\n    [[ $(candidate \"100\") = \"3\" ]]\n    [[ $(candidate \"200\") = \"6\" ]]\n    [[ $(candidate \"4000\") = \"192\" ]]\n    [[ $(candidate \"10000\") = \"639\" ]]\n    [[ $(candidate \"100000\") = \"8026\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_84_solve", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer N, return the total sum of its digits in binary.\n# Example\n# >>> $(solve \"1000\")\n# \"1\"\n# >>> $(solve \"150\")\n# \"110\"\n# >>> $(solve \"147\")\n# \"1100\"\n# Variables:\n# @N integer\n# Constraints: 0 \u2264 N \u2264 10000.\n# Output:\n# a string of binary number\n#\n# $1 is an integer\nsolve() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    solve \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1000\") = \"1\" ]]\n    [[ $(candidate \"150\") = \"110\" ]]\n    [[ $(candidate \"147\") = \"1100\" ]]\n    [[ $(candidate \"333\") = \"1001\" ]]\n    [[ $(candidate \"963\") = \"10010\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_84_solve", "test": "}\n\ncandidate() {\n    solve \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1000\") = \"1\" ]]\n    [[ $(candidate \"150\") = \"110\" ]]\n    [[ $(candidate \"147\") = \"1100\" ]]\n    [[ $(candidate \"333\") = \"1001\" ]]\n    [[ $(candidate \"963\") = \"10010\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_129_minPath", "language": "sh", "prompt": "#!/bin/bash\n# Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n# each cell of the grid contains a value. Every integer in the range [1, N * N]\n# inclusive appears exactly once on the cells of the grid.\n# You have to find the minimum path of length k in the grid. You can start\n# from any cell, and in each step you can move to any of the neighbor cells,\n# in other words, you can go to cells which share an edge with you current\n# cell.\n# Please note that a path of length k means visiting exactly k cells (not\n# necessarily distinct).\n# You CANNOT go off the grid.\n# A path A (of length k) is considered less than a path B (of length k) if\n# after making the ordered lists of the values on the cells that A and B go\n# through (let's call them lst_A and lst_B), lst_A is lexicographically less\n# than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n# such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n# lst_A[j] = lst_B[j].\n# It is guaranteed that the answer is unique.\n# Return an ordered list of the values on the cells that the minimum path go through.\n# Examples:    \n# >>> $(minPath \"1 2 3\\n4 5 6\\n7 8 9\" \"3\")\n# ['\"1\"', '\"2\"', '\"1\"']\n# >>> $(minPath \"5 9 3\\n4 1 6\\n7 8 2\" \"1\")\n# ['\"1\"']\n#\n# $1 is a newline-separated, space-separated list\n# $2 is an integer\nminPath() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    minPath \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\\n4 5 6\\n7 8 9\" \"3\") = \"1 2 1\" ]]\n    [[ $(candidate \"5 9 3\\n4 1 6\\n7 8 2\" \"1\") = \"1\" ]]\n    [[ $(candidate \"1 2 3 4\\n5 6 7 8\\n9 10 11 12\\n13 14 15 16\" \"4\") = \"1 2 1 2\" ]]\n    [[ $(candidate \"6 4 13 10\\n5 7 12 1\\n3 16 11 15\\n8 14 9 2\" \"7\") = \"1 10 1 10 1 10 1\" ]]\n    [[ $(candidate \"8 14 9 2\\n6 4 13 15\\n5 7 1 12\\n3 10 11 16\" \"5\") = \"1 7 1 7 1\" ]]\n    [[ $(candidate \"11 8 7 2\\n5 16 14 4\\n9 3 15 6\\n12 13 10 1\" \"9\") = \"1 6 1 6 1 6 1 6 1\" ]]\n    [[ $(candidate \"12 13 10 1\\n9 3 15 6\\n5 16 14 4\\n11 8 7 2\" \"12\") = \"1 6 1 6 1 6 1 6 1 6 1 6\" ]]\n    [[ $(candidate \"2 7 4\\n3 1 5\\n6 8 9\" \"8\") = \"1 3 1 3 1 3 1 3\" ]]\n    [[ $(candidate \"6 1 5\\n3 8 9\\n2 7 4\" \"8\") = \"1 5 1 5 1 5 1 5\" ]]\n    [[ $(candidate \"1 2\\n3 4\" \"10\") = \"1 2 1 2 1 2 1 2 1 2\" ]]\n    [[ $(candidate \"1 3\\n3 2\" \"10\") = \"1 3 1 3 1 3 1 3 1 3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_129_minPath", "test": "}\n\ncandidate() {\n    minPath \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\\n4 5 6\\n7 8 9\" \"3\") = \"1 2 1\" ]]\n    [[ $(candidate \"5 9 3\\n4 1 6\\n7 8 2\" \"1\") = \"1\" ]]\n    [[ $(candidate \"1 2 3 4\\n5 6 7 8\\n9 10 11 12\\n13 14 15 16\" \"4\") = \"1 2 1 2\" ]]\n    [[ $(candidate \"6 4 13 10\\n5 7 12 1\\n3 16 11 15\\n8 14 9 2\" \"7\") = \"1 10 1 10 1 10 1\" ]]\n    [[ $(candidate \"8 14 9 2\\n6 4 13 15\\n5 7 1 12\\n3 10 11 16\" \"5\") = \"1 7 1 7 1\" ]]\n    [[ $(candidate \"11 8 7 2\\n5 16 14 4\\n9 3 15 6\\n12 13 10 1\" \"9\") = \"1 6 1 6 1 6 1 6 1\" ]]\n    [[ $(candidate \"12 13 10 1\\n9 3 15 6\\n5 16 14 4\\n11 8 7 2\" \"12\") = \"1 6 1 6 1 6 1 6 1 6 1 6\" ]]\n    [[ $(candidate \"2 7 4\\n3 1 5\\n6 8 9\" \"8\") = \"1 3 1 3 1 3 1 3\" ]]\n    [[ $(candidate \"6 1 5\\n3 8 9\\n2 7 4\" \"8\") = \"1 5 1 5 1 5 1 5\" ]]\n    [[ $(candidate \"1 2\\n3 4\" \"10\") = \"1 2 1 2 1 2 1 2 1 2\" ]]\n    [[ $(candidate \"1 3\\n3 2\" \"10\") = \"1 3 1 3 1 3 1 3 1 3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_98_count_upper", "language": "sh", "prompt": "#!/bin/bash\n# Given a string s, count the number of uppercase vowels in even indices.\n# For example:\n# >>> $(count_upper \"aBCdEf\")\n# \"1\"\n# >>> $(count_upper \"abcdefg\")\n# \"0\"\n# >>> $(count_upper \"dBBE\")\n# \"0\"\n#\n# $1 is a string\ncount_upper() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    count_upper \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"aBCdEf\") = \"1\" ]]\n    [[ $(candidate \"abcdefg\") = \"0\" ]]\n    [[ $(candidate \"dBBE\") = \"0\" ]]\n    [[ $(candidate \"B\") = \"0\" ]]\n    [[ $(candidate \"U\") = \"1\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"EEEE\") = \"2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_98_count_upper", "test": "}\n\ncandidate() {\n    count_upper \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"aBCdEf\") = \"1\" ]]\n    [[ $(candidate \"abcdefg\") = \"0\" ]]\n    [[ $(candidate \"dBBE\") = \"0\" ]]\n    [[ $(candidate \"B\") = \"0\" ]]\n    [[ $(candidate \"U\") = \"1\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"EEEE\") = \"2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_120_maximum", "language": "sh", "prompt": "#!/bin/bash\n# Given an array arr of integers and a positive integer k, return a sorted list \n# of length k with the maximum k numbers in arr.\n# Example 1:\n# >>> $(maximum \"-3 -4 5\" \"3\")\n# ['\"-4\"', '\"-3\"', '\"5\"']\n# Example 2:\n# >>> $(maximum \"4 -4 4\" \"2\")\n# ['\"4\"', '\"4\"']\n# Example 3:\n# >>> $(maximum \"-3 2 1 2 -1 -2 1\" \"1\")\n# ['\"2\"']\n# Note:\n# 1. The length of the array will be in the range of [1, 1000].\n# 2. The elements in the array will be in the range of [-1000, 1000].\n# 3. 0 <= k <= len(arr)\n#\n# $1 is a space-separated list\n# $2 is an integer\nmaximum() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    maximum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"-3 -4 5\" \"3\") = \"-4 -3 5\" ]]\n    [[ $(candidate \"4 -4 4\" \"2\") = \"4 4\" ]]\n    [[ $(candidate \"-3 2 1 2 -1 -2 1\" \"1\") = \"2\" ]]\n    [[ $(candidate \"123 -123 20 0 1 2 -3\" \"3\") = \"2 20 123\" ]]\n    [[ $(candidate \"-123 20 0 1 2 -3\" \"4\") = \"0 1 2 20\" ]]\n    [[ $(candidate \"5 15 0 3 -13 -8 0\" \"7\") = \"-13 -8 0 0 3 5 15\" ]]\n    [[ $(candidate \"-1 0 2 5 3 -10\" \"2\") = \"3 5\" ]]\n    [[ $(candidate \"1 0 5 -7\" \"1\") = \"5\" ]]\n    [[ $(candidate \"4 -4\" \"2\") = \"-4 4\" ]]\n    [[ $(candidate \"-10 10\" \"2\") = \"-10 10\" ]]\n    [[ $(candidate \"1 2 3 -23 243 -400 0\" \"0\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_120_maximum", "test": "}\n\ncandidate() {\n    maximum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"-3 -4 5\" \"3\") = \"-4 -3 5\" ]]\n    [[ $(candidate \"4 -4 4\" \"2\") = \"4 4\" ]]\n    [[ $(candidate \"-3 2 1 2 -1 -2 1\" \"1\") = \"2\" ]]\n    [[ $(candidate \"123 -123 20 0 1 2 -3\" \"3\") = \"2 20 123\" ]]\n    [[ $(candidate \"-123 20 0 1 2 -3\" \"4\") = \"0 1 2 20\" ]]\n    [[ $(candidate \"5 15 0 3 -13 -8 0\" \"7\") = \"-13 -8 0 0 3 5 15\" ]]\n    [[ $(candidate \"-1 0 2 5 3 -10\" \"2\") = \"3 5\" ]]\n    [[ $(candidate \"1 0 5 -7\" \"1\") = \"5\" ]]\n    [[ $(candidate \"4 -4\" \"2\") = \"-4 4\" ]]\n    [[ $(candidate \"-10 10\" \"2\") = \"-10 10\" ]]\n    [[ $(candidate \"1 2 3 -23 243 -400 0\" \"0\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_24_largest_divisor", "language": "sh", "prompt": "#!/bin/bash\n# For a given number n, find the largest number that divides n evenly, smaller than n\n# >>> $(largest_divisor \"15\")\n# \"5\"\n#\n# $1 is an integer\nlargest_divisor() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    largest_divisor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"1\" ]]\n    [[ $(candidate \"7\") = \"1\" ]]\n    [[ $(candidate \"10\") = \"5\" ]]\n    [[ $(candidate \"100\") = \"50\" ]]\n    [[ $(candidate \"49\") = \"7\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_24_largest_divisor", "test": "}\n\ncandidate() {\n    largest_divisor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"1\" ]]\n    [[ $(candidate \"7\") = \"1\" ]]\n    [[ $(candidate \"10\") = \"5\" ]]\n    [[ $(candidate \"100\") = \"50\" ]]\n    [[ $(candidate \"49\") = \"7\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_88_sort_array", "language": "sh", "prompt": "#!/bin/bash\n# Given an array of non-negative integers, return a cosh of the given array after sorting,\n# you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n# or sort it in descending order if the sum( first index value, last index value) is even.\n# Note:\n# * don't change the given array.\n# Examples:\n# >>> $(sort_array \"\")\n# []\n# >>> $(sort_array \"5\")\n# ['\"5\"']\n# >>> $(sort_array \"2 4 3 0 1 5\")\n# ['\"0\"', '\"1\"', '\"2\"', '\"3\"', '\"4\"', '\"5\"']\n# >>> $(sort_array \"2 4 3 0 1 5 6\")\n# ['\"6\"', '\"5\"', '\"4\"', '\"3\"', '\"2\"', '\"1\"', '\"0\"']\n#\n# $1 is a space-separated list\nsort_array() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sort_array \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"2 4 3 0 1 5\") = \"0 1 2 3 4 5\" ]]\n    [[ $(candidate \"2 4 3 0 1 5 6\") = \"6 5 4 3 2 1 0\" ]]\n    [[ $(candidate \"2 1\") = \"1 2\" ]]\n    [[ $(candidate \"15 42 87 32 11 0\") = \"0 11 15 32 42 87\" ]]\n    [[ $(candidate \"21 14 23 11\") = \"23 21 14 11\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_88_sort_array", "test": "}\n\ncandidate() {\n    sort_array \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"2 4 3 0 1 5\") = \"0 1 2 3 4 5\" ]]\n    [[ $(candidate \"2 4 3 0 1 5 6\") = \"6 5 4 3 2 1 0\" ]]\n    [[ $(candidate \"2 1\") = \"1 2\" ]]\n    [[ $(candidate \"15 42 87 32 11 0\") = \"0 11 15 32 42 87\" ]]\n    [[ $(candidate \"21 14 23 11\") = \"23 21 14 11\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_106_f", "language": "sh", "prompt": "#!/bin/bash\n# Implement the function f that takes n as a parameter,\n# and returns a list of size n, such that the value of the element at index i is the factorial of i if i is even\n# or the sum of numbers from 1 to i otherwise.\n# i starts from 1.\n# the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n# Example:\n# >>> $(f \"5\")\n# ['\"1\"', '\"2\"', '\"6\"', '\"24\"', '\"15\"']\n#\n# $1 is an integer\nf() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    f \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"1 2 6 24 15\" ]]\n    [[ $(candidate \"7\") = \"1 2 6 24 15 720 28\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"3\") = \"1 2 6\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_106_f", "test": "}\n\ncandidate() {\n    f \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"1 2 6 24 15\" ]]\n    [[ $(candidate \"7\") = \"1 2 6 24 15 720 28\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"3\") = \"1 2 6\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_77_iscube", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that takes an integer a and returns true \n# if this ingeger is a cube of some integer number.\n# Note: you may assume the input is always valid.\n# Examples:\n# >>> $(iscube \"1\")\n# \"true\"\n# >>> $(iscube \"2\")\n# \"false\"\n# >>> $(iscube \"-1\")\n# \"true\"\n# >>> $(iscube \"64\")\n# \"true\"\n# >>> $(iscube \"0\")\n# \"true\"\n# >>> $(iscube \"180\")\n# \"false\"\n#\n# $1 is an integer\niscube() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    iscube \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"true\" ]]\n    [[ $(candidate \"2\") = \"false\" ]]\n    [[ $(candidate \"-1\") = \"true\" ]]\n    [[ $(candidate \"64\") = \"true\" ]]\n    [[ $(candidate \"180\") = \"false\" ]]\n    [[ $(candidate \"1000\") = \"true\" ]]\n    [[ $(candidate \"0\") = \"true\" ]]\n    [[ $(candidate \"1729\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_77_iscube", "test": "}\n\ncandidate() {\n    iscube \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"true\" ]]\n    [[ $(candidate \"2\") = \"false\" ]]\n    [[ $(candidate \"-1\") = \"true\" ]]\n    [[ $(candidate \"64\") = \"true\" ]]\n    [[ $(candidate \"180\") = \"false\" ]]\n    [[ $(candidate \"1000\") = \"true\" ]]\n    [[ $(candidate \"0\") = \"true\" ]]\n    [[ $(candidate \"1729\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_93_encode", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that takes a message, and encodes in such a \n# way that it swaps case of all letters, replaces all vowels in \n# the message with the letter that appears 2 places ahead of that \n# vowel in the english alphabet. \n# Assume only letters. \n# Examples:\n# >>> $(encode \"test\")\n# \"TGST\"\n# >>> $(encode \"This is a message\")\n# \"tHKS KS C MGSSCGG\"\n#\n# $1 is a string\nencode() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    encode \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"TEST\") = \"tgst\" ]]\n    [[ $(candidate \"Mudasir\") = \"mWDCSKR\" ]]\n    [[ $(candidate \"YES\") = \"ygs\" ]]\n    [[ $(candidate \"This is a message\") = \"tHKS KS C MGSSCGG\" ]]\n    [[ $(candidate \"I DoNt KnOw WhAt tO WrItE\") = \"k dQnT kNqW wHcT Tq wRkTg\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_93_encode", "test": "}\n\ncandidate() {\n    encode \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"TEST\") = \"tgst\" ]]\n    [[ $(candidate \"Mudasir\") = \"mWDCSKR\" ]]\n    [[ $(candidate \"YES\") = \"ygs\" ]]\n    [[ $(candidate \"This is a message\") = \"tHKS KS C MGSSCGG\" ]]\n    [[ $(candidate \"I DoNt KnOw WhAt tO WrItE\") = \"k dQnT kNqW wHcT Tq wRkTg\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_91_is_bored", "language": "sh", "prompt": "#!/bin/bash\n# You'll be given a string of words, and your task is to count the number\n# of boredoms. A boredom is a sentence that starts with the word \"I\".\n# Sentences are delimited by '.', '?' or '!'.\n# For example:\n# >>> $(is_bored \"Hello world\")\n# \"0\"\n# >>> $(is_bored \"The sky is blue. The sun is shining. I love this weather\")\n# \"1\"\n#\n# $1 is a string\nis_bored() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_bored \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\") = \"0\" ]]\n    [[ $(candidate \"Is the sky blue?\") = \"0\" ]]\n    [[ $(candidate \"I love It \\!\") = \"1\" ]]\n    [[ $(candidate \"bIt\") = \"0\" ]]\n    [[ $(candidate \"I feel good today. I will be productive. will kill It\") = \"2\" ]]\n    [[ $(candidate \"You and I are going for a walk\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_91_is_bored", "test": "}\n\ncandidate() {\n    is_bored \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\") = \"0\" ]]\n    [[ $(candidate \"Is the sky blue?\") = \"0\" ]]\n    [[ $(candidate \"I love It \\!\") = \"1\" ]]\n    [[ $(candidate \"bIt\") = \"0\" ]]\n    [[ $(candidate \"I feel good today. I will be productive. will kill It\") = \"2\" ]]\n    [[ $(candidate \"You and I are going for a walk\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "sh", "prompt": "#!/bin/bash\n# pairs_sum_to_zero takes a list of integers as an input.\n# it returns true if there are two distinct elements in the list that\n# sum to zero, and false otherwise.\n# >>> $(pairs_sum_to_zero \"1 3 5 0\")\n# \"false\"\n# >>> $(pairs_sum_to_zero \"1 3 -2 1\")\n# \"false\"\n# >>> $(pairs_sum_to_zero \"1 2 3 7\")\n# \"false\"\n# >>> $(pairs_sum_to_zero \"2 4 -5 3 5 7\")\n# \"true\"\n# >>> $(pairs_sum_to_zero \"1\")\n# \"false\"\n#\n# $1 is a space-separated list\npairs_sum_to_zero() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    pairs_sum_to_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 3 5 0\") = \"false\" ]]\n    [[ $(candidate \"1 3 -2 1\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 7\") = \"false\" ]]\n    [[ $(candidate \"2 4 -5 3 5 7\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"-3 9 -1 3 2 30\") = \"true\" ]]\n    [[ $(candidate \"-3 9 -1 3 2 31\") = \"true\" ]]\n    [[ $(candidate \"-3 9 -1 4 2 30\") = \"false\" ]]\n    [[ $(candidate \"-3 9 -1 4 2 31\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "}\n\ncandidate() {\n    pairs_sum_to_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 3 5 0\") = \"false\" ]]\n    [[ $(candidate \"1 3 -2 1\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 7\") = \"false\" ]]\n    [[ $(candidate \"2 4 -5 3 5 7\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"-3 9 -1 3 2 30\") = \"true\" ]]\n    [[ $(candidate \"-3 9 -1 3 2 31\") = \"true\" ]]\n    [[ $(candidate \"-3 9 -1 4 2 30\") = \"false\" ]]\n    [[ $(candidate \"-3 9 -1 4 2 31\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_71_triangle_area", "language": "sh", "prompt": "#!/bin/bash\n# Given the lengths of the three sides of a triangle. Return the area of\n# the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n# Otherwise return -1\n# Three sides make a valid triangle when the sum of any two sides is greater \n# than the third side.\n# Example:\n# >>> $(triangle_area \"3\" \"4\" \"5\")\n# \"6.0\"\n# >>> $(triangle_area \"1\" \"2\" \"10\")\n# \"-1\"\n#\n# $1 is an integer\n# $2 is an integer\n# $3 is an integer\ntriangle_area() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    triangle_area \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"4\" \"5\") = \"6.0\" ]]\n    [[ $(candidate \"1\" \"2\" \"10\") = \"-1\" ]]\n    [[ $(candidate \"4\" \"8\" \"5\") = \"8.18\" ]]\n    [[ $(candidate \"2\" \"2\" \"2\") = \"1.73\" ]]\n    [[ $(candidate \"1\" \"2\" \"3\") = \"-1\" ]]\n    [[ $(candidate \"10\" \"5\" \"7\") = \"16.25\" ]]\n    [[ $(candidate \"2\" \"6\" \"3\") = \"-1\" ]]\n    [[ $(candidate \"1\" \"1\" \"1\") = \"0.43\" ]]\n    [[ $(candidate \"2\" \"2\" \"10\") = \"-1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_71_triangle_area", "test": "}\n\ncandidate() {\n    triangle_area \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"4\" \"5\") = \"6.0\" ]]\n    [[ $(candidate \"1\" \"2\" \"10\") = \"-1\" ]]\n    [[ $(candidate \"4\" \"8\" \"5\") = \"8.18\" ]]\n    [[ $(candidate \"2\" \"2\" \"2\") = \"1.73\" ]]\n    [[ $(candidate \"1\" \"2\" \"3\") = \"-1\" ]]\n    [[ $(candidate \"10\" \"5\" \"7\") = \"16.25\" ]]\n    [[ $(candidate \"2\" \"6\" \"3\") = \"-1\" ]]\n    [[ $(candidate \"1\" \"1\" \"1\") = \"0.43\" ]]\n    [[ $(candidate \"2\" \"2\" \"10\") = \"-1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_148_bf", "language": "sh", "prompt": "#!/bin/bash\n# There are eight planets in our solar system: the closerst to the Sun \n# is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n# Uranus, Neptune.\n# Write a function that takes two planet names as strings planet1 and planet2. \n# The function should return a list containing all planets whose orbits are \n# located between the orbit of planet1 and the orbit of planet2, sorted by \n# the proximity to the sun. \n# The function should return an empty list if planet1 or planet2\n# are not correct planet names. \n# Examples\n# >>> $(bf \"Jupiter\" \"Neptune\")\n# ['\"Saturn\"', '\"Uranus\"']\n# >>> $(bf \"Earth\" \"Mercury\")\n# \"Venus\"\n# >>> $(bf \"Mercury\" \"Uranus\")\n# ['\"Venus\"', '\"Earth\"', '\"Mars\"', '\"Jupiter\"', '\"Saturn\"']\n#\n# $1 is a string\n# $2 is a string\nbf() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    bf \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Jupiter\" \"Neptune\") = \"Saturn Uranus\" ]]\n    [[ $(candidate \"Earth\" \"Mercury\") = \"Venus\" ]]\n    [[ $(candidate \"Mercury\" \"Uranus\") = \"Venus Earth Mars Jupiter Saturn\" ]]\n    [[ $(candidate \"Neptune\" \"Venus\") = \"Earth Mars Jupiter Saturn Uranus\" ]]\n    [[ $(candidate \"Earth\" \"Earth\") = \"\" ]]\n    [[ $(candidate \"Mars\" \"Earth\") = \"\" ]]\n    [[ $(candidate \"Jupiter\" \"Makemake\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_148_bf", "test": "}\n\ncandidate() {\n    bf \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Jupiter\" \"Neptune\") = \"Saturn Uranus\" ]]\n    [[ $(candidate \"Earth\" \"Mercury\") = \"Venus\" ]]\n    [[ $(candidate \"Mercury\" \"Uranus\") = \"Venus Earth Mars Jupiter Saturn\" ]]\n    [[ $(candidate \"Neptune\" \"Venus\") = \"Earth Mars Jupiter Saturn Uranus\" ]]\n    [[ $(candidate \"Earth\" \"Earth\") = \"\" ]]\n    [[ $(candidate \"Mars\" \"Earth\") = \"\" ]]\n    [[ $(candidate \"Jupiter\" \"Makemake\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_131_digits", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer n, return the product of the odd digits.\n# Return 0 if all digits are even.\n# For example:\n# >>> $(digits \"1\")\n# \"1\"\n# >>> $(digits \"4\")\n# \"0\"\n# >>> $(digits \"235\")\n# \"15\"\n#\n# $1 is an integer\ndigits() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    digits \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"54\") = \"5\" ]]\n    [[ $(candidate \"120\") = \"1\" ]]\n    [[ $(candidate \"5014\") = \"5\" ]]\n    [[ $(candidate \"98765\") = \"315\" ]]\n    [[ $(candidate \"5576543\") = \"2625\" ]]\n    [[ $(candidate \"2468\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_131_digits", "test": "}\n\ncandidate() {\n    digits \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"54\") = \"5\" ]]\n    [[ $(candidate \"120\") = \"1\" ]]\n    [[ $(candidate \"5014\") = \"5\" ]]\n    [[ $(candidate \"98765\") = \"315\" ]]\n    [[ $(candidate \"5576543\") = \"2625\" ]]\n    [[ $(candidate \"2468\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_101_words_string", "language": "sh", "prompt": "#!/bin/bash\n# You will be given a string of words separated by commas or spaces. Your task is\n# to split the string into words and return an array of the words.\n# For example:\n# >>> $(words_string \"Hi, my name is John\")\n# ['\"Hi\"', '\"my\"', '\"name\"', '\"is\"', '\"John\"']\n# >>> $(words_string \"One, two, three, four, five, six\")\n# ['\"One\"', '\"two\"', '\"three\"', '\"four\"', '\"five\"', '\"six\"']\n#\n# $1 is a string\nwords_string() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    words_string \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hi, my name is John\") = \"Hi my name is John\" ]]\n    [[ $(candidate \"One, two, three, four, five, six\") = \"One two three four five six\" ]]\n    [[ $(candidate \"Hi, my name\") = \"Hi my name\" ]]\n    [[ $(candidate \"One,, two, three, four, five, six,\") = \"One two three four five six\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"ahmed     , gamal\") = \"ahmed gamal\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_101_words_string", "test": "}\n\ncandidate() {\n    words_string \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hi, my name is John\") = \"Hi my name is John\" ]]\n    [[ $(candidate \"One, two, three, four, five, six\") = \"One two three four five six\" ]]\n    [[ $(candidate \"Hi, my name\") = \"Hi my name\" ]]\n    [[ $(candidate \"One,, two, three, four, five, six,\") = \"One two three four five six\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"ahmed     , gamal\") = \"ahmed gamal\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_18_how_many_times", "language": "sh", "prompt": "#!/bin/bash\n# Find how many times a given substring can be found in the original string. Count overlaping cases.\n# >>> $(how_many_times \"\" \"a\")\n# \"0\"\n# >>> $(how_many_times \"aaa\" \"a\")\n# \"3\"\n# >>> $(how_many_times \"aaaa\" \"aa\")\n# \"3\"\n#\n# $1 is a string\n# $2 is a string\nhow_many_times() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    how_many_times \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"x\") = \"0\" ]]\n    [[ $(candidate \"xyxyxyx\" \"x\") = \"4\" ]]\n    [[ $(candidate \"cacacacac\" \"cac\") = \"4\" ]]\n    [[ $(candidate \"john doe\" \"john\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_18_how_many_times", "test": "}\n\ncandidate() {\n    how_many_times \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"x\") = \"0\" ]]\n    [[ $(candidate \"xyxyxyx\" \"x\") = \"4\" ]]\n    [[ $(candidate \"cacacacac\" \"cac\") = \"4\" ]]\n    [[ $(candidate \"john doe\" \"john\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_137_compare_one", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that takes integers, floats, or strings representing\n# real numbers, and returns the larger variable in its given variable type.\n# Return None if the values are equal.\n# Note: If a real number is represented as a string, the floating point might be . or ,\n# >>> $(compare_one \"1\" \"2.5\")\n# \"2.5\"\n# >>> $(compare_one \"1\" \"2,3\")\n# \"2,3\"\n# >>> $(compare_one \"5,1\" \"6\")\n# \"6\"\n# >>> $(compare_one \"1\" \"1\")\n# \"None\"\n#\n# $1 is an argument\n# $2 is an argument\ncompare_one() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_137_compare_one.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    compare_one \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\" \"2\") = \"2\" ]]\n    [[ $(candidate \"1\" \"2.5\") = \"2.5\" ]]\n    [[ $(candidate \"2\" \"3\") = \"3\" ]]\n    [[ $(candidate \"5\" \"6\") = \"6\" ]]\n    [[ $(candidate \"1\" \"2,3\") = \"2,3\" ]]\n    [[ $(candidate \"5,1\" \"6\") = \"6\" ]]\n    [[ $(candidate \"1\" \"2\") = \"2\" ]]\n    [[ $(candidate \"1\" \"1\") = \"None\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_137_compare_one", "test": "}\n\ncandidate() {\n    compare_one \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\" \"2\") = \"2\" ]]\n    [[ $(candidate \"1\" \"2.5\") = \"2.5\" ]]\n    [[ $(candidate \"2\" \"3\") = \"3\" ]]\n    [[ $(candidate \"5\" \"6\") = \"6\" ]]\n    [[ $(candidate \"1\" \"2,3\") = \"2,3\" ]]\n    [[ $(candidate \"5,1\" \"6\") = \"6\" ]]\n    [[ $(candidate \"1\" \"2\") = \"2\" ]]\n    [[ $(candidate \"1\" \"1\") = \"None\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_51_remove_vowels", "language": "sh", "prompt": "#!/bin/bash\n# remove_vowels is a function that takes string and returns string without vowels.\n# >>> $(remove_vowels \"\")\n# \"\"\n# >>> $(remove_vowels \"abcdef\")\n# \"bcdf\"\n# >>> $(remove_vowels \"aaaaa\")\n# \"\"\n# >>> $(remove_vowels \"aaBAA\")\n# \"B\"\n# >>> $(remove_vowels \"zbcd\")\n# \"zbcd\"\n#\n# $1 is a string\nremove_vowels() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    remove_vowels \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"abcdef\\nghijklm\") = \"bcdf\\nghjklm\" ]]\n    [[ $(candidate \"fedcba\") = \"fdcb\" ]]\n    [[ $(candidate \"eeeee\") = \"\" ]]\n    [[ $(candidate \"acBAA\") = \"cB\" ]]\n    [[ $(candidate \"EcBOO\") = \"cB\" ]]\n    [[ $(candidate \"ybcd\") = \"ybcd\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_51_remove_vowels", "test": "}\n\ncandidate() {\n    remove_vowels \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"abcdef\\nghijklm\") = \"bcdf\\nghjklm\" ]]\n    [[ $(candidate \"fedcba\") = \"fdcb\" ]]\n    [[ $(candidate \"eeeee\") = \"\" ]]\n    [[ $(candidate \"acBAA\") = \"cB\" ]]\n    [[ $(candidate \"EcBOO\") = \"cB\" ]]\n    [[ $(candidate \"ybcd\") = \"ybcd\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_70_strange_sort_list", "language": "sh", "prompt": "#!/bin/bash\n# Given list of integers, return list in strange order.\n# Strange sorting, is when you start with the minimum value,\n# then maximum of the remaining integers, then minimum and so on.\n# Examples:\n# >>> $(strange_sort_list \"1 2 3 4\")\n# ['\"1\"', '\"4\"', '\"2\"', '\"3\"']\n# >>> $(strange_sort_list \"5 5 5 5\")\n# ['\"5\"', '\"5\"', '\"5\"', '\"5\"']\n# >>> $(strange_sort_list \"\")\n# []\n#\n# $1 is a space-separated list\nstrange_sort_list() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    strange_sort_list \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4\") = \"1 4 2 3\" ]]\n    [[ $(candidate \"5 6 7 8 9\") = \"5 9 6 8 7\" ]]\n    [[ $(candidate \"1 2 3 4 5\") = \"1 5 2 4 3\" ]]\n    [[ $(candidate \"5 6 7 8 9 1\") = \"1 9 5 8 6 7\" ]]\n    [[ $(candidate \"5 5 5 5\") = \"5 5 5 5\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7 8\") = \"1 8 2 7 3 6 4 5\" ]]\n    [[ $(candidate \"0 2 2 2 5 5 -5 -5\") = \"-5 5 -5 5 0 2 2 2\" ]]\n    [[ $(candidate \"111111\") = \"111111\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_70_strange_sort_list", "test": "}\n\ncandidate() {\n    strange_sort_list \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4\") = \"1 4 2 3\" ]]\n    [[ $(candidate \"5 6 7 8 9\") = \"5 9 6 8 7\" ]]\n    [[ $(candidate \"1 2 3 4 5\") = \"1 5 2 4 3\" ]]\n    [[ $(candidate \"5 6 7 8 9 1\") = \"1 9 5 8 6 7\" ]]\n    [[ $(candidate \"5 5 5 5\") = \"5 5 5 5\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7 8\") = \"1 8 2 7 3 6 4 5\" ]]\n    [[ $(candidate \"0 2 2 2 5 5 -5 -5\") = \"-5 5 -5 5 0 2 2 2\" ]]\n    [[ $(candidate \"111111\") = \"111111\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_20_find_closest_elements", "language": "sh", "prompt": "#!/bin/bash\n# From a supplied list of numbers (of length at least two) select and return two that are the closest to each\n# other and return them in order (smaller number, larger number).\n# >>> $(find_closest_elements \"1.0 2.0 3.0 4.0 5.0 2.2\")\n# ['\"2.0\"', '\"2.2\"']\n# >>> $(find_closest_elements \"1.0 2.0 3.0 4.0 5.0 2.0\")\n# ['\"2.0\"', '\"2.0\"']\n#\n# $1 is a space-separated list\nfind_closest_elements() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    find_closest_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\") = \"3.9 4.0\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\") = \"5.0 5.9\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.2\") = \"2.0 2.2\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.0\") = \"2.0 2.0\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\") = \"2.2 3.1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_20_find_closest_elements", "test": "}\n\ncandidate() {\n    find_closest_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\") = \"3.9 4.0\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\") = \"5.0 5.9\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.2\") = \"2.0 2.2\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.0\") = \"2.0 2.0\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\") = \"2.2 3.1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_76_is_simple_power", "language": "sh", "prompt": "#!/bin/bash\n# Your task is to write a function that returns true if a number x is a simple\n# power of n and false in other cases.\n# x is a simple power of n if n**int=x\n# For example:\n# >>> $(is_simple_power \"1\" \"4\")\n# \"true\"\n# >>> $(is_simple_power \"2\" \"2\")\n# \"true\"\n# >>> $(is_simple_power \"8\" \"2\")\n# \"true\"\n# >>> $(is_simple_power \"3\" \"2\")\n# \"false\"\n# >>> $(is_simple_power \"3\" \"1\")\n# \"false\"\n# >>> $(is_simple_power \"5\" \"3\")\n# \"false\"\n#\n# $1 is an integer\n# $2 is an integer\nis_simple_power() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_simple_power \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"16\" \"2\") = \"true\" ]]\n    [[ $(candidate \"143214\" \"16\") = \"false\" ]]\n    [[ $(candidate \"4\" \"2\") = \"true\" ]]\n    [[ $(candidate \"9\" \"3\") = \"true\" ]]\n    [[ $(candidate \"16\" \"4\") = \"true\" ]]\n    [[ $(candidate \"24\" \"2\") = \"false\" ]]\n    [[ $(candidate \"128\" \"4\") = \"false\" ]]\n    [[ $(candidate \"12\" \"6\") = \"false\" ]]\n    [[ $(candidate \"1\" \"1\") = \"true\" ]]\n    [[ $(candidate \"1\" \"12\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_76_is_simple_power", "test": "}\n\ncandidate() {\n    is_simple_power \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"16\" \"2\") = \"true\" ]]\n    [[ $(candidate \"143214\" \"16\") = \"false\" ]]\n    [[ $(candidate \"4\" \"2\") = \"true\" ]]\n    [[ $(candidate \"9\" \"3\") = \"true\" ]]\n    [[ $(candidate \"16\" \"4\") = \"true\" ]]\n    [[ $(candidate \"24\" \"2\") = \"false\" ]]\n    [[ $(candidate \"128\" \"4\") = \"false\" ]]\n    [[ $(candidate \"12\" \"6\") = \"false\" ]]\n    [[ $(candidate \"1\" \"1\") = \"true\" ]]\n    [[ $(candidate \"1\" \"12\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_39_prime_fib", "language": "sh", "prompt": "#!/bin/bash\n# prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n# >>> $(prime_fib \"1\")\n# \"2\"\n# >>> $(prime_fib \"2\")\n# \"3\"\n# >>> $(prime_fib \"3\")\n# \"5\"\n# >>> $(prime_fib \"4\")\n# \"13\"\n# >>> $(prime_fib \"5\")\n# \"89\"\n#\n# $1 is an integer\nprime_fib() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    prime_fib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"2\" ]]\n    [[ $(candidate \"2\") = \"3\" ]]\n    [[ $(candidate \"3\") = \"5\" ]]\n    [[ $(candidate \"4\") = \"13\" ]]\n    [[ $(candidate \"5\") = \"89\" ]]\n    [[ $(candidate \"6\") = \"233\" ]]\n    [[ $(candidate \"7\") = \"1597\" ]]\n    [[ $(candidate \"8\") = \"28657\" ]]\n    [[ $(candidate \"9\") = \"514229\" ]]\n    [[ $(candidate \"10\") = \"433494437\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_39_prime_fib", "test": "}\n\ncandidate() {\n    prime_fib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"2\" ]]\n    [[ $(candidate \"2\") = \"3\" ]]\n    [[ $(candidate \"3\") = \"5\" ]]\n    [[ $(candidate \"4\") = \"13\" ]]\n    [[ $(candidate \"5\") = \"89\" ]]\n    [[ $(candidate \"6\") = \"233\" ]]\n    [[ $(candidate \"7\") = \"1597\" ]]\n    [[ $(candidate \"8\") = \"28657\" ]]\n    [[ $(candidate \"9\") = \"514229\" ]]\n    [[ $(candidate \"10\") = \"433494437\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_145_order_by_points", "language": "sh", "prompt": "#!/bin/bash\n# Write a function which sorts the given list of integers\n# in ascending order according to the sum of their digits.\n# Note: if there are several items with similar sum of their digits,\n# order them based on their index in original list.\n# For example:\n# >>> $(order_by_points \"1 11 -1 -11 -12\")\n# ['\"-1\"', '\"-11\"', '\"1\"', '\"-12\"', '\"11\"']\n# >>> $(order_by_points \"\")\n# []\n#\n# $1 is a space-separated list\norder_by_points() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    order_by_points \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 11 -1 -11 -12\") = \"-1 -11 1 -12 11\" ]]\n    [[ $(candidate \"1234 423 463 145 2 423 423 53 6 37 3457 3 56 0 46\") = \"0 2 3 6 53 423 423 423 1234 145 37 46 56 463 3457\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 -11 -32 43 54 -98 2 -3\") = \"-3 -32 -98 -11 1 2 43 54\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7 8 9 10 11\") = \"1 10 2 11 3 4 5 6 7 8 9\" ]]\n    [[ $(candidate \"0 6 6 -76 -21 23 4\") = \"-76 -21 0 4 23 6 6\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_145_order_by_points", "test": "}\n\ncandidate() {\n    order_by_points \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 11 -1 -11 -12\") = \"-1 -11 1 -12 11\" ]]\n    [[ $(candidate \"1234 423 463 145 2 423 423 53 6 37 3457 3 56 0 46\") = \"0 2 3 6 53 423 423 423 1234 145 37 46 56 463 3457\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 -11 -32 43 54 -98 2 -3\") = \"-3 -32 -98 -11 1 2 43 54\" ]]\n    [[ $(candidate \"1 2 3 4 5 6 7 8 9 10 11\") = \"1 10 2 11 3 4 5 6 7 8 9\" ]]\n    [[ $(candidate \"0 6 6 -76 -21 23 4\") = \"-76 -21 0 4 23 6 6\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_0_has_close_elements", "language": "sh", "prompt": "#!/bin/bash\n# Check if in given list of numbers, are any two numbers closer to each other than\n# given threshold.\n# >>> $(has_close_elements \"1.0 2.0 3.0\" \"0.5\")\n# \"false\"\n# >>> $(has_close_elements \"1.0 2.8 3.0 4.0 5.0 2.0\" \"0.3\")\n# \"true\"\n#\n# $1 is a space-separated list\n# $2 is a floating point\nhas_close_elements() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    has_close_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\" \"0.3\") = \"true\" ]]\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\" \"0.05\") = \"false\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\" \"0.95\") = \"true\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\" \"0.8\") = \"false\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.0\" \"0.1\") = \"true\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\" \"1.0\") = \"true\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\" \"0.5\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_0_has_close_elements", "test": "}\n\ncandidate() {\n    has_close_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\" \"0.3\") = \"true\" ]]\n    [[ $(candidate \"1.0 2.0 3.9 4.0 5.0 2.2\" \"0.05\") = \"false\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\" \"0.95\") = \"true\" ]]\n    [[ $(candidate \"1.0 2.0 5.9 4.0 5.0\" \"0.8\") = \"false\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0 2.0\" \"0.1\") = \"true\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\" \"1.0\") = \"true\" ]]\n    [[ $(candidate \"1.1 2.2 3.1 4.1 5.1\" \"0.5\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_10_make_palindrome", "language": "sh", "prompt": "#!/bin/bash\n# Find the shortest palindrome that begins with a supplied string.\n# Algorithm idea is simple:\n# - Find the longest postfix of supplied string that is a palindrome.\n# - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n# >>> $(make_palindrome \"\")\n# \"\"\n# >>> $(make_palindrome \"cat\")\n# \"catac\"\n# >>> $(make_palindrome \"cata\")\n# \"catac\"\n#\n# $1 is a string\nmake_palindrome() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    make_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"x\") = \"x\" ]]\n    [[ $(candidate \"xyz\") = \"xyzyx\" ]]\n    [[ $(candidate \"xyx\") = \"xyx\" ]]\n    [[ $(candidate \"jerry\") = \"jerryrrej\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_10_make_palindrome", "test": "}\n\ncandidate() {\n    make_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"x\") = \"x\" ]]\n    [[ $(candidate \"xyz\") = \"xyzyx\" ]]\n    [[ $(candidate \"xyx\") = \"xyx\" ]]\n    [[ $(candidate \"jerry\") = \"jerryrrej\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_11_string_xor", "language": "sh", "prompt": "#!/bin/bash\n# Input are two strings a and b consisting only of 1s and 0s.\n# Perform binary XOR on these inputs and return result also as a string.\n# >>> $(string_xor \"010\" \"110\")\n# \"100\"\n#\n# $1 is a string\n# $2 is a string\nstring_xor() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    string_xor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"111000\" \"101010\") = \"010010\" ]]\n    [[ $(candidate \"1\" \"1\") = \"0\" ]]\n    [[ $(candidate \"0101\" \"0000\") = \"0101\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_11_string_xor", "test": "}\n\ncandidate() {\n    string_xor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"111000\" \"101010\") = \"010010\" ]]\n    [[ $(candidate \"1\" \"1\") = \"0\" ]]\n    [[ $(candidate \"0101\" \"0000\") = \"0101\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_139_special_factorial", "language": "sh", "prompt": "#!/bin/bash\n# The Brazilian factorial is defined as:\n# brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n# where n > 0\n# For example:\n# >>> $(special_factorial \"4\")\n# \"288\"\n# The function will receive an integer as input and should return the special\n# factorial of this integer.\n#\n# $1 is an integer\nspecial_factorial() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    special_factorial \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4\") = \"288\" ]]\n    [[ $(candidate \"5\") = \"34560\" ]]\n    [[ $(candidate \"7\") = \"125411328000\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_139_special_factorial", "test": "}\n\ncandidate() {\n    special_factorial \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4\") = \"288\" ]]\n    [[ $(candidate \"5\") = \"34560\" ]]\n    [[ $(candidate \"7\") = \"125411328000\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_122_add_elements", "language": "sh", "prompt": "#!/bin/bash\n# Given a non-empty array of integers arr and an integer k, return\n# the sum of the elements with at most two digits from the first k elements of arr.\n# Example:\n# >>> $(add_elements \"111 21 3 4000 5 6 7 8 9\" \"4\")\n# \"24\"\n# Constraints:\n# 1. 1 <= len(arr) <= 100\n# 2. 1 <= k <= len(arr)\n#\n# $1 is a space-separated list\n# $2 is an integer\nadd_elements() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    add_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 -2 -3 41 57 76 87 88 99\" \"3\") = \"-4\" ]]\n    [[ $(candidate \"111 121 3 4000 5 6\" \"2\") = \"0\" ]]\n    [[ $(candidate \"11 21 3 90 5 6 7 8 9\" \"4\") = \"125\" ]]\n    [[ $(candidate \"111 21 3 4000 5 6 7 8 9\" \"4\") = \"24\" ]]\n    [[ $(candidate \"1\" \"1\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_122_add_elements", "test": "}\n\ncandidate() {\n    add_elements \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 -2 -3 41 57 76 87 88 99\" \"3\") = \"-4\" ]]\n    [[ $(candidate \"111 121 3 4000 5 6\" \"2\") = \"0\" ]]\n    [[ $(candidate \"11 21 3 90 5 6 7 8 9\" \"4\") = \"125\" ]]\n    [[ $(candidate \"111 21 3 4000 5 6 7 8 9\" \"4\") = \"24\" ]]\n    [[ $(candidate \"1\" \"1\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_46_fib4", "language": "sh", "prompt": "#!/bin/bash\n# The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n# fib4(0) -> 0\n# fib4(1) -> 0\n# fib4(2) -> 2\n# fib4(3) -> 0\n# fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n# Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n# >>> $(fib4 \"5\")\n# \"4\"\n# >>> $(fib4 \"6\")\n# \"8\"\n# >>> $(fib4 \"7\")\n# \"14\"\n#\n# $1 is an integer\nfib4() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fib4 \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"4\" ]]\n    [[ $(candidate \"8\") = \"28\" ]]\n    [[ $(candidate \"10\") = \"104\" ]]\n    [[ $(candidate \"12\") = \"386\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_46_fib4", "test": "}\n\ncandidate() {\n    fib4 \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"4\" ]]\n    [[ $(candidate \"8\") = \"28\" ]]\n    [[ $(candidate \"10\") = \"104\" ]]\n    [[ $(candidate \"12\") = \"386\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_104_unique_digits", "language": "sh", "prompt": "#!/bin/bash\n# Given a list of positive integers x. return a sorted list of all \n# elements that hasn't any even digit.\n# Note: Returned list should be sorted in increasing order.\n# For example:\n# >>> $(unique_digits \"15 33 1422 1\")\n# ['\"1\"', '\"15\"', '\"33\"']\n# >>> $(unique_digits \"152 323 1422 10\")\n# []\n#\n# $1 is a space-separated list\nunique_digits() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    unique_digits \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"15 33 1422 1\") = \"1 15 33\" ]]\n    [[ $(candidate \"152 323 1422 10\") = \"\" ]]\n    [[ $(candidate \"12345 2033 111 151\") = \"111 151\" ]]\n    [[ $(candidate \"135 103 31\") = \"31 135\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_104_unique_digits", "test": "}\n\ncandidate() {\n    unique_digits \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"15 33 1422 1\") = \"1 15 33\" ]]\n    [[ $(candidate \"152 323 1422 10\") = \"\" ]]\n    [[ $(candidate \"12345 2033 111 151\") = \"111 151\" ]]\n    [[ $(candidate \"135 103 31\") = \"31 135\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_117_select_words", "language": "sh", "prompt": "#!/bin/bash\n# Given a string s and a natural number n, you have been tasked to implement \n# a function that returns a list of all words from string s that contain exactly \n# n consonants, in order these words appear in the string s.\n# If the string s is empty then the function should return an empty list.\n# Note: you may assume the input string contains only letters and spaces.\n# Examples:\n# >>> $(select_words \"Mary had a little lamb\" \"4\")\n# ['\"little\"']\n# >>> $(select_words \"Mary had a little lamb\" \"3\")\n# ['\"Mary\"', '\"lamb\"']\n# >>> $(select_words \"simple white space\" \"2\")\n# []\n# >>> $(select_words \"Hello world\" \"4\")\n# ['\"world\"']\n# >>> $(select_words \"Uncle sam\" \"3\")\n# ['\"Uncle\"']\n#\n# $1 is a string\n# $2 is an integer\nselect_words() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    select_words \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Mary had a little lamb\" \"4\") = \"little\" ]]\n    [[ $(candidate \"Mary had a little lamb\" \"3\") = \"Mary lamb\" ]]\n    [[ $(candidate \"simple white space\" \"2\") = \"\" ]]\n    [[ $(candidate \"Hello world\" \"4\") = \"world\" ]]\n    [[ $(candidate \"Uncle sam\" \"3\") = \"Uncle\" ]]\n    [[ $(candidate \"\" \"4\") = \"\" ]]\n    [[ $(candidate \"a b c d e f\" \"1\") = \"b c d f\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_117_select_words", "test": "}\n\ncandidate() {\n    select_words \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Mary had a little lamb\" \"4\") = \"little\" ]]\n    [[ $(candidate \"Mary had a little lamb\" \"3\") = \"Mary lamb\" ]]\n    [[ $(candidate \"simple white space\" \"2\") = \"\" ]]\n    [[ $(candidate \"Hello world\" \"4\") = \"world\" ]]\n    [[ $(candidate \"Uncle sam\" \"3\") = \"Uncle\" ]]\n    [[ $(candidate \"\" \"4\") = \"\" ]]\n    [[ $(candidate \"a b c d e f\" \"1\") = \"b c d f\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_72_will_it_fly", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that returns true if the object q will fly, and false otherwise.\n# The object q will fly if it's balanced (it is a palindromic list) and the sum of its elements is less than or equal the maximum possible weight w.\n# Example:\n# >>> $(will_it_fly \"1 2\" \"5\")\n# \"false\"\n# # 1+2 is less than the maximum possible weight, but it's unbalanced.\n# >>> $(will_it_fly \"3 2 3\" \"1\")\n# \"false\"\n# # it's balanced, but 3+2+3 is more than the maximum possible weight.\n# >>> $(will_it_fly \"3 2 3\" \"9\")\n# \"true\"\n# # 3+2+3 is less than the maximum possible weight, and it's balanced.\n# >>> $(will_it_fly \"3\" \"5\")\n# \"true\"\n# # 3 is less than the maximum possible weight, and it's balanced.\n#\n# $1 is a space-separated list\n# $2 is an integer\nwill_it_fly() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    will_it_fly \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 2 3\" \"9\") = \"true\" ]]\n    [[ $(candidate \"1 2\" \"5\") = \"false\" ]]\n    [[ $(candidate \"3\" \"5\") = \"true\" ]]\n    [[ $(candidate \"3 2 3\" \"1\") = \"false\" ]]\n    [[ $(candidate \"1 2 3\" \"6\") = \"false\" ]]\n    [[ $(candidate \"5\" \"5\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_72_will_it_fly", "test": "}\n\ncandidate() {\n    will_it_fly \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 2 3\" \"9\") = \"true\" ]]\n    [[ $(candidate \"1 2\" \"5\") = \"false\" ]]\n    [[ $(candidate \"3\" \"5\") = \"true\" ]]\n    [[ $(candidate \"3 2 3\" \"1\") = \"false\" ]]\n    [[ $(candidate \"1 2 3\" \"6\") = \"false\" ]]\n    [[ $(candidate \"5\" \"5\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_55_fib", "language": "sh", "prompt": "#!/bin/bash\n# Return n-th Fibonacci number.\n# >>> $(fib \"10\")\n# \"55\"\n# >>> $(fib \"1\")\n# \"1\"\n# >>> $(fib \"8\")\n# \"21\"\n#\n# $1 is an integer\nfib() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"10\") = \"55\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"8\") = \"21\" ]]\n    [[ $(candidate \"11\") = \"89\" ]]\n    [[ $(candidate \"12\") = \"144\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_55_fib", "test": "}\n\ncandidate() {\n    fib \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"10\") = \"55\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"8\") = \"21\" ]]\n    [[ $(candidate \"11\") = \"89\" ]]\n    [[ $(candidate \"12\") = \"144\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_153_Strongest_Extension", "language": "sh", "prompt": "#!/bin/bash\n# You will be given the name of a class (a string) and a list of extensions.\n# The extensions are to be used to load additional classes to the class. The\n# strength of the extension is as follows: Let CAP be the number of the uppercase\n# letters in the extension's name, and let SM be the number of lowercase letters \n# in the extension's name, the strength is given by the fraction CAP - SM. \n# You should find the strongest extension and return a string in this \n# format: ClassName.StrongestExtensionName.\n# If there are two or more extensions with the same strength, you should\n# choose the one that comes first in the list.\n# For example, if you are given \"Slices\" as the class and a list of the\n# extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n# return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n# (its strength is -1).\n# Example:\n# >>> $(Strongest_Extension \"my_class\" \"AA Be CC\")\n# \"my_class.AA\"\n#\n# $1 is a string\n# $2 is a space-separated list\nStrongest_Extension() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    Strongest_Extension \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Watashi\" \"tEN niNE eIGHt8OKe\") = \"Watashi.eIGHt8OKe\" ]]\n    [[ $(candidate \"Boku123\" \"nani NazeDa YEs.WeCaNe 32145tggg\") = \"Boku123.YEs.WeCaNe\" ]]\n    [[ $(candidate \"__YESIMHERE\" \"t eMptY nothing zeR00 NuLl__ 123NoooneB321\") = \"__YESIMHERE.NuLl__\" ]]\n    [[ $(candidate \"K\" \"Ta TAR t234An cosSo\") = \"K.TAR\" ]]\n    [[ $(candidate \"__HAHA\" \"Tab 123 781345 -_-\") = \"__HAHA.123\" ]]\n    [[ $(candidate \"YameRore\" \"HhAas okIWILL123 WorkOut Fails -_-\") = \"YameRore.okIWILL123\" ]]\n    [[ $(candidate \"finNNalLLly\" \"Die NowW Wow WoW\") = \"finNNalLLly.WoW\" ]]\n    [[ $(candidate \"_\" \"Bb 91245\") = \"_.Bb\" ]]\n    [[ $(candidate \"Sp\" \"671235 Bb\") = \"Sp.671235\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_153_Strongest_Extension", "test": "}\n\ncandidate() {\n    Strongest_Extension \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Watashi\" \"tEN niNE eIGHt8OKe\") = \"Watashi.eIGHt8OKe\" ]]\n    [[ $(candidate \"Boku123\" \"nani NazeDa YEs.WeCaNe 32145tggg\") = \"Boku123.YEs.WeCaNe\" ]]\n    [[ $(candidate \"__YESIMHERE\" \"t eMptY nothing zeR00 NuLl__ 123NoooneB321\") = \"__YESIMHERE.NuLl__\" ]]\n    [[ $(candidate \"K\" \"Ta TAR t234An cosSo\") = \"K.TAR\" ]]\n    [[ $(candidate \"__HAHA\" \"Tab 123 781345 -_-\") = \"__HAHA.123\" ]]\n    [[ $(candidate \"YameRore\" \"HhAas okIWILL123 WorkOut Fails -_-\") = \"YameRore.okIWILL123\" ]]\n    [[ $(candidate \"finNNalLLly\" \"Die NowW Wow WoW\") = \"finNNalLLly.WoW\" ]]\n    [[ $(candidate \"_\" \"Bb 91245\") = \"_.Bb\" ]]\n    [[ $(candidate \"Sp\" \"671235 Bb\") = \"Sp.671235\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_119_match_parens", "language": "sh", "prompt": "#!/bin/bash\n# You are given a list of two strings, both strings consist of open\n# parentheses '(' or close parentheses ')' only.\n# Your job is to check if it is possible to concatenate the two strings in\n# some order, that the resulting string will be good.\n# A string S is considered to be good if and only if all parentheses in S\n# are balanced. For example: the string '(())()' is good, while the string\n# '())' is not.\n# Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n# Examples:\n# >>> $(match_parens \"()( )\")\n# \"Yes\"\n# >>> $(match_parens \") )\")\n# \"No\"\n#\n# $1 is a space-separated list\nmatch_parens() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    match_parens \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"()( )\") = \"Yes\" ]]\n    [[ $(candidate \") )\") = \"No\" ]]\n    [[ $(candidate \"(()(()) ())())\") = \"No\" ]]\n    [[ $(candidate \")()) (()()(\") = \"Yes\" ]]\n    [[ $(candidate \"(()))) (()())((\") = \"Yes\" ]]\n    [[ $(candidate \"() ())\") = \"No\" ]]\n    [[ $(candidate \"(()( ()))()\") = \"Yes\" ]]\n    [[ $(candidate \"(((( ((())\") = \"No\" ]]\n    [[ $(candidate \")(() (()(\") = \"No\" ]]\n    [[ $(candidate \")( )(\") = \"No\" ]]\n    [[ $(candidate \"( )\") = \"Yes\" ]]\n    [[ $(candidate \") (\") = \"Yes\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_119_match_parens", "test": "}\n\ncandidate() {\n    match_parens \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"()( )\") = \"Yes\" ]]\n    [[ $(candidate \") )\") = \"No\" ]]\n    [[ $(candidate \"(()(()) ())())\") = \"No\" ]]\n    [[ $(candidate \")()) (()()(\") = \"Yes\" ]]\n    [[ $(candidate \"(()))) (()())((\") = \"Yes\" ]]\n    [[ $(candidate \"() ())\") = \"No\" ]]\n    [[ $(candidate \"(()( ()))()\") = \"Yes\" ]]\n    [[ $(candidate \"(((( ((())\") = \"No\" ]]\n    [[ $(candidate \")(() (()(\") = \"No\" ]]\n    [[ $(candidate \")( )(\") = \"No\" ]]\n    [[ $(candidate \"( )\") = \"Yes\" ]]\n    [[ $(candidate \") (\") = \"Yes\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_90_next_smallest", "language": "sh", "prompt": "#!/bin/bash\n# You are given a list of integers.\n# Write a function next_smallest() that returns the 2nd smallest element of the list.\n# Return None if there is no such element.\n# >>> $(next_smallest \"1 2 3 4 5\")\n# \"2\"\n# >>> $(next_smallest \"5 1 4 3 2\")\n# \"2\"\n# >>> $(next_smallest \"\")\n# \"None\"\n# >>> $(next_smallest \"1 1\")\n# \"None\"\n#\n# $1 is a space-separated list\nnext_smallest() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    next_smallest \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5\") = \"2\" ]]\n    [[ $(candidate \"5 1 4 3 2\") = \"2\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"1 1\") = \"None\" ]]\n    [[ $(candidate \"1 1 1 1 0\") = \"1\" ]]\n    [[ $(candidate \"1 1\") = \"None\" ]]\n    [[ $(candidate \"-35 34 12 -45\") = \"-35\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_90_next_smallest", "test": "}\n\ncandidate() {\n    next_smallest \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5\") = \"2\" ]]\n    [[ $(candidate \"5 1 4 3 2\") = \"2\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"1 1\") = \"None\" ]]\n    [[ $(candidate \"1 1 1 1 0\") = \"1\" ]]\n    [[ $(candidate \"1 1\") = \"None\" ]]\n    [[ $(candidate \"-35 34 12 -45\") = \"-35\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_92_any_int", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that takes 3 numbers.\n# Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n# Returns false in any other cases.\n# Examples\n# >>> $(any_int \"5\" \"2\" \"7\")\n# \"true\"\n# >>> $(any_int \"3\" \"2\" \"2\")\n# \"false\"\n# >>> $(any_int \"3\" \"-2\" \"1\")\n# \"true\"\n# >>> $(any_int \"3.6\" \"-2.2\" \"2\")\n# \"false\"\n#\n# $1 is a floating point\n# $2 is a floating point\n# $3 is a floating point\nany_int() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    any_int \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\" \"3\" \"1\") = \"true\" ]]\n    [[ $(candidate \"2.5\" \"2\" \"3\") = \"false\" ]]\n    [[ $(candidate \"1.5\" \"5\" \"3.5\") = \"false\" ]]\n    [[ $(candidate \"2\" \"6\" \"2\") = \"false\" ]]\n    [[ $(candidate \"4\" \"2\" \"2\") = \"true\" ]]\n    [[ $(candidate \"2.2\" \"2.2\" \"2.2\") = \"false\" ]]\n    [[ $(candidate \"-4\" \"6\" \"2\") = \"true\" ]]\n    [[ $(candidate \"2\" \"1\" \"1\") = \"true\" ]]\n    [[ $(candidate \"3\" \"4\" \"7\") = \"true\" ]]\n    [[ $(candidate \"3.0\" \"4\" \"7\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_92_any_int", "test": "}\n\ncandidate() {\n    any_int \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\" \"3\" \"1\") = \"true\" ]]\n    [[ $(candidate \"2.5\" \"2\" \"3\") = \"false\" ]]\n    [[ $(candidate \"1.5\" \"5\" \"3.5\") = \"false\" ]]\n    [[ $(candidate \"2\" \"6\" \"2\") = \"false\" ]]\n    [[ $(candidate \"4\" \"2\" \"2\") = \"true\" ]]\n    [[ $(candidate \"2.2\" \"2.2\" \"2.2\") = \"false\" ]]\n    [[ $(candidate \"-4\" \"6\" \"2\") = \"true\" ]]\n    [[ $(candidate \"2\" \"1\" \"1\") = \"true\" ]]\n    [[ $(candidate \"3\" \"4\" \"7\") = \"true\" ]]\n    [[ $(candidate \"3.0\" \"4\" \"7\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_2_truncate_number", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive floating point number, it can be decomposed into\n# and integer part (largest integer smaller than given number) and decimals\n# (leftover part always smaller than 1).\n# Return the decimal part of the number.\n# >>> $(truncate_number \"3.5\")\n# \"0.5\"\n#\n# $1 is a floating point\ntruncate_number() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    truncate_number \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3.5\") = \"0.5\" ]]\n    [[ $(candidate \"1.25\") = \"0.25\" ]]\n    [[ $(candidate \"123.0\") = \"0.0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_2_truncate_number", "test": "}\n\ncandidate() {\n    truncate_number \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3.5\") = \"0.5\" ]]\n    [[ $(candidate \"1.25\") = \"0.25\" ]]\n    [[ $(candidate \"123.0\") = \"0.0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_42_incr_list", "language": "sh", "prompt": "#!/bin/bash\n# Return list with elements incremented by 1.\n# >>> $(incr_list \"1 2 3\")\n# ['\"2\"', '\"3\"', '\"4\"']\n# >>> $(incr_list \"5 3 5 2 3 3 9 0 123\")\n# ['\"6\"', '\"4\"', '\"6\"', '\"3\"', '\"4\"', '\"4\"', '\"10\"', '\"1\"', '\"124\"']\n#\n# $1 is a space-separated list\nincr_list() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    incr_list \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"3 2 1\") = \"4 3 2\" ]]\n    [[ $(candidate \"5 2 5 2 3 3 9 0 123\") = \"6 3 6 3 4 4 10 1 124\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_42_incr_list", "test": "}\n\ncandidate() {\n    incr_list \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"3 2 1\") = \"4 3 2\" ]]\n    [[ $(candidate \"5 2 5 2 3 3 9 0 123\") = \"6 3 6 3 4 4 10 1 124\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_150_x_or_y", "language": "sh", "prompt": "#!/bin/bash\n# A simple program which should return the value of x if n is \n# a prime number and should return the value of y otherwise.\n# Examples:\n# >>> $(x_or_y \"7\" \"34\" \"12\")\n# \"34\"\n# >>> $(x_or_y \"15\" \"8\" \"5\")\n# \"5\"\n#\n# $1 is an integer\n# $2 is an integer\n# $3 is an integer\nx_or_y() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    x_or_y \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"7\" \"34\" \"12\") = \"34\" ]]\n    [[ $(candidate \"15\" \"8\" \"5\") = \"5\" ]]\n    [[ $(candidate \"3\" \"33\" \"5212\") = \"33\" ]]\n    [[ $(candidate \"1259\" \"3\" \"52\") = \"3\" ]]\n    [[ $(candidate \"7919\" \"-1\" \"12\") = \"-1\" ]]\n    [[ $(candidate \"3609\" \"1245\" \"583\") = \"583\" ]]\n    [[ $(candidate \"91\" \"56\" \"129\") = \"129\" ]]\n    [[ $(candidate \"6\" \"34\" \"1234\") = \"1234\" ]]\n    [[ $(candidate \"1\" \"2\" \"0\") = \"0\" ]]\n    [[ $(candidate \"2\" \"2\" \"0\") = \"2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_150_x_or_y", "test": "}\n\ncandidate() {\n    x_or_y \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"7\" \"34\" \"12\") = \"34\" ]]\n    [[ $(candidate \"15\" \"8\" \"5\") = \"5\" ]]\n    [[ $(candidate \"3\" \"33\" \"5212\") = \"33\" ]]\n    [[ $(candidate \"1259\" \"3\" \"52\") = \"3\" ]]\n    [[ $(candidate \"7919\" \"-1\" \"12\") = \"-1\" ]]\n    [[ $(candidate \"3609\" \"1245\" \"583\") = \"583\" ]]\n    [[ $(candidate \"91\" \"56\" \"129\") = \"129\" ]]\n    [[ $(candidate \"6\" \"34\" \"1234\") = \"1234\" ]]\n    [[ $(candidate \"1\" \"2\" \"0\") = \"0\" ]]\n    [[ $(candidate \"2\" \"2\" \"0\") = \"2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_49_modp", "language": "sh", "prompt": "#!/bin/bash\n# Return 2^n modulo p (be aware of numerics).\n# >>> $(modp \"3\" \"5\")\n# \"3\"\n# >>> $(modp \"1101\" \"101\")\n# \"2\"\n# >>> $(modp \"0\" \"101\")\n# \"1\"\n# >>> $(modp \"3\" \"11\")\n# \"8\"\n# >>> $(modp \"100\" \"101\")\n# \"1\"\n#\n# $1 is an integer\n# $2 is an integer\nmodp() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    modp \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"5\") = \"3\" ]]\n    [[ $(candidate \"1101\" \"101\") = \"2\" ]]\n    [[ $(candidate \"0\" \"101\") = \"1\" ]]\n    [[ $(candidate \"3\" \"11\") = \"8\" ]]\n    [[ $(candidate \"100\" \"101\") = \"1\" ]]\n    [[ $(candidate \"30\" \"5\") = \"4\" ]]\n    [[ $(candidate \"31\" \"5\") = \"3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_49_modp", "test": "}\n\ncandidate() {\n    modp \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"5\") = \"3\" ]]\n    [[ $(candidate \"1101\" \"101\") = \"2\" ]]\n    [[ $(candidate \"0\" \"101\") = \"1\" ]]\n    [[ $(candidate \"3\" \"11\") = \"8\" ]]\n    [[ $(candidate \"100\" \"101\") = \"1\" ]]\n    [[ $(candidate \"30\" \"5\") = \"4\" ]]\n    [[ $(candidate \"31\" \"5\") = \"3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_155_even_odd_count", "language": "sh", "prompt": "#!/bin/bash\n# Given an integer. return a list that has the number of even and odd digits respectively.\n# Example:\n# >>> $(even_odd_count \"-12\")\n# ['\"1\"', '\"1\"']\n# >>> $(even_odd_count \"123\")\n# ['\"1\"', '\"2\"']\n#\n# $1 is an integer\neven_odd_count() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    even_odd_count \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"7\") = \"0 1\" ]]\n    [[ $(candidate \"-78\") = \"1 1\" ]]\n    [[ $(candidate \"3452\") = \"2 2\" ]]\n    [[ $(candidate \"346211\") = \"3 3\" ]]\n    [[ $(candidate \"-345821\") = \"3 3\" ]]\n    [[ $(candidate \"-2\") = \"1 0\" ]]\n    [[ $(candidate \"-45347\") = \"2 3\" ]]\n    [[ $(candidate \"0\") = \"1 0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_155_even_odd_count", "test": "}\n\ncandidate() {\n    even_odd_count \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"7\") = \"0 1\" ]]\n    [[ $(candidate \"-78\") = \"1 1\" ]]\n    [[ $(candidate \"3452\") = \"2 2\" ]]\n    [[ $(candidate \"346211\") = \"3 3\" ]]\n    [[ $(candidate \"-345821\") = \"3 3\" ]]\n    [[ $(candidate \"-2\") = \"1 0\" ]]\n    [[ $(candidate \"-45347\") = \"2 3\" ]]\n    [[ $(candidate \"0\") = \"1 0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_80_is_happy", "language": "sh", "prompt": "#!/bin/bash\n# You are given a string s.\n# Your task is to check if the string is hapsh or not.\n# A string is hapsh if its length is at least 3 and every 3 consecutive letters are distinct\n# For example:\n# >>> $(is_happy \"a\")\n# \"false\"\n# >>> $(is_happy \"aa\")\n# \"false\"\n# >>> $(is_happy \"abcd\")\n# \"true\"\n# >>> $(is_happy \"aabb\")\n# \"false\"\n# >>> $(is_happy \"adb\")\n# \"true\"\n# >>> $(is_happy \"xyy\")\n# \"false\"\n#\n# $1 is a string\nis_happy() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_happy \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"a\") = \"false\" ]]\n    [[ $(candidate \"aa\") = \"false\" ]]\n    [[ $(candidate \"abcd\") = \"true\" ]]\n    [[ $(candidate \"aabb\") = \"false\" ]]\n    [[ $(candidate \"adb\") = \"true\" ]]\n    [[ $(candidate \"xyy\") = \"false\" ]]\n    [[ $(candidate \"iopaxpoi\") = \"true\" ]]\n    [[ $(candidate \"iopaxioi\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_80_is_happy", "test": "}\n\ncandidate() {\n    is_happy \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"a\") = \"false\" ]]\n    [[ $(candidate \"aa\") = \"false\" ]]\n    [[ $(candidate \"abcd\") = \"true\" ]]\n    [[ $(candidate \"aabb\") = \"false\" ]]\n    [[ $(candidate \"adb\") = \"true\" ]]\n    [[ $(candidate \"xyy\") = \"false\" ]]\n    [[ $(candidate \"iopaxpoi\") = \"true\" ]]\n    [[ $(candidate \"iopaxioi\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_59_largest_prime_factor", "language": "sh", "prompt": "#!/bin/bash\n# Return the largest prime factor of n. Assume n > 1 and is not a prime.\n# >>> $(largest_prime_factor \"13195\")\n# \"29\"\n# >>> $(largest_prime_factor \"2048\")\n# \"2\"\n#\n# $1 is an integer\nlargest_prime_factor() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    largest_prime_factor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"15\") = \"5\" ]]\n    [[ $(candidate \"27\") = \"3\" ]]\n    [[ $(candidate \"63\") = \"7\" ]]\n    [[ $(candidate \"330\") = \"11\" ]]\n    [[ $(candidate \"13195\") = \"29\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_59_largest_prime_factor", "test": "}\n\ncandidate() {\n    largest_prime_factor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"15\") = \"5\" ]]\n    [[ $(candidate \"27\") = \"3\" ]]\n    [[ $(candidate \"63\") = \"7\" ]]\n    [[ $(candidate \"330\") = \"11\" ]]\n    [[ $(candidate \"13195\") = \"29\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_66_digitSum", "language": "sh", "prompt": "#!/bin/bash\n# Task\n# Write a function that takes a string as input and returns the sum of the upper characters only'\n# ASCII codes.\n# Examples:\n# >>> $(digitSum \"\")\n# \"0\"\n# >>> $(digitSum \"abAB\")\n# \"131\"\n# >>> $(digitSum \"abcCd\")\n# \"67\"\n# >>> $(digitSum \"helloE\")\n# \"69\"\n# >>> $(digitSum \"woArBld\")\n# \"131\"\n# >>> $(digitSum \"aAaaaXa\")\n# \"153\"\n#\n# $1 is a string\ndigitSum() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    digitSum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"abAB\") = \"131\" ]]\n    [[ $(candidate \"abcCd\") = \"67\" ]]\n    [[ $(candidate \"helloE\") = \"69\" ]]\n    [[ $(candidate \"woArBld\") = \"131\" ]]\n    [[ $(candidate \"aAaaaXa\") = \"153\" ]]\n    [[ $(candidate \" How are yOu?\") = \"151\" ]]\n    [[ $(candidate \"You arE Very Smart\") = \"327\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_66_digitSum", "test": "}\n\ncandidate() {\n    digitSum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"abAB\") = \"131\" ]]\n    [[ $(candidate \"abcCd\") = \"67\" ]]\n    [[ $(candidate \"helloE\") = \"69\" ]]\n    [[ $(candidate \"woArBld\") = \"131\" ]]\n    [[ $(candidate \"aAaaaXa\") = \"153\" ]]\n    [[ $(candidate \" How are yOu?\") = \"151\" ]]\n    [[ $(candidate \"You arE Very Smart\") = \"327\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_21_rescale_to_unit", "language": "sh", "prompt": "#!/bin/bash\n# Given list of numbers (of at least two elements), apply a linear transform to that list,\n# such that the smallest number will become 0 and the largest will become 1\n# >>> $(rescale_to_unit \"1.0 2.0 3.0 4.0 5.0\")\n# ['\"0.0\"', '\"0.25\"', '\"0.5\"', '\"0.75\"', '\"1.0\"']\n#\n# $1 is a space-separated list\nrescale_to_unit() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    rescale_to_unit \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2.0 49.9\") = \"0.0 1.0\" ]]\n    [[ $(candidate \"100.0 49.9\") = \"1.0 0.0\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0\") = \"0.0 0.25 0.5 0.75 1.0\" ]]\n    [[ $(candidate \"2.0 1.0 5.0 3.0 4.0\") = \"0.25 0.0 1.0 0.5 0.75\" ]]\n    [[ $(candidate \"12.0 11.0 15.0 13.0 14.0\") = \"0.25 0.0 1.0 0.5 0.75\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_21_rescale_to_unit", "test": "}\n\ncandidate() {\n    rescale_to_unit \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2.0 49.9\") = \"0.0 1.0\" ]]\n    [[ $(candidate \"100.0 49.9\") = \"1.0 0.0\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0\") = \"0.0 0.25 0.5 0.75 1.0\" ]]\n    [[ $(candidate \"2.0 1.0 5.0 3.0 4.0\") = \"0.25 0.0 1.0 0.5 0.75\" ]]\n    [[ $(candidate \"12.0 11.0 15.0 13.0 14.0\") = \"0.25 0.0 1.0 0.5 0.75\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_121_solution", "language": "sh", "prompt": "#!/bin/bash\n# Given a non-empty list of integers, return the sum of all of the odd elements that are in even positions.\n# Examples\n# >>> $(solution \"5 8 7 1\")\n# \"12\"\n# >>> $(solution \"3 3 3 3 3\")\n# \"9\"\n# >>> $(solution \"30 13 24 321\")\n# \"0\"\n#\n# $1 is a space-separated list\nsolution() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    solution \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 8 7 1\") = \"12\" ]]\n    [[ $(candidate \"3 3 3 3 3\") = \"9\" ]]\n    [[ $(candidate \"30 13 24 321\") = \"0\" ]]\n    [[ $(candidate \"5 9\") = \"5\" ]]\n    [[ $(candidate \"2 4 8\") = \"0\" ]]\n    [[ $(candidate \"30 13 23 32\") = \"23\" ]]\n    [[ $(candidate \"3 13 2 9\") = \"3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_121_solution", "test": "}\n\ncandidate() {\n    solution \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 8 7 1\") = \"12\" ]]\n    [[ $(candidate \"3 3 3 3 3\") = \"9\" ]]\n    [[ $(candidate \"30 13 24 321\") = \"0\" ]]\n    [[ $(candidate \"5 9\") = \"5\" ]]\n    [[ $(candidate \"2 4 8\") = \"0\" ]]\n    [[ $(candidate \"30 13 23 32\") = \"23\" ]]\n    [[ $(candidate \"3 13 2 9\") = \"3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_68_pluck", "language": "sh", "prompt": "#!/bin/bash\n# \"Given an array representing a branch of a tree that has non-negative integer nodes\n# your task is to pluck one of the nodes and return it.\n# The plucked node should be the node with the smallest even value.\n# If multiple nodes with the same smallest even value are found return the node that has smallest index.\n# The plucked node should be returned in a list, [ smalest_value, its index ],\n# If there are no even values or the given array is empty, return [].\n# Example 1:\n# >>> $(pluck \"4 2 3\")\n# ['\"2\"', '\"1\"']\n# Explanation: 2 has the smallest even value, and 2 has the smallest index.\n# Example 2:\n# >>> $(pluck \"1 2 3\")\n# ['\"2\"', '\"1\"']\n# Explanation: 2 has the smallest even value, and 2 has the smallest index.\n# Example 3:\n# >>> $(pluck \"\")\n# []\n# Example 4:\n# >>> $(pluck \"5 0 3 0 4 2\")\n# ['\"0\"', '\"1\"']\n# Explanation: 0 is the smallest value, but  there are two zeros,\n# so we will choose the first zero, which has the smallest index.\n# Constraints:\n# * 1 <= nodes.length <= 10000\n# * 0 <= node.value\n#\n# $1 is a space-separated list\npluck() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    pluck \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4 2 3\") = \"2 1\" ]]\n    [[ $(candidate \"1 2 3\") = \"2 1\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"5 0 3 0 4 2\") = \"0 1\" ]]\n    [[ $(candidate \"1 2 3 0 5 3\") = \"0 3\" ]]\n    [[ $(candidate \"5 4 8 4 8\") = \"4 1\" ]]\n    [[ $(candidate \"7 6 7 1\") = \"6 1\" ]]\n    [[ $(candidate \"7 9 7 1\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_68_pluck", "test": "}\n\ncandidate() {\n    pluck \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4 2 3\") = \"2 1\" ]]\n    [[ $(candidate \"1 2 3\") = \"2 1\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"5 0 3 0 4 2\") = \"0 1\" ]]\n    [[ $(candidate \"1 2 3 0 5 3\") = \"0 3\" ]]\n    [[ $(candidate \"5 4 8 4 8\") = \"4 1\" ]]\n    [[ $(candidate \"7 6 7 1\") = \"6 1\" ]]\n    [[ $(candidate \"7 9 7 1\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_147_get_max_triples", "language": "sh", "prompt": "#!/bin/bash\n# You are given a positive integer n. You have to create an integer array a of length n.\n# For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n# Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n# and a[i] + a[j] + a[k] is a multiple of 3.\n# Example :\n# >>> $(get_max_triples \"5\")\n# \"1\"\n# Explanation: \n# a = [1, 3, 7, 13, 21]\n# The only valid triple is (1, 7, 13).\n#\n# $1 is an integer\nget_max_triples() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    get_max_triples \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"1\" ]]\n    [[ $(candidate \"6\") = \"4\" ]]\n    [[ $(candidate \"10\") = \"36\" ]]\n    [[ $(candidate \"100\") = \"53361\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_147_get_max_triples", "test": "}\n\ncandidate() {\n    get_max_triples \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"1\" ]]\n    [[ $(candidate \"6\") = \"4\" ]]\n    [[ $(candidate \"10\") = \"36\" ]]\n    [[ $(candidate \"100\") = \"53361\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_110_exchange", "language": "sh", "prompt": "#!/bin/bash\n# In this problem, you will implement a function that takes two lists of numbers,\n# and determines whether it is possible to perform an exchange of elements\n# between them to make lst1 a list of only even numbers.\n# There is no limit on the number of exchanged elements between lst1 and lst2.\n# If it is possible to exchange elements between the lst1 and lst2 to make\n# all the elements of lst1 to be even, return \"YES\".\n# Otherwise, return \"NO\".\n# For example:\n# >>> $(exchange \"1 2 3 4\" \"1 2 3 4\")\n# \"YES\"\n# >>> $(exchange \"1 2 3 4\" \"1 5 3 4\")\n# \"NO\"\n# It is assumed that the input lists will be non-empty.\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\nexchange() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    exchange \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4\" \"1 2 3 4\") = \"YES\" ]]\n    [[ $(candidate \"1 2 3 4\" \"1 5 3 4\") = \"NO\" ]]\n    [[ $(candidate \"1 2 3 4\" \"2 1 4 3\") = \"YES\" ]]\n    [[ $(candidate \"5 7 3\" \"2 6 4\") = \"YES\" ]]\n    [[ $(candidate \"5 7 3\" \"2 6 3\") = \"NO\" ]]\n    [[ $(candidate \"3 2 6 1 8 9\" \"3 5 5 1 1 1\") = \"NO\" ]]\n    [[ $(candidate \"100 200\" \"200 200\") = \"YES\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_110_exchange", "test": "}\n\ncandidate() {\n    exchange \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4\" \"1 2 3 4\") = \"YES\" ]]\n    [[ $(candidate \"1 2 3 4\" \"1 5 3 4\") = \"NO\" ]]\n    [[ $(candidate \"1 2 3 4\" \"2 1 4 3\") = \"YES\" ]]\n    [[ $(candidate \"5 7 3\" \"2 6 4\") = \"YES\" ]]\n    [[ $(candidate \"5 7 3\" \"2 6 3\") = \"NO\" ]]\n    [[ $(candidate \"3 2 6 1 8 9\" \"3 5 5 1 1 1\") = \"NO\" ]]\n    [[ $(candidate \"100 200\" \"200 200\") = \"YES\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_47_median", "language": "sh", "prompt": "#!/bin/bash\n# Return median of elements in the list l.\n# >>> $(median \"3 1 2 4 5\")\n# \"3\"\n# >>> $(median \"-10 4 6 1000 10 20\")\n# \"15.0\"\n#\n# $1 is a space-separated list\nmedian() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    median \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 1 2 4 5\") = \"3\" ]]\n    [[ $(candidate \"-10 4 6 1000 10 20\") = \"8.0\" ]]\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"6 5\") = \"5.5\" ]]\n    [[ $(candidate \"8 1 3 9 9 2 7\") = \"7\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_47_median", "test": "}\n\ncandidate() {\n    median \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3 1 2 4 5\") = \"3\" ]]\n    [[ $(candidate \"-10 4 6 1000 10 20\") = \"8.0\" ]]\n    [[ $(candidate \"5\") = \"5\" ]]\n    [[ $(candidate \"6 5\") = \"5.5\" ]]\n    [[ $(candidate \"8 1 3 9 9 2 7\") = \"7\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_82_prime_length", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that takes a string and returns true if the string\n# length is a prime number or false otherwise\n# Examples\n# >>> $(prime_length \"Hello\")\n# \"true\"\n# >>> $(prime_length \"abcdcba\")\n# \"true\"\n# >>> $(prime_length \"kittens\")\n# \"true\"\n# >>> $(prime_length \"orange\")\n# \"false\"\n#\n# $1 is a string\nprime_length() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    prime_length \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello\") = \"true\" ]]\n    [[ $(candidate \"abcdcba\") = \"true\" ]]\n    [[ $(candidate \"kittens\") = \"true\" ]]\n    [[ $(candidate \"orange\") = \"false\" ]]\n    [[ $(candidate \"wow\") = \"true\" ]]\n    [[ $(candidate \"world\") = \"true\" ]]\n    [[ $(candidate \"MadaM\") = \"true\" ]]\n    [[ $(candidate \"Wow\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"HI\") = \"true\" ]]\n    [[ $(candidate \"go\") = \"true\" ]]\n    [[ $(candidate \"gogo\") = \"false\" ]]\n    [[ $(candidate \"aaaaaaaaaaaaaaa\") = \"false\" ]]\n    [[ $(candidate \"Madam\") = \"true\" ]]\n    [[ $(candidate \"M\") = \"false\" ]]\n    [[ $(candidate \"0\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_82_prime_length", "test": "}\n\ncandidate() {\n    prime_length \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello\") = \"true\" ]]\n    [[ $(candidate \"abcdcba\") = \"true\" ]]\n    [[ $(candidate \"kittens\") = \"true\" ]]\n    [[ $(candidate \"orange\") = \"false\" ]]\n    [[ $(candidate \"wow\") = \"true\" ]]\n    [[ $(candidate \"world\") = \"true\" ]]\n    [[ $(candidate \"MadaM\") = \"true\" ]]\n    [[ $(candidate \"Wow\") = \"true\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"HI\") = \"true\" ]]\n    [[ $(candidate \"go\") = \"true\" ]]\n    [[ $(candidate \"gogo\") = \"false\" ]]\n    [[ $(candidate \"aaaaaaaaaaaaaaa\") = \"false\" ]]\n    [[ $(candidate \"Madam\") = \"true\" ]]\n    [[ $(candidate \"M\") = \"false\" ]]\n    [[ $(candidate \"0\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_73_smallest_change", "language": "sh", "prompt": "#!/bin/bash\n# Given an array arr of integers, find the minimum number of elements that\n# need to be changed to make the array palindromic. A palindromic array is an array that\n# is read the same backwards and forwards. In one change, you can change one element to any other element.\n# For example:\n# >>> $(smallest_change \"1 2 3 5 4 7 9 6\")\n# \"4\"\n# >>> $(smallest_change \"1 2 3 4 3 2 2\")\n# \"1\"\n# >>> $(smallest_change \"1 2 3 2 1\")\n# \"0\"\n#\n# $1 is a space-separated list\nsmallest_change() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    smallest_change \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 5 4 7 9 6\") = \"4\" ]]\n    [[ $(candidate \"1 2 3 4 3 2 2\") = \"1\" ]]\n    [[ $(candidate \"1 4 2\") = \"1\" ]]\n    [[ $(candidate \"1 4 4 2\") = \"1\" ]]\n    [[ $(candidate \"1 2 3 2 1\") = \"0\" ]]\n    [[ $(candidate \"3 1 1 3\") = \"0\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"0 1\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_73_smallest_change", "test": "}\n\ncandidate() {\n    smallest_change \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 5 4 7 9 6\") = \"4\" ]]\n    [[ $(candidate \"1 2 3 4 3 2 2\") = \"1\" ]]\n    [[ $(candidate \"1 4 2\") = \"1\" ]]\n    [[ $(candidate \"1 4 4 2\") = \"1\" ]]\n    [[ $(candidate \"1 2 3 2 1\") = \"0\" ]]\n    [[ $(candidate \"3 1 1 3\") = \"0\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"0 1\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_133_sum_squares", "language": "sh", "prompt": "#!/bin/bash\n# You are given a list of numbers.\n# You need to return the sum of squared numbers in the given list,\n# round each element in the list to the upper int(Ceiling) first.\n# Examples:\n# >>> $(lst \"1.0 2.0 3.0\")\n# \"14\"\n# >>> $(lst \"1.0 4.0 9.0\")\n# \"98\"\n# >>> $(lst \"1.0 3.0 5.0 7.0\")\n# \"84\"\n# >>> $(lst \"1.4 4.2 0.0\")\n# \"29\"\n# >>> $(lst \"-2.4 1.0 1.0\")\n# \"6\"\n#\n# $1 is a space-separated list\nsum_squares() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sum_squares \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.0\") = \"14\" ]]\n    [[ $(candidate \"1.0 2.0 3.0\") = \"14\" ]]\n    [[ $(candidate \"1.0 3.0 5.0 7.0\") = \"84\" ]]\n    [[ $(candidate \"1.4 4.2 0.0\") = \"29\" ]]\n    [[ $(candidate \"-2.4 1.0 1.0\") = \"6\" ]]\n    [[ $(candidate \"100.0 1.0 15.0 2.0\") = \"10230\" ]]\n    [[ $(candidate \"10000.0 10000.0\") = \"200000000\" ]]\n    [[ $(candidate \"-1.4 4.6 6.3\") = \"75\" ]]\n    [[ $(candidate \"-1.4 17.9 18.9 19.9\") = \"1086\" ]]\n    [[ $(candidate \"0.0\") = \"0\" ]]\n    [[ $(candidate \"-1.0\") = \"1\" ]]\n    [[ $(candidate \"-1.0 1.0 0.0\") = \"2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_133_sum_squares", "test": "}\n\ncandidate() {\n    sum_squares \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0 3.0\") = \"14\" ]]\n    [[ $(candidate \"1.0 2.0 3.0\") = \"14\" ]]\n    [[ $(candidate \"1.0 3.0 5.0 7.0\") = \"84\" ]]\n    [[ $(candidate \"1.4 4.2 0.0\") = \"29\" ]]\n    [[ $(candidate \"-2.4 1.0 1.0\") = \"6\" ]]\n    [[ $(candidate \"100.0 1.0 15.0 2.0\") = \"10230\" ]]\n    [[ $(candidate \"10000.0 10000.0\") = \"200000000\" ]]\n    [[ $(candidate \"-1.4 4.6 6.3\") = \"75\" ]]\n    [[ $(candidate \"-1.4 17.9 18.9 19.9\") = \"1086\" ]]\n    [[ $(candidate \"0.0\") = \"0\" ]]\n    [[ $(candidate \"-1.0\") = \"1\" ]]\n    [[ $(candidate \"-1.0 1.0 0.0\") = \"2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_141_file_name_check", "language": "sh", "prompt": "#!/bin/bash\n# Create a function which takes a string representing a file's name, and returns\n# 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n# A file's name is considered to be valid if and only if all the following conditions \n# are met:\n# - There should not be more than three digits ('0'-'9') in the file's name.\n# - The file's name contains exactly one dot '.'\n# - The substring before the dot should not be empty, and it starts with a letter from \n# the latin alphapet ('a'-'z' and 'A'-'Z').\n# - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n# Examples:\n# >>> $(file_name_check \"example.txt\")\n# \"Yes\"\n# >>> $(file_name_check \"1example.dll\")\n# \"No\"\n#\n# $1 is a string\nfile_name_check() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    file_name_check \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"example.txt\") = \"Yes\" ]]\n    [[ $(candidate \"1example.dll\") = \"No\" ]]\n    [[ $(candidate \"s1sdf3.asd\") = \"No\" ]]\n    [[ $(candidate \"K.dll\") = \"Yes\" ]]\n    [[ $(candidate \"MY16FILE3.exe\") = \"Yes\" ]]\n    [[ $(candidate \"His12FILE94.exe\") = \"No\" ]]\n    [[ $(candidate \"_Y.txt\") = \"No\" ]]\n    [[ $(candidate \"?aREYA.exe\") = \"No\" ]]\n    [[ $(candidate \"/this_is_valid.dll\") = \"No\" ]]\n    [[ $(candidate \"this_is_valid.wow\") = \"No\" ]]\n    [[ $(candidate \"this_is_valid.txt\") = \"Yes\" ]]\n    [[ $(candidate \"this_is_valid.txtexe\") = \"No\" ]]\n    [[ $(candidate \"#this2_i4s_5valid.ten\") = \"No\" ]]\n    [[ $(candidate \"@this1_is6_valid.exe\") = \"No\" ]]\n    [[ $(candidate \"this_is_12valid.6exe4.txt\") = \"No\" ]]\n    [[ $(candidate \"all.exe.txt\") = \"No\" ]]\n    [[ $(candidate \"I563_No.exe\") = \"Yes\" ]]\n    [[ $(candidate \"Is3youfault.txt\") = \"Yes\" ]]\n    [[ $(candidate \"no_one#knows.dll\") = \"Yes\" ]]\n    [[ $(candidate \"1I563_Yes3.exe\") = \"No\" ]]\n    [[ $(candidate \"I563_Yes3.txtt\") = \"No\" ]]\n    [[ $(candidate \"final..txt\") = \"No\" ]]\n    [[ $(candidate \"final132\") = \"No\" ]]\n    [[ $(candidate \"_f4indsartal132.\") = \"No\" ]]\n    [[ $(candidate \".txt\") = \"No\" ]]\n    [[ $(candidate \"s.\") = \"No\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_141_file_name_check", "test": "}\n\ncandidate() {\n    file_name_check \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"example.txt\") = \"Yes\" ]]\n    [[ $(candidate \"1example.dll\") = \"No\" ]]\n    [[ $(candidate \"s1sdf3.asd\") = \"No\" ]]\n    [[ $(candidate \"K.dll\") = \"Yes\" ]]\n    [[ $(candidate \"MY16FILE3.exe\") = \"Yes\" ]]\n    [[ $(candidate \"His12FILE94.exe\") = \"No\" ]]\n    [[ $(candidate \"_Y.txt\") = \"No\" ]]\n    [[ $(candidate \"?aREYA.exe\") = \"No\" ]]\n    [[ $(candidate \"/this_is_valid.dll\") = \"No\" ]]\n    [[ $(candidate \"this_is_valid.wow\") = \"No\" ]]\n    [[ $(candidate \"this_is_valid.txt\") = \"Yes\" ]]\n    [[ $(candidate \"this_is_valid.txtexe\") = \"No\" ]]\n    [[ $(candidate \"#this2_i4s_5valid.ten\") = \"No\" ]]\n    [[ $(candidate \"@this1_is6_valid.exe\") = \"No\" ]]\n    [[ $(candidate \"this_is_12valid.6exe4.txt\") = \"No\" ]]\n    [[ $(candidate \"all.exe.txt\") = \"No\" ]]\n    [[ $(candidate \"I563_No.exe\") = \"Yes\" ]]\n    [[ $(candidate \"Is3youfault.txt\") = \"Yes\" ]]\n    [[ $(candidate \"no_one#knows.dll\") = \"Yes\" ]]\n    [[ $(candidate \"1I563_Yes3.exe\") = \"No\" ]]\n    [[ $(candidate \"I563_Yes3.txtt\") = \"No\" ]]\n    [[ $(candidate \"final..txt\") = \"No\" ]]\n    [[ $(candidate \"final132\") = \"No\" ]]\n    [[ $(candidate \"_f4indsartal132.\") = \"No\" ]]\n    [[ $(candidate \".txt\") = \"No\" ]]\n    [[ $(candidate \"s.\") = \"No\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "sh", "prompt": "#!/bin/bash\n# triples_sum_to_zero takes a list of integers as an input.\n# it returns true if there are three distinct elements in the list that\n# sum to zero, and false otherwise.\n# >>> $(triples_sum_to_zero \"1 3 5 0\")\n# \"false\"\n# >>> $(triples_sum_to_zero \"1 3 -2 1\")\n# \"true\"\n# >>> $(triples_sum_to_zero \"1 2 3 7\")\n# \"false\"\n# >>> $(triples_sum_to_zero \"2 4 -5 3 9 7\")\n# \"true\"\n# >>> $(triples_sum_to_zero \"1\")\n# \"false\"\n#\n# $1 is a space-separated list\ntriples_sum_to_zero() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    triples_sum_to_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 3 5 0\") = \"false\" ]]\n    [[ $(candidate \"1 3 5 -1\") = \"false\" ]]\n    [[ $(candidate \"1 3 -2 1\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 7\") = \"false\" ]]\n    [[ $(candidate \"1 2 5 7\") = \"false\" ]]\n    [[ $(candidate \"2 4 -5 3 9 7\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"1 3 5 -100\") = \"false\" ]]\n    [[ $(candidate \"100 3 5 -100\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "}\n\ncandidate() {\n    triples_sum_to_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 3 5 0\") = \"false\" ]]\n    [[ $(candidate \"1 3 5 -1\") = \"false\" ]]\n    [[ $(candidate \"1 3 -2 1\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 7\") = \"false\" ]]\n    [[ $(candidate \"1 2 5 7\") = \"false\" ]]\n    [[ $(candidate \"2 4 -5 3 9 7\") = \"true\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"1 3 5 -100\") = \"false\" ]]\n    [[ $(candidate \"100 3 5 -100\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_127_intersection", "language": "sh", "prompt": "#!/bin/bash\n# You are given two intervals,\n# where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n# The given intervals are closed which means that the interval (start, end)\n# includes both start and end.\n# For each given interval, it is assumed that its start is less or equal its end.\n# Your task is to determine whether the length of intersection of these two \n# intervals is a prime number.\n# Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n# which its length is 1, which not a prime number.\n# If the length of the intersection is a prime number, return \"YES\",\n# otherwise, return \"NO\".\n# If the two intervals don't intersect, return \"NO\".\n# [input/output] samples:\n# >>> $(intersection \"1 2\" \"2 3\")\n# \"NO\"\n# >>> $(intersection \"-1 1\" \"0 4\")\n# \"NO\"\n# >>> $(intersection \"-3 -1\" \"-5 5\")\n# \"YES\"\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\nintersection() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    intersection \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2\" \"2 3\") = \"NO\" ]]\n    [[ $(candidate \"-1 1\" \"0 4\") = \"NO\" ]]\n    [[ $(candidate \"-3 -1\" \"-5 5\") = \"YES\" ]]\n    [[ $(candidate \"-2 2\" \"-4 0\") = \"YES\" ]]\n    [[ $(candidate \"-11 2\" \"-1 -1\") = \"NO\" ]]\n    [[ $(candidate \"1 2\" \"3 5\") = \"NO\" ]]\n    [[ $(candidate \"1 2\" \"1 2\") = \"NO\" ]]\n    [[ $(candidate \"-2 -2\" \"-3 -2\") = \"NO\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_127_intersection", "test": "}\n\ncandidate() {\n    intersection \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2\" \"2 3\") = \"NO\" ]]\n    [[ $(candidate \"-1 1\" \"0 4\") = \"NO\" ]]\n    [[ $(candidate \"-3 -1\" \"-5 5\") = \"YES\" ]]\n    [[ $(candidate \"-2 2\" \"-4 0\") = \"YES\" ]]\n    [[ $(candidate \"-11 2\" \"-1 -1\") = \"NO\" ]]\n    [[ $(candidate \"1 2\" \"3 5\") = \"NO\" ]]\n    [[ $(candidate \"1 2\" \"1 2\") = \"NO\" ]]\n    [[ $(candidate \"-2 -2\" \"-3 -2\") = \"NO\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_1_separate_paren_groups", "language": "sh", "prompt": "#!/bin/bash\n# Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n# separate those group into separate strings and return the list of those.\n# Separate groups are balanced (each open brace is properly closed) and not nested within each other\n# Ignore any spaces in the input string.\n# >>> $(separate_paren_groups \"( ) (( )) (( )( ))\")\n# ['\"()\"', '\"(())\"', '\"(()())\"']\n#\n# $1 is a string\nseparate_paren_groups() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    separate_paren_groups \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"(()()) ((())) () ((())()())\") = \"(()()) ((())) () ((())()())\" ]]\n    [[ $(candidate \"() (()) ((())) (((())))\") = \"() (()) ((())) (((())))\" ]]\n    [[ $(candidate \"(()(())((())))\") = \"(()(())((())))\" ]]\n    [[ $(candidate \"( ) (( )) (( )( ))\") = \"() (()) (()())\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_1_separate_paren_groups", "test": "}\n\ncandidate() {\n    separate_paren_groups \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"(()()) ((())) () ((())()())\") = \"(()()) ((())) () ((())()())\" ]]\n    [[ $(candidate \"() (()) ((())) (((())))\") = \"() (()) ((())) (((())))\" ]]\n    [[ $(candidate \"(()(())((())))\") = \"(()(())((())))\" ]]\n    [[ $(candidate \"( ) (( )) (( )( ))\") = \"() (()) (()())\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_152_compare", "language": "sh", "prompt": "#!/bin/bash\n# I think we all remember that feeling when the result of some long-awaited\n# event is finally known. The feelings and thoughts you have at that moment are\n# definitely worth noting down and comparing.\n# Your task is to determine if a person correctly guessed the results of a number of matches.\n# You are given two arrays of scores and guesses of equal length, where each index shows a match. \n# Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n# the value is 0, and if not, the value is the absolute difference between the guess and the score.\n# example:\n# >>> $(compare \"1 2 3 4 5 1\" \"1 2 3 4 2 -2\")\n# ['\"0\"', '\"0\"', '\"0\"', '\"0\"', '\"3\"', '\"3\"']\n# >>> $(compare \"0 5 0 0 0 4\" \"4 1 1 0 0 -2\")\n# ['\"4\"', '\"4\"', '\"1\"', '\"0\"', '\"0\"', '\"6\"']\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\ncompare() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    compare \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5 1\" \"1 2 3 4 2 -2\") = \"0 0 0 0 3 3\" ]]\n    [[ $(candidate \"0 0 0 0 0 0\" \"0 0 0 0 0 0\") = \"0 0 0 0 0 0\" ]]\n    [[ $(candidate \"1 2 3\" \"-1 -2 -3\") = \"2 4 6\" ]]\n    [[ $(candidate \"1 2 3 5\" \"-1 2 3 4\") = \"2 0 0 1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_152_compare", "test": "}\n\ncandidate() {\n    compare \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5 1\" \"1 2 3 4 2 -2\") = \"0 0 0 0 3 3\" ]]\n    [[ $(candidate \"0 0 0 0 0 0\" \"0 0 0 0 0 0\") = \"0 0 0 0 0 0\" ]]\n    [[ $(candidate \"1 2 3\" \"-1 -2 -3\") = \"2 4 6\" ]]\n    [[ $(candidate \"1 2 3 5\" \"-1 2 3 4\") = \"2 0 0 1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_83_starts_one_ends", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer n, return the count of the numbers of n-digit\n# positive integers that start or end with 1.\n#\n# $1 is an integer\nstarts_one_ends() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    starts_one_ends \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"2\") = \"18\" ]]\n    [[ $(candidate \"3\") = \"180\" ]]\n    [[ $(candidate \"4\") = \"1800\" ]]\n    [[ $(candidate \"5\") = \"18000\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_83_starts_one_ends", "test": "}\n\ncandidate() {\n    starts_one_ends \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"2\") = \"18\" ]]\n    [[ $(candidate \"3\") = \"180\" ]]\n    [[ $(candidate \"4\") = \"1800\" ]]\n    [[ $(candidate \"5\") = \"18000\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that returns true if the last character\n# of a given string is an alphabetical character and is not\n# a part of a word, and false otherwise.\n# Note: \"word\" is a group of characters separated by space.\n# Examples:\n# >>> $(check_if_last_char_is_a_letter \"apple pie\")\n# \"false\"\n# >>> $(check_if_last_char_is_a_letter \"apple pi e\")\n# \"true\"\n# >>> $(check_if_last_char_is_a_letter \"apple pi e \")\n# \"false\"\n# >>> $(check_if_last_char_is_a_letter \"\")\n# \"false\"\n#\n# $1 is a string\ncheck_if_last_char_is_a_letter() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    check_if_last_char_is_a_letter \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"apple\") = \"false\" ]]\n    [[ $(candidate \"apple pi e\") = \"true\" ]]\n    [[ $(candidate \"eeeee\") = \"false\" ]]\n    [[ $(candidate \"A\") = \"true\" ]]\n    [[ $(candidate \"Pumpkin pie \") = \"false\" ]]\n    [[ $(candidate \"Pumpkin pie 1\") = \"false\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"eeeee e \") = \"false\" ]]\n    [[ $(candidate \"apple pie\") = \"false\" ]]\n    [[ $(candidate \"apple pi e \") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "}\n\ncandidate() {\n    check_if_last_char_is_a_letter \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"apple\") = \"false\" ]]\n    [[ $(candidate \"apple pi e\") = \"true\" ]]\n    [[ $(candidate \"eeeee\") = \"false\" ]]\n    [[ $(candidate \"A\") = \"true\" ]]\n    [[ $(candidate \"Pumpkin pie \") = \"false\" ]]\n    [[ $(candidate \"Pumpkin pie 1\") = \"false\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"eeeee e \") = \"false\" ]]\n    [[ $(candidate \"apple pie\") = \"false\" ]]\n    [[ $(candidate \"apple pi e \") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_124_valid_date", "language": "sh", "prompt": "#!/bin/bash\n# You have to write a function which validates a given date string and\n# returns true if the date is valid otherwise false.\n# The date is valid if all of the following rules are satisfied:\n# 1. The date string is not empty.\n# 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n# 3. The months should not be less than 1 or higher than 12.\n# 4. The date should be in the format: mm-dd-yyyy\n# >>> $(valid_date \"03-11-2000\")\n# \"true\"\n# >>> $(valid_date \"15-01-2012\")\n# \"false\"\n# >>> $(valid_date \"04-0-2040\")\n# \"false\"\n# >>> $(valid_date \"06-04-2020\")\n# \"true\"\n# >>> $(valid_date \"06/04/2020\")\n# \"false\"\n#\n# $1 is a string\nvalid_date() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    valid_date \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"03-11-2000\") = \"true\" ]]\n    [[ $(candidate \"15-01-2012\") = \"false\" ]]\n    [[ $(candidate \"04-0-2040\") = \"false\" ]]\n    [[ $(candidate \"06-04-2020\") = \"true\" ]]\n    [[ $(candidate \"01-01-2007\") = \"true\" ]]\n    [[ $(candidate \"03-32-2011\") = \"false\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"04-31-3000\") = \"false\" ]]\n    [[ $(candidate \"06-06-2005\") = \"true\" ]]\n    [[ $(candidate \"21-31-2000\") = \"false\" ]]\n    [[ $(candidate \"04-12-2003\") = \"true\" ]]\n    [[ $(candidate \"04122003\") = \"false\" ]]\n    [[ $(candidate \"20030412\") = \"false\" ]]\n    [[ $(candidate \"2003-04\") = \"false\" ]]\n    [[ $(candidate \"2003-04-12\") = \"false\" ]]\n    [[ $(candidate \"04-2003\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_124_valid_date", "test": "}\n\ncandidate() {\n    valid_date \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"03-11-2000\") = \"true\" ]]\n    [[ $(candidate \"15-01-2012\") = \"false\" ]]\n    [[ $(candidate \"04-0-2040\") = \"false\" ]]\n    [[ $(candidate \"06-04-2020\") = \"true\" ]]\n    [[ $(candidate \"01-01-2007\") = \"true\" ]]\n    [[ $(candidate \"03-32-2011\") = \"false\" ]]\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"04-31-3000\") = \"false\" ]]\n    [[ $(candidate \"06-06-2005\") = \"true\" ]]\n    [[ $(candidate \"21-31-2000\") = \"false\" ]]\n    [[ $(candidate \"04-12-2003\") = \"true\" ]]\n    [[ $(candidate \"04122003\") = \"false\" ]]\n    [[ $(candidate \"20030412\") = \"false\" ]]\n    [[ $(candidate \"2003-04\") = \"false\" ]]\n    [[ $(candidate \"2003-04-12\") = \"false\" ]]\n    [[ $(candidate \"04-2003\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_108_count_nums", "language": "sh", "prompt": "#!/bin/bash\n# Write a function count_nums which takes an array of integers and returns\n# the number of elements which has a sum of digits > 0.\n# If a number is negative, then its first signed digit will be negative:\n# e.g. -123 has signed digits -1, 2, and 3.\n# >>> $(count_nums \"\")\n# \"0\"\n# >>> $(count_nums \"-1 11 -11\")\n# \"1\"\n# >>> $(count_nums \"1 1 2\")\n# \"3\"\n#\n# $1 is a space-separated list\ncount_nums() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    count_nums \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"-1 -2 0\") = \"0\" ]]\n    [[ $(candidate \"1 1 2 -2 3 4 5\") = \"6\" ]]\n    [[ $(candidate \"1 6 9 -6 0 1 5\") = \"5\" ]]\n    [[ $(candidate \"1 100 98 -7 1 -1\") = \"4\" ]]\n    [[ $(candidate \"12 23 34 -45 -56 0\") = \"5\" ]]\n    [[ $(candidate \"0 1\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_108_count_nums", "test": "}\n\ncandidate() {\n    count_nums \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"-1 -2 0\") = \"0\" ]]\n    [[ $(candidate \"1 1 2 -2 3 4 5\") = \"6\" ]]\n    [[ $(candidate \"1 6 9 -6 0 1 5\") = \"5\" ]]\n    [[ $(candidate \"1 100 98 -7 1 -1\") = \"4\" ]]\n    [[ $(candidate \"12 23 34 -45 -56 0\") = \"5\" ]]\n    [[ $(candidate \"0 1\") = \"1\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_86_anti_shuffle", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that takes a string and returns an ordered version of it.\n# Ordered version of string, is a string where all words (separated by space)\n# are replaced by a new word where all the characters arranged in\n# ascending order based on ascii value.\n# Note: You should keep the order of words and blank spaces in the sentence.\n# For example:\n# >>> $(anti_shuffle \"Hi\")\n# \"Hi\"\n# >>> $(anti_shuffle \"hello\")\n# \"ehllo\"\n# >>> $(anti_shuffle \"Hello World\\!\\!\\!\")\n# \"Hello \\!\\!\\!Wdlor\"\n#\n# $1 is a string\nanti_shuffle() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    anti_shuffle \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hi\") = \"Hi\" ]]\n    [[ $(candidate \"hello\") = \"ehllo\" ]]\n    [[ $(candidate \"number\") = \"bemnru\" ]]\n    [[ $(candidate \"abcd\") = \"abcd\" ]]\n    [[ $(candidate \"Hello World\\!\\!\\!\") = \"Hello \\!\\!\\!Wdlor\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"Hi. My name is Mister Robot. How are you?\") = \".Hi My aemn is Meirst .Rboot How aer ?ouy\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_86_anti_shuffle", "test": "}\n\ncandidate() {\n    anti_shuffle \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hi\") = \"Hi\" ]]\n    [[ $(candidate \"hello\") = \"ehllo\" ]]\n    [[ $(candidate \"number\") = \"bemnru\" ]]\n    [[ $(candidate \"abcd\") = \"abcd\" ]]\n    [[ $(candidate \"Hello World\\!\\!\\!\") = \"Hello \\!\\!\\!Wdlor\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"Hi. My name is Mister Robot. How are you?\") = \".Hi My aemn is Meirst .Rboot How aer ?ouy\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_48_is_palindrome", "language": "sh", "prompt": "#!/bin/bash\n# Checks if given string is a palindrome\n# >>> $(is_palindrome \"\")\n# \"true\"\n# >>> $(is_palindrome \"aba\")\n# \"true\"\n# >>> $(is_palindrome \"aaaaa\")\n# \"true\"\n# >>> $(is_palindrome \"zbcd\")\n# \"false\"\n#\n# $1 is a string\nis_palindrome() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"true\" ]]\n    [[ $(candidate \"aba\") = \"true\" ]]\n    [[ $(candidate \"aaaaa\") = \"true\" ]]\n    [[ $(candidate \"zbcd\") = \"false\" ]]\n    [[ $(candidate \"xywyx\") = \"true\" ]]\n    [[ $(candidate \"xywyz\") = \"false\" ]]\n    [[ $(candidate \"xywzx\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_48_is_palindrome", "test": "}\n\ncandidate() {\n    is_palindrome \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"true\" ]]\n    [[ $(candidate \"aba\") = \"true\" ]]\n    [[ $(candidate \"aaaaa\") = \"true\" ]]\n    [[ $(candidate \"zbcd\") = \"false\" ]]\n    [[ $(candidate \"xywyx\") = \"true\" ]]\n    [[ $(candidate \"xywyz\") = \"false\" ]]\n    [[ $(candidate \"xywzx\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_118_get_closest_vowel", "language": "sh", "prompt": "#!/bin/bash\n# You are given a word. Your task is to find the closest vowel that stands between \n# two consonants from the right side of the word (case sensitive).\n# Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n# find any vowel met the above condition. \n# You may assume that the given string contains English letter only.\n# Example:\n# >>> $(get_closest_vowel \"yogurt\")\n# \"u\"\n# >>> $(get_closest_vowel \"FULL\")\n# \"U\"\n# >>> $(get_closest_vowel \"quick\")\n# \"\"\n# >>> $(get_closest_vowel \"ab\")\n# \"\"\n#\n# $1 is a string\nget_closest_vowel() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    get_closest_vowel \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"yogurt\") = \"u\" ]]\n    [[ $(candidate \"full\") = \"u\" ]]\n    [[ $(candidate \"easy\") = \"\" ]]\n    [[ $(candidate \"eAsy\") = \"\" ]]\n    [[ $(candidate \"ali\") = \"\" ]]\n    [[ $(candidate \"bad\") = \"a\" ]]\n    [[ $(candidate \"most\") = \"o\" ]]\n    [[ $(candidate \"ab\") = \"\" ]]\n    [[ $(candidate \"ba\") = \"\" ]]\n    [[ $(candidate \"quick\") = \"\" ]]\n    [[ $(candidate \"anime\") = \"i\" ]]\n    [[ $(candidate \"Asia\") = \"\" ]]\n    [[ $(candidate \"Above\") = \"o\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_118_get_closest_vowel", "test": "}\n\ncandidate() {\n    get_closest_vowel \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"yogurt\") = \"u\" ]]\n    [[ $(candidate \"full\") = \"u\" ]]\n    [[ $(candidate \"easy\") = \"\" ]]\n    [[ $(candidate \"eAsy\") = \"\" ]]\n    [[ $(candidate \"ali\") = \"\" ]]\n    [[ $(candidate \"bad\") = \"a\" ]]\n    [[ $(candidate \"most\") = \"o\" ]]\n    [[ $(candidate \"ab\") = \"\" ]]\n    [[ $(candidate \"ba\") = \"\" ]]\n    [[ $(candidate \"quick\") = \"\" ]]\n    [[ $(candidate \"anime\") = \"i\" ]]\n    [[ $(candidate \"Asia\") = \"\" ]]\n    [[ $(candidate \"Above\") = \"o\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_31_is_prime", "language": "sh", "prompt": "#!/bin/bash\n# Return true if a given number is prime, and false otherwise.\n# >>> $(is_prime \"6\")\n# \"false\"\n# >>> $(is_prime \"101\")\n# \"true\"\n# >>> $(is_prime \"11\")\n# \"true\"\n# >>> $(is_prime \"13441\")\n# \"true\"\n# >>> $(is_prime \"61\")\n# \"true\"\n# >>> $(is_prime \"4\")\n# \"false\"\n# >>> $(is_prime \"1\")\n# \"false\"\n#\n# $1 is an integer\nis_prime() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_prime \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"6\") = \"false\" ]]\n    [[ $(candidate \"101\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"true\" ]]\n    [[ $(candidate \"13441\") = \"true\" ]]\n    [[ $(candidate \"61\") = \"true\" ]]\n    [[ $(candidate \"4\") = \"false\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"5\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"true\" ]]\n    [[ $(candidate \"17\") = \"true\" ]]\n    [[ $(candidate \"85\") = \"false\" ]]\n    [[ $(candidate \"77\") = \"false\" ]]\n    [[ $(candidate \"255379\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_31_is_prime", "test": "}\n\ncandidate() {\n    is_prime \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"6\") = \"false\" ]]\n    [[ $(candidate \"101\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"true\" ]]\n    [[ $(candidate \"13441\") = \"true\" ]]\n    [[ $(candidate \"61\") = \"true\" ]]\n    [[ $(candidate \"4\") = \"false\" ]]\n    [[ $(candidate \"1\") = \"false\" ]]\n    [[ $(candidate \"5\") = \"true\" ]]\n    [[ $(candidate \"11\") = \"true\" ]]\n    [[ $(candidate \"17\") = \"true\" ]]\n    [[ $(candidate \"85\") = \"false\" ]]\n    [[ $(candidate \"77\") = \"false\" ]]\n    [[ $(candidate \"255379\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_144_simplify", "language": "sh", "prompt": "#!/bin/bash\n# Your task is to implement a function that will simplify the expression\n# x * n. The function returns true if x * n evaluates to a whole number and false\n# otherwise. Both x and n, are string representation of a fraction, and have the following format,\n# <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n# You can assume that x, and n are valid fractions, and do not have zero as denominator.\n# >>> $(simplify \"1/5\" \"5/1\")\n# \"true\"\n# >>> $(simplify \"1/6\" \"2/1\")\n# \"false\"\n# >>> $(simplify \"7/10\" \"10/2\")\n# \"false\"\n#\n# $1 is a string\n# $2 is a string\nsimplify() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    simplify \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1/5\" \"5/1\") = \"true\" ]]\n    [[ $(candidate \"1/6\" \"2/1\") = \"false\" ]]\n    [[ $(candidate \"5/1\" \"3/1\") = \"true\" ]]\n    [[ $(candidate \"7/10\" \"10/2\") = \"false\" ]]\n    [[ $(candidate \"2/10\" \"50/10\") = \"true\" ]]\n    [[ $(candidate \"7/2\" \"4/2\") = \"true\" ]]\n    [[ $(candidate \"11/6\" \"6/1\") = \"true\" ]]\n    [[ $(candidate \"2/3\" \"5/2\") = \"false\" ]]\n    [[ $(candidate \"5/2\" \"3/5\") = \"false\" ]]\n    [[ $(candidate \"2/4\" \"8/4\") = \"true\" ]]\n    [[ $(candidate \"2/4\" \"4/2\") = \"true\" ]]\n    [[ $(candidate \"1/5\" \"5/1\") = \"true\" ]]\n    [[ $(candidate \"1/5\" \"1/5\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_144_simplify", "test": "}\n\ncandidate() {\n    simplify \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1/5\" \"5/1\") = \"true\" ]]\n    [[ $(candidate \"1/6\" \"2/1\") = \"false\" ]]\n    [[ $(candidate \"5/1\" \"3/1\") = \"true\" ]]\n    [[ $(candidate \"7/10\" \"10/2\") = \"false\" ]]\n    [[ $(candidate \"2/10\" \"50/10\") = \"true\" ]]\n    [[ $(candidate \"7/2\" \"4/2\") = \"true\" ]]\n    [[ $(candidate \"11/6\" \"6/1\") = \"true\" ]]\n    [[ $(candidate \"2/3\" \"5/2\") = \"false\" ]]\n    [[ $(candidate \"5/2\" \"3/5\") = \"false\" ]]\n    [[ $(candidate \"2/4\" \"8/4\") = \"true\" ]]\n    [[ $(candidate \"2/4\" \"4/2\") = \"true\" ]]\n    [[ $(candidate \"1/5\" \"5/1\") = \"true\" ]]\n    [[ $(candidate \"1/5\" \"1/5\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_78_hex_key", "language": "sh", "prompt": "#!/bin/bash\n# You have been tasked to write a function that receives \n# a hexadecimal number as a string and counts the number of hexadecimal \n# digits that are primes (prime number, or a prime, is a natural number \n# greater than 1 that is not a product of two smaller natural numbers).\n# Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n# Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n# So you have to determine a number of the following digits: 2, 3, 5, 7, \n# B (=decimal 11), D (=decimal 13).\n# Note: you may assume the input is always correct or empty string, \n# and symbols A,B,C,D,E,F are always uppercase.\n# Examples:\n# >>> $(hex_key \"AB\")\n# \"1\"\n# >>> $(hex_key \"1077E\")\n# \"2\"\n# >>> $(hex_key \"ABED1A33\")\n# \"4\"\n# >>> $(hex_key \"123456789ABCDEF0\")\n# \"6\"\n# >>> $(hex_key \"2020\")\n# \"2\"\n#\n# $1 is a string\nhex_key() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    hex_key \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"AB\") = \"1\" ]]\n    [[ $(candidate \"1077E\") = \"2\" ]]\n    [[ $(candidate \"ABED1A33\") = \"4\" ]]\n    [[ $(candidate \"2020\") = \"2\" ]]\n    [[ $(candidate \"123456789ABCDEF0\") = \"6\" ]]\n    [[ $(candidate \"112233445566778899AABBCCDDEEFF00\") = \"12\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_78_hex_key", "test": "}\n\ncandidate() {\n    hex_key \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"AB\") = \"1\" ]]\n    [[ $(candidate \"1077E\") = \"2\" ]]\n    [[ $(candidate \"ABED1A33\") = \"4\" ]]\n    [[ $(candidate \"2020\") = \"2\" ]]\n    [[ $(candidate \"123456789ABCDEF0\") = \"6\" ]]\n    [[ $(candidate \"112233445566778899AABBCCDDEEFF00\") = \"12\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_143_words_in_sentence", "language": "sh", "prompt": "#!/bin/bash\n# You are given a string representing a sentence,\n# the sentence contains some words separated by a space,\n# and you have to return a string that contains the words from the original sentence,\n# whose lengths are prime numbers,\n# the order of the words in the new string should be the same as the original one.\n# Example 1:\n# >>> $(words_in_sentence \"This is a test\")\n# \"is\"\n# Example 2:\n# >>> $(words_in_sentence \"lets go for swimming\")\n# \"go for\"\n# Constraints:\n# * 1 <= len(sentence) <= 100\n# * sentence contains only letters\n#\n# $1 is a string\nwords_in_sentence() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    words_in_sentence \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"This is a test\") = \"is\" ]]\n    [[ $(candidate \"lets go for swimming\") = \"go for\" ]]\n    [[ $(candidate \"there is no place available here\") = \"there is no place\" ]]\n    [[ $(candidate \"Hi I am Hussein\") = \"Hi am Hussein\" ]]\n    [[ $(candidate \"go for it\") = \"go for it\" ]]\n    [[ $(candidate \"here\") = \"\" ]]\n    [[ $(candidate \"here is\") = \"is\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_143_words_in_sentence", "test": "}\n\ncandidate() {\n    words_in_sentence \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"This is a test\") = \"is\" ]]\n    [[ $(candidate \"lets go for swimming\") = \"go for\" ]]\n    [[ $(candidate \"there is no place available here\") = \"there is no place\" ]]\n    [[ $(candidate \"Hi I am Hussein\") = \"Hi am Hussein\" ]]\n    [[ $(candidate \"go for it\") = \"go for it\" ]]\n    [[ $(candidate \"here\") = \"\" ]]\n    [[ $(candidate \"here is\") = \"is\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_111_histogram", "language": "sh", "prompt": "#!/bin/bash\n# Given a string representing a space separated lowercase letters, return a CSV\n# of the letter with the most repetition and containing the corresponding count.\n# If several letters have the same occurrence, return all of them.\n# Example:\n# >>> $(histogram \"a b c\")\n# {'\"a\"': '\"1\"', '\"b\"': '\"1\"', '\"c\"': '\"1\"'}\n# >>> $(histogram \"a b b a\")\n# {'\"a\"': '\"2\"', '\"b\"': '\"2\"'}\n# >>> $(histogram \"a b c a b\")\n# {'\"a\"': '\"2\"', '\"b\"': '\"2\"'}\n# >>> $(histogram \"b b b b a\")\n# {'\"b\"': '\"4\"'}\n# >>> $(histogram \"\")\n# {}\n#\n# $1 is a string\nhistogram() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    histogram \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"a b b a\") = \"a,2\\nb,2\" ]]\n    [[ $(candidate \"a b c a b\") = \"a,2\\nb,2\" ]]\n    [[ $(candidate \"a b c d g\") = \"a,1\\nb,1\\nc,1\\nd,1\\ng,1\" ]]\n    [[ $(candidate \"r t g\") = \"r,1\\nt,1\\ng,1\" ]]\n    [[ $(candidate \"b b b b a\") = \"b,4\" ]]\n    [[ $(candidate \"r t g\") = \"r,1\\nt,1\\ng,1\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"a\") = \"a,1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_111_histogram", "test": "}\n\ncandidate() {\n    histogram \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"a b b a\") = \"a,2\\nb,2\" ]]\n    [[ $(candidate \"a b c a b\") = \"a,2\\nb,2\" ]]\n    [[ $(candidate \"a b c d g\") = \"a,1\\nb,1\\nc,1\\nd,1\\ng,1\" ]]\n    [[ $(candidate \"r t g\") = \"r,1\\nt,1\\ng,1\" ]]\n    [[ $(candidate \"b b b b a\") = \"b,4\" ]]\n    [[ $(candidate \"r t g\") = \"r,1\\nt,1\\ng,1\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"a\") = \"a,1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_87_get_row", "language": "sh", "prompt": "#!/bin/bash\n# You are given a 2 dimensional data, as a nested lists,\n# which is similar to matrix, however, unlike matrices,\n# each row may contain a different number of columns.\n# Given lst, and integer x, find integers x in the list,\n# and return list of lists, [(x1, y1), (x2, y2) ...] such that\n# each list is a coordinate - (row, columns), starting with 0.\n# Sort coordinates initially by rows in ascending order.\n# Also, sort coordinates of the row by columns in descending order.\n# Examples:\n# >>> $(get_row \"1 2 3 4 5 6\\n1 2 3 4 1 6\\n1 2 3 4 5 1\" \"1\")\n# [['\"0\"', '\"0\"'], ['\"1\"', '\"4\"'], ['\"1\"', '\"0\"'], ['\"2\"', '\"5\"'], ['\"2\"', '\"0\"']]\n# >>> $(get_row \"\" \"1\")\n# []\n# >>> $(get_row \"\\n1\\n1 2 3\" \"3\")\n# [['\"2\"', '\"2\"']]\n#\n# $1 is a newline-separated, space-separated list\n# $2 is an integer\nget_row() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    get_row \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 1 6\\n1 2 3 4 5 1\" \"1\") = \"0 0\\n1 4\\n1 0\\n2 5\\n2 0\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\" \"2\") = \"0 1\\n1 1\\n2 1\\n3 1\\n4 1\\n5 1\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 1 3 4 5 6\\n1 2 1 4 5 6\\n1 2 3 1 5 6\\n1 2 3 4 1 6\\n1 2 3 4 5 1\" \"1\") = \"0 0\\n1 0\\n2 1\\n2 0\\n3 2\\n3 0\\n4 3\\n4 0\\n5 4\\n5 0\\n6 5\\n6 0\" ]]\n    [[ $(candidate \"\" \"1\") = \"\" ]]\n    [[ $(candidate \"1\" \"2\") = \"\" ]]\n    [[ $(candidate \"\\n1\\n1 2 3\" \"3\") = \"2 2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_87_get_row", "test": "}\n\ncandidate() {\n    get_row \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 1 6\\n1 2 3 4 5 1\" \"1\") = \"0 0\\n1 4\\n1 0\\n2 5\\n2 0\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 2 3 4 5 6\" \"2\") = \"0 1\\n1 1\\n2 1\\n3 1\\n4 1\\n5 1\" ]]\n    [[ $(candidate \"1 2 3 4 5 6\\n1 2 3 4 5 6\\n1 1 3 4 5 6\\n1 2 1 4 5 6\\n1 2 3 1 5 6\\n1 2 3 4 1 6\\n1 2 3 4 5 1\" \"1\") = \"0 0\\n1 0\\n2 1\\n2 0\\n3 2\\n3 0\\n4 3\\n4 0\\n5 4\\n5 0\\n6 5\\n6 0\" ]]\n    [[ $(candidate \"\" \"1\") = \"\" ]]\n    [[ $(candidate \"1\" \"2\") = \"\" ]]\n    [[ $(candidate \"\\n1\\n1 2 3\" \"3\") = \"2 2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_123_get_odd_collatz", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer n, return a sorted list that has the odd numbers in collatz sequence.\n# The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n# as follows: start with any positive integer n. Then each term is obtained from the \n# previous term as follows: if the previous term is even, the next term is one half of \n# the previous term. If the previous term is odd, the next term is 3 times the previous\n# term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n# Note: \n# 1. Collatz(1) is [1].\n# 2. returned list sorted in increasing order.\n# For example:\n# get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n# >>> $(get_odd_collatz \"5\")\n# ['\"1\"', '\"5\"']\n#\n# $1 is an integer\nget_odd_collatz() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    get_odd_collatz \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"14\") = \"1 5 7 11 13 17\" ]]\n    [[ $(candidate \"5\") = \"1 5\" ]]\n    [[ $(candidate \"12\") = \"1 3 5\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_123_get_odd_collatz", "test": "}\n\ncandidate() {\n    get_odd_collatz \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"14\") = \"1 5 7 11 13 17\" ]]\n    [[ $(candidate \"5\") = \"1 5\" ]]\n    [[ $(candidate \"12\") = \"1 3 5\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_135_can_arrange", "language": "sh", "prompt": "#!/bin/bash\n# Create a function which returns the largest index of an element which\n# is not greater than or equal to the element immediately preceding it. If\n# no such element exists then return -1. The given array will not contain\n# duplicate values.\n# Examples:\n# >>> $(can_arrange \"1 2 4 3 5\")\n# \"3\"\n# >>> $(can_arrange \"1 2 3\")\n# \"-1\"\n#\n# $1 is a space-separated list\ncan_arrange() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    can_arrange \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 3 5\") = \"3\" ]]\n    [[ $(candidate \"1 2 4 5\") = \"-1\" ]]\n    [[ $(candidate \"1 4 2 5 6 7 8 9 10\") = \"2\" ]]\n    [[ $(candidate \"4 8 5 7 3\") = \"4\" ]]\n    [[ $(candidate \"\") = \"-1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_135_can_arrange", "test": "}\n\ncandidate() {\n    can_arrange \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 3 5\") = \"3\" ]]\n    [[ $(candidate \"1 2 4 5\") = \"-1\" ]]\n    [[ $(candidate \"1 4 2 5 6 7 8 9 10\") = \"2\" ]]\n    [[ $(candidate \"4 8 5 7 3\") = \"4\" ]]\n    [[ $(candidate \"\") = \"-1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_19_sort_numbers", "language": "sh", "prompt": "#!/bin/bash\n# Input is a space-delimited string of numberals from 'zero' to 'nine'.\n# Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n# Return the string with numbers sorted from smallest to largest\n# >>> $(sort_numbers \"three one five\")\n# \"one three five\"\n#\n# $1 is a string\nsort_numbers() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sort_numbers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"three\") = \"three\" ]]\n    [[ $(candidate \"three five nine\") = \"three five nine\" ]]\n    [[ $(candidate \"five zero four seven nine eight\") = \"zero four five seven eight nine\" ]]\n    [[ $(candidate \"six five four three two one zero\") = \"zero one two three four five six\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_19_sort_numbers", "test": "}\n\ncandidate() {\n    sort_numbers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"three\") = \"three\" ]]\n    [[ $(candidate \"three five nine\") = \"three five nine\" ]]\n    [[ $(candidate \"five zero four seven nine eight\") = \"zero four five seven eight nine\" ]]\n    [[ $(candidate \"six five four three two one zero\") = \"zero one two three four five six\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_65_circular_shift", "language": "sh", "prompt": "#!/bin/bash\n# Circular shift the digits of the integer x, shift the digits right by shift\n# and return the result as a string.\n# If shift > number of digits, return digits reversed.\n# >>> $(circular_shift \"12\" \"1\")\n# \"21\"\n# >>> $(circular_shift \"12\" \"2\")\n# \"12\"\n#\n# $1 is an integer\n# $2 is an integer\ncircular_shift() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    circular_shift \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"100\" \"2\") = \"001\" ]]\n    [[ $(candidate \"12\" \"2\") = \"12\" ]]\n    [[ $(candidate \"97\" \"8\") = \"79\" ]]\n    [[ $(candidate \"12\" \"1\") = \"21\" ]]\n    [[ $(candidate \"11\" \"101\") = \"11\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_65_circular_shift", "test": "}\n\ncandidate() {\n    circular_shift \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"100\" \"2\") = \"001\" ]]\n    [[ $(candidate \"12\" \"2\") = \"12\" ]]\n    [[ $(candidate \"97\" \"8\") = \"79\" ]]\n    [[ $(candidate \"12\" \"1\") = \"21\" ]]\n    [[ $(candidate \"11\" \"101\") = \"11\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_142_sum_squares", "language": "sh", "prompt": "#!/bin/bash\n# \"\n# This function will take a list of integers. For all entries in the list, the function shall square the integer entry if its index is a \n# multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n# change the entries in the list whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n# Examples:\n# >>> lst\n# ['\"1\"', '\"2\"', '\"3\"']\n# >>> lst\n# []\n# >>> lst\n# ['\"-1\"', '\"-5\"', '\"2\"', '\"-1\"', '\"-5\"']\n#\n# $1 is a space-separated list\nsum_squares() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sum_squares \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"6\" ]]\n    [[ $(candidate \"1 4 9\") = \"14\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 1 1 1 1 1 1\") = \"9\" ]]\n    [[ $(candidate \"-1 -1 -1 -1 -1 -1 -1 -1 -1\") = \"-3\" ]]\n    [[ $(candidate \"0\") = \"0\" ]]\n    [[ $(candidate \"-1 -5 2 -1 -5\") = \"-126\" ]]\n    [[ $(candidate \"-56 -99 1 0 -2\") = \"3030\" ]]\n    [[ $(candidate \"-1 0 0 0 0 0 0 0 -1\") = \"0\" ]]\n    [[ $(candidate \"-16 -9 -2 36 36 26 -20 25 -40 20 -4 12 -26 35 37\") = \"-14196\" ]]\n    [[ $(candidate \"-1 -3 17 -1 -15 13 -1 14 -14 -12 -5 14 -14 6 13 11 16 16 4 10\") = \"-1448\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_142_sum_squares", "test": "}\n\ncandidate() {\n    sum_squares \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"6\" ]]\n    [[ $(candidate \"1 4 9\") = \"14\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 1 1 1 1 1 1\") = \"9\" ]]\n    [[ $(candidate \"-1 -1 -1 -1 -1 -1 -1 -1 -1\") = \"-3\" ]]\n    [[ $(candidate \"0\") = \"0\" ]]\n    [[ $(candidate \"-1 -5 2 -1 -5\") = \"-126\" ]]\n    [[ $(candidate \"-56 -99 1 0 -2\") = \"3030\" ]]\n    [[ $(candidate \"-1 0 0 0 0 0 0 0 -1\") = \"0\" ]]\n    [[ $(candidate \"-16 -9 -2 36 36 26 -20 25 -40 20 -4 12 -26 35 37\") = \"-14196\" ]]\n    [[ $(candidate \"-1 -3 17 -1 -15 13 -1 14 -14 -12 -5 14 -14 6 13 11 16 16 4 10\") = \"-1448\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_94_skjkasdkd", "language": "sh", "prompt": "#!/bin/bash\n# You are given a list of integers.\n# You need to find the largest prime value and return the sum of its digits.\n# Examples:\n# >>> $(skjkasdkd \"0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3\")\n# \"10\"\n# >>> $(skjkasdkd \"1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1\")\n# \"25\"\n# >>> $(skjkasdkd \"1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3\")\n# \"13\"\n# >>> $(skjkasdkd \"0 724 32 71 99 32 6 0 5 91 83 0 5 6\")\n# \"11\"\n# >>> $(skjkasdkd \"0 81 12 3 1 21\")\n# \"3\"\n# >>> $(skjkasdkd \"0 8 1 2 1 7\")\n# \"7\"\n#\n# $1 is a space-separated list\nskjkasdkd() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    skjkasdkd \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3\") = \"10\" ]]\n    [[ $(candidate \"1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1\") = \"25\" ]]\n    [[ $(candidate \"1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3\") = \"13\" ]]\n    [[ $(candidate \"0 724 32 71 99 32 6 0 5 91 83 0 5 6\") = \"11\" ]]\n    [[ $(candidate \"0 81 12 3 1 21\") = \"3\" ]]\n    [[ $(candidate \"0 8 1 2 1 7\") = \"7\" ]]\n    [[ $(candidate \"8191\") = \"19\" ]]\n    [[ $(candidate \"8191 123456 127 7\") = \"19\" ]]\n    [[ $(candidate \"127 97 8192\") = \"10\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_94_skjkasdkd", "test": "}\n\ncandidate() {\n    skjkasdkd \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0 3 2 1 3 5 7 4 5 5 5 2 181 32 4 32 3 2 32 324 4 3\") = \"10\" ]]\n    [[ $(candidate \"1 0 1 8 2 4597 2 1 3 40 1 2 1 2 4 2 5 1\") = \"25\" ]]\n    [[ $(candidate \"1 3 1 32 5107 34 83278 109 163 23 2323 32 30 1 9 3\") = \"13\" ]]\n    [[ $(candidate \"0 724 32 71 99 32 6 0 5 91 83 0 5 6\") = \"11\" ]]\n    [[ $(candidate \"0 81 12 3 1 21\") = \"3\" ]]\n    [[ $(candidate \"0 8 1 2 1 7\") = \"7\" ]]\n    [[ $(candidate \"8191\") = \"19\" ]]\n    [[ $(candidate \"8191 123456 127 7\") = \"19\" ]]\n    [[ $(candidate \"127 97 8192\") = \"10\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_8_sum_product", "language": "sh", "prompt": "#!/bin/bash\n# For a given list of integers, return a list consisting of a sum and a product of all the integers in a list.\n# Empty sum should be equal to 0 and empty product should be equal to 1.\n# >>> $(sum_product \"\")\n# ['\"0\"', '\"1\"']\n# >>> $(sum_product \"1 2 3 4\")\n# ['\"10\"', '\"24\"']\n#\n# $1 is a space-separated list\nsum_product() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sum_product \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0 1\" ]]\n    [[ $(candidate \"1 1 1\") = \"3 1\" ]]\n    [[ $(candidate \"100 0\") = \"100 0\" ]]\n    [[ $(candidate \"3 5 7\") = \"15 105\" ]]\n    [[ $(candidate \"10\") = \"10 10\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_8_sum_product", "test": "}\n\ncandidate() {\n    sum_product \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0 1\" ]]\n    [[ $(candidate \"1 1 1\") = \"3 1\" ]]\n    [[ $(candidate \"100 0\") = \"100 0\" ]]\n    [[ $(candidate \"3 5 7\") = \"15 105\" ]]\n    [[ $(candidate \"10\") = \"10 10\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_102_choose_num", "language": "sh", "prompt": "#!/bin/bash\n# This function takes two positive numbers x and y and returns the\n# biggest even integer number that is in the range [x, y] inclusive. If \n# there's no such number, then the function should return -1.\n# For example:\n# >>> $(choose_num \"12\" \"15\")\n# \"14\"\n# >>> $(choose_num \"13\" \"12\")\n# \"-1\"\n#\n# $1 is an integer\n# $2 is an integer\nchoose_num() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    choose_num \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"12\" \"15\") = \"14\" ]]\n    [[ $(candidate \"13\" \"12\") = \"-1\" ]]\n    [[ $(candidate \"33\" \"12354\") = \"12354\" ]]\n    [[ $(candidate \"5234\" \"5233\") = \"-1\" ]]\n    [[ $(candidate \"6\" \"29\") = \"28\" ]]\n    [[ $(candidate \"27\" \"10\") = \"-1\" ]]\n    [[ $(candidate \"7\" \"7\") = \"-1\" ]]\n    [[ $(candidate \"546\" \"546\") = \"546\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_102_choose_num", "test": "}\n\ncandidate() {\n    choose_num \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"12\" \"15\") = \"14\" ]]\n    [[ $(candidate \"13\" \"12\") = \"-1\" ]]\n    [[ $(candidate \"33\" \"12354\") = \"12354\" ]]\n    [[ $(candidate \"5234\" \"5233\") = \"-1\" ]]\n    [[ $(candidate \"6\" \"29\") = \"28\" ]]\n    [[ $(candidate \"27\" \"10\") = \"-1\" ]]\n    [[ $(candidate \"7\" \"7\") = \"-1\" ]]\n    [[ $(candidate \"546\" \"546\") = \"546\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that returns a list (a, b), where 'a' is\n# the largest of negative integers, and 'b' is the smallest\n# of positive integers in a list.\n# If there is no negative or positive integers, return them as None.\n# Examples:\n# >>> $(largest_smallest_integers \"2 4 1 3 5 7\")\n# ['\"None\"', '\"1\"']\n# >>> $(largest_smallest_integers \"\")\n# ['\"None\"', '\"None\"']\n# >>> $(largest_smallest_integers \"0\")\n# ['\"None\"', '\"None\"']\n#\n# $1 is a space-separated list\nlargest_smallest_integers() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    largest_smallest_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 4 1 3 5 7\") = \"None 1\" ]]\n    [[ $(candidate \"2 4 1 3 5 7 0\") = \"None 1\" ]]\n    [[ $(candidate \"1 3 2 4 5 6 -2\") = \"-2 1\" ]]\n    [[ $(candidate \"4 5 3 6 2 7 -7\") = \"-7 2\" ]]\n    [[ $(candidate \"7 3 8 4 9 2 5 -9\") = \"-9 2\" ]]\n    [[ $(candidate \"\") = \"None None\" ]]\n    [[ $(candidate \"0\") = \"None None\" ]]\n    [[ $(candidate \"-1 -3 -5 -6\") = \"-1 None\" ]]\n    [[ $(candidate \"-1 -3 -5 -6 0\") = \"-1 None\" ]]\n    [[ $(candidate \"-6 -4 -4 -3 1\") = \"-3 1\" ]]\n    [[ $(candidate \"-6 -4 -4 -3 -100 1\") = \"-3 1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "}\n\ncandidate() {\n    largest_smallest_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 4 1 3 5 7\") = \"None 1\" ]]\n    [[ $(candidate \"2 4 1 3 5 7 0\") = \"None 1\" ]]\n    [[ $(candidate \"1 3 2 4 5 6 -2\") = \"-2 1\" ]]\n    [[ $(candidate \"4 5 3 6 2 7 -7\") = \"-7 2\" ]]\n    [[ $(candidate \"7 3 8 4 9 2 5 -9\") = \"-9 2\" ]]\n    [[ $(candidate \"\") = \"None None\" ]]\n    [[ $(candidate \"0\") = \"None None\" ]]\n    [[ $(candidate \"-1 -3 -5 -6\") = \"-1 None\" ]]\n    [[ $(candidate \"-1 -3 -5 -6 0\") = \"-1 None\" ]]\n    [[ $(candidate \"-6 -4 -4 -3 1\") = \"-3 1\" ]]\n    [[ $(candidate \"-6 -4 -4 -3 -100 1\") = \"-3 1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_16_count_distinct_characters", "language": "sh", "prompt": "#!/bin/bash\n# Given a string, find out how many distinct characters (regardless of case) does it consist of\n# >>> $(count_distinct_characters \"xyzXYZ\")\n# \"3\"\n# >>> $(count_distinct_characters \"Jerry\")\n# \"4\"\n#\n# $1 is a string\ncount_distinct_characters() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    count_distinct_characters \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"abcde\") = \"5\" ]]\n    [[ $(candidate \"abcdecadeCADE\") = \"5\" ]]\n    [[ $(candidate \"aaaaAAAAaaaa\") = \"1\" ]]\n    [[ $(candidate \"Jerry jERRY JeRRRY\") = \"5\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_16_count_distinct_characters", "test": "}\n\ncandidate() {\n    count_distinct_characters \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"0\" ]]\n    [[ $(candidate \"abcde\") = \"5\" ]]\n    [[ $(candidate \"abcdecadeCADE\") = \"5\" ]]\n    [[ $(candidate \"aaaaAAAAaaaa\") = \"1\" ]]\n    [[ $(candidate \"Jerry jERRY JeRRRY\") = \"5\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_100_make_a_pile", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer n, you have to make a pile of n levels of stones.\n# The first level has n stones.\n# The number of stones in the next level is:\n# - the next odd number if n is odd.\n# - the next even number if n is even.\n# Return the number of stones in each level in a list, where element at index\n# i represents the number of stones in the level (i+1).\n# Examples:\n# >>> $(make_a_pile \"3\")\n# ['\"3\"', '\"5\"', '\"7\"']\n#\n# $1 is an integer\nmake_a_pile() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    make_a_pile \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"3 5 7\" ]]\n    [[ $(candidate \"4\") = \"4 6 8 10\" ]]\n    [[ $(candidate \"5\") = \"5 7 9 11 13\" ]]\n    [[ $(candidate \"6\") = \"6 8 10 12 14 16\" ]]\n    [[ $(candidate \"8\") = \"8 10 12 14 16 18 20 22\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_100_make_a_pile", "test": "}\n\ncandidate() {\n    make_a_pile \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\") = \"3 5 7\" ]]\n    [[ $(candidate \"4\") = \"4 6 8 10\" ]]\n    [[ $(candidate \"5\") = \"5 7 9 11 13\" ]]\n    [[ $(candidate \"6\") = \"6 8 10 12 14 16\" ]]\n    [[ $(candidate \"8\") = \"8 10 12 14 16 18 20 22\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_128_prod_signs", "language": "sh", "prompt": "#!/bin/bash\n# You are given an array arr of integers and you need to return\n# sum of magnitudes of integers multiplied by product of all signs\n# of each number in the array, represented by 1, -1 or 0.\n# Note: return None for empty arr.\n# Example:\n# >>> $(prod_signs \"1 2 2 -4\")\n# \"9\"\n# >>> $(prod_signs \"0 1\")\n# \"0\"\n# >>> $(prod_signs \"\")\n# \"None\"\n#\n# $1 is a space-separated list\nprod_signs() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    prod_signs \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 2 -4\") = \"-9\" ]]\n    [[ $(candidate \"0 1\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 2 3 -1 1\") = \"-10\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"2 4 1 2 -1 -1 9\") = \"20\" ]]\n    [[ $(candidate \"-1 1 -1 1\") = \"4\" ]]\n    [[ $(candidate \"-1 1 1 1\") = \"-4\" ]]\n    [[ $(candidate \"-1 1 1 0\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_128_prod_signs", "test": "}\n\ncandidate() {\n    prod_signs \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 2 -4\") = \"-9\" ]]\n    [[ $(candidate \"0 1\") = \"0\" ]]\n    [[ $(candidate \"1 1 1 2 3 -1 1\") = \"-10\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"2 4 1 2 -1 -1 9\") = \"20\" ]]\n    [[ $(candidate \"-1 1 -1 1\") = \"4\" ]]\n    [[ $(candidate \"-1 1 1 1\") = \"-4\" ]]\n    [[ $(candidate \"-1 1 1 0\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_114_minSubArraySum", "language": "sh", "prompt": "#!/bin/bash\n# Given an array of integers nums, find the minimum sum of any non-empty sub-array\n# of nums.\n# Example\n# >>> $(minSubArraySum \"2 3 4 1 2 4\")\n# \"1\"\n# >>> $(minSubArraySum \"-1 -2 -3\")\n# \"-6\"\n#\n# $1 is a space-separated list\nminSubArraySum() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    minSubArraySum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 3 4 1 2 4\") = \"1\" ]]\n    [[ $(candidate \"-1 -2 -3\") = \"-6\" ]]\n    [[ $(candidate \"-1 -2 -3 2 -10\") = \"-14\" ]]\n    [[ $(candidate \"-9999999999999999\") = \"-9999999999999999\" ]]\n    [[ $(candidate \"0 10 20 1000000\") = \"0\" ]]\n    [[ $(candidate \"-1 -2 -3 10 -5\") = \"-6\" ]]\n    [[ $(candidate \"100 -1 -2 -3 10 -5\") = \"-6\" ]]\n    [[ $(candidate \"10 11 13 8 3 4\") = \"3\" ]]\n    [[ $(candidate \"100 -33 32 -1 0 -2\") = \"-33\" ]]\n    [[ $(candidate \"-10\") = \"-10\" ]]\n    [[ $(candidate \"7\") = \"7\" ]]\n    [[ $(candidate \"1 -1\") = \"-1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_114_minSubArraySum", "test": "}\n\ncandidate() {\n    minSubArraySum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2 3 4 1 2 4\") = \"1\" ]]\n    [[ $(candidate \"-1 -2 -3\") = \"-6\" ]]\n    [[ $(candidate \"-1 -2 -3 2 -10\") = \"-14\" ]]\n    [[ $(candidate \"-9999999999999999\") = \"-9999999999999999\" ]]\n    [[ $(candidate \"0 10 20 1000000\") = \"0\" ]]\n    [[ $(candidate \"-1 -2 -3 10 -5\") = \"-6\" ]]\n    [[ $(candidate \"100 -1 -2 -3 10 -5\") = \"-6\" ]]\n    [[ $(candidate \"10 11 13 8 3 4\") = \"3\" ]]\n    [[ $(candidate \"100 -33 32 -1 0 -2\") = \"-33\" ]]\n    [[ $(candidate \"-10\") = \"-10\" ]]\n    [[ $(candidate \"7\") = \"7\" ]]\n    [[ $(candidate \"1 -1\") = \"-1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_15_string_sequence", "language": "sh", "prompt": "#!/bin/bash\n# Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n# >>> $(string_sequence \"0\")\n# \"0\"\n# >>> $(string_sequence \"5\")\n# \"0 1 2 3 4 5\"\n#\n# $1 is an integer\nstring_sequence() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    string_sequence \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\") = \"0\" ]]\n    [[ $(candidate \"3\") = \"0 1 2 3\" ]]\n    [[ $(candidate \"10\") = \"0 1 2 3 4 5 6 7 8 9 10\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_15_string_sequence", "test": "}\n\ncandidate() {\n    string_sequence \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"0\") = \"0\" ]]\n    [[ $(candidate \"3\") = \"0 1 2 3\" ]]\n    [[ $(candidate \"10\") = \"0 1 2 3 4 5 6 7 8 9 10\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_154_cycpattern_check", "language": "sh", "prompt": "#!/bin/bash\n# You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n# >>> $(cycpattern_check \"abcd\" \"abd\")\n# \"false\"\n# >>> $(cycpattern_check \"hello\" \"ell\")\n# \"true\"\n# >>> $(cycpattern_check \"whassup\" \"psus\")\n# \"false\"\n# >>> $(cycpattern_check \"abab\" \"baa\")\n# \"true\"\n# >>> $(cycpattern_check \"efef\" \"eeff\")\n# \"false\"\n# >>> $(cycpattern_check \"himenss\" \"simen\")\n# \"true\"\n#\n# $1 is a string\n# $2 is a string\ncycpattern_check() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    cycpattern_check \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"xyzw\" \"xyw\") = \"false\" ]]\n    [[ $(candidate \"yello\" \"ell\") = \"true\" ]]\n    [[ $(candidate \"whattup\" \"ptut\") = \"false\" ]]\n    [[ $(candidate \"efef\" \"fee\") = \"true\" ]]\n    [[ $(candidate \"abab\" \"aabb\") = \"false\" ]]\n    [[ $(candidate \"winemtt\" \"tinem\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_154_cycpattern_check", "test": "}\n\ncandidate() {\n    cycpattern_check \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"xyzw\" \"xyw\") = \"false\" ]]\n    [[ $(candidate \"yello\" \"ell\") = \"true\" ]]\n    [[ $(candidate \"whattup\" \"ptut\") = \"false\" ]]\n    [[ $(candidate \"efef\" \"fee\") = \"true\" ]]\n    [[ $(candidate \"abab\" \"aabb\") = \"false\" ]]\n    [[ $(candidate \"winemtt\" \"tinem\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_57_monotonic", "language": "sh", "prompt": "#!/bin/bash\n# Return true is list elements are monotonically increasing or decreasing.\n# >>> $(monotonic \"1 2 4 20\")\n# \"true\"\n# >>> $(monotonic \"1 20 4 10\")\n# \"false\"\n# >>> $(monotonic \"4 1 0 -10\")\n# \"true\"\n#\n# $1 is a space-separated list\nmonotonic() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    monotonic \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 10\") = \"true\" ]]\n    [[ $(candidate \"1 2 4 20\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\") = \"false\" ]]\n    [[ $(candidate \"4 1 0 -10\") = \"true\" ]]\n    [[ $(candidate \"4 1 1 0\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 2 5 60\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 4 5 60\") = \"true\" ]]\n    [[ $(candidate \"9 9 9 9\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_57_monotonic", "test": "}\n\ncandidate() {\n    monotonic \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 10\") = \"true\" ]]\n    [[ $(candidate \"1 2 4 20\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\") = \"false\" ]]\n    [[ $(candidate \"4 1 0 -10\") = \"true\" ]]\n    [[ $(candidate \"4 1 1 0\") = \"true\" ]]\n    [[ $(candidate \"1 2 3 2 5 60\") = \"false\" ]]\n    [[ $(candidate \"1 2 3 4 5 60\") = \"true\" ]]\n    [[ $(candidate \"9 9 9 9\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_12_longest", "language": "sh", "prompt": "#!/bin/bash\n# Out of list of strings, return the longest one. Return the first one in case of multiple\n# strings of the same length. Return None in case the input list is empty.\n# >>> $(longest \"\")\n# \"None\"\n# >>> $(longest \"a b c\")\n# \"a\"\n# >>> $(longest \"a bb ccc\")\n# \"ccc\"\n#\n# $1 is a space-separated list\nlongest() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    longest \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"x y z\") = \"x\" ]]\n    [[ $(candidate \"x yyy zzzz www kkkk abc\") = \"zzzz\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_12_longest", "test": "}\n\ncandidate() {\n    longest \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"x y z\") = \"x\" ]]\n    [[ $(candidate \"x yyy zzzz www kkkk abc\") = \"zzzz\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_52_below_threshold", "language": "sh", "prompt": "#!/bin/bash\n# Return true if all numbers in the list l are below threshold t.\n# >>> $(below_threshold \"1 2 4 10\" \"100\")\n# \"true\"\n# >>> $(below_threshold \"1 20 4 10\" \"5\")\n# \"false\"\n#\n# $1 is a space-separated list\n# $2 is an integer\nbelow_threshold() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    below_threshold \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 10\" \"100\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\" \"5\") = \"false\" ]]\n    [[ $(candidate \"1 20 4 10\" \"21\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\" \"22\") = \"true\" ]]\n    [[ $(candidate \"1 8 4 10\" \"11\") = \"true\" ]]\n    [[ $(candidate \"1 8 4 10\" \"10\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_52_below_threshold", "test": "}\n\ncandidate() {\n    below_threshold \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 4 10\" \"100\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\" \"5\") = \"false\" ]]\n    [[ $(candidate \"1 20 4 10\" \"21\") = \"true\" ]]\n    [[ $(candidate \"1 20 4 10\" \"22\") = \"true\" ]]\n    [[ $(candidate \"1 8 4 10\" \"11\") = \"true\" ]]\n    [[ $(candidate \"1 8 4 10\" \"10\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_75_is_multiply_prime", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that returns true if the given number is the multiplication of 3 prime numbers\n# and false otherwise.\n# Knowing that (a) is less then 100. \n# Example:\n# >>> $(is_multiply_prime \"30\")\n# \"true\"\n# 30 = 2 * 3 * 5\n#\n# $1 is an integer\nis_multiply_prime() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    is_multiply_prime \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"false\" ]]\n    [[ $(candidate \"30\") = \"true\" ]]\n    [[ $(candidate \"8\") = \"true\" ]]\n    [[ $(candidate \"10\") = \"false\" ]]\n    [[ $(candidate \"125\") = \"true\" ]]\n    [[ $(candidate \"105\") = \"true\" ]]\n    [[ $(candidate \"126\") = \"false\" ]]\n    [[ $(candidate \"729\") = \"false\" ]]\n    [[ $(candidate \"891\") = \"false\" ]]\n    [[ $(candidate \"1001\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_75_is_multiply_prime", "test": "}\n\ncandidate() {\n    is_multiply_prime \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\") = \"false\" ]]\n    [[ $(candidate \"30\") = \"true\" ]]\n    [[ $(candidate \"8\") = \"true\" ]]\n    [[ $(candidate \"10\") = \"false\" ]]\n    [[ $(candidate \"125\") = \"true\" ]]\n    [[ $(candidate \"105\") = \"true\" ]]\n    [[ $(candidate \"126\") = \"false\" ]]\n    [[ $(candidate \"729\") = \"false\" ]]\n    [[ $(candidate \"891\") = \"false\" ]]\n    [[ $(candidate \"1001\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_30_get_positive", "language": "sh", "prompt": "#!/bin/bash\n# Return only positive numbers in the list.\n# >>> $(get_positive \"-1 2 -4 5 6\")\n# ['\"2\"', '\"5\"', '\"6\"']\n# >>> $(get_positive \"5 3 -5 2 -3 3 9 0 123 1 -10\")\n# ['\"5\"', '\"3\"', '\"2\"', '\"3\"', '\"9\"', '\"123\"', '\"1\"']\n#\n# $1 is a space-separated list\nget_positive() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    get_positive \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"-1 -2 4 5 6\") = \"4 5 6\" ]]\n    [[ $(candidate \"5 3 -5 2 3 3 9 0 123 1 -10\") = \"5 3 2 3 3 9 123 1\" ]]\n    [[ $(candidate \"-1 -2\") = \"\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_30_get_positive", "test": "}\n\ncandidate() {\n    get_positive \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"-1 -2 4 5 6\") = \"4 5 6\" ]]\n    [[ $(candidate \"5 3 -5 2 3 3 9 0 123 1 -10\") = \"5 3 2 3 3 9 123 1\" ]]\n    [[ $(candidate \"-1 -2\") = \"\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_33_sort_third", "language": "sh", "prompt": "#!/bin/bash\n# This function takes a list l and returns a list l' such that\n# l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n# to the values of the corresponding indicies of l, but sorted.\n# >>> $(sort_third \"1 2 3\")\n# ['\"1\"', '\"2\"', '\"3\"']\n# >>> $(sort_third \"5 6 3 4 8 9 2\")\n# ['\"2\"', '\"6\"', '\"3\"', '\"4\"', '\"8\"', '\"9\"', '\"5\"']\n#\n# $1 is a space-separated list\nsort_third() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sort_third \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 6 3 4 8 9 2\") = \"2 6 3 4 8 9 5\" ]]\n    [[ $(candidate \"5 8 3 4 6 9 2\") = \"2 8 3 4 6 9 5\" ]]\n    [[ $(candidate \"5 6 9 4 8 3 2\") = \"2 6 9 4 8 3 5\" ]]\n    [[ $(candidate \"5 6 3 4 8 9 2 1\") = \"2 6 3 4 8 9 5 1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_33_sort_third", "test": "}\n\ncandidate() {\n    sort_third \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 6 3 4 8 9 2\") = \"2 6 3 4 8 9 5\" ]]\n    [[ $(candidate \"5 8 3 4 6 9 2\") = \"2 8 3 4 6 9 5\" ]]\n    [[ $(candidate \"5 6 9 4 8 3 2\") = \"2 6 9 4 8 3 5\" ]]\n    [[ $(candidate \"5 6 3 4 8 9 2 1\") = \"2 6 3 4 8 9 5 1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_6_parse_nested_parens", "language": "sh", "prompt": "#!/bin/bash\n# Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n# For each of the group, output the deepest level of nesting of parentheses.\n# E.g. (()()) has maximum two levels of nesting while ((())) has three.\n# >>> $(parse_nested_parens \"(()()) ((())) () ((())()())\")\n# ['\"2\"', '\"3\"', '\"1\"', '\"3\"']\n#\n# $1 is a string\nparse_nested_parens() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    parse_nested_parens \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"(()()) ((())) () ((())()())\") = \"2 3 1 3\" ]]\n    [[ $(candidate \"() (()) ((())) (((())))\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"(()(())((())))\") = \"4\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_6_parse_nested_parens", "test": "}\n\ncandidate() {\n    parse_nested_parens \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"(()()) ((())) () ((())()())\") = \"2 3 1 3\" ]]\n    [[ $(candidate \"() (()) ((())) (((())))\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"(()(())((())))\") = \"4\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_45_triangle_area", "language": "sh", "prompt": "#!/bin/bash\n# Given length of a side and high return area for a triangle.\n# >>> $(triangle_area \"5\" \"3\")\n# \"7.5\"\n#\n# $1 is an integer\n# $2 is an integer\ntriangle_area() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    triangle_area \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\" \"3\") = \"7.5\" ]]\n    [[ $(candidate \"2\" \"2\") = \"2.0\" ]]\n    [[ $(candidate \"10\" \"8\") = \"40.0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_45_triangle_area", "test": "}\n\ncandidate() {\n    triangle_area \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5\" \"3\") = \"7.5\" ]]\n    [[ $(candidate \"2\" \"2\") = \"2.0\" ]]\n    [[ $(candidate \"10\" \"8\") = \"40.0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_97_multiply", "language": "sh", "prompt": "#!/bin/bash\n# Complete the function that takes two integers and returns \n# the product of their unit digits.\n# Assume the input is always valid.\n# Examples:\n# >>> $(multiply \"148\" \"412\")\n# \"16\"\n# >>> $(multiply \"19\" \"28\")\n# \"72\"\n# >>> $(multiply \"2020\" \"1851\")\n# \"0\"\n# >>> $(multiply \"14\" \"-15\")\n# \"20\"\n#\n# $1 is an integer\n# $2 is an integer\nmultiply() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    multiply \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"148\" \"412\") = \"16\" ]]\n    [[ $(candidate \"19\" \"28\") = \"72\" ]]\n    [[ $(candidate \"2020\" \"1851\") = \"0\" ]]\n    [[ $(candidate \"14\" \"-15\") = \"20\" ]]\n    [[ $(candidate \"76\" \"67\") = \"42\" ]]\n    [[ $(candidate \"17\" \"27\") = \"49\" ]]\n    [[ $(candidate \"0\" \"1\") = \"0\" ]]\n    [[ $(candidate \"0\" \"0\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_97_multiply", "test": "}\n\ncandidate() {\n    multiply \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"148\" \"412\") = \"16\" ]]\n    [[ $(candidate \"19\" \"28\") = \"72\" ]]\n    [[ $(candidate \"2020\" \"1851\") = \"0\" ]]\n    [[ $(candidate \"14\" \"-15\") = \"20\" ]]\n    [[ $(candidate \"76\" \"67\") = \"42\" ]]\n    [[ $(candidate \"17\" \"27\") = \"49\" ]]\n    [[ $(candidate \"0\" \"1\") = \"0\" ]]\n    [[ $(candidate \"0\" \"0\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "sh", "prompt": "#!/bin/bash\n# For a given list of input numbers, calculate Mean Absolute Deviation\n# around the mean of this dataset.\n# Mean Absolute Deviation is the average absolute difference between each\n# element and a centerpoint (mean in this case):\n# MAD = average | x - x_mean |\n# >>> $(mean_absolute_deviation \"1.0 2.0 3.0 4.0\")\n# \"1.0\"\n#\n# $1 is a space-separated list\nmean_absolute_deviation() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    mean_absolute_deviation \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0\") = \"0.5\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0\") = \"1.0\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0\") = \"1.2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "}\n\ncandidate() {\n    mean_absolute_deviation \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1.0 2.0\") = \"0.5\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0\") = \"1.0\" ]]\n    [[ $(candidate \"1.0 2.0 3.0 4.0 5.0\") = \"1.2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_58_common", "language": "sh", "prompt": "#!/bin/bash\n# Return sorted unique common elements for two lists.\n# >>> $(common \"1 4 3 34 653 2 5\" \"5 7 1 5 9 653 121\")\n# ['\"1\"', '\"5\"', '\"653\"']\n# >>> $(common \"5 3 2 8\" \"3 2\")\n# ['\"2\"', '\"3\"']\n#\n# $1 is a space-separated list\n# $2 is a space-separated list\ncommon() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    common \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 4 3 34 653 2 5\" \"5 7 1 5 9 653 121\") = \"1 5 653\" ]]\n    [[ $(candidate \"5 3 2 8\" \"3 2\") = \"2 3\" ]]\n    [[ $(candidate \"4 3 2 8\" \"3 2 4\") = \"2 3 4\" ]]\n    [[ $(candidate \"4 3 2 8\" \"\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_58_common", "test": "}\n\ncandidate() {\n    common \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 4 3 34 653 2 5\" \"5 7 1 5 9 653 121\") = \"1 5 653\" ]]\n    [[ $(candidate \"5 3 2 8\" \"3 2\") = \"2 3\" ]]\n    [[ $(candidate \"4 3 2 8\" \"3 2 4\") = \"2 3 4\" ]]\n    [[ $(candidate \"4 3 2 8\" \"\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "sh", "prompt": "#!/bin/bash\n# Given a positive integer, obtain its roman numeral equivalent as a string,\n# and return it in lowercase.\n# Restrictions: 1 <= num <= 1000\n# Examples:\n# >>> $(int_to_mini_roman \"19\")\n# \"xix\"\n# >>> $(int_to_mini_roman \"152\")\n# \"clii\"\n# >>> $(int_to_mini_roman \"426\")\n# \"cdxxvi\"\n#\n# $1 is an integer\nint_to_mini_roman() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    int_to_mini_roman \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"19\") = \"xix\" ]]\n    [[ $(candidate \"152\") = \"clii\" ]]\n    [[ $(candidate \"251\") = \"ccli\" ]]\n    [[ $(candidate \"426\") = \"cdxxvi\" ]]\n    [[ $(candidate \"500\") = \"d\" ]]\n    [[ $(candidate \"1\") = \"i\" ]]\n    [[ $(candidate \"4\") = \"iv\" ]]\n    [[ $(candidate \"43\") = \"xliii\" ]]\n    [[ $(candidate \"90\") = \"xc\" ]]\n    [[ $(candidate \"94\") = \"xciv\" ]]\n    [[ $(candidate \"532\") = \"dxxxii\" ]]\n    [[ $(candidate \"900\") = \"cm\" ]]\n    [[ $(candidate \"994\") = \"cmxciv\" ]]\n    [[ $(candidate \"1000\") = \"m\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "}\n\ncandidate() {\n    int_to_mini_roman \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"19\") = \"xix\" ]]\n    [[ $(candidate \"152\") = \"clii\" ]]\n    [[ $(candidate \"251\") = \"ccli\" ]]\n    [[ $(candidate \"426\") = \"cdxxvi\" ]]\n    [[ $(candidate \"500\") = \"d\" ]]\n    [[ $(candidate \"1\") = \"i\" ]]\n    [[ $(candidate \"4\") = \"iv\" ]]\n    [[ $(candidate \"43\") = \"xliii\" ]]\n    [[ $(candidate \"90\") = \"xc\" ]]\n    [[ $(candidate \"94\") = \"xciv\" ]]\n    [[ $(candidate \"532\") = \"dxxxii\" ]]\n    [[ $(candidate \"900\") = \"cm\" ]]\n    [[ $(candidate \"994\") = \"cmxciv\" ]]\n    [[ $(candidate \"1000\") = \"m\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_67_fruit_distribution", "language": "sh", "prompt": "#!/bin/bash\n# In this task, you will be given a string that represents a number of apples and oranges \n# that are distributed in a basket of fruit this basket contains \n# apples, oranges, and mango fruits. Given the string that represents the total number of \n# the oranges and apples and an integer that represent the total number of the fruits \n# in the basket return the number of the mango fruits in the basket.\n# for examble:\n# >>> $(fruit_distribution \"5 apples and 6 oranges\" \"19\")\n# \"8\"\n# >>> $(fruit_distribution \"0 apples and 1 oranges\" \"3\")\n# \"2\"\n# >>> $(fruit_distribution \"2 apples and 3 oranges\" \"100\")\n# \"95\"\n# >>> $(fruit_distribution \"100 apples and 1 oranges\" \"120\")\n# \"19\"\n#\n# $1 is a string\n# $2 is an integer\nfruit_distribution() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    fruit_distribution \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 apples and 6 oranges\" \"19\") = \"8\" ]]\n    [[ $(candidate \"5 apples and 6 oranges\" \"21\") = \"10\" ]]\n    [[ $(candidate \"0 apples and 1 oranges\" \"3\") = \"2\" ]]\n    [[ $(candidate \"1 apples and 0 oranges\" \"3\") = \"2\" ]]\n    [[ $(candidate \"2 apples and 3 oranges\" \"100\") = \"95\" ]]\n    [[ $(candidate \"2 apples and 3 oranges\" \"5\") = \"0\" ]]\n    [[ $(candidate \"1 apples and 100 oranges\" \"120\") = \"19\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_67_fruit_distribution", "test": "}\n\ncandidate() {\n    fruit_distribution \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 apples and 6 oranges\" \"19\") = \"8\" ]]\n    [[ $(candidate \"5 apples and 6 oranges\" \"21\") = \"10\" ]]\n    [[ $(candidate \"0 apples and 1 oranges\" \"3\") = \"2\" ]]\n    [[ $(candidate \"1 apples and 0 oranges\" \"3\") = \"2\" ]]\n    [[ $(candidate \"2 apples and 3 oranges\" \"100\") = \"95\" ]]\n    [[ $(candidate \"2 apples and 3 oranges\" \"5\") = \"0\" ]]\n    [[ $(candidate \"1 apples and 100 oranges\" \"120\") = \"19\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_112_reverse_delete", "language": "sh", "prompt": "#!/bin/bash\n# Task\n# We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n# then check if the result string is palindrome.\n# A string is called palindrome if it reads the same backward as forward.\n# You should return a list containing the result string and true/false for the check.\n# Example\n# >>> $(reverse_delete \"abcde\" \"ae\")\n# ['\"bcd\"', '\"false\"']\n# >>> $(reverse_delete \"abcdef\" \"b\")\n# ['\"acdef\"', '\"false\"']\n# >>> $(reverse_delete \"abcdedcba\" \"ab\")\n# ['\"cdedc\"', '\"true\"']\n#\n# $1 is a string\n# $2 is a string\nreverse_delete() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    reverse_delete \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"abcde\" \"ae\") = \"bcd false\" ]]\n    [[ $(candidate \"abcdef\" \"b\") = \"acdef false\" ]]\n    [[ $(candidate \"abcdedcba\" \"ab\") = \"cdedc true\" ]]\n    [[ $(candidate \"dwik\" \"w\") = \"dik false\" ]]\n    [[ $(candidate \"a\" \"a\") = \" true\" ]]\n    [[ $(candidate \"abcdedcba\" \"\") = \"abcdedcba true\" ]]\n    [[ $(candidate \"abcdedcba\" \"v\") = \"abcdedcba true\" ]]\n    [[ $(candidate \"vabba\" \"v\") = \"abba true\" ]]\n    [[ $(candidate \"mamma\" \"mia\") = \" true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_112_reverse_delete", "test": "}\n\ncandidate() {\n    reverse_delete \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"abcde\" \"ae\") = \"bcd false\" ]]\n    [[ $(candidate \"abcdef\" \"b\") = \"acdef false\" ]]\n    [[ $(candidate \"abcdedcba\" \"ab\") = \"cdedc true\" ]]\n    [[ $(candidate \"dwik\" \"w\") = \"dik false\" ]]\n    [[ $(candidate \"a\" \"a\") = \" true\" ]]\n    [[ $(candidate \"abcdedcba\" \"\") = \"abcdedcba true\" ]]\n    [[ $(candidate \"abcdedcba\" \"v\") = \"abcdedcba true\" ]]\n    [[ $(candidate \"vabba\" \"v\") = \"abba true\" ]]\n    [[ $(candidate \"mamma\" \"mia\") = \" true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "sh", "prompt": "#!/bin/bash\n# Return a greatest common divisor of two integers a and b\n# >>> $(greatest_common_divisor \"3\" \"5\")\n# \"1\"\n# >>> $(greatest_common_divisor \"25\" \"15\")\n# \"5\"\n#\n# $1 is an integer\n# $2 is an integer\ngreatest_common_divisor() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    greatest_common_divisor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"7\") = \"1\" ]]\n    [[ $(candidate \"10\" \"15\") = \"5\" ]]\n    [[ $(candidate \"49\" \"14\") = \"7\" ]]\n    [[ $(candidate \"144\" \"60\") = \"12\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "}\n\ncandidate() {\n    greatest_common_divisor \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"7\") = \"1\" ]]\n    [[ $(candidate \"10\" \"15\") = \"5\" ]]\n    [[ $(candidate \"49\" \"14\") = \"7\" ]]\n    [[ $(candidate \"144\" \"60\") = \"12\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_125_split_words", "language": "sh", "prompt": "#!/bin/bash\n# Given a string of words, return a list of words split on whitespace, if no whitespaces exists in the text you\n# should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n# alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n# Examples\n# >>> $(split_words \"Hello world\\!\")\n# ['\"Hello\"', '\"world\\\\!\"']\n# >>> $(split_words \"Hello,world\\!\")\n# ['\"Hello\"', '\"world\\\\!\"']\n# >>> $(split_words \"abcdef\")\n# \"3\"\n#\n# $1 is a string\nsplit_words() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    split_words \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\\!\") = \"Hello world\\!\" ]]\n    [[ $(candidate \"Hello,world\\!\") = \"Hello world\\!\" ]]\n    [[ $(candidate \"Hello world,\\!\") = \"Hello world,\\!\" ]]\n    [[ $(candidate \"Hello,Hello,world \\!\") = \"Hello,Hello,world \\!\" ]]\n    [[ $(candidate \"abcdef\") = \"3\" ]]\n    [[ $(candidate \"aaabb\") = \"2\" ]]\n    [[ $(candidate \"aaaBb\") = \"1\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_125_split_words", "test": "}\n\ncandidate() {\n    split_words \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\\!\") = \"Hello world\\!\" ]]\n    [[ $(candidate \"Hello,world\\!\") = \"Hello world\\!\" ]]\n    [[ $(candidate \"Hello world,\\!\") = \"Hello world,\\!\" ]]\n    [[ $(candidate \"Hello,Hello,world \\!\") = \"Hello,Hello,world \\!\" ]]\n    [[ $(candidate \"abcdef\") = \"3\" ]]\n    [[ $(candidate \"aaabb\") = \"2\" ]]\n    [[ $(candidate \"aaaBb\") = \"1\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_116_sort_array", "language": "sh", "prompt": "#!/bin/bash\n# In this Kata, you have to sort an array of non-negative integers according to\n# number of ones in their binary representation in ascending order.\n# For similar number of ones, sort based on decimal value.\n# It must be implemented like this:\n# >>> $(sort_array \"1 5 2 3 4\")\n# ['\"1\"', '\"2\"', '\"3\"', '\"4\"', '\"5\"']\n# >>> $(sort_array \"-2 -3 -4 -5 -6\")\n# ['\"-6\"', '\"-5\"', '\"-4\"', '\"-3\"', '\"-2\"']\n# >>> $(sort_array \"1 0 2 3 4\")\n# ['\"0\"', '\"1\"', '\"2\"', '\"3\"', '\"4\"']\n#\n# $1 is a space-separated list\nsort_array() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sort_array \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 5 2 3 4\") = \"1 2 4 3 5\" ]]\n    [[ $(candidate \"-2 -3 -4 -5 -6\") = \"-4 -2 -6 -5 -3\" ]]\n    [[ $(candidate \"1 0 2 3 4\") = \"0 1 2 4 3\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"2 5 77 4 5 3 5 7 2 3 4\") = \"2 2 4 4 3 3 5 5 5 7 77\" ]]\n    [[ $(candidate \"3 6 44 12 32 5\") = \"32 3 5 6 12 44\" ]]\n    [[ $(candidate \"2 4 8 16 32\") = \"2 4 8 16 32\" ]]\n    [[ $(candidate \"2 4 8 16 32\") = \"2 4 8 16 32\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_116_sort_array", "test": "}\n\ncandidate() {\n    sort_array \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 5 2 3 4\") = \"1 2 4 3 5\" ]]\n    [[ $(candidate \"-2 -3 -4 -5 -6\") = \"-4 -2 -6 -5 -3\" ]]\n    [[ $(candidate \"1 0 2 3 4\") = \"0 1 2 4 3\" ]]\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"2 5 77 4 5 3 5 7 2 3 4\") = \"2 2 4 4 3 3 5 5 5 7 77\" ]]\n    [[ $(candidate \"3 6 44 12 32 5\") = \"32 3 5 6 12 44\" ]]\n    [[ $(candidate \"2 4 8 16 32\") = \"2 4 8 16 32\" ]]\n    [[ $(candidate \"2 4 8 16 32\") = \"2 4 8 16 32\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_28_concatenate", "language": "sh", "prompt": "#!/bin/bash\n# Concatenate list of strings into a single string\n# >>> $(concatenate \"\")\n# \"\"\n# >>> $(concatenate \"a b c\")\n# \"abc\"\n#\n# $1 is a space-separated list\nconcatenate() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    concatenate \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"x y z\") = \"xyz\" ]]\n    [[ $(candidate \"x y z w k\") = \"xyzwk\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_28_concatenate", "test": "}\n\ncandidate() {\n    concatenate \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"x y z\") = \"xyz\" ]]\n    [[ $(candidate \"x y z w k\") = \"xyzwk\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_149_sorted_list_sum", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that accepts a list of strings as a parameter,\n# deletes the strings that have odd lengths from it,\n# and returns the resulted list with a sorted order,\n# The list is always a list of strings and never an array of numbers,\n# and it may contain duplicates.\n# The order of the list should be ascending by length of each word, and you\n# should return the list sorted by that rule.\n# If two words have the same length, sort the list alphabetically.\n# The function should return a list of strings in sorted order.\n# You may assume that all words will have the same length.\n# For example:\n# >>> $(list_sort \"aa a aaa\")\n# ['\"aa\"']\n# >>> $(list_sort \"ab a aaa cd\")\n# ['\"ab\"', '\"cd\"']\n#\n# $1 is a space-separated list\nsorted_list_sum() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sorted_list_sum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"aa a aaa\") = \"aa\" ]]\n    [[ $(candidate \"school AI asdf b\") = \"AI asdf school\" ]]\n    [[ $(candidate \"d b c a\") = \"\" ]]\n    [[ $(candidate \"d dcba abcd a\") = \"abcd dcba\" ]]\n    [[ $(candidate \"AI ai au\") = \"AI ai au\" ]]\n    [[ $(candidate \"a b b c c a\") = \"\" ]]\n    [[ $(candidate \"aaaa bbbb dd cc\") = \"cc dd aaaa bbbb\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_149_sorted_list_sum", "test": "}\n\ncandidate() {\n    sorted_list_sum \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"aa a aaa\") = \"aa\" ]]\n    [[ $(candidate \"school AI asdf b\") = \"AI asdf school\" ]]\n    [[ $(candidate \"d b c a\") = \"\" ]]\n    [[ $(candidate \"d dcba abcd a\") = \"abcd dcba\" ]]\n    [[ $(candidate \"AI ai au\") = \"AI ai au\" ]]\n    [[ $(candidate \"a b b c c a\") = \"\" ]]\n    [[ $(candidate \"aaaa bbbb dd cc\") = \"cc dd aaaa bbbb\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_99_closest_integer", "language": "sh", "prompt": "#!/bin/bash\n# Create a function that takes a value (string) representing a number\n# and returns the closest integer to it. If the number is equidistant\n# from two integers, round it away from zero.\n# Examples\n# >>> $(closest_integer \"10\")\n# \"10\"\n# >>> $(closest_integer \"15.3\")\n# \"15\"\n# Note:\n# Rounding away from zero means that if the given number is equidistant\n# from two integers, the one you should return is the one that is the\n# farthest from zero. For example closest_integer(\"14.5\") should\n# return 15 and closest_integer(\"-14.5\") should return -15.\n#\n# $1 is a string\nclosest_integer() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    closest_integer \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"10\") = \"10\" ]]\n    [[ $(candidate \"14.5\") = \"15\" ]]\n    [[ $(candidate \"-15.5\") = \"-16\" ]]\n    [[ $(candidate \"15.3\") = \"15\" ]]\n    [[ $(candidate \"0\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_99_closest_integer", "test": "}\n\ncandidate() {\n    closest_integer \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"10\") = \"10\" ]]\n    [[ $(candidate \"14.5\") = \"15\" ]]\n    [[ $(candidate \"-15.5\") = \"-16\" ]]\n    [[ $(candidate \"15.3\") = \"15\" ]]\n    [[ $(candidate \"0\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_64_vowels_count", "language": "sh", "prompt": "#!/bin/bash\n# Write a function vowels_count which takes a string representing\n# a word as input and returns the number of vowels in the string.\n# Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n# vowel, but only when it is at the end of the given word.\n# Example:\n# >>> $(vowels_count \"abcde\")\n# \"2\"\n# >>> $(vowels_count \"ACEDY\")\n# \"3\"\n#\n# $1 is a string\nvowels_count() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    vowels_count \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"abcde\") = \"2\" ]]\n    [[ $(candidate \"Alone\") = \"3\" ]]\n    [[ $(candidate \"key\") = \"2\" ]]\n    [[ $(candidate \"bye\") = \"1\" ]]\n    [[ $(candidate \"keY\") = \"2\" ]]\n    [[ $(candidate \"bYe\") = \"1\" ]]\n    [[ $(candidate \"ACEDY\") = \"3\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_64_vowels_count", "test": "}\n\ncandidate() {\n    vowels_count \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"abcde\") = \"2\" ]]\n    [[ $(candidate \"Alone\") = \"3\" ]]\n    [[ $(candidate \"key\") = \"2\" ]]\n    [[ $(candidate \"bye\") = \"1\" ]]\n    [[ $(candidate \"keY\") = \"2\" ]]\n    [[ $(candidate \"bYe\") = \"1\" ]]\n    [[ $(candidate \"ACEDY\") = \"3\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_158_find_max", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that accepts a list of strings.\n# The list contains different words. Return the word with maximum number\n# of unique characters. If multiple strings have maximum number of unique\n# characters, return the one which comes first in lexicographical order.\n# >>> $(find_max \"name of string\")\n# \"string\"\n# >>> $(find_max \"name enam game\")\n# \"enam\"\n# >>> $(find_max \"aaaaaaa bb cc\")\n# \"aaaaaaa\"\n#\n# $1 is a space-separated list\nfind_max() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    find_max \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"name of string\") = \"string\" ]]\n    [[ $(candidate \"name enam game\") = \"enam\" ]]\n    [[ $(candidate \"aaaaaaa bb cc\") = \"aaaaaaa\" ]]\n    [[ $(candidate \"abc cba\") = \"abc\" ]]\n    [[ $(candidate \"play this game of footbott\") = \"footbott\" ]]\n    [[ $(candidate \"we are gonna rock\") = \"gonna\" ]]\n    [[ $(candidate \"we are a mad nation\") = \"nation\" ]]\n    [[ $(candidate \"this is a prrk\") = \"this\" ]]\n    [[ $(candidate \"b\") = \"b\" ]]\n    [[ $(candidate \"play play play\") = \"play\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_158_find_max", "test": "}\n\ncandidate() {\n    find_max \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"name of string\") = \"string\" ]]\n    [[ $(candidate \"name enam game\") = \"enam\" ]]\n    [[ $(candidate \"aaaaaaa bb cc\") = \"aaaaaaa\" ]]\n    [[ $(candidate \"abc cba\") = \"abc\" ]]\n    [[ $(candidate \"play this game of footbott\") = \"footbott\" ]]\n    [[ $(candidate \"we are gonna rock\") = \"gonna\" ]]\n    [[ $(candidate \"we are a mad nation\") = \"nation\" ]]\n    [[ $(candidate \"this is a prrk\") = \"this\" ]]\n    [[ $(candidate \"b\") = \"b\" ]]\n    [[ $(candidate \"play play play\") = \"play\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_162_string_to_md5", "language": "sh", "prompt": "#!/bin/bash\n# Given a string 'text', return its md5 hash equivalent string.\n# If 'text' is an empty string, return None.\n# >>> $(string_to_md5 \"Hello world\")\n# \"3e25960a79dbc69b674cd4ec67a72c62\"\n#\n# $1 is a string\nstring_to_md5() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    string_to_md5 \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\") = \"3e25960a79dbc69b674cd4ec67a72c62\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"A B C\") = \"0ef78513b0cb8cef12743f5aeb35f888\" ]]\n    [[ $(candidate \"password\") = \"5f4dcc3b5aa765d61d8327deb882cf99\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_162_string_to_md5", "test": "}\n\ncandidate() {\n    string_to_md5 \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"Hello world\") = \"3e25960a79dbc69b674cd4ec67a72c62\" ]]\n    [[ $(candidate \"\") = \"None\" ]]\n    [[ $(candidate \"A B C\") = \"0ef78513b0cb8cef12743f5aeb35f888\" ]]\n    [[ $(candidate \"password\") = \"5f4dcc3b5aa765d61d8327deb882cf99\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_44_change_base", "language": "sh", "prompt": "#!/bin/bash\n# Change numerical base of input number x to base.\n# return string representation after the conversion.\n# base numbers are less than 10.\n# >>> $(change_base \"8\" \"3\")\n# \"22\"\n# >>> $(change_base \"8\" \"2\")\n# \"1000\"\n# >>> $(change_base \"7\" \"2\")\n# \"111\"\n#\n# $1 is an integer\n# $2 is an integer\nchange_base() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    change_base \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"8\" \"3\") = \"22\" ]]\n    [[ $(candidate \"9\" \"3\") = \"100\" ]]\n    [[ $(candidate \"234\" \"2\") = \"11101010\" ]]\n    [[ $(candidate \"16\" \"2\") = \"10000\" ]]\n    [[ $(candidate \"8\" \"2\") = \"1000\" ]]\n    [[ $(candidate \"7\" \"2\") = \"111\" ]]\n    [[ $(candidate \"2\" \"3\") = \"2\" ]]\n    [[ $(candidate \"3\" \"4\") = \"3\" ]]\n    [[ $(candidate \"4\" \"5\") = \"4\" ]]\n    [[ $(candidate \"5\" \"6\") = \"5\" ]]\n    [[ $(candidate \"6\" \"7\") = \"6\" ]]\n    [[ $(candidate \"7\" \"8\") = \"7\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_44_change_base", "test": "}\n\ncandidate() {\n    change_base \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"8\" \"3\") = \"22\" ]]\n    [[ $(candidate \"9\" \"3\") = \"100\" ]]\n    [[ $(candidate \"234\" \"2\") = \"11101010\" ]]\n    [[ $(candidate \"16\" \"2\") = \"10000\" ]]\n    [[ $(candidate \"8\" \"2\") = \"1000\" ]]\n    [[ $(candidate \"7\" \"2\") = \"111\" ]]\n    [[ $(candidate \"2\" \"3\") = \"2\" ]]\n    [[ $(candidate \"3\" \"4\") = \"3\" ]]\n    [[ $(candidate \"4\" \"5\") = \"4\" ]]\n    [[ $(candidate \"5\" \"6\") = \"5\" ]]\n    [[ $(candidate \"6\" \"7\") = \"6\" ]]\n    [[ $(candidate \"7\" \"8\") = \"7\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_157_right_angle_triangle", "language": "sh", "prompt": "#!/bin/bash\n# Given the lengths of the three sides of a triangle. Return true if the three\n# sides form a right-angled triangle, false otherwise.\n# A right-angled triangle is a triangle in which one angle is right angle or \n# 90 degree.\n# Example:\n# >>> $(right_angle_triangle \"3\" \"4\" \"5\")\n# \"true\"\n# >>> $(right_angle_triangle \"1\" \"2\" \"3\")\n# \"false\"\n#\n# $1 is an integer\n# $2 is an integer\n# $3 is an integer\nright_angle_triangle() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    right_angle_triangle \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"4\" \"5\") = \"true\" ]]\n    [[ $(candidate \"1\" \"2\" \"3\") = \"false\" ]]\n    [[ $(candidate \"10\" \"6\" \"8\") = \"true\" ]]\n    [[ $(candidate \"2\" \"2\" \"2\") = \"false\" ]]\n    [[ $(candidate \"7\" \"24\" \"25\") = \"true\" ]]\n    [[ $(candidate \"10\" \"5\" \"7\") = \"false\" ]]\n    [[ $(candidate \"5\" \"12\" \"13\") = \"true\" ]]\n    [[ $(candidate \"15\" \"8\" \"17\") = \"true\" ]]\n    [[ $(candidate \"48\" \"55\" \"73\") = \"true\" ]]\n    [[ $(candidate \"1\" \"1\" \"1\") = \"false\" ]]\n    [[ $(candidate \"2\" \"2\" \"10\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_157_right_angle_triangle", "test": "}\n\ncandidate() {\n    right_angle_triangle \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"3\" \"4\" \"5\") = \"true\" ]]\n    [[ $(candidate \"1\" \"2\" \"3\") = \"false\" ]]\n    [[ $(candidate \"10\" \"6\" \"8\") = \"true\" ]]\n    [[ $(candidate \"2\" \"2\" \"2\") = \"false\" ]]\n    [[ $(candidate \"7\" \"24\" \"25\") = \"true\" ]]\n    [[ $(candidate \"10\" \"5\" \"7\") = \"false\" ]]\n    [[ $(candidate \"5\" \"12\" \"13\") = \"true\" ]]\n    [[ $(candidate \"15\" \"8\" \"17\") = \"true\" ]]\n    [[ $(candidate \"48\" \"55\" \"73\") = \"true\" ]]\n    [[ $(candidate \"1\" \"1\" \"1\") = \"false\" ]]\n    [[ $(candidate \"2\" \"2\" \"10\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "sh", "prompt": "#!/bin/bash\n# It is the last week of the semester and the teacher has to give the grades\n# to students. The teacher has been making her own algorithm for grading.\n# The only problem is, she has lost the code she used for grading.\n# She has given you a list of GPAs for some students and you have to write \n# a function that can output a list of letter grades using the following table:\n# GPA       |    Letter grade\n# 4.0                A+\n# > 3.7                A \n# > 3.3                A- \n# > 3.0                B+\n# > 2.7                B \n# > 2.3                B-\n# > 2.0                C+\n# > 1.7                C\n# > 1.3                C-\n# > 1.0                D+ \n# > 0.7                D \n# > 0.0                D-\n# 0.0                E\n# Example:\n# >>> $(grade_equation \"4.0 3 1.7 2 3.5\")\n# ['\"A+\"', '\"B\"', '\"C-\"', '\"C\"', '\"A-\"']\n#\n# $1 is a space-separated list\nnumerical_letter_grade() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    numerical_letter_grade \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4.0 3 1.7 2 3.5\") = \"A+ B C- C A-\" ]]\n    [[ $(candidate \"1.2\") = \"D+\" ]]\n    [[ $(candidate \"0.5\") = \"D-\" ]]\n    [[ $(candidate \"0.0\") = \"E\" ]]\n    [[ $(candidate \"1.0 0.3 1.5 2.8 3.3\") = \"D D- C- B B+\" ]]\n    [[ $(candidate \"0.0 0.7\") = \"E D-\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "}\n\ncandidate() {\n    numerical_letter_grade \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"4.0 3 1.7 2 3.5\") = \"A+ B C- C A-\" ]]\n    [[ $(candidate \"1.2\") = \"D+\" ]]\n    [[ $(candidate \"0.5\") = \"D-\" ]]\n    [[ $(candidate \"0.0\") = \"E\" ]]\n    [[ $(candidate \"1.0 0.3 1.5 2.8 3.3\") = \"D D- C- B B+\" ]]\n    [[ $(candidate \"0.0 0.7\") = \"E D-\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_5_intersperse", "language": "sh", "prompt": "#!/bin/bash\n# Insert a number 'delimeter' between every two consecutive elements of input list `numbers'\n# >>> $(intersperse \"\" \"4\")\n# []\n# >>> $(intersperse \"1 2 3\" \"4\")\n# ['\"1\"', '\"4\"', '\"2\"', '\"4\"', '\"3\"']\n#\n# $1 is a space-separated list\n# $2 is an integer\nintersperse() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    intersperse \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"7\") = \"\" ]]\n    [[ $(candidate \"5 6 3 2\" \"8\") = \"5 8 6 8 3 8 2\" ]]\n    [[ $(candidate \"2 2 2\" \"2\") = \"2 2 2 2 2\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_5_intersperse", "test": "}\n\ncandidate() {\n    intersperse \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\" \"7\") = \"\" ]]\n    [[ $(candidate \"5 6 3 2\" \"8\") = \"5 8 6 8 3 8 2\" ]]\n    [[ $(candidate \"2 2 2\" \"2\") = \"2 2 2 2 2\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_146_specialFilter", "language": "sh", "prompt": "#!/bin/bash\n# Write a function that takes an array of numbers as input and returns \n# the number of elements in the array that are greater than 10 and both \n# first and last digits of a number are odd (1, 3, 5, 7, 9).\n# For example:\n# >>> $(specialFilter \"15 -73 14 -15\")\n# \"1\"\n# >>> $(specialFilter \"33 -2 -3 45 21 109\")\n# \"2\"\n#\n# $1 is a space-separated list\nspecialFilter() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    specialFilter \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 -2 1 -5\") = \"0\" ]]\n    [[ $(candidate \"15 -73 14 -15\") = \"1\" ]]\n    [[ $(candidate \"33 -2 -3 45 21 109\") = \"2\" ]]\n    [[ $(candidate \"43 -12 93 125 121 109\") = \"4\" ]]\n    [[ $(candidate \"71 -2 -33 75 21 19\") = \"3\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_146_specialFilter", "test": "}\n\ncandidate() {\n    specialFilter \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 -2 1 -5\") = \"0\" ]]\n    [[ $(candidate \"15 -73 14 -15\") = \"1\" ]]\n    [[ $(candidate \"33 -2 -3 45 21 109\") = \"2\" ]]\n    [[ $(candidate \"43 -12 93 125 121 109\") = \"4\" ]]\n    [[ $(candidate \"71 -2 -33 75 21 19\") = \"3\" ]]\n    [[ $(candidate \"1\") = \"0\" ]]\n    [[ $(candidate \"\") = \"0\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_60_sum_to_n", "language": "sh", "prompt": "#!/bin/bash\n# sum_to_n is a function that sums numbers from 1 to n.\n# >>> $(sum_to_n \"30\")\n# \"465\"\n# >>> $(sum_to_n \"100\")\n# \"5050\"\n# >>> $(sum_to_n \"5\")\n# \"15\"\n# >>> $(sum_to_n \"10\")\n# \"55\"\n# >>> $(sum_to_n \"1\")\n# \"1\"\n#\n# $1 is an integer\nsum_to_n() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sum_to_n \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"6\") = \"21\" ]]\n    [[ $(candidate \"11\") = \"66\" ]]\n    [[ $(candidate \"30\") = \"465\" ]]\n    [[ $(candidate \"100\") = \"5050\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_60_sum_to_n", "test": "}\n\ncandidate() {\n    sum_to_n \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"6\") = \"21\" ]]\n    [[ $(candidate \"11\") = \"66\" ]]\n    [[ $(candidate \"30\") = \"465\" ]]\n    [[ $(candidate \"100\") = \"5050\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_26_remove_duplicates", "language": "sh", "prompt": "#!/bin/bash\n# From a list of integers, remove all elements that occur more than once.\n# Keep order of elements left the same as in the input.\n# >>> $(remove_duplicates \"1 2 3 2 4\")\n# ['\"1\"', '\"3\"', '\"4\"']\n#\n# $1 is a space-separated list\nremove_duplicates() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    remove_duplicates \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"1 2 3 2 4 3 5\") = \"1 4 5\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_26_remove_duplicates", "test": "}\n\ncandidate() {\n    remove_duplicates \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"1 2 3 2 4 3 5\") = \"1 4 5\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_163_generate_integers", "language": "sh", "prompt": "#!/bin/bash\n# Given two positive integers a and b, return the even digits between a\n# and b, in ascending order.\n# For example:\n# >>> $(generate_integers \"2\" \"8\")\n# ['\"2\"', '\"4\"', '\"6\"', '\"8\"']\n# >>> $(generate_integers \"8\" \"2\")\n# ['\"2\"', '\"4\"', '\"6\"', '\"8\"']\n# >>> $(generate_integers \"10\" \"14\")\n# []\n#\n# $1 is an integer\n# $2 is an integer\ngenerate_integers() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    generate_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\" \"10\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"10\" \"2\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"132\" \"2\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"17\" \"89\") = \"\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_163_generate_integers", "test": "}\n\ncandidate() {\n    generate_integers \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"2\" \"10\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"10\" \"2\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"132\" \"2\") = \"2 4 6 8\" ]]\n    [[ $(candidate \"17\" \"89\") = \"\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_9_rolling_max", "language": "sh", "prompt": "#!/bin/bash\n# From a given list of integers, generate a list of rolling maximum element found until given moment\n# in the sequence.\n# >>> $(rolling_max \"1 2 3 2 3 4 2\")\n# ['\"1\"', '\"2\"', '\"3\"', '\"3\"', '\"3\"', '\"4\"', '\"4\"']\n#\n# $1 is a space-separated list\nrolling_max() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    rolling_max \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"4 3 2 1\") = \"4 4 4 4\" ]]\n    [[ $(candidate \"3 2 3 100 3\") = \"3 3 3 100 100\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_9_rolling_max", "test": "}\n\ncandidate() {\n    rolling_max \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"\" ]]\n    [[ $(candidate \"1 2 3 4\") = \"1 2 3 4\" ]]\n    [[ $(candidate \"4 3 2 1\") = \"4 4 4 4\" ]]\n    [[ $(candidate \"3 2 3 100 3\") = \"3 3 3 100 100\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_3_below_zero", "language": "sh", "prompt": "#!/bin/bash\n# You're given a list of deposit and withdrawal operations on a bank account that starts with\n# zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n# at that point function should return true. Otherwise it should return false.\n# >>> $(below_zero \"1 2 3\")\n# \"false\"\n# >>> $(below_zero \"1 2 -4 5\")\n# \"true\"\n#\n# $1 is a space-separated list\nbelow_zero() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    below_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"1 2 -3 1 2 -3\") = \"false\" ]]\n    [[ $(candidate \"1 2 -4 5 6\") = \"true\" ]]\n    [[ $(candidate \"1 -1 2 -2 5 -5 4 -4\") = \"false\" ]]\n    [[ $(candidate \"1 -1 2 -2 5 -5 4 -5\") = \"true\" ]]\n    [[ $(candidate \"1 -2 2 -2 5 -5 4 -4\") = \"true\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_3_below_zero", "test": "}\n\ncandidate() {\n    below_zero \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"\") = \"false\" ]]\n    [[ $(candidate \"1 2 -3 1 2 -3\") = \"false\" ]]\n    [[ $(candidate \"1 2 -4 5 6\") = \"true\" ]]\n    [[ $(candidate \"1 -1 2 -2 5 -5 4 -4\") = \"false\" ]]\n    [[ $(candidate \"1 -1 2 -2 5 -5 4 -5\") = \"true\" ]]\n    [[ $(candidate \"1 -2 2 -2 5 -5 4 -4\") = \"true\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_69_search", "language": "sh", "prompt": "#!/bin/bash\n# You are given a non-empty list of positive integers. Return the greatest integer that is greater than \n# zero, and has a frequency greater than or equal to the value of the integer itself. \n# The frequency of an integer is the number of times it appears in the list.\n# If no such a value exist, return -1.\n# Examples:\n# >>> $(search \"4 1 2 2 3 1\")\n# \"2\"\n# >>> $(search \"1 2 2 3 3 3 4 4 4\")\n# \"3\"\n# >>> $(search \"5 5 4 4 4\")\n# \"-1\"\n#\n# $1 is a space-separated list\nsearch() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    search \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 5 5 5 1\") = \"1\" ]]\n    [[ $(candidate \"4 1 4 1 4 4\") = \"4\" ]]\n    [[ $(candidate \"3 3\") = \"-1\" ]]\n    [[ $(candidate \"8 8 8 8 8 8 8 8\") = \"8\" ]]\n    [[ $(candidate \"2 3 3 2 2\") = \"2\" ]]\n    [[ $(candidate \"2 7 8 8 4 8 7 3 9 6 5 10 4 3 6 7 1 7 4 10 8 1\") = \"1\" ]]\n    [[ $(candidate \"3 2 8 2\") = \"2\" ]]\n    [[ $(candidate \"6 7 1 8 8 10 5 8 5 3 10\") = \"1\" ]]\n    [[ $(candidate \"8 8 3 6 5 6 4\") = \"-1\" ]]\n    [[ $(candidate \"6 9 6 7 1 4 7 1 8 8 9 8 10 10 8 4 10 4 10 1 2 9 5 7 9\") = \"1\" ]]\n    [[ $(candidate \"1 9 10 1 3\") = \"1\" ]]\n    [[ $(candidate \"6 9 7 5 8 7 5 3 7 5 10 10 3 6 10 2 8 6 5 4 9 5 3 10\") = \"5\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"8 8 10 6 4 3 5 8 2 4 2 8 4 6 10 4 2 1 10 2 1 1 5\") = \"4\" ]]\n    [[ $(candidate \"2 10 4 8 2 10 5 1 2 9 5 5 6 3 8 6 4 10\") = \"2\" ]]\n    [[ $(candidate \"1 6 10 1 6 9 10 8 6 8 7 3\") = \"1\" ]]\n    [[ $(candidate \"9 2 4 1 5 1 5 2 5 7 7 7 3 10 1 5 4 2 8 4 1 9 10 7 10 2 8 10 9 4\") = \"4\" ]]\n    [[ $(candidate \"2 6 4 2 8 7 5 6 4 10 4 6 3 7 8 8 3 1 4 2 2 10 7\") = \"4\" ]]\n    [[ $(candidate \"9 8 6 10 2 6 10 2 7 8 10 3 8 2 6 2 3 1\") = \"2\" ]]\n    [[ $(candidate \"5 5 3 9 5 6 3 2 8 5 6 10 10 6 8 4 10 7 7 10 8\") = \"-1\" ]]\n    [[ $(candidate \"10\") = \"-1\" ]]\n    [[ $(candidate \"9 7 7 2 4 7 2 10 9 7 5 7 2\") = \"2\" ]]\n    [[ $(candidate \"5 4 10 2 1 1 10 3 6 1 8\") = \"1\" ]]\n    [[ $(candidate \"7 9 9 9 3 4 1 5 9 1 2 1 1 10 7 5 6 7 6 7 7 6\") = \"1\" ]]\n    [[ $(candidate \"3 10 10 9 2\") = \"-1\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_69_search", "test": "}\n\ncandidate() {\n    search \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"5 5 5 5 1\") = \"1\" ]]\n    [[ $(candidate \"4 1 4 1 4 4\") = \"4\" ]]\n    [[ $(candidate \"3 3\") = \"-1\" ]]\n    [[ $(candidate \"8 8 8 8 8 8 8 8\") = \"8\" ]]\n    [[ $(candidate \"2 3 3 2 2\") = \"2\" ]]\n    [[ $(candidate \"2 7 8 8 4 8 7 3 9 6 5 10 4 3 6 7 1 7 4 10 8 1\") = \"1\" ]]\n    [[ $(candidate \"3 2 8 2\") = \"2\" ]]\n    [[ $(candidate \"6 7 1 8 8 10 5 8 5 3 10\") = \"1\" ]]\n    [[ $(candidate \"8 8 3 6 5 6 4\") = \"-1\" ]]\n    [[ $(candidate \"6 9 6 7 1 4 7 1 8 8 9 8 10 10 8 4 10 4 10 1 2 9 5 7 9\") = \"1\" ]]\n    [[ $(candidate \"1 9 10 1 3\") = \"1\" ]]\n    [[ $(candidate \"6 9 7 5 8 7 5 3 7 5 10 10 3 6 10 2 8 6 5 4 9 5 3 10\") = \"5\" ]]\n    [[ $(candidate \"1\") = \"1\" ]]\n    [[ $(candidate \"8 8 10 6 4 3 5 8 2 4 2 8 4 6 10 4 2 1 10 2 1 1 5\") = \"4\" ]]\n    [[ $(candidate \"2 10 4 8 2 10 5 1 2 9 5 5 6 3 8 6 4 10\") = \"2\" ]]\n    [[ $(candidate \"1 6 10 1 6 9 10 8 6 8 7 3\") = \"1\" ]]\n    [[ $(candidate \"9 2 4 1 5 1 5 2 5 7 7 7 3 10 1 5 4 2 8 4 1 9 10 7 10 2 8 10 9 4\") = \"4\" ]]\n    [[ $(candidate \"2 6 4 2 8 7 5 6 4 10 4 6 3 7 8 8 3 1 4 2 2 10 7\") = \"4\" ]]\n    [[ $(candidate \"9 8 6 10 2 6 10 2 7 8 10 3 8 2 6 2 3 1\") = \"2\" ]]\n    [[ $(candidate \"5 5 3 9 5 6 3 2 8 5 6 10 10 6 8 4 10 7 7 10 8\") = \"-1\" ]]\n    [[ $(candidate \"10\") = \"-1\" ]]\n    [[ $(candidate \"9 7 7 2 4 7 2 10 9 7 5 7 2\") = \"2\" ]]\n    [[ $(candidate \"5 4 10 2 1 1 10 3 6 1 8\") = \"1\" ]]\n    [[ $(candidate \"7 9 9 9 3 4 1 5 9 1 2 1 1 10 7 5 6 7 6 7 7 6\") = \"1\" ]]\n    [[ $(candidate \"3 10 10 9 2\") = \"-1\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_61_correct_bracketing", "language": "sh", "prompt": "#!/bin/bash\n# brackets is a string of \"(\" and \")\".\n# return true if every opening bracket has a corresponding closing bracket.\n# >>> $(correct_bracketing \"(\")\n# \"false\"\n# >>> $(correct_bracketing \"()\")\n# \"true\"\n# >>> $(correct_bracketing \"(()())\")\n# \"true\"\n# >>> $(correct_bracketing \")(()\")\n# \"false\"\n#\n# $1 is a string\ncorrect_bracketing() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    correct_bracketing \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"()\") = \"true\" ]]\n    [[ $(candidate \"(()())\") = \"true\" ]]\n    [[ $(candidate \"()()(()())()\") = \"true\" ]]\n    [[ $(candidate \"()()((()()())())(()()(()))\") = \"true\" ]]\n    [[ $(candidate \"((()())))\") = \"false\" ]]\n    [[ $(candidate \")(()\") = \"false\" ]]\n    [[ $(candidate \"(\") = \"false\" ]]\n    [[ $(candidate \"((((\") = \"false\" ]]\n    [[ $(candidate \")\") = \"false\" ]]\n    [[ $(candidate \"(()\") = \"false\" ]]\n    [[ $(candidate \"()()(()())())(()\") = \"false\" ]]\n    [[ $(candidate \"()()(()())()))()\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_61_correct_bracketing", "test": "}\n\ncandidate() {\n    correct_bracketing \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"()\") = \"true\" ]]\n    [[ $(candidate \"(()())\") = \"true\" ]]\n    [[ $(candidate \"()()(()())()\") = \"true\" ]]\n    [[ $(candidate \"()()((()()())())(()()(()))\") = \"true\" ]]\n    [[ $(candidate \"((()())))\") = \"false\" ]]\n    [[ $(candidate \")(()\") = \"false\" ]]\n    [[ $(candidate \"(\") = \"false\" ]]\n    [[ $(candidate \"((((\") = \"false\" ]]\n    [[ $(candidate \")\") = \"false\" ]]\n    [[ $(candidate \"(()\") = \"false\" ]]\n    [[ $(candidate \"()()(()())())(()\") = \"false\" ]]\n    [[ $(candidate \"()()(()())()))()\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_37_sort_even", "language": "sh", "prompt": "#!/bin/bash\n# This function takes a list l and returns a list l' such that\n# l' is identical to l in the odd indicies, while its values at the even indicies are equal\n# to the values of the even indicies of l, but sorted.\n# >>> $(sort_even \"1 2 3\")\n# ['\"1\"', '\"2\"', '\"3\"']\n# >>> $(sort_even \"5 6 3 4\")\n# ['\"3\"', '\"6\"', '\"5\"', '\"4\"']\n#\n# $1 is a space-separated list\nsort_even() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    sort_even \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"1 2 3\" ]]\n    [[ $(candidate \"5 3 -5 2 -3 3 9 0 123 1 -10\") = \"-10 3 -5 2 -3 3 5 0 9 1 123\" ]]\n    [[ $(candidate \"5 8 -12 4 23 2 3 11 12 -10\") = \"-12 8 3 4 5 2 12 11 23 -10\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_37_sort_even", "test": "}\n\ncandidate() {\n    sort_even \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"1 2 3\") = \"1 2 3\" ]]\n    [[ $(candidate \"5 3 -5 2 -3 3 9 0 123 1 -10\") = \"-10 3 -5 2 -3 3 5 0 9 1 123\" ]]\n    [[ $(candidate \"5 8 -12 4 23 2 3 11 12 -10\") = \"-12 8 3 4 5 2 12 11 23 -10\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_54_same_chars", "language": "sh", "prompt": "#!/bin/bash\n# Check if two words have the same characters.\n# >>> $(same_chars \"eabcdzzzz\" \"dddzzzzzzzddeddabc\")\n# \"true\"\n# >>> $(same_chars \"abcd\" \"dddddddabc\")\n# \"true\"\n# >>> $(same_chars \"dddddddabc\" \"abcd\")\n# \"true\"\n# >>> $(same_chars \"eabcd\" \"dddddddabc\")\n# \"false\"\n# >>> $(same_chars \"abcd\" \"dddddddabce\")\n# \"false\"\n# >>> $(same_chars \"eabcdzzzz\" \"dddzzzzzzzddddabc\")\n# \"false\"\n#\n# $1 is a string\n# $2 is a string\nsame_chars() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    same_chars \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"eabcdzzzz\" \"dddzzzzzzzddeddabc\") = \"true\" ]]\n    [[ $(candidate \"abcd\" \"dddddddabc\") = \"true\" ]]\n    [[ $(candidate \"dddddddabc\" \"abcd\") = \"true\" ]]\n    [[ $(candidate \"eabcd\" \"dddddddabc\") = \"false\" ]]\n    [[ $(candidate \"abcd\" \"dddddddabcf\") = \"false\" ]]\n    [[ $(candidate \"eabcdzzzz\" \"dddzzzzzzzddddabc\") = \"false\" ]]\n    [[ $(candidate \"aabb\" \"aaccc\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_54_same_chars", "test": "}\n\ncandidate() {\n    same_chars \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"eabcdzzzz\" \"dddzzzzzzzddeddabc\") = \"true\" ]]\n    [[ $(candidate \"abcd\" \"dddddddabc\") = \"true\" ]]\n    [[ $(candidate \"dddddddabc\" \"abcd\") = \"true\" ]]\n    [[ $(candidate \"eabcd\" \"dddddddabc\") = \"false\" ]]\n    [[ $(candidate \"abcd\" \"dddddddabcf\") = \"false\" ]]\n    [[ $(candidate \"eabcdzzzz\" \"dddzzzzzzzddddabc\") = \"false\" ]]\n    [[ $(candidate \"aabb\" \"aaccc\") = \"false\" ]]\n}\n\nrun_test"}
{"name": "HumanEval_56_correct_bracketing", "language": "sh", "prompt": "#!/bin/bash\n# brackets is a string of \"<\" and \">\".\n# return true if every opening bracket has a corresponding closing bracket.\n# >>> $(correct_bracketing \"<\")\n# \"false\"\n# >>> $(correct_bracketing \"<>\")\n# \"true\"\n# >>> $(correct_bracketing \"<<><>>\")\n# \"true\"\n# >>> $(correct_bracketing \"><<>\")\n# \"false\"\n#\n# $1 is a string\ncorrect_bracketing() {\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "}\n\ncandidate() {\n    correct_bracketing \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"<>\") = \"true\" ]]\n    [[ $(candidate \"<<><>>\") = \"true\" ]]\n    [[ $(candidate \"<><><<><>><>\") = \"true\" ]]\n    [[ $(candidate \"<><><<<><><>><>><<><><<>>>\") = \"true\" ]]\n    [[ $(candidate \"<<<><>>>>\") = \"false\" ]]\n    [[ $(candidate \"><<>\") = \"false\" ]]\n    [[ $(candidate \"<\") = \"false\" ]]\n    [[ $(candidate \"<<<<\") = \"false\" ]]\n    [[ $(candidate \">\") = \"false\" ]]\n    [[ $(candidate \"<<>\") = \"false\" ]]\n    [[ $(candidate \"<><><<><>><>><<>\") = \"false\" ]]\n    [[ $(candidate \"<><><<><>><>>><>\") = \"false\" ]]\n}\n\nrun_test", "stop_tokens": ["\n}"], "task_id": "HumanEval_56_correct_bracketing", "test": "}\n\ncandidate() {\n    correct_bracketing \"$@\"\n}\n\nset -e\nrun_test() {\n    [[ $(candidate \"<>\") = \"true\" ]]\n    [[ $(candidate \"<<><>>\") = \"true\" ]]\n    [[ $(candidate \"<><><<><>><>\") = \"true\" ]]\n    [[ $(candidate \"<><><<<><><>><>><<><><<>>>\") = \"true\" ]]\n    [[ $(candidate \"<<<><>>>>\") = \"false\" ]]\n    [[ $(candidate \"><<>\") = \"false\" ]]\n    [[ $(candidate \"<\") = \"false\" ]]\n    [[ $(candidate \"<<<<\") = \"false\" ]]\n    [[ $(candidate \">\") = \"false\" ]]\n    [[ $(candidate \"<<>\") = \"false\" ]]\n    [[ $(candidate \"<><><<><>><>><<>\") = \"false\" ]]\n    [[ $(candidate \"<><><<><>><>>><>\") = \"false\" ]]\n}\n\nrun_test"}
