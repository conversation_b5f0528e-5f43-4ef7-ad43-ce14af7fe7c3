{"name": "HumanEval_23_strlen", "language": "rb", "prompt": "# Return length of given string\n# >>> strlen.call(\"\")\n# 0\n# >>> strlen.call(\"abc\")\n# 3\ndef strlen(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_23_strlen.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_strlen\n    candidate = method(:strlen)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(1, candidate.call(\"x\"))\n    assert_equal(9, candidate.call(\"asdasnakj\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_23_strlen", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_strlen\n    candidate = method(:strlen)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(1, candidate.call(\"x\"))\n    assert_equal(9, candidate.call(\"asdasnakj\"))\n  end\nend\n"}
{"name": "HumanEval_89_encrypt", "language": "rb", "prompt": "# Create a function encrypt that takes a string as an argument and\n# returns a string encrypted with the alphabet being rotated. \n# The alphabet should be rotated in a manner such that the letters \n# shift down by two multiplied to two places.\n# For example:\n# >>> encrypt.call(\"hi\")\n# \"lm\"\n# >>> encrypt.call(\"asdfghjkl\")\n# \"ewhjklnop\"\n# >>> encrypt.call(\"gf\")\n# \"kj\"\n# >>> encrypt.call(\"et\")\n# \"ix\"\ndef encrypt(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_89_encrypt.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_encrypt\n    candidate = method(:encrypt)\n    assert_equal(\"lm\", candidate.call(\"hi\"))\n    assert_equal(\"ewhjklnop\", candidate.call(\"asdfghjkl\"))\n    assert_equal(\"kj\", candidate.call(\"gf\"))\n    assert_equal(\"ix\", candidate.call(\"et\"))\n    assert_equal(\"jeiajeaijeiak\", candidate.call(\"faewfawefaewg\"))\n    assert_equal(\"lippsqcjvmirh\", candidate.call(\"hellomyfriend\"))\n    assert_equal(\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\", candidate.call(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\"))\n    assert_equal(\"e\", candidate.call(\"a\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_89_encrypt", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_encrypt\n    candidate = method(:encrypt)\n    assert_equal(\"lm\", candidate.call(\"hi\"))\n    assert_equal(\"ewhjklnop\", candidate.call(\"asdfghjkl\"))\n    assert_equal(\"kj\", candidate.call(\"gf\"))\n    assert_equal(\"ix\", candidate.call(\"et\"))\n    assert_equal(\"jeiajeaijeiak\", candidate.call(\"faewfawefaewg\"))\n    assert_equal(\"lippsqcjvmirh\", candidate.call(\"hellomyfriend\"))\n    assert_equal(\"hbdhpqrmpjylqmpyjlpmlyjrqpmqryjlpmqryjljygyjl\", candidate.call(\"dxzdlmnilfuhmilufhlihufnmlimnufhlimnufhfucufh\"))\n    assert_equal(\"e\", candidate.call(\"a\"))\n  end\nend\n"}
{"name": "HumanEval_95_check_dict_case", "language": "rb", "prompt": "# Given a hash, return true if all keys are strings in lower \n# case or all keys are strings in upper case, else return false.\n# The function should return false is the given hash is empty.\n# Examples:\n# >>> check_dict_case.call({\"a\" => \"apple\", \"b\" => \"banana\"})\n# true\n# >>> check_dict_case.call({\"a\" => \"apple\", \"A\" => \"banana\", \"B\" => \"banana\"})\n# false\n# >>> check_dict_case.call({\"a\" => \"apple\", 8 => \"banana\", \"a\" => \"apple\"})\n# false\n# >>> check_dict_case.call({\"Name\" => \"John\", \"Age\" => \"36\", \"City\" => \"Houston\"})\n# false\n# >>> check_dict_case.call({\"STATE\" => \"NC\", \"ZIP\" => \"12345\"})\n# true\ndef check_dict_case(dict)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_95_check_dict_case.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_check_dict_case\n    candidate = method(:check_dict_case)\n    assert_equal(true, candidate.call({\"p\" => \"pineapple\", \"b\" => \"banana\"}))\n    assert_equal(false, candidate.call({\"p\" => \"pineapple\", \"A\" => \"banana\", \"B\" => \"banana\"}))\n    assert_equal(false, candidate.call({\"p\" => \"pineapple\", \"5\" => \"banana\", \"a\" => \"apple\"}))\n    assert_equal(false, candidate.call({\"Name\" => \"John\", \"Age\" => \"36\", \"City\" => \"Houston\"}))\n    assert_equal(true, candidate.call({\"STATE\" => \"NC\", \"ZIP\" => \"12345\"}))\n    assert_equal(true, candidate.call({\"fruit\" => \"Orange\", \"taste\" => \"Sweet\"}))\n    assert_equal(false, candidate.call({}))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_95_check_dict_case", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_check_dict_case\n    candidate = method(:check_dict_case)\n    assert_equal(true, candidate.call({\"p\" => \"pineapple\", \"b\" => \"banana\"}))\n    assert_equal(false, candidate.call({\"p\" => \"pineapple\", \"A\" => \"banana\", \"B\" => \"banana\"}))\n    assert_equal(false, candidate.call({\"p\" => \"pineapple\", \"5\" => \"banana\", \"a\" => \"apple\"}))\n    assert_equal(false, candidate.call({\"Name\" => \"John\", \"Age\" => \"36\", \"City\" => \"Houston\"}))\n    assert_equal(true, candidate.call({\"STATE\" => \"NC\", \"ZIP\" => \"12345\"}))\n    assert_equal(true, candidate.call({\"fruit\" => \"Orange\", \"taste\" => \"Sweet\"}))\n    assert_equal(false, candidate.call({}))\n  end\nend\n"}
{"name": "HumanEval_85_add", "language": "rb", "prompt": "# Given a non-empty array of integers lst. add the even elements that are at odd indices..\n# Examples:\n# >>> add.call([4, 2, 6, 7])\n# 2\ndef add(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_85_add.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add\n    candidate = method(:add)\n    assert_equal(88, candidate.call([4, 88]))\n    assert_equal(122, candidate.call([4, 5, 6, 7, 2, 122]))\n    assert_equal(0, candidate.call([4, 0, 6, 7]))\n    assert_equal(12, candidate.call([4, 4, 6, 8]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_85_add", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add\n    candidate = method(:add)\n    assert_equal(88, candidate.call([4, 88]))\n    assert_equal(122, candidate.call([4, 5, 6, 7, 2, 122]))\n    assert_equal(0, candidate.call([4, 0, 6, 7]))\n    assert_equal(12, candidate.call([4, 4, 6, 8]))\n  end\nend\n"}
{"name": "HumanEval_140_fix_spaces", "language": "rb", "prompt": "# Given a string text, replace all spaces in it with underscores, \n# and if a string has more than 2 consecutive spaces, \n# then replace all consecutive spaces with - \n# >>> fix_spaces.call(\" Example\")\n# \"Example\"\n# >>> fix_spaces.call(\" Example 1\")\n# \"Example_1\"\n# >>> fix_spaces.call(\" Example 2\")\n# \"_Example_2\"\n# >>> fix_spaces.call(\" Example 3\")\n# \"_Example-3\"\ndef fix_spaces(text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_140_fix_spaces.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fix_spaces\n    candidate = method(:fix_spaces)\n    assert_equal(\"Example\", candidate.call(\"Example\"))\n    assert_equal(\"Mudasir_Hanif_\", candidate.call(\"Mudasir Hanif \"))\n    assert_equal(\"Yellow_Yellow__Dirty__Fellow\", candidate.call(\"Yellow Yellow  Dirty  Fellow\"))\n    assert_equal(\"Exa-mple\", candidate.call(\"Exa   mple\"))\n    assert_equal(\"-Exa_1_2_2_mple\", candidate.call(\"   Exa 1 2 2 mple\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_140_fix_spaces", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fix_spaces\n    candidate = method(:fix_spaces)\n    assert_equal(\"Example\", candidate.call(\"Example\"))\n    assert_equal(\"Mudasir_Hanif_\", candidate.call(\"Mudasir Hanif \"))\n    assert_equal(\"Yellow_Yellow__Dirty__Fellow\", candidate.call(\"Yellow Yellow  Dirty  Fellow\"))\n    assert_equal(\"Exa-mple\", candidate.call(\"Exa   mple\"))\n    assert_equal(\"-Exa_1_2_2_mple\", candidate.call(\"   Exa 1 2 2 mple\"))\n  end\nend\n"}
{"name": "HumanEval_63_fibfib", "language": "rb", "prompt": "# The FibFib number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n# fibfib(0) == 0\n# fibfib(1) == 0\n# fibfib(2) == 1\n# fibfib(n) == fibfib(n-1) + fibfib(n-2) + fibfib(n-3).\n# Please write a function to efficiently compute the n-th element of the fibfib number sequence.\n# >>> fibfib.call(1)\n# 0\n# >>> fibfib.call(5)\n# 4\n# >>> fibfib.call(8)\n# 24\ndef fibfib(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_63_fibfib.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fibfib\n    candidate = method(:fibfib)\n    assert_equal(1, candidate.call(2))\n    assert_equal(0, candidate.call(1))\n    assert_equal(4, candidate.call(5))\n    assert_equal(24, candidate.call(8))\n    assert_equal(81, candidate.call(10))\n    assert_equal(274, candidate.call(12))\n    assert_equal(927, candidate.call(14))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_63_fibfib", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fibfib\n    candidate = method(:fibfib)\n    assert_equal(1, candidate.call(2))\n    assert_equal(0, candidate.call(1))\n    assert_equal(4, candidate.call(5))\n    assert_equal(24, candidate.call(8))\n    assert_equal(81, candidate.call(10))\n    assert_equal(274, candidate.call(12))\n    assert_equal(927, candidate.call(14))\n  end\nend\n"}
{"name": "HumanEval_151_double_the_difference", "language": "rb", "prompt": "# Given an array of numbers, return the sum of squares of the numbers\n# in the array that are odd. Ignore numbers that are negative or not integers.\n# >>> double_the_difference.call([1, 3, 2, 0])\n# 10\n# >>> double_the_difference.call([-1, -2, 0])\n# 0\n# >>> double_the_difference.call([9, -2])\n# 81\n# >>> double_the_difference.call([0])\n# 0\n# If the input array is empty, return 0.\ndef double_the_difference(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_151_double_the_difference.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_double_the_difference\n    candidate = method(:double_the_difference)\n    assert_equal(0, candidate.call([]))\n    assert_equal(25, candidate.call([5.0, 4.0]))\n    assert_equal(0, candidate.call([0.1, 0.2, 0.3]))\n    assert_equal(0, candidate.call([-10.0, -20.0, -30.0]))\n    assert_equal(0, candidate.call([-1.0, -2.0, 8.0]))\n    assert_equal(34, candidate.call([0.2, 3.0, 5.0]))\n    assert_equal(165, candidate.call([-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_151_double_the_difference", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_double_the_difference\n    candidate = method(:double_the_difference)\n    assert_equal(0, candidate.call([]))\n    assert_equal(25, candidate.call([5.0, 4.0]))\n    assert_equal(0, candidate.call([0.1, 0.2, 0.3]))\n    assert_equal(0, candidate.call([-10.0, -20.0, -30.0]))\n    assert_equal(0, candidate.call([-1.0, -2.0, 8.0]))\n    assert_equal(34, candidate.call([0.2, 3.0, 5.0]))\n    assert_equal(165, candidate.call([-9.0, -7.0, -5.0, -3.0, -1.0, 1.0, 3.0, 5.0, 7.0, 9.0]))\n  end\nend\n"}
{"name": "HumanEval_22_filter_integers", "language": "rb", "prompt": "# Filter given array of any rbthon values only for integers\n# >>> filter_integers.call([\"a\", 3.14, 5])\n# [5]\n# >>> filter_integers.call([1, 2, 3, \"abc\", {}, []])\n# [1, 2, 3]\ndef filter_integers(values)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_22_filter_integers.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_integers\n    candidate = method(:filter_integers)\n    assert_equal([], candidate.call([]))\n    assert_equal([4, 9], candidate.call([4, {}, [], 23.2, 9, \"adasd\"]))\n    assert_equal([3, 3, 3], candidate.call([3, \"c\", 3, 3, \"a\", \"b\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_22_filter_integers", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_integers\n    candidate = method(:filter_integers)\n    assert_equal([], candidate.call([]))\n    assert_equal([4, 9], candidate.call([4, {}, [], 23.2, 9, \"adasd\"]))\n    assert_equal([3, 3, 3], candidate.call([3, \"c\", 3, 3, \"a\", \"b\"]))\n  end\nend\n"}
{"name": "HumanEval_41_car_race_collision", "language": "rb", "prompt": "# Imagine a road that's a perfectly straight infinitely long line.\n# n cars are driving left to right;  simultaneously, a different set of n cars\n# are driving right to left.   The two sets of cars start out being very far from\n# each other.  All cars move in the same speed.  Two cars are said to collide\n# when a car that's moving left to right hits a car that's moving right to left.\n# However, the cars are infinitely sturdy and strong; as a result, they continue moving\n# in their trajectory as if they did not collide.\n# This function outputs the number of such collisions.\ndef car_race_collision(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_41_car_race_collision.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_car_race_collision\n    candidate = method(:car_race_collision)\n    assert_equal(4, candidate.call(2))\n    assert_equal(9, candidate.call(3))\n    assert_equal(16, candidate.call(4))\n    assert_equal(64, candidate.call(8))\n    assert_equal(100, candidate.call(10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_41_car_race_collision", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_car_race_collision\n    candidate = method(:car_race_collision)\n    assert_equal(4, candidate.call(2))\n    assert_equal(9, candidate.call(3))\n    assert_equal(16, candidate.call(4))\n    assert_equal(64, candidate.call(8))\n    assert_equal(100, candidate.call(10))\n  end\nend\n"}
{"name": "HumanEval_17_parse_music", "language": "rb", "prompt": "# Input to this function is a string representing musical notes in a special ASCII format.\n# Your task is to parse this string and return array of integers corresponding to how many beats does each\n# not last.\n# Here is a legend:\n# 'o' - whole note, lasts four beats\n# 'o|' - half note, lasts two beats\n# '.|' - quater note, lasts one beat\n# >>> parse_music.call(\"o o| .| o| o| .| .| .| .| o o\")\n# [4, 2, 1, 2, 2, 1, 1, 1, 1, 4, 4]\ndef parse_music(music_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_17_parse_music.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_parse_music\n    candidate = method(:parse_music)\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([4, 4, 4, 4], candidate.call(\"o o o o\"))\n    assert_equal([1, 1, 1, 1], candidate.call(\".| .| .| .|\"))\n    assert_equal([2, 2, 1, 1, 4, 4, 4, 4], candidate.call(\"o| o| .| .| o o o o\"))\n    assert_equal([2, 1, 2, 1, 4, 2, 4, 2], candidate.call(\"o| .| o| .| o o| o o|\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_17_parse_music", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_parse_music\n    candidate = method(:parse_music)\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([4, 4, 4, 4], candidate.call(\"o o o o\"))\n    assert_equal([1, 1, 1, 1], candidate.call(\".| .| .| .|\"))\n    assert_equal([2, 2, 1, 1, 4, 4, 4, 4], candidate.call(\"o| o| .| .| o o o o\"))\n    assert_equal([2, 1, 2, 1, 4, 2, 4, 2], candidate.call(\"o| .| o| .| o o| o o|\"))\n  end\nend\n"}
{"name": "HumanEval_79_decimal_to_binary", "language": "rb", "prompt": "# You will be given a number in decimal form and your task is to convert it to\n# binary format. The function should return a string, with each character representing a binary\n# number. Each character in the string will be '0' or '1'.\n# There will be an extra couple of characters 'db' at the beginning and at the end of the string.\n# The extra characters are there to help with the format.\n# Examples:\n# >>> decimal_to_binary.call(15)\n# \"db1111db\"\n# >>> decimal_to_binary.call(32)\n# \"db100000db\"\ndef decimal_to_binary(decimal)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_79_decimal_to_binary.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_decimal_to_binary\n    candidate = method(:decimal_to_binary)\n    assert_equal(\"db0db\", candidate.call(0))\n    assert_equal(\"db100000db\", candidate.call(32))\n    assert_equal(\"db1100111db\", candidate.call(103))\n    assert_equal(\"db1111db\", candidate.call(15))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_79_decimal_to_binary", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_decimal_to_binary\n    candidate = method(:decimal_to_binary)\n    assert_equal(\"db0db\", candidate.call(0))\n    assert_equal(\"db100000db\", candidate.call(32))\n    assert_equal(\"db1100111db\", candidate.call(103))\n    assert_equal(\"db1111db\", candidate.call(15))\n  end\nend\n"}
{"name": "HumanEval_14_all_prefixes", "language": "rb", "prompt": "# Return array of all prefixes from shortest to longest of the input string\n# >>> all_prefixes.call(\"abc\")\n# [\"a\", \"ab\", \"abc\"]\ndef all_prefixes(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_14_all_prefixes.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_all_prefixes\n    candidate = method(:all_prefixes)\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"], candidate.call(\"asdfgh\"))\n    assert_equal([\"W\", \"WW\", \"WWW\"], candidate.call(\"WWW\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_14_all_prefixes", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_all_prefixes\n    candidate = method(:all_prefixes)\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([\"a\", \"as\", \"asd\", \"asdf\", \"asdfg\", \"asdfgh\"], candidate.call(\"asdfgh\"))\n    assert_equal([\"W\", \"WW\", \"WWW\"], candidate.call(\"WWW\"))\n  end\nend\n"}
{"name": "HumanEval_53_add", "language": "rb", "prompt": "# Add two numbers x and y\n# >>> add.call(2, 3)\n# 5\n# >>> add.call(5, 7)\n# 12\ndef add(x, y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_53_add.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add\n    candidate = method(:add)\n    assert_equal(1, candidate.call(0, 1))\n    assert_equal(1, candidate.call(1, 0))\n    assert_equal(5, candidate.call(2, 3))\n    assert_equal(12, candidate.call(5, 7))\n    assert_equal(12, candidate.call(7, 5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_53_add", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add\n    candidate = method(:add)\n    assert_equal(1, candidate.call(0, 1))\n    assert_equal(1, candidate.call(1, 0))\n    assert_equal(5, candidate.call(2, 3))\n    assert_equal(12, candidate.call(5, 7))\n    assert_equal(12, candidate.call(7, 5))\n  end\nend\n"}
{"name": "HumanEval_159_eat", "language": "rb", "prompt": "# You're a hungry rabbit, and you already have eaten a certain number of carrots,\n# but now you need to eat more carrots to complete the day's meals.\n# you should return an array of [ total number of eaten carrots after your meals,\n# the number of carrots left after your meals ]\n# if there are not enough remaining carrots, you will eat all remaining carrots, but will still be hungry.\n# Example:\n# >>> eat.call(5, 6, 10)\n# [11, 4]\n# >>> eat.call(4, 8, 9)\n# [12, 1]\n# >>> eat.call(1, 10, 10)\n# [11, 0]\n# >>> eat.call(2, 11, 5)\n# [7, 0]\n# Variables:\n# @number : integer\n# the number of carrots that you have eaten.\n# @need : integer\n# the number of carrots that you need to eat.\n# @remaining : integer\n# the number of remaining carrots thet exist in stock\n# Constrain:\n# * 0 <= number <= 1000\n# * 0 <= need <= 1000\n# * 0 <= remaining <= 1000\n# Have fun :)\ndef eat(number, need, remaining)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_159_eat.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_eat\n    candidate = method(:eat)\n    assert_equal([11, 4], candidate.call(5, 6, 10))\n    assert_equal([12, 1], candidate.call(4, 8, 9))\n    assert_equal([11, 0], candidate.call(1, 10, 10))\n    assert_equal([7, 0], candidate.call(2, 11, 5))\n    assert_equal([9, 2], candidate.call(4, 5, 7))\n    assert_equal([5, 0], candidate.call(4, 5, 1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_159_eat", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_eat\n    candidate = method(:eat)\n    assert_equal([11, 4], candidate.call(5, 6, 10))\n    assert_equal([12, 1], candidate.call(4, 8, 9))\n    assert_equal([11, 0], candidate.call(1, 10, 10))\n    assert_equal([7, 0], candidate.call(2, 11, 5))\n    assert_equal([9, 2], candidate.call(4, 5, 7))\n    assert_equal([5, 0], candidate.call(4, 5, 1))\n  end\nend\n"}
{"name": "HumanEval_115_max_fill", "language": "rb", "prompt": "# You are given a rectangular grid of wells. Each row represents a single well,\n# and each 1 in a row represents a single unit of water.\n# Each well has a corresponding bucket that can be used to extract water from it, \n# and all buckets have the same capacity.\n# Your task is to use the buckets to empty the wells.\n# Output the number of times you need to lower the buckets.\n# Example 1:\n# >>> max_fill.call([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1)\n# 6\n# Example 2:\n# >>> max_fill.call([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2)\n# 5\n# Example 3:\n# >>> max_fill.call([[0, 0, 0], [0, 0, 0]], 5)\n# 0\n# Constraints:\n# * all wells have the same length\n# * 1 <= grid.length <= 10^2\n# * 1 <= grid[:,1].length <= 10^2\n# * grid[i][j] -> 0 | 1\n# * 1 <= capacity <= 10\ndef max_fill(grid, capacity)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_115_max_fill.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_max_fill\n    candidate = method(:max_fill)\n    assert_equal(6, candidate.call([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1))\n    assert_equal(5, candidate.call([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2))\n    assert_equal(0, candidate.call([[0, 0, 0], [0, 0, 0]], 5))\n    assert_equal(4, candidate.call([[1, 1, 1, 1], [1, 1, 1, 1]], 2))\n    assert_equal(2, candidate.call([[1, 1, 1, 1], [1, 1, 1, 1]], 9))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_115_max_fill", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_max_fill\n    candidate = method(:max_fill)\n    assert_equal(6, candidate.call([[0, 0, 1, 0], [0, 1, 0, 0], [1, 1, 1, 1]], 1))\n    assert_equal(5, candidate.call([[0, 0, 1, 1], [0, 0, 0, 0], [1, 1, 1, 1], [0, 1, 1, 1]], 2))\n    assert_equal(0, candidate.call([[0, 0, 0], [0, 0, 0]], 5))\n    assert_equal(4, candidate.call([[1, 1, 1, 1], [1, 1, 1, 1]], 2))\n    assert_equal(2, candidate.call([[1, 1, 1, 1], [1, 1, 1, 1]], 9))\n  end\nend\n"}
{"name": "HumanEval_160_do_algebra", "language": "rb", "prompt": "# Given two arrays operator, and operand. The first array has basic algebra operations, and \n# the second array is an array of integers. Use the two given arrays to build the algebric \n# expression and return the evaluation of this expression.\n# The basic algebra operations:\n# Addition ( + ) \n# Subtraction ( - ) \n# Multiplication ( * ) \n# Floor division ( // ) \n# Exponentiation ( ** ) \n# Example:\n# operator['+', '*', '-']\n# array = [2, 3, 4, 5]\n# result = 2 + 3 * 4 - 5\n# => result = 9\n# Note:\n# The length of operator array is equal to the length of operand array minus one.\n# Operand is an array of of non-negative integers.\n# Operator array has at least one operator, and operand array has at least two operands.\ndef do_algebra(operator, operand)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_160_do_algebra.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_do_algebra\n    candidate = method(:do_algebra)\n    assert_equal(37, candidate.call([\"**\", \"*\", \"+\"], [2, 3, 4, 5]))\n    assert_equal(9, candidate.call([\"+\", \"*\", \"-\"], [2, 3, 4, 5]))\n    assert_equal(8, candidate.call([\"//\", \"*\"], [7, 3, 4]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_160_do_algebra", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_do_algebra\n    candidate = method(:do_algebra)\n    assert_equal(37, candidate.call([\"**\", \"*\", \"+\"], [2, 3, 4, 5]))\n    assert_equal(9, candidate.call([\"+\", \"*\", \"-\"], [2, 3, 4, 5]))\n    assert_equal(8, candidate.call([\"//\", \"*\"], [7, 3, 4]))\n  end\nend\n"}
{"name": "HumanEval_27_flip_case", "language": "rb", "prompt": "# For a given string, flip lowercase characters to uppercase and uppercase to lowercase.\n# >>> flip_case.call(\"Hello\")\n# \"hELLO\"\ndef flip_case(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_27_flip_case.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_flip_case\n    candidate = method(:flip_case)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"hELLO!\", candidate.call(\"Hello!\"))\n    assert_equal(\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\", candidate.call(\"These violent delights have violent ends\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_27_flip_case", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_flip_case\n    candidate = method(:flip_case)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"hELLO!\", candidate.call(\"Hello!\"))\n    assert_equal(\"tHESE VIOLENT DELIGHTS HAVE VIOLENT ENDS\", candidate.call(\"These violent delights have violent ends\"))\n  end\nend\n"}
{"name": "HumanEval_105_by_length", "language": "rb", "prompt": "# Given an array of integers, sort the integers that are between 1 and 9 inclusive,\n# reverse the resulting array, and then replace each digit by its corresponding name from\n# \"One\", \"Two\", \"Three\", \"Four\", \"Five\", \"Six\", \"Seven\", \"Eight\", \"Nine\".\n# For example:\n# >>> by_length.call([2, 1, 1, 4, 5, 8, 2, 3])\n# [\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"]\n# If the array is empty, return an empty array:\n# >>> by_length.call([])\n# []\n# If the array has any strange number ignore it:\n# >>> by_length.call([1, -1, 55])\n# [\"One\"]\ndef by_length(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_105_by_length.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_by_length\n    candidate = method(:by_length)\n    assert_equal([\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"], candidate.call([2, 1, 1, 4, 5, 8, 2, 3]))\n    assert_equal([], candidate.call([]))\n    assert_equal([\"One\"], candidate.call([1, -1, 55]))\n    assert_equal([\"Three\", \"Two\", \"One\"], candidate.call([1, -1, 3, 2]))\n    assert_equal([\"Nine\", \"Eight\", \"Four\"], candidate.call([9, 4, 8]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_105_by_length", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_by_length\n    candidate = method(:by_length)\n    assert_equal([\"Eight\", \"Five\", \"Four\", \"Three\", \"Two\", \"Two\", \"One\", \"One\"], candidate.call([2, 1, 1, 4, 5, 8, 2, 3]))\n    assert_equal([], candidate.call([]))\n    assert_equal([\"One\"], candidate.call([1, -1, 55]))\n    assert_equal([\"Three\", \"Two\", \"One\"], candidate.call([1, -1, 3, 2]))\n    assert_equal([\"Nine\", \"Eight\", \"Four\"], candidate.call([9, 4, 8]))\n  end\nend\n"}
{"name": "HumanEval_25_factorize", "language": "rb", "prompt": "# Return array of prime factors of given integer in the order from smallest to largest.\n# Each of the factors should be arrayed number of times corresponding to how many times it appeares in factorization.\n# Input number should be equal to the product of all factors\n# >>> factorize.call(8)\n# [2, 2, 2]\n# >>> factorize.call(25)\n# [5, 5]\n# >>> factorize.call(70)\n# [2, 5, 7]\ndef factorize(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_25_factorize.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_factorize\n    candidate = method(:factorize)\n    assert_equal([2], candidate.call(2))\n    assert_equal([2, 2], candidate.call(4))\n    assert_equal([2, 2, 2], candidate.call(8))\n    assert_equal([3, 19], candidate.call(57))\n    assert_equal([3, 3, 19, 19], candidate.call(3249))\n    assert_equal([3, 3, 3, 19, 19, 19], candidate.call(185193))\n    assert_equal([3, 19, 19, 19], candidate.call(20577))\n    assert_equal([2, 3, 3], candidate.call(18))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_25_factorize", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_factorize\n    candidate = method(:factorize)\n    assert_equal([2], candidate.call(2))\n    assert_equal([2, 2], candidate.call(4))\n    assert_equal([2, 2, 2], candidate.call(8))\n    assert_equal([3, 19], candidate.call(57))\n    assert_equal([3, 3, 19, 19], candidate.call(3249))\n    assert_equal([3, 3, 3, 19, 19, 19], candidate.call(185193))\n    assert_equal([3, 19, 19, 19], candidate.call(20577))\n    assert_equal([2, 3, 3], candidate.call(18))\n  end\nend\n"}
{"name": "HumanEval_96_count_up_to", "language": "rb", "prompt": "# Implement a function that takes an non-negative integer and returns an array of the first n\n# integers that are prime numbers and less than n.\n# for example:\n# >>> count_up_to.call(5)\n# [2, 3]\n# >>> count_up_to.call(11)\n# [2, 3, 5, 7]\n# >>> count_up_to.call(0)\n# []\n# >>> count_up_to.call(20)\n# [2, 3, 5, 7, 11, 13, 17, 19]\n# >>> count_up_to.call(1)\n# []\n# >>> count_up_to.call(18)\n# [2, 3, 5, 7, 11, 13, 17]\ndef count_up_to(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_96_count_up_to.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_up_to\n    candidate = method(:count_up_to)\n    assert_equal([2, 3], candidate.call(5))\n    assert_equal([2, 3, 5], candidate.call(6))\n    assert_equal([2, 3, 5], candidate.call(7))\n    assert_equal([2, 3, 5, 7], candidate.call(10))\n    assert_equal([], candidate.call(0))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19], candidate.call(22))\n    assert_equal([], candidate.call(1))\n    assert_equal([2, 3, 5, 7, 11, 13, 17], candidate.call(18))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43], candidate.call(47))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97], candidate.call(101))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_96_count_up_to", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_up_to\n    candidate = method(:count_up_to)\n    assert_equal([2, 3], candidate.call(5))\n    assert_equal([2, 3, 5], candidate.call(6))\n    assert_equal([2, 3, 5], candidate.call(7))\n    assert_equal([2, 3, 5, 7], candidate.call(10))\n    assert_equal([], candidate.call(0))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19], candidate.call(22))\n    assert_equal([], candidate.call(1))\n    assert_equal([2, 3, 5, 7, 11, 13, 17], candidate.call(18))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43], candidate.call(47))\n    assert_equal([2, 3, 5, 7, 11, 13, 17, 19, 23, 29, 31, 37, 41, 43, 47, 53, 59, 61, 67, 71, 73, 79, 83, 89, 97], candidate.call(101))\n  end\nend\n"}
{"name": "HumanEval_34_unique", "language": "rb", "prompt": "# Return sorted unique elements in an array\n# >>> unique.call([5, 3, 5, 2, 3, 3, 9, 0, 123])\n# [0, 2, 3, 5, 9, 123]\ndef unique(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_34_unique.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_unique\n    candidate = method(:unique)\n    assert_equal([0, 2, 3, 5, 9, 123], candidate.call([5, 3, 5, 2, 3, 3, 9, 0, 123]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_34_unique", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_unique\n    candidate = method(:unique)\n    assert_equal([0, 2, 3, 5, 9, 123], candidate.call([5, 3, 5, 2, 3, 3, 9, 0, 123]))\n  end\nend\n"}
{"name": "HumanEval_74_total_match", "language": "rb", "prompt": "# Write a function that accepts two arrays of strings and returns the array that has \n# total number of chars in the all strings of the array less than the other array.\n# if the two arrays have the same number of chars, return the first array.\n# Examples\n# >>> total_match.call([], [])\n# []\n# >>> total_match.call([\"hi\", \"admin\"], [\"hI\", \"Hi\"])\n# [\"hI\", \"Hi\"]\n# >>> total_match.call([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"])\n# [\"hi\", \"admin\"]\n# >>> total_match.call([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"])\n# [\"hI\", \"hi\", \"hi\"]\n# >>> total_match.call([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"])\n# [\"4\"]\ndef total_match(lst1, lst2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_74_total_match.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_total_match\n    candidate = method(:total_match)\n    assert_equal([], candidate.call([], []))\n    assert_equal([\"hi\", \"hi\"], candidate.call([\"hi\", \"admin\"], [\"hi\", \"hi\"]))\n    assert_equal([\"hi\", \"admin\"], candidate.call([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"]))\n    assert_equal([\"4\"], candidate.call([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"]))\n    assert_equal([\"hI\", \"Hi\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"Hi\"]))\n    assert_equal([\"hI\", \"hi\", \"hi\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"]))\n    assert_equal([\"hi\", \"admin\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hii\"]))\n    assert_equal([], candidate.call([], [\"this\"]))\n    assert_equal([], candidate.call([\"this\"], []))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_74_total_match", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_total_match\n    candidate = method(:total_match)\n    assert_equal([], candidate.call([], []))\n    assert_equal([\"hi\", \"hi\"], candidate.call([\"hi\", \"admin\"], [\"hi\", \"hi\"]))\n    assert_equal([\"hi\", \"admin\"], candidate.call([\"hi\", \"admin\"], [\"hi\", \"hi\", \"admin\", \"project\"]))\n    assert_equal([\"4\"], candidate.call([\"4\"], [\"1\", \"2\", \"3\", \"4\", \"5\"]))\n    assert_equal([\"hI\", \"Hi\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"Hi\"]))\n    assert_equal([\"hI\", \"hi\", \"hi\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hi\"]))\n    assert_equal([\"hi\", \"admin\"], candidate.call([\"hi\", \"admin\"], [\"hI\", \"hi\", \"hii\"]))\n    assert_equal([], candidate.call([], [\"this\"]))\n    assert_equal([], candidate.call([\"this\"], []))\n  end\nend\n"}
{"name": "HumanEval_35_max_element", "language": "rb", "prompt": "# Return maximum element in the array.\n# >>> max_element.call([1, 2, 3])\n# 3\n# >>> max_element.call([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n# 123\ndef max_element(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_35_max_element.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_max_element\n    candidate = method(:max_element)\n    assert_equal(3, candidate.call([1, 2, 3]))\n    assert_equal(124, candidate.call([5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_35_max_element", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_max_element\n    candidate = method(:max_element)\n    assert_equal(3, candidate.call([1, 2, 3]))\n    assert_equal(124, candidate.call([5, 3, -5, 2, -3, 3, 9, 0, 124, 1, -10]))\n  end\nend\n"}
{"name": "HumanEval_132_is_nested", "language": "rb", "prompt": "# Create a function that takes a string as input which contains only square brackets.\n# The function should return true if and only if there is a valid subsequence of brackets \n# where at least one bracket in the subsequence is nested.\n# >>> is_nested.call(\"[[]]\")\n# true\n# >>> is_nested.call(\"[]]]]]]][[[[[]\")\n# false\n# >>> is_nested.call(\"[][]\")\n# false\n# >>> is_nested.call(\"[]\")\n# false\n# >>> is_nested.call(\"[[][]]\")\n# true\n# >>> is_nested.call(\"[[]][[\")\n# true\ndef is_nested(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_132_is_nested.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_nested\n    candidate = method(:is_nested)\n    assert_equal(true, candidate.call(\"[[]]\"))\n    assert_equal(false, candidate.call(\"[]]]]]]][[[[[]\"))\n    assert_equal(false, candidate.call(\"[][]\"))\n    assert_equal(false, candidate.call(\"[]\"))\n    assert_equal(true, candidate.call(\"[[[[]]]]\"))\n    assert_equal(false, candidate.call(\"[]]]]]]]]]]\"))\n    assert_equal(true, candidate.call(\"[][][[]]\"))\n    assert_equal(false, candidate.call(\"[[]\"))\n    assert_equal(false, candidate.call(\"[]]\"))\n    assert_equal(true, candidate.call(\"[[]][[\"))\n    assert_equal(true, candidate.call(\"[[][]]\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"[[[[[[[[\"))\n    assert_equal(false, candidate.call(\"]]]]]]]]\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_132_is_nested", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_nested\n    candidate = method(:is_nested)\n    assert_equal(true, candidate.call(\"[[]]\"))\n    assert_equal(false, candidate.call(\"[]]]]]]][[[[[]\"))\n    assert_equal(false, candidate.call(\"[][]\"))\n    assert_equal(false, candidate.call(\"[]\"))\n    assert_equal(true, candidate.call(\"[[[[]]]]\"))\n    assert_equal(false, candidate.call(\"[]]]]]]]]]]\"))\n    assert_equal(true, candidate.call(\"[][][[]]\"))\n    assert_equal(false, candidate.call(\"[[]\"))\n    assert_equal(false, candidate.call(\"[]]\"))\n    assert_equal(true, candidate.call(\"[[]][[\"))\n    assert_equal(true, candidate.call(\"[[][]]\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"[[[[[[[[\"))\n    assert_equal(false, candidate.call(\"]]]]]]]]\"))\n  end\nend\n"}
{"name": "HumanEval_103_rounded_avg", "language": "rb", "prompt": "# You are given two positive integers n and m, and your task is to compute the\n# average of the integers from n through m (including n and m). \n# Round the answer to the nearest integer and convert that to binary.\n# If n is greater than m, return -1.\n# Example:\n# >>> rounded_avg.call(1, 5)\n# \"0b11\"\n# >>> rounded_avg.call(7, 5)\n# -1\n# >>> rounded_avg.call(10, 20)\n# \"0b1111\"\n# >>> rounded_avg.call(20, 33)\n# \"0b11010\"\ndef rounded_avg(n, m)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_103_rounded_avg.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rounded_avg\n    candidate = method(:rounded_avg)\n    assert_equal(\"0b11\", candidate.call(1, 5))\n    assert_equal(\"0b1010\", candidate.call(7, 13))\n    assert_equal(\"0b1111001010\", candidate.call(964, 977))\n    assert_equal(\"0b1111100100\", candidate.call(996, 997))\n    assert_equal(\"0b1011000010\", candidate.call(560, 851))\n    assert_equal(\"0b101101110\", candidate.call(185, 546))\n    assert_equal(\"0b110101101\", candidate.call(362, 496))\n    assert_equal(\"0b1001110010\", candidate.call(350, 902))\n    assert_equal(\"0b11010111\", candidate.call(197, 233))\n    assert_equal(-1, candidate.call(7, 5))\n    assert_equal(-1, candidate.call(5, 1))\n    assert_equal(\"0b101\", candidate.call(5, 5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_103_rounded_avg", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rounded_avg\n    candidate = method(:rounded_avg)\n    assert_equal(\"0b11\", candidate.call(1, 5))\n    assert_equal(\"0b1010\", candidate.call(7, 13))\n    assert_equal(\"0b1111001010\", candidate.call(964, 977))\n    assert_equal(\"0b1111100100\", candidate.call(996, 997))\n    assert_equal(\"0b1011000010\", candidate.call(560, 851))\n    assert_equal(\"0b101101110\", candidate.call(185, 546))\n    assert_equal(\"0b110101101\", candidate.call(362, 496))\n    assert_equal(\"0b1001110010\", candidate.call(350, 902))\n    assert_equal(\"0b11010111\", candidate.call(197, 233))\n    assert_equal(-1, candidate.call(7, 5))\n    assert_equal(-1, candidate.call(5, 1))\n    assert_equal(\"0b101\", candidate.call(5, 5))\n  end\nend\n"}
{"name": "HumanEval_113_odd_count", "language": "rb", "prompt": "# Given an array of strings, where each string consists of only digits, return an array.\n# Each element i of the output should be \"the number of odd elements in the\n# string i of the input.\" where all the i's should be replaced by the number\n# of odd digits in the i'th string of the input.\n# >>> odd_count.call([\"1234567\"])\n# [\"the number of odd elements 4n the str4ng 4 of the 4nput.\"]\n# >>> odd_count.call([\"3\", \"11111111\"])\n# [\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"]\ndef odd_count(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_113_odd_count.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_odd_count\n    candidate = method(:odd_count)\n    assert_equal([\"the number of odd elements 4n the str4ng 4 of the 4nput.\"], candidate.call([\"1234567\"]))\n    assert_equal([\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"], candidate.call([\"3\", \"11111111\"]))\n    assert_equal([\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"], candidate.call([\"271\", \"137\", \"314\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_113_odd_count", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_odd_count\n    candidate = method(:odd_count)\n    assert_equal([\"the number of odd elements 4n the str4ng 4 of the 4nput.\"], candidate.call([\"1234567\"]))\n    assert_equal([\"the number of odd elements 1n the str1ng 1 of the 1nput.\", \"the number of odd elements 8n the str8ng 8 of the 8nput.\"], candidate.call([\"3\", \"11111111\"]))\n    assert_equal([\"the number of odd elements 2n the str2ng 2 of the 2nput.\", \"the number of odd elements 3n the str3ng 3 of the 3nput.\", \"the number of odd elements 2n the str2ng 2 of the 2nput.\"], candidate.call([\"271\", \"137\", \"314\"]))\n  end\nend\n"}
{"name": "HumanEval_109_move_one_ball", "language": "rb", "prompt": "# We have an array 'arr' of N integers arr[1], arr[2], ..., arr[N].The\n# numbers in the array will be randomly ordered. Your task is to determine if\n# it is possible to get an array sorted in non-decreasing order by performing \n# the following operation on the given array:\n# You are allowed to perform right shift operation any number of times.\n# One right shift operation means shifting all elements of the array by one\n# position in the right direction. The last element of the array will be moved to\n# the starting position in the array i.e. 0th index. \n# If it is possible to obtain the sorted array by performing the above operation\n# then return true else return false.\n# If the given array is empty then return true.\n# Note: The given array is guaranteed to have unique elements.\n# For Example:\n# >>> move_one_ball.call([3, 4, 5, 1, 2])\n# true\n# Explanation: By performin 2 right shift operations, non-decreasing order can\n# be achieved for the given array.\n# >>> move_one_ball.call([3, 5, 4, 1, 2])\n# false\n# Explanation:It is not possible to get non-decreasing order for the given\n# array by performing any number of right shift operations.\ndef move_one_ball(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_109_move_one_ball.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_move_one_ball\n    candidate = method(:move_one_ball)\n    assert_equal(true, candidate.call([3, 4, 5, 1, 2]))\n    assert_equal(true, candidate.call([3, 5, 10, 1, 2]))\n    assert_equal(false, candidate.call([4, 3, 1, 2]))\n    assert_equal(false, candidate.call([3, 5, 4, 1, 2]))\n    assert_equal(true, candidate.call([]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_109_move_one_ball", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_move_one_ball\n    candidate = method(:move_one_ball)\n    assert_equal(true, candidate.call([3, 4, 5, 1, 2]))\n    assert_equal(true, candidate.call([3, 5, 10, 1, 2]))\n    assert_equal(false, candidate.call([4, 3, 1, 2]))\n    assert_equal(false, candidate.call([3, 5, 4, 1, 2]))\n    assert_equal(true, candidate.call([]))\n  end\nend\n"}
{"name": "HumanEval_107_even_odd_palindrome", "language": "rb", "prompt": "# Given a positive integer n, return an array that has the number of even and odd\n# integer palindromes that fall within the range(1, n), inclusive.\n# Example 1:\n# >>> even_odd_palindrome.call(3)\n# [1, 2]\n# Explanation:\n# Integer palindrome are 1, 2, 3. one of them is even, and two of them are odd.\n# Example 2:\n# >>> even_odd_palindrome.call(12)\n# [4, 6]\n# Explanation:\n# Integer palindrome are 1, 2, 3, 4, 5, 6, 7, 8, 9, 11. four of them are even, and 6 of them are odd.\n# Note:\n# 1. 1 <= n <= 10^3\n# 2. returned array has the number of even and odd integer palindromes respectively.\ndef even_odd_palindrome(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_107_even_odd_palindrome.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_even_odd_palindrome\n    candidate = method(:even_odd_palindrome)\n    assert_equal([8, 13], candidate.call(123))\n    assert_equal([4, 6], candidate.call(12))\n    assert_equal([1, 2], candidate.call(3))\n    assert_equal([6, 8], candidate.call(63))\n    assert_equal([5, 6], candidate.call(25))\n    assert_equal([4, 6], candidate.call(19))\n    assert_equal([4, 5], candidate.call(9))\n    assert_equal([0, 1], candidate.call(1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_107_even_odd_palindrome", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_even_odd_palindrome\n    candidate = method(:even_odd_palindrome)\n    assert_equal([8, 13], candidate.call(123))\n    assert_equal([4, 6], candidate.call(12))\n    assert_equal([1, 2], candidate.call(3))\n    assert_equal([6, 8], candidate.call(63))\n    assert_equal([5, 6], candidate.call(25))\n    assert_equal([4, 6], candidate.call(19))\n    assert_equal([4, 5], candidate.call(9))\n    assert_equal([0, 1], candidate.call(1))\n  end\nend\n"}
{"name": "HumanEval_138_is_equal_to_sum_even", "language": "rb", "prompt": "# Evaluate whether the given number n can be written as the sum of exactly 4 positive even numbers\n# Example\n# >>> is_equal_to_sum_even.call(4)\n# false\n# >>> is_equal_to_sum_even.call(6)\n# false\n# >>> is_equal_to_sum_even.call(8)\n# true\ndef is_equal_to_sum_even(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_138_is_equal_to_sum_even.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_equal_to_sum_even\n    candidate = method(:is_equal_to_sum_even)\n    assert_equal(false, candidate.call(4))\n    assert_equal(false, candidate.call(6))\n    assert_equal(true, candidate.call(8))\n    assert_equal(true, candidate.call(10))\n    assert_equal(false, candidate.call(11))\n    assert_equal(true, candidate.call(12))\n    assert_equal(false, candidate.call(13))\n    assert_equal(true, candidate.call(16))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_138_is_equal_to_sum_even", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_equal_to_sum_even\n    candidate = method(:is_equal_to_sum_even)\n    assert_equal(false, candidate.call(4))\n    assert_equal(false, candidate.call(6))\n    assert_equal(true, candidate.call(8))\n    assert_equal(true, candidate.call(10))\n    assert_equal(false, candidate.call(11))\n    assert_equal(true, candidate.call(12))\n    assert_equal(false, candidate.call(13))\n    assert_equal(true, candidate.call(16))\n  end\nend\n"}
{"name": "HumanEval_62_derivative", "language": "rb", "prompt": "# xs represent coefficients of a polynomial.\n# xs[0] + xs[1] * x + xs[2] * x^2 + ....\n# Return derivative of this polynomial in the same form.\n# >>> derivative.call([3, 1, 2, 4, 5])\n# [1, 4, 12, 20]\n# >>> derivative.call([1, 2, 3])\n# [2, 6]\ndef derivative(xs)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_62_derivative.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_derivative\n    candidate = method(:derivative)\n    assert_equal([1, 4, 12, 20], candidate.call([3, 1, 2, 4, 5]))\n    assert_equal([2, 6], candidate.call([1, 2, 3]))\n    assert_equal([2, 2], candidate.call([3, 2, 1]))\n    assert_equal([2, 2, 0, 16], candidate.call([3, 2, 1, 0, 4]))\n    assert_equal([], candidate.call([1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_62_derivative", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_derivative\n    candidate = method(:derivative)\n    assert_equal([1, 4, 12, 20], candidate.call([3, 1, 2, 4, 5]))\n    assert_equal([2, 6], candidate.call([1, 2, 3]))\n    assert_equal([2, 2], candidate.call([3, 2, 1]))\n    assert_equal([2, 2, 0, 16], candidate.call([3, 2, 1, 0, 4]))\n    assert_equal([], candidate.call([1]))\n  end\nend\n"}
{"name": "HumanEval_126_is_sorted", "language": "rb", "prompt": "# Given an array of numbers, return whether or not they are sorted\n# in ascending order. If array has more than 1 duplicate of the same\n# number, return false. Assume no negative numbers and only integers.\n# Examples\n# >>> is_sorted.call([5])\n# true\n# >>> is_sorted.call([1, 2, 3, 4, 5])\n# true\n# >>> is_sorted.call([1, 3, 2, 4, 5])\n# false\n# >>> is_sorted.call([1, 2, 3, 4, 5, 6])\n# true\n# >>> is_sorted.call([1, 2, 3, 4, 5, 6, 7])\n# true\n# >>> is_sorted.call([1, 3, 2, 4, 5, 6, 7])\n# false\n# >>> is_sorted.call([1, 2, 2, 3, 3, 4])\n# true\n# >>> is_sorted.call([1, 2, 2, 2, 3, 4])\n# false\ndef is_sorted(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_126_is_sorted.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_sorted\n    candidate = method(:is_sorted)\n    assert_equal(true, candidate.call([5]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5]))\n    assert_equal(false, candidate.call([1, 3, 2, 4, 5]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 6]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 6, 7]))\n    assert_equal(false, candidate.call([1, 3, 2, 4, 5, 6, 7]))\n    assert_equal(true, candidate.call([]))\n    assert_equal(true, candidate.call([1]))\n    assert_equal(false, candidate.call([3, 2, 1]))\n    assert_equal(false, candidate.call([1, 2, 2, 2, 3, 4]))\n    assert_equal(false, candidate.call([1, 2, 3, 3, 3, 4]))\n    assert_equal(true, candidate.call([1, 2, 2, 3, 3, 4]))\n    assert_equal(true, candidate.call([1, 2, 3, 4]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_126_is_sorted", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_sorted\n    candidate = method(:is_sorted)\n    assert_equal(true, candidate.call([5]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5]))\n    assert_equal(false, candidate.call([1, 3, 2, 4, 5]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 6]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 6, 7]))\n    assert_equal(false, candidate.call([1, 3, 2, 4, 5, 6, 7]))\n    assert_equal(true, candidate.call([]))\n    assert_equal(true, candidate.call([1]))\n    assert_equal(false, candidate.call([3, 2, 1]))\n    assert_equal(false, candidate.call([1, 2, 2, 2, 3, 4]))\n    assert_equal(false, candidate.call([1, 2, 3, 3, 3, 4]))\n    assert_equal(true, candidate.call([1, 2, 2, 3, 3, 4]))\n    assert_equal(true, candidate.call([1, 2, 3, 4]))\n  end\nend\n"}
{"name": "HumanEval_161_solve", "language": "rb", "prompt": "# You are given a string s.\n# if s[i] is a letter, reverse its case from lower to upper or vise versa, \n# otherwise keep it as it is.\n# If the string contains no letters, reverse the string.\n# The function should return the resulted string.\n# Examples\n# >>> solve.call(\"1234\")\n# \"4321\"\n# >>> solve.call(\"ab\")\n# \"AB\"\n# >>> solve.call(\"#a@C\")\n# \"#A@c\"\ndef solve(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_161_solve.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solve\n    candidate = method(:solve)\n    assert_equal(\"aSdF\", candidate.call(\"AsDf\"))\n    assert_equal(\"4321\", candidate.call(\"1234\"))\n    assert_equal(\"AB\", candidate.call(\"ab\"))\n    assert_equal(\"#A@c\", candidate.call(\"#a@C\"))\n    assert_equal(\"#aSDFw^45\", candidate.call(\"#AsdfW^45\"))\n    assert_equal(\"2@6#\", candidate.call(\"#6@2\"))\n    assert_equal(\"#$A^d\", candidate.call(\"#$a^D\"))\n    assert_equal(\"#CCC\", candidate.call(\"#ccc\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_161_solve", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solve\n    candidate = method(:solve)\n    assert_equal(\"aSdF\", candidate.call(\"AsDf\"))\n    assert_equal(\"4321\", candidate.call(\"1234\"))\n    assert_equal(\"AB\", candidate.call(\"ab\"))\n    assert_equal(\"#A@c\", candidate.call(\"#a@C\"))\n    assert_equal(\"#aSDFw^45\", candidate.call(\"#AsdfW^45\"))\n    assert_equal(\"2@6#\", candidate.call(\"#6@2\"))\n    assert_equal(\"#$A^d\", candidate.call(\"#$a^D\"))\n    assert_equal(\"#CCC\", candidate.call(\"#ccc\"))\n  end\nend\n"}
{"name": "HumanEval_130_tri", "language": "rb", "prompt": "# Everyone knows Fibonacci sequence, it was studied deeply by mathematicians in \n# the last couple centuries. However, what people don't know is Tribonacci sequence.\n# Tribonacci sequence is defined by the recurrence:\n# tri(1) = 3\n# tri(n) = 1 + n / 2, if n is even.\n# tri(n) =  tri(n - 1) + tri(n - 2) + tri(n + 1), if n is odd.\n# For example:\n# tri(2) = 1 + (2 / 2) = 2\n# tri(4) = 3\n# tri(3) = tri(2) + tri(1) + tri(4)\n# = 2 + 3 + 3 = 8 \n# You are given a non-negative integer number n, you have to a return an array of the \n# first n + 1 numbers of the Tribonacci sequence.\n# Examples:\n# >>> tri.call(3)\n# [1, 3, 2, 8]\ndef tri(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_130_tri.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_tri\n    candidate = method(:tri)\n    assert_equal([1, 3, 2, 8], candidate.call(3))\n    assert_equal([1, 3, 2, 8, 3], candidate.call(4))\n    assert_equal([1, 3, 2, 8, 3, 15], candidate.call(5))\n    assert_equal([1, 3, 2, 8, 3, 15, 4], candidate.call(6))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24], candidate.call(7))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5], candidate.call(8))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5, 35], candidate.call(9))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11], candidate.call(20))\n    assert_equal([1], candidate.call(0))\n    assert_equal([1, 3], candidate.call(1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_130_tri", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_tri\n    candidate = method(:tri)\n    assert_equal([1, 3, 2, 8], candidate.call(3))\n    assert_equal([1, 3, 2, 8, 3], candidate.call(4))\n    assert_equal([1, 3, 2, 8, 3, 15], candidate.call(5))\n    assert_equal([1, 3, 2, 8, 3, 15, 4], candidate.call(6))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24], candidate.call(7))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5], candidate.call(8))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5, 35], candidate.call(9))\n    assert_equal([1, 3, 2, 8, 3, 15, 4, 24, 5, 35, 6, 48, 7, 63, 8, 80, 9, 99, 10, 120, 11], candidate.call(20))\n    assert_equal([1], candidate.call(0))\n    assert_equal([1, 3], candidate.call(1))\n  end\nend\n"}
{"name": "HumanEval_36_fizz_buzz", "language": "rb", "prompt": "# Return the number of times the digit 7 appears in integers less than n which are divisible by 11 or 13.\n# >>> fizz_buzz.call(50)\n# 0\n# >>> fizz_buzz.call(78)\n# 2\n# >>> fizz_buzz.call(79)\n# 3\ndef fizz_buzz(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_36_fizz_buzz.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fizz_buzz\n    candidate = method(:fizz_buzz)\n    assert_equal(0, candidate.call(50))\n    assert_equal(2, candidate.call(78))\n    assert_equal(3, candidate.call(79))\n    assert_equal(3, candidate.call(100))\n    assert_equal(6, candidate.call(200))\n    assert_equal(192, candidate.call(4000))\n    assert_equal(639, candidate.call(10000))\n    assert_equal(8026, candidate.call(100000))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_36_fizz_buzz", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fizz_buzz\n    candidate = method(:fizz_buzz)\n    assert_equal(0, candidate.call(50))\n    assert_equal(2, candidate.call(78))\n    assert_equal(3, candidate.call(79))\n    assert_equal(3, candidate.call(100))\n    assert_equal(6, candidate.call(200))\n    assert_equal(192, candidate.call(4000))\n    assert_equal(639, candidate.call(10000))\n    assert_equal(8026, candidate.call(100000))\n  end\nend\n"}
{"name": "HumanEval_29_filter_by_prefix", "language": "rb", "prompt": "# Filter an input array of strings only for ones that start with a given prefix.\n# >>> filter_by_prefix.call([], \"a\")\n# []\n# >>> filter_by_prefix.call([\"abc\", \"bcd\", \"cde\", \"array\"], \"a\")\n# [\"abc\", \"array\"]\ndef filter_by_prefix(strings, prefix)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_29_filter_by_prefix.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_by_prefix\n    candidate = method(:filter_by_prefix)\n    assert_equal([], candidate.call([], \"john\"))\n    assert_equal([\"xxx\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_29_filter_by_prefix", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_by_prefix\n    candidate = method(:filter_by_prefix)\n    assert_equal([], candidate.call([], \"john\"))\n    assert_equal([\"xxx\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\"))\n  end\nend\n"}
{"name": "HumanEval_84_solve", "language": "rb", "prompt": "# Given a positive integer N, return the total sum of its digits in binary.\n# Example\n# >>> solve.call(1000)\n# \"1\"\n# >>> solve.call(150)\n# \"110\"\n# >>> solve.call(147)\n# \"1100\"\n# Variables:\n# @N integer\n# Constraints: 0 \u2264 N \u2264 10000.\n# Output:\n# a string of binary number\ndef solve(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_84_solve.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solve\n    candidate = method(:solve)\n    assert_equal(\"1\", candidate.call(1000))\n    assert_equal(\"110\", candidate.call(150))\n    assert_equal(\"1100\", candidate.call(147))\n    assert_equal(\"1001\", candidate.call(333))\n    assert_equal(\"10010\", candidate.call(963))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_84_solve", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solve\n    candidate = method(:solve)\n    assert_equal(\"1\", candidate.call(1000))\n    assert_equal(\"110\", candidate.call(150))\n    assert_equal(\"1100\", candidate.call(147))\n    assert_equal(\"1001\", candidate.call(333))\n    assert_equal(\"10010\", candidate.call(963))\n  end\nend\n"}
{"name": "HumanEval_129_minPath", "language": "rb", "prompt": "# Given a grid with N rows and N columns (N >= 2) and a positive integer k, \n# each cell of the grid contains a value. Every integer in the range [1, N * N]\n# inclusive appears exactly once on the cells of the grid.\n# You have to find the minimum path of length k in the grid. You can start\n# from any cell, and in each step you can move to any of the neighbor cells,\n# in other words, you can go to cells which share an edge with you current\n# cell.\n# Please note that a path of length k means visiting exactly k cells (not\n# necessarily distinct).\n# You CANNOT go off the grid.\n# A path A (of length k) is considered less than a path B (of length k) if\n# after making the ordered arrays of the values on the cells that A and B go\n# through (let's call them lst_A and lst_B), lst_A is lexicographically less\n# than lst_B, in other words, there exist an integer index i (1 <= i <= k)\n# such that lst_A[i] < lst_B[i] and for any j (1 <= j < i) we have\n# lst_A[j] = lst_B[j].\n# It is guaranteed that the answer is unique.\n# Return an ordered array of the values on the cells that the minimum path go through.\n# Examples:    \n# >>> minPath.call([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3)\n# [1, 2, 1]\n# >>> minPath.call([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1)\n# [1]\ndef minPath(grid, k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_129_minPath.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_minPath\n    candidate = method(:minPath)\n    assert_equal([1, 2, 1], candidate.call([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3))\n    assert_equal([1], candidate.call([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1))\n    assert_equal([1, 2, 1, 2], candidate.call([[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12], [13, 14, 15, 16]], 4))\n    assert_equal([1, 10, 1, 10, 1, 10, 1], candidate.call([[6, 4, 13, 10], [5, 7, 12, 1], [3, 16, 11, 15], [8, 14, 9, 2]], 7))\n    assert_equal([1, 7, 1, 7, 1], candidate.call([[8, 14, 9, 2], [6, 4, 13, 15], [5, 7, 1, 12], [3, 10, 11, 16]], 5))\n    assert_equal([1, 6, 1, 6, 1, 6, 1, 6, 1], candidate.call([[11, 8, 7, 2], [5, 16, 14, 4], [9, 3, 15, 6], [12, 13, 10, 1]], 9))\n    assert_equal([1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6], candidate.call([[12, 13, 10, 1], [9, 3, 15, 6], [5, 16, 14, 4], [11, 8, 7, 2]], 12))\n    assert_equal([1, 3, 1, 3, 1, 3, 1, 3], candidate.call([[2, 7, 4], [3, 1, 5], [6, 8, 9]], 8))\n    assert_equal([1, 5, 1, 5, 1, 5, 1, 5], candidate.call([[6, 1, 5], [3, 8, 9], [2, 7, 4]], 8))\n    assert_equal([1, 2, 1, 2, 1, 2, 1, 2, 1, 2], candidate.call([[1, 2], [3, 4]], 10))\n    assert_equal([1, 3, 1, 3, 1, 3, 1, 3, 1, 3], candidate.call([[1, 3], [3, 2]], 10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_129_minPath", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_minPath\n    candidate = method(:minPath)\n    assert_equal([1, 2, 1], candidate.call([[1, 2, 3], [4, 5, 6], [7, 8, 9]], 3))\n    assert_equal([1], candidate.call([[5, 9, 3], [4, 1, 6], [7, 8, 2]], 1))\n    assert_equal([1, 2, 1, 2], candidate.call([[1, 2, 3, 4], [5, 6, 7, 8], [9, 10, 11, 12], [13, 14, 15, 16]], 4))\n    assert_equal([1, 10, 1, 10, 1, 10, 1], candidate.call([[6, 4, 13, 10], [5, 7, 12, 1], [3, 16, 11, 15], [8, 14, 9, 2]], 7))\n    assert_equal([1, 7, 1, 7, 1], candidate.call([[8, 14, 9, 2], [6, 4, 13, 15], [5, 7, 1, 12], [3, 10, 11, 16]], 5))\n    assert_equal([1, 6, 1, 6, 1, 6, 1, 6, 1], candidate.call([[11, 8, 7, 2], [5, 16, 14, 4], [9, 3, 15, 6], [12, 13, 10, 1]], 9))\n    assert_equal([1, 6, 1, 6, 1, 6, 1, 6, 1, 6, 1, 6], candidate.call([[12, 13, 10, 1], [9, 3, 15, 6], [5, 16, 14, 4], [11, 8, 7, 2]], 12))\n    assert_equal([1, 3, 1, 3, 1, 3, 1, 3], candidate.call([[2, 7, 4], [3, 1, 5], [6, 8, 9]], 8))\n    assert_equal([1, 5, 1, 5, 1, 5, 1, 5], candidate.call([[6, 1, 5], [3, 8, 9], [2, 7, 4]], 8))\n    assert_equal([1, 2, 1, 2, 1, 2, 1, 2, 1, 2], candidate.call([[1, 2], [3, 4]], 10))\n    assert_equal([1, 3, 1, 3, 1, 3, 1, 3, 1, 3], candidate.call([[1, 3], [3, 2]], 10))\n  end\nend\n"}
{"name": "HumanEval_98_count_upper", "language": "rb", "prompt": "# Given a string s, count the number of uppercase vowels in even indices.\n# For example:\n# >>> count_upper.call(\"aBCdEf\")\n# 1\n# >>> count_upper.call(\"abcdefg\")\n# 0\n# >>> count_upper.call(\"dBBE\")\n# 0\ndef count_upper(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_98_count_upper.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_upper\n    candidate = method(:count_upper)\n    assert_equal(1, candidate.call(\"aBCdEf\"))\n    assert_equal(0, candidate.call(\"abcdefg\"))\n    assert_equal(0, candidate.call(\"dBBE\"))\n    assert_equal(0, candidate.call(\"B\"))\n    assert_equal(1, candidate.call(\"U\"))\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(2, candidate.call(\"EEEE\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_98_count_upper", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_upper\n    candidate = method(:count_upper)\n    assert_equal(1, candidate.call(\"aBCdEf\"))\n    assert_equal(0, candidate.call(\"abcdefg\"))\n    assert_equal(0, candidate.call(\"dBBE\"))\n    assert_equal(0, candidate.call(\"B\"))\n    assert_equal(1, candidate.call(\"U\"))\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(2, candidate.call(\"EEEE\"))\n  end\nend\n"}
{"name": "HumanEval_120_maximum", "language": "rb", "prompt": "# Given an array arr of integers and a positive integer k, return a sorted array \n# of length k with the maximum k numbers in arr.\n# Example 1:\n# >>> maximum.call([-3, -4, 5], 3)\n# [-4, -3, 5]\n# Example 2:\n# >>> maximum.call([4, -4, 4], 2)\n# [4, 4]\n# Example 3:\n# >>> maximum.call([-3, 2, 1, 2, -1, -2, 1], 1)\n# [2]\n# Note:\n# 1. The length of the array will be in the range of [1, 1000].\n# 2. The elements in the array will be in the range of [-1000, 1000].\n# 3. 0 <= k <= len(arr)\ndef maximum(arr, k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_120_maximum.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_maximum\n    candidate = method(:maximum)\n    assert_equal([-4, -3, 5], candidate.call([-3, -4, 5], 3))\n    assert_equal([4, 4], candidate.call([4, -4, 4], 2))\n    assert_equal([2], candidate.call([-3, 2, 1, 2, -1, -2, 1], 1))\n    assert_equal([2, 20, 123], candidate.call([123, -123, 20, 0, 1, 2, -3], 3))\n    assert_equal([0, 1, 2, 20], candidate.call([-123, 20, 0, 1, 2, -3], 4))\n    assert_equal([-13, -8, 0, 0, 3, 5, 15], candidate.call([5, 15, 0, 3, -13, -8, 0], 7))\n    assert_equal([3, 5], candidate.call([-1, 0, 2, 5, 3, -10], 2))\n    assert_equal([5], candidate.call([1, 0, 5, -7], 1))\n    assert_equal([-4, 4], candidate.call([4, -4], 2))\n    assert_equal([-10, 10], candidate.call([-10, 10], 2))\n    assert_equal([], candidate.call([1, 2, 3, -23, 243, -400, 0], 0))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_120_maximum", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_maximum\n    candidate = method(:maximum)\n    assert_equal([-4, -3, 5], candidate.call([-3, -4, 5], 3))\n    assert_equal([4, 4], candidate.call([4, -4, 4], 2))\n    assert_equal([2], candidate.call([-3, 2, 1, 2, -1, -2, 1], 1))\n    assert_equal([2, 20, 123], candidate.call([123, -123, 20, 0, 1, 2, -3], 3))\n    assert_equal([0, 1, 2, 20], candidate.call([-123, 20, 0, 1, 2, -3], 4))\n    assert_equal([-13, -8, 0, 0, 3, 5, 15], candidate.call([5, 15, 0, 3, -13, -8, 0], 7))\n    assert_equal([3, 5], candidate.call([-1, 0, 2, 5, 3, -10], 2))\n    assert_equal([5], candidate.call([1, 0, 5, -7], 1))\n    assert_equal([-4, 4], candidate.call([4, -4], 2))\n    assert_equal([-10, 10], candidate.call([-10, 10], 2))\n    assert_equal([], candidate.call([1, 2, 3, -23, 243, -400, 0], 0))\n  end\nend\n"}
{"name": "HumanEval_24_largest_divisor", "language": "rb", "prompt": "# For a given number n, find the largest number that divides n evenly, smaller than n\n# >>> largest_divisor.call(15)\n# 5\ndef largest_divisor(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_24_largest_divisor.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_divisor\n    candidate = method(:largest_divisor)\n    assert_equal(1, candidate.call(3))\n    assert_equal(1, candidate.call(7))\n    assert_equal(5, candidate.call(10))\n    assert_equal(50, candidate.call(100))\n    assert_equal(7, candidate.call(49))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_24_largest_divisor", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_divisor\n    candidate = method(:largest_divisor)\n    assert_equal(1, candidate.call(3))\n    assert_equal(1, candidate.call(7))\n    assert_equal(5, candidate.call(10))\n    assert_equal(50, candidate.call(100))\n    assert_equal(7, candidate.call(49))\n  end\nend\n"}
{"name": "HumanEval_88_sort_array", "language": "rb", "prompt": "# Given an array of non-negative integers, return a corb of the given array after sorting,\n# you will sort the given array in ascending order if the sum( first index value, last index value) is odd,\n# or sort it in descending order if the sum( first index value, last index value) is even.\n# Note:\n# * don't change the given array.\n# Examples:\n# >>> sort_array.call([])\n# []\n# >>> sort_array.call([5])\n# [5]\n# >>> sort_array.call([2, 4, 3, 0, 1, 5])\n# [0, 1, 2, 3, 4, 5]\n# >>> sort_array.call([2, 4, 3, 0, 1, 5, 6])\n# [6, 5, 4, 3, 2, 1, 0]\ndef sort_array(array)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_88_sort_array.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_array\n    candidate = method(:sort_array)\n    assert_equal([], candidate.call([]))\n    assert_equal([5], candidate.call([5]))\n    assert_equal([0, 1, 2, 3, 4, 5], candidate.call([2, 4, 3, 0, 1, 5]))\n    assert_equal([6, 5, 4, 3, 2, 1, 0], candidate.call([2, 4, 3, 0, 1, 5, 6]))\n    assert_equal([1, 2], candidate.call([2, 1]))\n    assert_equal([0, 11, 15, 32, 42, 87], candidate.call([15, 42, 87, 32, 11, 0]))\n    assert_equal([23, 21, 14, 11], candidate.call([21, 14, 23, 11]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_88_sort_array", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_array\n    candidate = method(:sort_array)\n    assert_equal([], candidate.call([]))\n    assert_equal([5], candidate.call([5]))\n    assert_equal([0, 1, 2, 3, 4, 5], candidate.call([2, 4, 3, 0, 1, 5]))\n    assert_equal([6, 5, 4, 3, 2, 1, 0], candidate.call([2, 4, 3, 0, 1, 5, 6]))\n    assert_equal([1, 2], candidate.call([2, 1]))\n    assert_equal([0, 11, 15, 32, 42, 87], candidate.call([15, 42, 87, 32, 11, 0]))\n    assert_equal([23, 21, 14, 11], candidate.call([21, 14, 23, 11]))\n  end\nend\n"}
{"name": "HumanEval_106_f", "language": "rb", "prompt": "# Implement the function f that takes n as a parameter,\n# and returns an array of size n, such that the value of the element at index i is the factorial of i if i is even\n# or the sum of numbers from 1 to i otherwise.\n# i starts from 1.\n# the factorial of i is the multiplication of the numbers from 1 to i (1 * 2 * ... * i).\n# Example:\n# >>> f.call(5)\n# [1, 2, 6, 24, 15]\ndef f(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_106_f.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_f\n    candidate = method(:f)\n    assert_equal([1, 2, 6, 24, 15], candidate.call(5))\n    assert_equal([1, 2, 6, 24, 15, 720, 28], candidate.call(7))\n    assert_equal([1], candidate.call(1))\n    assert_equal([1, 2, 6], candidate.call(3))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_106_f", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_f\n    candidate = method(:f)\n    assert_equal([1, 2, 6, 24, 15], candidate.call(5))\n    assert_equal([1, 2, 6, 24, 15, 720, 28], candidate.call(7))\n    assert_equal([1], candidate.call(1))\n    assert_equal([1, 2, 6], candidate.call(3))\n  end\nend\n"}
{"name": "HumanEval_77_iscube", "language": "rb", "prompt": "# Write a function that takes an integer a and returns true \n# if this ingeger is a cube of some integer number.\n# Note: you may assume the input is always valid.\n# Examples:\n# >>> iscube.call(1)\n# true\n# >>> iscube.call(2)\n# false\n# >>> iscube.call(-1)\n# true\n# >>> iscube.call(64)\n# true\n# >>> iscube.call(0)\n# true\n# >>> iscube.call(180)\n# false\ndef iscube(a)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_77_iscube.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_iscube\n    candidate = method(:iscube)\n    assert_equal(true, candidate.call(1))\n    assert_equal(false, candidate.call(2))\n    assert_equal(true, candidate.call(-1))\n    assert_equal(true, candidate.call(64))\n    assert_equal(false, candidate.call(180))\n    assert_equal(true, candidate.call(1000))\n    assert_equal(true, candidate.call(0))\n    assert_equal(false, candidate.call(1729))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_77_iscube", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_iscube\n    candidate = method(:iscube)\n    assert_equal(true, candidate.call(1))\n    assert_equal(false, candidate.call(2))\n    assert_equal(true, candidate.call(-1))\n    assert_equal(true, candidate.call(64))\n    assert_equal(false, candidate.call(180))\n    assert_equal(true, candidate.call(1000))\n    assert_equal(true, candidate.call(0))\n    assert_equal(false, candidate.call(1729))\n  end\nend\n"}
{"name": "HumanEval_93_encode", "language": "rb", "prompt": "# Write a function that takes a message, and encodes in such a \n# way that it swaps case of all letters, replaces all vowels in \n# the message with the letter that appears 2 places ahead of that \n# vowel in the english alphabet. \n# Assume only letters. \n# Examples:\n# >>> encode.call(\"test\")\n# \"TGST\"\n# >>> encode.call(\"This is a message\")\n# \"tHKS KS C MGSSCGG\"\ndef encode(message)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_93_encode.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_encode\n    candidate = method(:encode)\n    assert_equal(\"tgst\", candidate.call(\"TEST\"))\n    assert_equal(\"mWDCSKR\", candidate.call(\"Mudasir\"))\n    assert_equal(\"ygs\", candidate.call(\"YES\"))\n    assert_equal(\"tHKS KS C MGSSCGG\", candidate.call(\"This is a message\"))\n    assert_equal(\"k dQnT kNqW wHcT Tq wRkTg\", candidate.call(\"I DoNt KnOw WhAt tO WrItE\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_93_encode", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_encode\n    candidate = method(:encode)\n    assert_equal(\"tgst\", candidate.call(\"TEST\"))\n    assert_equal(\"mWDCSKR\", candidate.call(\"Mudasir\"))\n    assert_equal(\"ygs\", candidate.call(\"YES\"))\n    assert_equal(\"tHKS KS C MGSSCGG\", candidate.call(\"This is a message\"))\n    assert_equal(\"k dQnT kNqW wHcT Tq wRkTg\", candidate.call(\"I DoNt KnOw WhAt tO WrItE\"))\n  end\nend\n"}
{"name": "HumanEval_91_is_bored", "language": "rb", "prompt": "# You'll be given a string of words, and your task is to count the number\n# of boredoms. A boredom is a sentence that starts with the word \"I\".\n# Sentences are delimited by '.', '?' or '!'.\n# For example:\n# >>> is_bored.call(\"Hello world\")\n# 0\n# >>> is_bored.call(\"The sky is blue. The sun is shining. I love this weather\")\n# 1\ndef is_bored(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_91_is_bored.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_bored\n    candidate = method(:is_bored)\n    assert_equal(0, candidate.call(\"Hello world\"))\n    assert_equal(0, candidate.call(\"Is the sky blue?\"))\n    assert_equal(1, candidate.call(\"I love It !\"))\n    assert_equal(0, candidate.call(\"bIt\"))\n    assert_equal(2, candidate.call(\"I feel good today. I will be productive. will kill It\"))\n    assert_equal(0, candidate.call(\"You and I are going for a walk\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_91_is_bored", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_bored\n    candidate = method(:is_bored)\n    assert_equal(0, candidate.call(\"Hello world\"))\n    assert_equal(0, candidate.call(\"Is the sky blue?\"))\n    assert_equal(1, candidate.call(\"I love It !\"))\n    assert_equal(0, candidate.call(\"bIt\"))\n    assert_equal(2, candidate.call(\"I feel good today. I will be productive. will kill It\"))\n    assert_equal(0, candidate.call(\"You and I are going for a walk\"))\n  end\nend\n"}
{"name": "HumanEval_43_pairs_sum_to_zero", "language": "rb", "prompt": "# pairs_sum_to_zero takes an array of integers as an input.\n# it returns true if there are two distinct elements in the array that\n# sum to zero, and false otherwise.\n# >>> pairs_sum_to_zero.call([1, 3, 5, 0])\n# false\n# >>> pairs_sum_to_zero.call([1, 3, -2, 1])\n# false\n# >>> pairs_sum_to_zero.call([1, 2, 3, 7])\n# false\n# >>> pairs_sum_to_zero.call([2, 4, -5, 3, 5, 7])\n# true\n# >>> pairs_sum_to_zero.call([1])\n# false\ndef pairs_sum_to_zero(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_43_pairs_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_pairs_sum_to_zero\n    candidate = method(:pairs_sum_to_zero)\n    assert_equal(false, candidate.call([1, 3, 5, 0]))\n    assert_equal(false, candidate.call([1, 3, -2, 1]))\n    assert_equal(false, candidate.call([1, 2, 3, 7]))\n    assert_equal(true, candidate.call([2, 4, -5, 3, 5, 7]))\n    assert_equal(false, candidate.call([1]))\n    assert_equal(true, candidate.call([-3, 9, -1, 3, 2, 30]))\n    assert_equal(true, candidate.call([-3, 9, -1, 3, 2, 31]))\n    assert_equal(false, candidate.call([-3, 9, -1, 4, 2, 30]))\n    assert_equal(false, candidate.call([-3, 9, -1, 4, 2, 31]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_43_pairs_sum_to_zero", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_pairs_sum_to_zero\n    candidate = method(:pairs_sum_to_zero)\n    assert_equal(false, candidate.call([1, 3, 5, 0]))\n    assert_equal(false, candidate.call([1, 3, -2, 1]))\n    assert_equal(false, candidate.call([1, 2, 3, 7]))\n    assert_equal(true, candidate.call([2, 4, -5, 3, 5, 7]))\n    assert_equal(false, candidate.call([1]))\n    assert_equal(true, candidate.call([-3, 9, -1, 3, 2, 30]))\n    assert_equal(true, candidate.call([-3, 9, -1, 3, 2, 31]))\n    assert_equal(false, candidate.call([-3, 9, -1, 4, 2, 30]))\n    assert_equal(false, candidate.call([-3, 9, -1, 4, 2, 31]))\n  end\nend\n"}
{"name": "HumanEval_71_triangle_area", "language": "rb", "prompt": "# Given the lengths of the three sides of a triangle. Return the area of\n# the triangle rounded to 2 decimal points if the three sides form a valid triangle. \n# Otherwise return -1\n# Three sides make a valid triangle when the sum of any two sides is greater \n# than the third side.\n# Example:\n# >>> triangle_area.call(3, 4, 5)\n# 6.0\n# >>> triangle_area.call(1, 2, 10)\n# -1\ndef triangle_area(a, b, c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_71_triangle_area.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triangle_area\n    candidate = method(:triangle_area)\n    assert_equal(6.0, candidate.call(3, 4, 5))\n    assert_equal(-1, candidate.call(1, 2, 10))\n    assert_equal(8.18, candidate.call(4, 8, 5))\n    assert_equal(1.73, candidate.call(2, 2, 2))\n    assert_equal(-1, candidate.call(1, 2, 3))\n    assert_equal(16.25, candidate.call(10, 5, 7))\n    assert_equal(-1, candidate.call(2, 6, 3))\n    assert_equal(0.43, candidate.call(1, 1, 1))\n    assert_equal(-1, candidate.call(2, 2, 10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_71_triangle_area", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triangle_area\n    candidate = method(:triangle_area)\n    assert_equal(6.0, candidate.call(3, 4, 5))\n    assert_equal(-1, candidate.call(1, 2, 10))\n    assert_equal(8.18, candidate.call(4, 8, 5))\n    assert_equal(1.73, candidate.call(2, 2, 2))\n    assert_equal(-1, candidate.call(1, 2, 3))\n    assert_equal(16.25, candidate.call(10, 5, 7))\n    assert_equal(-1, candidate.call(2, 6, 3))\n    assert_equal(0.43, candidate.call(1, 1, 1))\n    assert_equal(-1, candidate.call(2, 2, 10))\n  end\nend\n"}
{"name": "HumanEval_148_bf", "language": "rb", "prompt": "# There are eight planets in our solar system: the closerst to the Sun \n# is Mercury, the next one is Venus, then Earth, Mars, Jupiter, Saturn, \n# Uranus, Neptune.\n# Write a function that takes two planet names as strings planet1 and planet2. \n# The function should return an array containing all planets whose orbits are \n# located between the orbit of planet1 and the orbit of planet2, sorted by \n# the proximity to the sun. \n# The function should return an empty array if planet1 or planet2\n# are not correct planet names. \n# Examples\n# >>> bf.call(\"Jupiter\", \"Neptune\")\n# [\"Saturn\", \"Uranus\"]\n# >>> bf.call(\"Earth\", \"Mercury\")\n# \"Venus\"\n# >>> bf.call(\"Mercury\", \"Uranus\")\n# [\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"]\ndef bf(planet1, planet2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_148_bf.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_bf\n    candidate = method(:bf)\n    assert_equal([\"Saturn\", \"Uranus\"], candidate.call(\"Jupiter\", \"Neptune\"))\n    assert_equal([\"Venus\"], candidate.call(\"Earth\", \"Mercury\"))\n    assert_equal([\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"], candidate.call(\"Mercury\", \"Uranus\"))\n    assert_equal([\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"], candidate.call(\"Neptune\", \"Venus\"))\n    assert_equal([], candidate.call(\"Earth\", \"Earth\"))\n    assert_equal([], candidate.call(\"Mars\", \"Earth\"))\n    assert_equal([], candidate.call(\"Jupiter\", \"Makemake\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_148_bf", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_bf\n    candidate = method(:bf)\n    assert_equal([\"Saturn\", \"Uranus\"], candidate.call(\"Jupiter\", \"Neptune\"))\n    assert_equal([\"Venus\"], candidate.call(\"Earth\", \"Mercury\"))\n    assert_equal([\"Venus\", \"Earth\", \"Mars\", \"Jupiter\", \"Saturn\"], candidate.call(\"Mercury\", \"Uranus\"))\n    assert_equal([\"Earth\", \"Mars\", \"Jupiter\", \"Saturn\", \"Uranus\"], candidate.call(\"Neptune\", \"Venus\"))\n    assert_equal([], candidate.call(\"Earth\", \"Earth\"))\n    assert_equal([], candidate.call(\"Mars\", \"Earth\"))\n    assert_equal([], candidate.call(\"Jupiter\", \"Makemake\"))\n  end\nend\n"}
{"name": "HumanEval_131_digits", "language": "rb", "prompt": "# Given a positive integer n, return the product of the odd digits.\n# Return 0 if all digits are even.\n# For example:\n# >>> digits.call(1)\n# 1\n# >>> digits.call(4)\n# 0\n# >>> digits.call(235)\n# 15\ndef digits(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_131_digits.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_digits\n    candidate = method(:digits)\n    assert_equal(5, candidate.call(5))\n    assert_equal(5, candidate.call(54))\n    assert_equal(1, candidate.call(120))\n    assert_equal(5, candidate.call(5014))\n    assert_equal(315, candidate.call(98765))\n    assert_equal(2625, candidate.call(5576543))\n    assert_equal(0, candidate.call(2468))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_131_digits", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_digits\n    candidate = method(:digits)\n    assert_equal(5, candidate.call(5))\n    assert_equal(5, candidate.call(54))\n    assert_equal(1, candidate.call(120))\n    assert_equal(5, candidate.call(5014))\n    assert_equal(315, candidate.call(98765))\n    assert_equal(2625, candidate.call(5576543))\n    assert_equal(0, candidate.call(2468))\n  end\nend\n"}
{"name": "HumanEval_101_words_string", "language": "rb", "prompt": "# You will be given a string of words separated by commas or spaces. Your task is\n# to split the string into words and return an array of the words.\n# For example:\n# >>> words_string.call(\"Hi, my name is John\")\n# [\"Hi\", \"my\", \"name\", \"is\", \"John\"]\n# >>> words_string.call(\"One, two, three, four, five, six\")\n# [\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"]\ndef words_string(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_101_words_string.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_words_string\n    candidate = method(:words_string)\n    assert_equal([\"Hi\", \"my\", \"name\", \"is\", \"John\"], candidate.call(\"Hi, my name is John\"))\n    assert_equal([\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"], candidate.call(\"One, two, three, four, five, six\"))\n    assert_equal([\"Hi\", \"my\", \"name\"], candidate.call(\"Hi, my name\"))\n    assert_equal([\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"], candidate.call(\"One,, two, three, four, five, six,\"))\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([\"ahmed\", \"gamal\"], candidate.call(\"ahmed     , gamal\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_101_words_string", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_words_string\n    candidate = method(:words_string)\n    assert_equal([\"Hi\", \"my\", \"name\", \"is\", \"John\"], candidate.call(\"Hi, my name is John\"))\n    assert_equal([\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"], candidate.call(\"One, two, three, four, five, six\"))\n    assert_equal([\"Hi\", \"my\", \"name\"], candidate.call(\"Hi, my name\"))\n    assert_equal([\"One\", \"two\", \"three\", \"four\", \"five\", \"six\"], candidate.call(\"One,, two, three, four, five, six,\"))\n    assert_equal([], candidate.call(\"\"))\n    assert_equal([\"ahmed\", \"gamal\"], candidate.call(\"ahmed     , gamal\"))\n  end\nend\n"}
{"name": "HumanEval_18_how_many_times", "language": "rb", "prompt": "# Find how many times a given substring can be found in the original string. Count overlaping cases.\n# >>> how_many_times.call(\"\", \"a\")\n# 0\n# >>> how_many_times.call(\"aaa\", \"a\")\n# 3\n# >>> how_many_times.call(\"aaaa\", \"aa\")\n# 3\ndef how_many_times(string, substring)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_18_how_many_times.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_how_many_times\n    candidate = method(:how_many_times)\n    assert_equal(0, candidate.call(\"\", \"x\"))\n    assert_equal(4, candidate.call(\"xyxyxyx\", \"x\"))\n    assert_equal(4, candidate.call(\"cacacacac\", \"cac\"))\n    assert_equal(1, candidate.call(\"john doe\", \"john\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_18_how_many_times", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_how_many_times\n    candidate = method(:how_many_times)\n    assert_equal(0, candidate.call(\"\", \"x\"))\n    assert_equal(4, candidate.call(\"xyxyxyx\", \"x\"))\n    assert_equal(4, candidate.call(\"cacacacac\", \"cac\"))\n    assert_equal(1, candidate.call(\"john doe\", \"john\"))\n  end\nend\n"}
{"name": "HumanEval_137_compare_one", "language": "rb", "prompt": "# Create a function that takes integers, floats, or strings representing\n# real numbers, and returns the larger variable in its given variable type.\n# Return nil if the values are equal.\n# Note: If a real number is represented as a string, the floating point might be . or ,\n# >>> compare_one.call(1, 2.5)\n# 2.5\n# >>> compare_one.call(1, \"2,3\")\n# \"2,3\"\n# >>> compare_one.call(\"5,1\", \"6\")\n# \"6\"\n# >>> compare_one.call(\"1\", 1)\n# nil\ndef compare_one(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_137_compare_one.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_compare_one\n    candidate = method(:compare_one)\n    assert_equal(2, candidate.call(1, 2))\n    assert_equal(2.5, candidate.call(1, 2.5))\n    assert_equal(3, candidate.call(2, 3))\n    assert_equal(6, candidate.call(5, 6))\n    assert_equal(\"2,3\", candidate.call(1, \"2,3\"))\n    assert_equal(\"6\", candidate.call(\"5,1\", \"6\"))\n    assert_equal(\"2\", candidate.call(\"1\", \"2\"))\n    assert_equal(nil, candidate.call(\"1\", 1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_137_compare_one", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_compare_one\n    candidate = method(:compare_one)\n    assert_equal(2, candidate.call(1, 2))\n    assert_equal(2.5, candidate.call(1, 2.5))\n    assert_equal(3, candidate.call(2, 3))\n    assert_equal(6, candidate.call(5, 6))\n    assert_equal(\"2,3\", candidate.call(1, \"2,3\"))\n    assert_equal(\"6\", candidate.call(\"5,1\", \"6\"))\n    assert_equal(\"2\", candidate.call(\"1\", \"2\"))\n    assert_equal(nil, candidate.call(\"1\", 1))\n  end\nend\n"}
{"name": "HumanEval_51_remove_vowels", "language": "rb", "prompt": "# remove_vowels is a function that takes string and returns string without vowels.\n# >>> remove_vowels.call(\"\")\n# \"\"\n# >>> remove_vowels.call(\"abcdef\")\n# \"bcdf\"\n# >>> remove_vowels.call(\"aaaaa\")\n# \"\"\n# >>> remove_vowels.call(\"aaBAA\")\n# \"B\"\n# >>> remove_vowels.call(\"zbcd\")\n# \"zbcd\"\ndef remove_vowels(text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_51_remove_vowels.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_remove_vowels\n    candidate = method(:remove_vowels)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"bcdf\nghjklm\", candidate.call(\"abcdef\nghijklm\"))\n    assert_equal(\"fdcb\", candidate.call(\"fedcba\"))\n    assert_equal(\"\", candidate.call(\"eeeee\"))\n    assert_equal(\"cB\", candidate.call(\"acBAA\"))\n    assert_equal(\"cB\", candidate.call(\"EcBOO\"))\n    assert_equal(\"ybcd\", candidate.call(\"ybcd\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_51_remove_vowels", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_remove_vowels\n    candidate = method(:remove_vowels)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"bcdf\nghjklm\", candidate.call(\"abcdef\nghijklm\"))\n    assert_equal(\"fdcb\", candidate.call(\"fedcba\"))\n    assert_equal(\"\", candidate.call(\"eeeee\"))\n    assert_equal(\"cB\", candidate.call(\"acBAA\"))\n    assert_equal(\"cB\", candidate.call(\"EcBOO\"))\n    assert_equal(\"ybcd\", candidate.call(\"ybcd\"))\n  end\nend\n"}
{"name": "HumanEval_70_strange_sort_list", "language": "rb", "prompt": "# Given array of integers, return array in strange order.\n# Strange sorting, is when you start with the minimum value,\n# then maximum of the remaining integers, then minimum and so on.\n# Examples:\n# >>> strange_sort_list.call([1, 2, 3, 4])\n# [1, 4, 2, 3]\n# >>> strange_sort_list.call([5, 5, 5, 5])\n# [5, 5, 5, 5]\n# >>> strange_sort_list.call([])\n# []\ndef strange_sort_list(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_70_strange_sort_list.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_strange_sort_list\n    candidate = method(:strange_sort_list)\n    assert_equal([1, 4, 2, 3], candidate.call([1, 2, 3, 4]))\n    assert_equal([5, 9, 6, 8, 7], candidate.call([5, 6, 7, 8, 9]))\n    assert_equal([1, 5, 2, 4, 3], candidate.call([1, 2, 3, 4, 5]))\n    assert_equal([1, 9, 5, 8, 6, 7], candidate.call([5, 6, 7, 8, 9, 1]))\n    assert_equal([5, 5, 5, 5], candidate.call([5, 5, 5, 5]))\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 8, 2, 7, 3, 6, 4, 5], candidate.call([1, 2, 3, 4, 5, 6, 7, 8]))\n    assert_equal([-5, 5, -5, 5, 0, 2, 2, 2], candidate.call([0, 2, 2, 2, 5, 5, -5, -5]))\n    assert_equal([111111], candidate.call([111111]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_70_strange_sort_list", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_strange_sort_list\n    candidate = method(:strange_sort_list)\n    assert_equal([1, 4, 2, 3], candidate.call([1, 2, 3, 4]))\n    assert_equal([5, 9, 6, 8, 7], candidate.call([5, 6, 7, 8, 9]))\n    assert_equal([1, 5, 2, 4, 3], candidate.call([1, 2, 3, 4, 5]))\n    assert_equal([1, 9, 5, 8, 6, 7], candidate.call([5, 6, 7, 8, 9, 1]))\n    assert_equal([5, 5, 5, 5], candidate.call([5, 5, 5, 5]))\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 8, 2, 7, 3, 6, 4, 5], candidate.call([1, 2, 3, 4, 5, 6, 7, 8]))\n    assert_equal([-5, 5, -5, 5, 0, 2, 2, 2], candidate.call([0, 2, 2, 2, 5, 5, -5, -5]))\n    assert_equal([111111], candidate.call([111111]))\n  end\nend\n"}
{"name": "HumanEval_20_find_closest_elements", "language": "rb", "prompt": "# From a supplied array of numbers (of length at least two) select and return two that are the closest to each\n# other and return them in order (smaller number, larger number).\n# >>> find_closest_elements.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.2])\n# [2.0, 2.2]\n# >>> find_closest_elements.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.0])\n# [2.0, 2.0]\ndef find_closest_elements(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_20_find_closest_elements.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_find_closest_elements\n    candidate = method(:find_closest_elements)\n    assert_equal([3.9, 4.0], candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2]))\n    assert_equal([5.0, 5.9], candidate.call([1.0, 2.0, 5.9, 4.0, 5.0]))\n    assert_equal([2.0, 2.2], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]))\n    assert_equal([2.0, 2.0], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]))\n    assert_equal([2.2, 3.1], candidate.call([1.1, 2.2, 3.1, 4.1, 5.1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_20_find_closest_elements", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_find_closest_elements\n    candidate = method(:find_closest_elements)\n    assert_equal([3.9, 4.0], candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2]))\n    assert_equal([5.0, 5.9], candidate.call([1.0, 2.0, 5.9, 4.0, 5.0]))\n    assert_equal([2.0, 2.2], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.2]))\n    assert_equal([2.0, 2.0], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.0]))\n    assert_equal([2.2, 3.1], candidate.call([1.1, 2.2, 3.1, 4.1, 5.1]))\n  end\nend\n"}
{"name": "HumanEval_76_is_simple_power", "language": "rb", "prompt": "# Your task is to write a function that returns true if a number x is a simple\n# power of n and false in other cases.\n# x is a simple power of n if n**int=x\n# For example:\n# >>> is_simple_power.call(1, 4)\n# true\n# >>> is_simple_power.call(2, 2)\n# true\n# >>> is_simple_power.call(8, 2)\n# true\n# >>> is_simple_power.call(3, 2)\n# false\n# >>> is_simple_power.call(3, 1)\n# false\n# >>> is_simple_power.call(5, 3)\n# false\ndef is_simple_power(x, n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_76_is_simple_power.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_simple_power\n    candidate = method(:is_simple_power)\n    assert_equal(true, candidate.call(16, 2))\n    assert_equal(false, candidate.call(143214, 16))\n    assert_equal(true, candidate.call(4, 2))\n    assert_equal(true, candidate.call(9, 3))\n    assert_equal(true, candidate.call(16, 4))\n    assert_equal(false, candidate.call(24, 2))\n    assert_equal(false, candidate.call(128, 4))\n    assert_equal(false, candidate.call(12, 6))\n    assert_equal(true, candidate.call(1, 1))\n    assert_equal(true, candidate.call(1, 12))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_76_is_simple_power", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_simple_power\n    candidate = method(:is_simple_power)\n    assert_equal(true, candidate.call(16, 2))\n    assert_equal(false, candidate.call(143214, 16))\n    assert_equal(true, candidate.call(4, 2))\n    assert_equal(true, candidate.call(9, 3))\n    assert_equal(true, candidate.call(16, 4))\n    assert_equal(false, candidate.call(24, 2))\n    assert_equal(false, candidate.call(128, 4))\n    assert_equal(false, candidate.call(12, 6))\n    assert_equal(true, candidate.call(1, 1))\n    assert_equal(true, candidate.call(1, 12))\n  end\nend\n"}
{"name": "HumanEval_39_prime_fib", "language": "rb", "prompt": "# prime_fib returns n-th number that is a Fibonacci number and it's also prime.\n# >>> prime_fib.call(1)\n# 2\n# >>> prime_fib.call(2)\n# 3\n# >>> prime_fib.call(3)\n# 5\n# >>> prime_fib.call(4)\n# 13\n# >>> prime_fib.call(5)\n# 89\ndef prime_fib(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_39_prime_fib.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prime_fib\n    candidate = method(:prime_fib)\n    assert_equal(2, candidate.call(1))\n    assert_equal(3, candidate.call(2))\n    assert_equal(5, candidate.call(3))\n    assert_equal(13, candidate.call(4))\n    assert_equal(89, candidate.call(5))\n    assert_equal(233, candidate.call(6))\n    assert_equal(1597, candidate.call(7))\n    assert_equal(28657, candidate.call(8))\n    assert_equal(514229, candidate.call(9))\n    assert_equal(433494437, candidate.call(10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_39_prime_fib", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prime_fib\n    candidate = method(:prime_fib)\n    assert_equal(2, candidate.call(1))\n    assert_equal(3, candidate.call(2))\n    assert_equal(5, candidate.call(3))\n    assert_equal(13, candidate.call(4))\n    assert_equal(89, candidate.call(5))\n    assert_equal(233, candidate.call(6))\n    assert_equal(1597, candidate.call(7))\n    assert_equal(28657, candidate.call(8))\n    assert_equal(514229, candidate.call(9))\n    assert_equal(433494437, candidate.call(10))\n  end\nend\n"}
{"name": "HumanEval_145_order_by_points", "language": "rb", "prompt": "# Write a function which sorts the given array of integers\n# in ascending order according to the sum of their digits.\n# Note: if there are several items with similar sum of their digits,\n# order them based on their index in original array.\n# For example:\n# >>> order_by_points.call([1, 11, -1, -11, -12])\n# [-1, -11, 1, -12, 11]\n# >>> order_by_points.call([])\n# []\ndef order_by_points(nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_145_order_by_points.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_order_by_points\n    candidate = method(:order_by_points)\n    assert_equal([-1, -11, 1, -12, 11], candidate.call([1, 11, -1, -11, -12]))\n    assert_equal([0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457], candidate.call([1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]))\n    assert_equal([], candidate.call([]))\n    assert_equal([-3, -32, -98, -11, 1, 2, 43, 54], candidate.call([1, -11, -32, 43, 54, -98, 2, -3]))\n    assert_equal([1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9], candidate.call([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]))\n    assert_equal([-76, -21, 0, 4, 23, 6, 6], candidate.call([0, 6, 6, -76, -21, 23, 4]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_145_order_by_points", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_order_by_points\n    candidate = method(:order_by_points)\n    assert_equal([-1, -11, 1, -12, 11], candidate.call([1, 11, -1, -11, -12]))\n    assert_equal([0, 2, 3, 6, 53, 423, 423, 423, 1234, 145, 37, 46, 56, 463, 3457], candidate.call([1234, 423, 463, 145, 2, 423, 423, 53, 6, 37, 3457, 3, 56, 0, 46]))\n    assert_equal([], candidate.call([]))\n    assert_equal([-3, -32, -98, -11, 1, 2, 43, 54], candidate.call([1, -11, -32, 43, 54, -98, 2, -3]))\n    assert_equal([1, 10, 2, 11, 3, 4, 5, 6, 7, 8, 9], candidate.call([1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11]))\n    assert_equal([-76, -21, 0, 4, 23, 6, 6], candidate.call([0, 6, 6, -76, -21, 23, 4]))\n  end\nend\n"}
{"name": "HumanEval_0_has_close_elements", "language": "rb", "prompt": "# Check if in given array of numbers, are any two numbers closer to each other than\n# given threshold.\n# >>> has_close_elements.call([1.0, 2.0, 3.0], 0.5)\n# false\n# >>> has_close_elements.call([1.0, 2.8, 3.0, 4.0, 5.0, 2.0], 0.3)\n# true\ndef has_close_elements(numbers, threshold)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_0_has_close_elements.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_has_close_elements\n    candidate = method(:has_close_elements)\n    assert_equal(true, candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3))\n    assert_equal(false, candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05))\n    assert_equal(true, candidate.call([1.0, 2.0, 5.9, 4.0, 5.0], 0.95))\n    assert_equal(false, candidate.call([1.0, 2.0, 5.9, 4.0, 5.0], 0.8))\n    assert_equal(true, candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1))\n    assert_equal(true, candidate.call([1.1, 2.2, 3.1, 4.1, 5.1], 1.0))\n    assert_equal(false, candidate.call([1.1, 2.2, 3.1, 4.1, 5.1], 0.5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_0_has_close_elements", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_has_close_elements\n    candidate = method(:has_close_elements)\n    assert_equal(true, candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.3))\n    assert_equal(false, candidate.call([1.0, 2.0, 3.9, 4.0, 5.0, 2.2], 0.05))\n    assert_equal(true, candidate.call([1.0, 2.0, 5.9, 4.0, 5.0], 0.95))\n    assert_equal(false, candidate.call([1.0, 2.0, 5.9, 4.0, 5.0], 0.8))\n    assert_equal(true, candidate.call([1.0, 2.0, 3.0, 4.0, 5.0, 2.0], 0.1))\n    assert_equal(true, candidate.call([1.1, 2.2, 3.1, 4.1, 5.1], 1.0))\n    assert_equal(false, candidate.call([1.1, 2.2, 3.1, 4.1, 5.1], 0.5))\n  end\nend\n"}
{"name": "HumanEval_10_make_palindrome", "language": "rb", "prompt": "# Find the shortest palindrome that begins with a supplied string.\n# Algorithm idea is simple:\n# - Find the longest postfix of supplied string that is a palindrome.\n# - Append to the end of the string reverse of a string prefix that comes before the palindromic suffix.\n# >>> make_palindrome.call(\"\")\n# \"\"\n# >>> make_palindrome.call(\"cat\")\n# \"catac\"\n# >>> make_palindrome.call(\"cata\")\n# \"catac\"\ndef make_palindrome(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_10_make_palindrome.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_make_palindrome\n    candidate = method(:make_palindrome)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"x\", candidate.call(\"x\"))\n    assert_equal(\"xyzyx\", candidate.call(\"xyz\"))\n    assert_equal(\"xyx\", candidate.call(\"xyx\"))\n    assert_equal(\"jerryrrej\", candidate.call(\"jerry\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_10_make_palindrome", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_make_palindrome\n    candidate = method(:make_palindrome)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"x\", candidate.call(\"x\"))\n    assert_equal(\"xyzyx\", candidate.call(\"xyz\"))\n    assert_equal(\"xyx\", candidate.call(\"xyx\"))\n    assert_equal(\"jerryrrej\", candidate.call(\"jerry\"))\n  end\nend\n"}
{"name": "HumanEval_11_string_xor", "language": "rb", "prompt": "# Input are two strings a and b consisting only of 1s and 0s.\n# Perform binary XOR on these inputs and return result also as a string.\n# >>> string_xor.call(\"010\", \"110\")\n# \"100\"\ndef string_xor(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_11_string_xor.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_xor\n    candidate = method(:string_xor)\n    assert_equal(\"010010\", candidate.call(\"111000\", \"101010\"))\n    assert_equal(\"0\", candidate.call(\"1\", \"1\"))\n    assert_equal(\"0101\", candidate.call(\"0101\", \"0000\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_11_string_xor", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_xor\n    candidate = method(:string_xor)\n    assert_equal(\"010010\", candidate.call(\"111000\", \"101010\"))\n    assert_equal(\"0\", candidate.call(\"1\", \"1\"))\n    assert_equal(\"0101\", candidate.call(\"0101\", \"0000\"))\n  end\nend\n"}
{"name": "HumanEval_139_special_factorial", "language": "rb", "prompt": "# The Brazilian factorial is defined as:\n# brazilian_factorial(n) = n! * (n-1)! * (n-2)! * ... * 1!\n# where n > 0\n# For example:\n# >>> special_factorial.call(4)\n# 288\n# The function will receive an integer as input and should return the special\n# factorial of this integer.\ndef special_factorial(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_139_special_factorial.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_special_factorial\n    candidate = method(:special_factorial)\n    assert_equal(288, candidate.call(4))\n    assert_equal(34560, candidate.call(5))\n    assert_equal(125411328000, candidate.call(7))\n    assert_equal(1, candidate.call(1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_139_special_factorial", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_special_factorial\n    candidate = method(:special_factorial)\n    assert_equal(288, candidate.call(4))\n    assert_equal(34560, candidate.call(5))\n    assert_equal(125411328000, candidate.call(7))\n    assert_equal(1, candidate.call(1))\n  end\nend\n"}
{"name": "HumanEval_122_add_elements", "language": "rb", "prompt": "# Given a non-empty array of integers arr and an integer k, return\n# the sum of the elements with at most two digits from the first k elements of arr.\n# Example:\n# >>> add_elements.call([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4)\n# 24\n# Constraints:\n# 1. 1 <= len(arr) <= 100\n# 2. 1 <= k <= len(arr)\ndef add_elements(arr, k)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_122_add_elements.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add_elements\n    candidate = method(:add_elements)\n    assert_equal(-4, candidate.call([1, -2, -3, 41, 57, 76, 87, 88, 99], 3))\n    assert_equal(0, candidate.call([111, 121, 3, 4000, 5, 6], 2))\n    assert_equal(125, candidate.call([11, 21, 3, 90, 5, 6, 7, 8, 9], 4))\n    assert_equal(24, candidate.call([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4))\n    assert_equal(1, candidate.call([1], 1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_122_add_elements", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_add_elements\n    candidate = method(:add_elements)\n    assert_equal(-4, candidate.call([1, -2, -3, 41, 57, 76, 87, 88, 99], 3))\n    assert_equal(0, candidate.call([111, 121, 3, 4000, 5, 6], 2))\n    assert_equal(125, candidate.call([11, 21, 3, 90, 5, 6, 7, 8, 9], 4))\n    assert_equal(24, candidate.call([111, 21, 3, 4000, 5, 6, 7, 8, 9], 4))\n    assert_equal(1, candidate.call([1], 1))\n  end\nend\n"}
{"name": "HumanEval_46_fib4", "language": "rb", "prompt": "# The Fib4 number sequence is a sequence similar to the Fibbonacci sequnece that's defined as follows:\n# fib4(0) -> 0\n# fib4(1) -> 0\n# fib4(2) -> 2\n# fib4(3) -> 0\n# fib4(n) -> fib4(n-1) + fib4(n-2) + fib4(n-3) + fib4(n-4).\n# Please write a function to efficiently compute the n-th element of the fib4 number sequence.  Do not use recursion.\n# >>> fib4.call(5)\n# 4\n# >>> fib4.call(6)\n# 8\n# >>> fib4.call(7)\n# 14\ndef fib4(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_46_fib4.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fib4\n    candidate = method(:fib4)\n    assert_equal(4, candidate.call(5))\n    assert_equal(28, candidate.call(8))\n    assert_equal(104, candidate.call(10))\n    assert_equal(386, candidate.call(12))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_46_fib4", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fib4\n    candidate = method(:fib4)\n    assert_equal(4, candidate.call(5))\n    assert_equal(28, candidate.call(8))\n    assert_equal(104, candidate.call(10))\n    assert_equal(386, candidate.call(12))\n  end\nend\n"}
{"name": "HumanEval_104_unique_digits", "language": "rb", "prompt": "# Given an array of positive integers x. return a sorted array of all \n# elements that hasn't any even digit.\n# Note: Returned array should be sorted in increasing order.\n# For example:\n# >>> unique_digits.call([15, 33, 1422, 1])\n# [1, 15, 33]\n# >>> unique_digits.call([152, 323, 1422, 10])\n# []\ndef unique_digits(x)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_104_unique_digits.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_unique_digits\n    candidate = method(:unique_digits)\n    assert_equal([1, 15, 33], candidate.call([15, 33, 1422, 1]))\n    assert_equal([], candidate.call([152, 323, 1422, 10]))\n    assert_equal([111, 151], candidate.call([12345, 2033, 111, 151]))\n    assert_equal([31, 135], candidate.call([135, 103, 31]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_104_unique_digits", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_unique_digits\n    candidate = method(:unique_digits)\n    assert_equal([1, 15, 33], candidate.call([15, 33, 1422, 1]))\n    assert_equal([], candidate.call([152, 323, 1422, 10]))\n    assert_equal([111, 151], candidate.call([12345, 2033, 111, 151]))\n    assert_equal([31, 135], candidate.call([135, 103, 31]))\n  end\nend\n"}
{"name": "HumanEval_117_select_words", "language": "rb", "prompt": "# Given a string s and a natural number n, you have been tasked to implement \n# a function that returns an array of all words from string s that contain exactly \n# n consonants, in order these words appear in the string s.\n# If the string s is empty then the function should return an empty array.\n# Note: you may assume the input string contains only letters and spaces.\n# Examples:\n# >>> select_words.call(\"Mary had a little lamb\", 4)\n# [\"little\"]\n# >>> select_words.call(\"Mary had a little lamb\", 3)\n# [\"Mary\", \"lamb\"]\n# >>> select_words.call(\"simple white space\", 2)\n# []\n# >>> select_words.call(\"Hello world\", 4)\n# [\"world\"]\n# >>> select_words.call(\"Uncle sam\", 3)\n# [\"Uncle\"]\ndef select_words(s, n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_117_select_words.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_select_words\n    candidate = method(:select_words)\n    assert_equal([\"little\"], candidate.call(\"Mary had a little lamb\", 4))\n    assert_equal([\"Mary\", \"lamb\"], candidate.call(\"Mary had a little lamb\", 3))\n    assert_equal([], candidate.call(\"simple white space\", 2))\n    assert_equal([\"world\"], candidate.call(\"Hello world\", 4))\n    assert_equal([\"Uncle\"], candidate.call(\"Uncle sam\", 3))\n    assert_equal([], candidate.call(\"\", 4))\n    assert_equal([\"b\", \"c\", \"d\", \"f\"], candidate.call(\"a b c d e f\", 1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_117_select_words", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_select_words\n    candidate = method(:select_words)\n    assert_equal([\"little\"], candidate.call(\"Mary had a little lamb\", 4))\n    assert_equal([\"Mary\", \"lamb\"], candidate.call(\"Mary had a little lamb\", 3))\n    assert_equal([], candidate.call(\"simple white space\", 2))\n    assert_equal([\"world\"], candidate.call(\"Hello world\", 4))\n    assert_equal([\"Uncle\"], candidate.call(\"Uncle sam\", 3))\n    assert_equal([], candidate.call(\"\", 4))\n    assert_equal([\"b\", \"c\", \"d\", \"f\"], candidate.call(\"a b c d e f\", 1))\n  end\nend\n"}
{"name": "HumanEval_72_will_it_fly", "language": "rb", "prompt": "# Write a function that returns true if the object q will fly, and false otherwise.\n# The object q will fly if it's balanced (it is a palindromic array) and the sum of its elements is less than or equal the maximum possible weight w.\n# Example:\n# >>> will_it_fly.call([1, 2], 5)\n# false\n# # 1+2 is less than the maximum possible weight, but it's unbalanced.\n# >>> will_it_fly.call([3, 2, 3], 1)\n# false\n# # it's balanced, but 3+2+3 is more than the maximum possible weight.\n# >>> will_it_fly.call([3, 2, 3], 9)\n# true\n# # 3+2+3 is less than the maximum possible weight, and it's balanced.\n# >>> will_it_fly.call([3], 5)\n# true\n# # 3 is less than the maximum possible weight, and it's balanced.\ndef will_it_fly(q, w)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_72_will_it_fly.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_will_it_fly\n    candidate = method(:will_it_fly)\n    assert_equal(true, candidate.call([3, 2, 3], 9))\n    assert_equal(false, candidate.call([1, 2], 5))\n    assert_equal(true, candidate.call([3], 5))\n    assert_equal(false, candidate.call([3, 2, 3], 1))\n    assert_equal(false, candidate.call([1, 2, 3], 6))\n    assert_equal(true, candidate.call([5], 5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_72_will_it_fly", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_will_it_fly\n    candidate = method(:will_it_fly)\n    assert_equal(true, candidate.call([3, 2, 3], 9))\n    assert_equal(false, candidate.call([1, 2], 5))\n    assert_equal(true, candidate.call([3], 5))\n    assert_equal(false, candidate.call([3, 2, 3], 1))\n    assert_equal(false, candidate.call([1, 2, 3], 6))\n    assert_equal(true, candidate.call([5], 5))\n  end\nend\n"}
{"name": "HumanEval_55_fib", "language": "rb", "prompt": "# Return n-th Fibonacci number.\n# >>> fib.call(10)\n# 55\n# >>> fib.call(1)\n# 1\n# >>> fib.call(8)\n# 21\ndef fib(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_55_fib.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fib\n    candidate = method(:fib)\n    assert_equal(55, candidate.call(10))\n    assert_equal(1, candidate.call(1))\n    assert_equal(21, candidate.call(8))\n    assert_equal(89, candidate.call(11))\n    assert_equal(144, candidate.call(12))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_55_fib", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fib\n    candidate = method(:fib)\n    assert_equal(55, candidate.call(10))\n    assert_equal(1, candidate.call(1))\n    assert_equal(21, candidate.call(8))\n    assert_equal(89, candidate.call(11))\n    assert_equal(144, candidate.call(12))\n  end\nend\n"}
{"name": "HumanEval_153_Strongest_Extension", "language": "rb", "prompt": "# You will be given the name of a class (a string) and an array of extensions.\n# The extensions are to be used to load additional classes to the class. The\n# strength of the extension is as follows: Let CAP be the number of the uppercase\n# letters in the extension's name, and let SM be the number of lowercase letters \n# in the extension's name, the strength is given by the fraction CAP - SM. \n# You should find the strongest extension and return a string in this \n# format: ClassName.StrongestExtensionName.\n# If there are two or more extensions with the same strength, you should\n# choose the one that comes first in the array.\n# For example, if you are given \"Slices\" as the class and an array of the\n# extensions: ['SErviNGSliCes', 'Cheese', 'StuFfed'] then you should\n# return 'Slices.SErviNGSliCes' since 'SErviNGSliCes' is the strongest extension \n# (its strength is -1).\n# Example:\n# >>> Strongest_Extension.call(\"my_class\", [\"AA\", \"Be\", \"CC\"])\n# \"my_class.AA\"\ndef Strongest_Extension(class_name, extensions)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_153_Strongest_Extension.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_Strongest_Extension\n    candidate = method(:Strongest_Extension)\n    assert_equal(\"Watashi.eIGHt8OKe\", candidate.call(\"Watashi\", [\"tEN\", \"niNE\", \"eIGHt8OKe\"]))\n    assert_equal(\"Boku123.YEs.WeCaNe\", candidate.call(\"Boku123\", [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]))\n    assert_equal(\"__YESIMHERE.NuLl__\", candidate.call(\"__YESIMHERE\", [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]))\n    assert_equal(\"K.TAR\", candidate.call(\"K\", [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]))\n    assert_equal(\"__HAHA.123\", candidate.call(\"__HAHA\", [\"Tab\", \"123\", \"781345\", \"-_-\"]))\n    assert_equal(\"YameRore.okIWILL123\", candidate.call(\"YameRore\", [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]))\n    assert_equal(\"finNNalLLly.WoW\", candidate.call(\"finNNalLLly\", [\"Die\", \"NowW\", \"Wow\", \"WoW\"]))\n    assert_equal(\"_.Bb\", candidate.call(\"_\", [\"Bb\", \"91245\"]))\n    assert_equal(\"Sp.671235\", candidate.call(\"Sp\", [\"671235\", \"Bb\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_153_Strongest_Extension", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_Strongest_Extension\n    candidate = method(:Strongest_Extension)\n    assert_equal(\"Watashi.eIGHt8OKe\", candidate.call(\"Watashi\", [\"tEN\", \"niNE\", \"eIGHt8OKe\"]))\n    assert_equal(\"Boku123.YEs.WeCaNe\", candidate.call(\"Boku123\", [\"nani\", \"NazeDa\", \"YEs.WeCaNe\", \"32145tggg\"]))\n    assert_equal(\"__YESIMHERE.NuLl__\", candidate.call(\"__YESIMHERE\", [\"t\", \"eMptY\", \"nothing\", \"zeR00\", \"NuLl__\", \"123NoooneB321\"]))\n    assert_equal(\"K.TAR\", candidate.call(\"K\", [\"Ta\", \"TAR\", \"t234An\", \"cosSo\"]))\n    assert_equal(\"__HAHA.123\", candidate.call(\"__HAHA\", [\"Tab\", \"123\", \"781345\", \"-_-\"]))\n    assert_equal(\"YameRore.okIWILL123\", candidate.call(\"YameRore\", [\"HhAas\", \"okIWILL123\", \"WorkOut\", \"Fails\", \"-_-\"]))\n    assert_equal(\"finNNalLLly.WoW\", candidate.call(\"finNNalLLly\", [\"Die\", \"NowW\", \"Wow\", \"WoW\"]))\n    assert_equal(\"_.Bb\", candidate.call(\"_\", [\"Bb\", \"91245\"]))\n    assert_equal(\"Sp.671235\", candidate.call(\"Sp\", [\"671235\", \"Bb\"]))\n  end\nend\n"}
{"name": "HumanEval_119_match_parens", "language": "rb", "prompt": "# You are given an array of two strings, both strings consist of open\n# parentheses '(' or close parentheses ')' only.\n# Your job is to check if it is possible to concatenate the two strings in\n# some order, that the resulting string will be good.\n# A string S is considered to be good if and only if all parentheses in S\n# are balanced. For example: the string '(())()' is good, while the string\n# '())' is not.\n# Return 'Yes' if there's a way to make a good string, and return 'No' otherwise.\n# Examples:\n# >>> match_parens.call([\"()(\", \")\"])\n# \"Yes\"\n# >>> match_parens.call([\")\", \")\"])\n# \"No\"\ndef match_parens(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_119_match_parens.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_match_parens\n    candidate = method(:match_parens)\n    assert_equal(\"Yes\", candidate.call([\"()(\", \")\"]))\n    assert_equal(\"No\", candidate.call([\")\", \")\"]))\n    assert_equal(\"No\", candidate.call([\"(()(())\", \"())())\"]))\n    assert_equal(\"Yes\", candidate.call([\")())\", \"(()()(\"]))\n    assert_equal(\"Yes\", candidate.call([\"(())))\", \"(()())((\"]))\n    assert_equal(\"No\", candidate.call([\"()\", \"())\"]))\n    assert_equal(\"Yes\", candidate.call([\"(()(\", \"()))()\"]))\n    assert_equal(\"No\", candidate.call([\"((((\", \"((())\"]))\n    assert_equal(\"No\", candidate.call([\")(()\", \"(()(\"]))\n    assert_equal(\"No\", candidate.call([\")(\", \")(\"]))\n    assert_equal(\"Yes\", candidate.call([\"(\", \")\"]))\n    assert_equal(\"Yes\", candidate.call([\")\", \"(\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_119_match_parens", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_match_parens\n    candidate = method(:match_parens)\n    assert_equal(\"Yes\", candidate.call([\"()(\", \")\"]))\n    assert_equal(\"No\", candidate.call([\")\", \")\"]))\n    assert_equal(\"No\", candidate.call([\"(()(())\", \"())())\"]))\n    assert_equal(\"Yes\", candidate.call([\")())\", \"(()()(\"]))\n    assert_equal(\"Yes\", candidate.call([\"(())))\", \"(()())((\"]))\n    assert_equal(\"No\", candidate.call([\"()\", \"())\"]))\n    assert_equal(\"Yes\", candidate.call([\"(()(\", \"()))()\"]))\n    assert_equal(\"No\", candidate.call([\"((((\", \"((())\"]))\n    assert_equal(\"No\", candidate.call([\")(()\", \"(()(\"]))\n    assert_equal(\"No\", candidate.call([\")(\", \")(\"]))\n    assert_equal(\"Yes\", candidate.call([\"(\", \")\"]))\n    assert_equal(\"Yes\", candidate.call([\")\", \"(\"]))\n  end\nend\n"}
{"name": "HumanEval_90_next_smallest", "language": "rb", "prompt": "# You are given an array of integers.\n# Write a function next_smallest() that returns the 2nd smallest element of the array.\n# Return nil if there is no such element.\n# >>> next_smallest.call([1, 2, 3, 4, 5])\n# 2\n# >>> next_smallest.call([5, 1, 4, 3, 2])\n# 2\n# >>> next_smallest.call([])\n# nil\n# >>> next_smallest.call([1, 1])\n# nil\ndef next_smallest(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_90_next_smallest.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_next_smallest\n    candidate = method(:next_smallest)\n    assert_equal(2, candidate.call([1, 2, 3, 4, 5]))\n    assert_equal(2, candidate.call([5, 1, 4, 3, 2]))\n    assert_equal(nil, candidate.call([]))\n    assert_equal(nil, candidate.call([1, 1]))\n    assert_equal(1, candidate.call([1, 1, 1, 1, 0]))\n    assert_equal(nil, candidate.call([1, 1]))\n    assert_equal(-35, candidate.call([-35, 34, 12, -45]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_90_next_smallest", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_next_smallest\n    candidate = method(:next_smallest)\n    assert_equal(2, candidate.call([1, 2, 3, 4, 5]))\n    assert_equal(2, candidate.call([5, 1, 4, 3, 2]))\n    assert_equal(nil, candidate.call([]))\n    assert_equal(nil, candidate.call([1, 1]))\n    assert_equal(1, candidate.call([1, 1, 1, 1, 0]))\n    assert_equal(nil, candidate.call([1, 1]))\n    assert_equal(-35, candidate.call([-35, 34, 12, -45]))\n  end\nend\n"}
{"name": "HumanEval_92_any_int", "language": "rb", "prompt": "# Create a function that takes 3 numbers.\n# Returns true if one of the numbers is equal to the sum of the other two, and all numbers are integers.\n# Returns false in any other cases.\n# Examples\n# >>> any_int.call(5, 2, 7)\n# true\n# >>> any_int.call(3, 2, 2)\n# false\n# >>> any_int.call(3, -2, 1)\n# true\n# >>> any_int.call(3.6, -2.2, 2)\n# false\ndef any_int(x, y, z)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_92_any_int.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_any_int\n    candidate = method(:any_int)\n    assert_equal(true, candidate.call(2, 3, 1))\n    assert_equal(false, candidate.call(2.5, 2, 3))\n    assert_equal(false, candidate.call(1.5, 5, 3.5))\n    assert_equal(false, candidate.call(2, 6, 2))\n    assert_equal(true, candidate.call(4, 2, 2))\n    assert_equal(false, candidate.call(2.2, 2.2, 2.2))\n    assert_equal(true, candidate.call(-4, 6, 2))\n    assert_equal(true, candidate.call(2, 1, 1))\n    assert_equal(true, candidate.call(3, 4, 7))\n    assert_equal(false, candidate.call(3.0, 4, 7))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_92_any_int", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_any_int\n    candidate = method(:any_int)\n    assert_equal(true, candidate.call(2, 3, 1))\n    assert_equal(false, candidate.call(2.5, 2, 3))\n    assert_equal(false, candidate.call(1.5, 5, 3.5))\n    assert_equal(false, candidate.call(2, 6, 2))\n    assert_equal(true, candidate.call(4, 2, 2))\n    assert_equal(false, candidate.call(2.2, 2.2, 2.2))\n    assert_equal(true, candidate.call(-4, 6, 2))\n    assert_equal(true, candidate.call(2, 1, 1))\n    assert_equal(true, candidate.call(3, 4, 7))\n    assert_equal(false, candidate.call(3.0, 4, 7))\n  end\nend\n"}
{"name": "HumanEval_2_truncate_number", "language": "rb", "prompt": "# Given a positive floating point number, it can be decomposed into\n# and integer part (largest integer smaller than given number) and decimals\n# (leftover part always smaller than 1).\n# Return the decimal part of the number.\n# >>> truncate_number.call(3.5)\n# 0.5\ndef truncate_number(number)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_2_truncate_number.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_truncate_number\n    candidate = method(:truncate_number)\n    assert_equal(0.5, candidate.call(3.5))\n    assert_equal(0.25, candidate.call(1.25))\n    assert_equal(0.0, candidate.call(123.0))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_2_truncate_number", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_truncate_number\n    candidate = method(:truncate_number)\n    assert_equal(0.5, candidate.call(3.5))\n    assert_equal(0.25, candidate.call(1.25))\n    assert_equal(0.0, candidate.call(123.0))\n  end\nend\n"}
{"name": "HumanEval_42_incr_list", "language": "rb", "prompt": "# Return array with elements incremented by 1.\n# >>> incr_list.call([1, 2, 3])\n# [2, 3, 4]\n# >>> incr_list.call([5, 3, 5, 2, 3, 3, 9, 0, 123])\n# [6, 4, 6, 3, 4, 4, 10, 1, 124]\ndef incr_list(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_42_incr_list.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_incr_list\n    candidate = method(:incr_list)\n    assert_equal([], candidate.call([]))\n    assert_equal([4, 3, 2], candidate.call([3, 2, 1]))\n    assert_equal([6, 3, 6, 3, 4, 4, 10, 1, 124], candidate.call([5, 2, 5, 2, 3, 3, 9, 0, 123]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_42_incr_list", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_incr_list\n    candidate = method(:incr_list)\n    assert_equal([], candidate.call([]))\n    assert_equal([4, 3, 2], candidate.call([3, 2, 1]))\n    assert_equal([6, 3, 6, 3, 4, 4, 10, 1, 124], candidate.call([5, 2, 5, 2, 3, 3, 9, 0, 123]))\n  end\nend\n"}
{"name": "HumanEval_150_x_or_y", "language": "rb", "prompt": "# A simple program which should return the value of x if n is \n# a prime number and should return the value of y otherwise.\n# Examples:\n# >>> x_or_y.call(7, 34, 12)\n# 34\n# >>> x_or_y.call(15, 8, 5)\n# 5\ndef x_or_y(n, x, y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_150_x_or_y.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_x_or_y\n    candidate = method(:x_or_y)\n    assert_equal(34, candidate.call(7, 34, 12))\n    assert_equal(5, candidate.call(15, 8, 5))\n    assert_equal(33, candidate.call(3, 33, 5212))\n    assert_equal(3, candidate.call(1259, 3, 52))\n    assert_equal(-1, candidate.call(7919, -1, 12))\n    assert_equal(583, candidate.call(3609, 1245, 583))\n    assert_equal(129, candidate.call(91, 56, 129))\n    assert_equal(1234, candidate.call(6, 34, 1234))\n    assert_equal(0, candidate.call(1, 2, 0))\n    assert_equal(2, candidate.call(2, 2, 0))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_150_x_or_y", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_x_or_y\n    candidate = method(:x_or_y)\n    assert_equal(34, candidate.call(7, 34, 12))\n    assert_equal(5, candidate.call(15, 8, 5))\n    assert_equal(33, candidate.call(3, 33, 5212))\n    assert_equal(3, candidate.call(1259, 3, 52))\n    assert_equal(-1, candidate.call(7919, -1, 12))\n    assert_equal(583, candidate.call(3609, 1245, 583))\n    assert_equal(129, candidate.call(91, 56, 129))\n    assert_equal(1234, candidate.call(6, 34, 1234))\n    assert_equal(0, candidate.call(1, 2, 0))\n    assert_equal(2, candidate.call(2, 2, 0))\n  end\nend\n"}
{"name": "HumanEval_49_modp", "language": "rb", "prompt": "# Return 2^n modulo p (be aware of numerics).\n# >>> modp.call(3, 5)\n# 3\n# >>> modp.call(1101, 101)\n# 2\n# >>> modp.call(0, 101)\n# 1\n# >>> modp.call(3, 11)\n# 8\n# >>> modp.call(100, 101)\n# 1\ndef modp(n, p)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_49_modp.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_modp\n    candidate = method(:modp)\n    assert_equal(3, candidate.call(3, 5))\n    assert_equal(2, candidate.call(1101, 101))\n    assert_equal(1, candidate.call(0, 101))\n    assert_equal(8, candidate.call(3, 11))\n    assert_equal(1, candidate.call(100, 101))\n    assert_equal(4, candidate.call(30, 5))\n    assert_equal(3, candidate.call(31, 5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_49_modp", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_modp\n    candidate = method(:modp)\n    assert_equal(3, candidate.call(3, 5))\n    assert_equal(2, candidate.call(1101, 101))\n    assert_equal(1, candidate.call(0, 101))\n    assert_equal(8, candidate.call(3, 11))\n    assert_equal(1, candidate.call(100, 101))\n    assert_equal(4, candidate.call(30, 5))\n    assert_equal(3, candidate.call(31, 5))\n  end\nend\n"}
{"name": "HumanEval_155_even_odd_count", "language": "rb", "prompt": "# Given an integer. return an array that has the number of even and odd digits respectively.\n# Example:\n# >>> even_odd_count.call(-12)\n# [1, 1]\n# >>> even_odd_count.call(123)\n# [1, 2]\ndef even_odd_count(num)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_155_even_odd_count.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_even_odd_count\n    candidate = method(:even_odd_count)\n    assert_equal([0, 1], candidate.call(7))\n    assert_equal([1, 1], candidate.call(-78))\n    assert_equal([2, 2], candidate.call(3452))\n    assert_equal([3, 3], candidate.call(346211))\n    assert_equal([3, 3], candidate.call(-345821))\n    assert_equal([1, 0], candidate.call(-2))\n    assert_equal([2, 3], candidate.call(-45347))\n    assert_equal([1, 0], candidate.call(0))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_155_even_odd_count", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_even_odd_count\n    candidate = method(:even_odd_count)\n    assert_equal([0, 1], candidate.call(7))\n    assert_equal([1, 1], candidate.call(-78))\n    assert_equal([2, 2], candidate.call(3452))\n    assert_equal([3, 3], candidate.call(346211))\n    assert_equal([3, 3], candidate.call(-345821))\n    assert_equal([1, 0], candidate.call(-2))\n    assert_equal([2, 3], candidate.call(-45347))\n    assert_equal([1, 0], candidate.call(0))\n  end\nend\n"}
{"name": "HumanEval_80_is_happy", "language": "rb", "prompt": "# You are given a string s.\n# Your task is to check if the string is haprb or not.\n# A string is haprb if its length is at least 3 and every 3 consecutive letters are distinct\n# For example:\n# >>> is_happy.call(\"a\")\n# false\n# >>> is_happy.call(\"aa\")\n# false\n# >>> is_happy.call(\"abcd\")\n# true\n# >>> is_happy.call(\"aabb\")\n# false\n# >>> is_happy.call(\"adb\")\n# true\n# >>> is_happy.call(\"xyy\")\n# false\ndef is_happy(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_80_is_happy.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_happy\n    candidate = method(:is_happy)\n    assert_equal(false, candidate.call(\"a\"))\n    assert_equal(false, candidate.call(\"aa\"))\n    assert_equal(true, candidate.call(\"abcd\"))\n    assert_equal(false, candidate.call(\"aabb\"))\n    assert_equal(true, candidate.call(\"adb\"))\n    assert_equal(false, candidate.call(\"xyy\"))\n    assert_equal(true, candidate.call(\"iopaxpoi\"))\n    assert_equal(false, candidate.call(\"iopaxioi\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_80_is_happy", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_happy\n    candidate = method(:is_happy)\n    assert_equal(false, candidate.call(\"a\"))\n    assert_equal(false, candidate.call(\"aa\"))\n    assert_equal(true, candidate.call(\"abcd\"))\n    assert_equal(false, candidate.call(\"aabb\"))\n    assert_equal(true, candidate.call(\"adb\"))\n    assert_equal(false, candidate.call(\"xyy\"))\n    assert_equal(true, candidate.call(\"iopaxpoi\"))\n    assert_equal(false, candidate.call(\"iopaxioi\"))\n  end\nend\n"}
{"name": "HumanEval_59_largest_prime_factor", "language": "rb", "prompt": "# Return the largest prime factor of n. Assume n > 1 and is not a prime.\n# >>> largest_prime_factor.call(13195)\n# 29\n# >>> largest_prime_factor.call(2048)\n# 2\ndef largest_prime_factor(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_59_largest_prime_factor.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_prime_factor\n    candidate = method(:largest_prime_factor)\n    assert_equal(5, candidate.call(15))\n    assert_equal(3, candidate.call(27))\n    assert_equal(7, candidate.call(63))\n    assert_equal(11, candidate.call(330))\n    assert_equal(29, candidate.call(13195))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_59_largest_prime_factor", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_prime_factor\n    candidate = method(:largest_prime_factor)\n    assert_equal(5, candidate.call(15))\n    assert_equal(3, candidate.call(27))\n    assert_equal(7, candidate.call(63))\n    assert_equal(11, candidate.call(330))\n    assert_equal(29, candidate.call(13195))\n  end\nend\n"}
{"name": "HumanEval_66_digitSum", "language": "rb", "prompt": "# Task\n# Write a function that takes a string as input and returns the sum of the upper characters only'\n# ASCII codes.\n# Examples:\n# >>> digitSum.call(\"\")\n# 0\n# >>> digitSum.call(\"abAB\")\n# 131\n# >>> digitSum.call(\"abcCd\")\n# 67\n# >>> digitSum.call(\"helloE\")\n# 69\n# >>> digitSum.call(\"woArBld\")\n# 131\n# >>> digitSum.call(\"aAaaaXa\")\n# 153\ndef digitSum(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_66_digitSum.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_digitSum\n    candidate = method(:digitSum)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(131, candidate.call(\"abAB\"))\n    assert_equal(67, candidate.call(\"abcCd\"))\n    assert_equal(69, candidate.call(\"helloE\"))\n    assert_equal(131, candidate.call(\"woArBld\"))\n    assert_equal(153, candidate.call(\"aAaaaXa\"))\n    assert_equal(151, candidate.call(\" How are yOu?\"))\n    assert_equal(327, candidate.call(\"You arE Very Smart\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_66_digitSum", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_digitSum\n    candidate = method(:digitSum)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(131, candidate.call(\"abAB\"))\n    assert_equal(67, candidate.call(\"abcCd\"))\n    assert_equal(69, candidate.call(\"helloE\"))\n    assert_equal(131, candidate.call(\"woArBld\"))\n    assert_equal(153, candidate.call(\"aAaaaXa\"))\n    assert_equal(151, candidate.call(\" How are yOu?\"))\n    assert_equal(327, candidate.call(\"You arE Very Smart\"))\n  end\nend\n"}
{"name": "HumanEval_21_rescale_to_unit", "language": "rb", "prompt": "# Given array of numbers (of at least two elements), apply a linear transform to that array,\n# such that the smallest number will become 0 and the largest will become 1\n# >>> rescale_to_unit.call([1.0, 2.0, 3.0, 4.0, 5.0])\n# [0.0, 0.25, 0.5, 0.75, 1.0]\ndef rescale_to_unit(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_21_rescale_to_unit.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rescale_to_unit\n    candidate = method(:rescale_to_unit)\n    assert_equal([0.0, 1.0], candidate.call([2.0, 49.9]))\n    assert_equal([1.0, 0.0], candidate.call([100.0, 49.9]))\n    assert_equal([0.0, 0.25, 0.5, 0.75, 1.0], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0]))\n    assert_equal([0.25, 0.0, 1.0, 0.5, 0.75], candidate.call([2.0, 1.0, 5.0, 3.0, 4.0]))\n    assert_equal([0.25, 0.0, 1.0, 0.5, 0.75], candidate.call([12.0, 11.0, 15.0, 13.0, 14.0]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_21_rescale_to_unit", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rescale_to_unit\n    candidate = method(:rescale_to_unit)\n    assert_equal([0.0, 1.0], candidate.call([2.0, 49.9]))\n    assert_equal([1.0, 0.0], candidate.call([100.0, 49.9]))\n    assert_equal([0.0, 0.25, 0.5, 0.75, 1.0], candidate.call([1.0, 2.0, 3.0, 4.0, 5.0]))\n    assert_equal([0.25, 0.0, 1.0, 0.5, 0.75], candidate.call([2.0, 1.0, 5.0, 3.0, 4.0]))\n    assert_equal([0.25, 0.0, 1.0, 0.5, 0.75], candidate.call([12.0, 11.0, 15.0, 13.0, 14.0]))\n  end\nend\n"}
{"name": "HumanEval_121_solution", "language": "rb", "prompt": "# Given a non-empty array of integers, return the sum of all of the odd elements that are in even positions.\n# Examples\n# >>> solution.call([5, 8, 7, 1])\n# 12\n# >>> solution.call([3, 3, 3, 3, 3])\n# 9\n# >>> solution.call([30, 13, 24, 321])\n# 0\ndef solution(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_121_solution.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solution\n    candidate = method(:solution)\n    assert_equal(12, candidate.call([5, 8, 7, 1]))\n    assert_equal(9, candidate.call([3, 3, 3, 3, 3]))\n    assert_equal(0, candidate.call([30, 13, 24, 321]))\n    assert_equal(5, candidate.call([5, 9]))\n    assert_equal(0, candidate.call([2, 4, 8]))\n    assert_equal(23, candidate.call([30, 13, 23, 32]))\n    assert_equal(3, candidate.call([3, 13, 2, 9]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_121_solution", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_solution\n    candidate = method(:solution)\n    assert_equal(12, candidate.call([5, 8, 7, 1]))\n    assert_equal(9, candidate.call([3, 3, 3, 3, 3]))\n    assert_equal(0, candidate.call([30, 13, 24, 321]))\n    assert_equal(5, candidate.call([5, 9]))\n    assert_equal(0, candidate.call([2, 4, 8]))\n    assert_equal(23, candidate.call([30, 13, 23, 32]))\n    assert_equal(3, candidate.call([3, 13, 2, 9]))\n  end\nend\n"}
{"name": "HumanEval_68_pluck", "language": "rb", "prompt": "# \"Given an array representing a branch of a tree that has non-negative integer nodes\n# your task is to pluck one of the nodes and return it.\n# The plucked node should be the node with the smallest even value.\n# If multiple nodes with the same smallest even value are found return the node that has smallest index.\n# The plucked node should be returned in an array, [ smalest_value, its index ],\n# If there are no even values or the given array is empty, return [].\n# Example 1:\n# >>> pluck.call([4, 2, 3])\n# [2, 1]\n# Explanation: 2 has the smallest even value, and 2 has the smallest index.\n# Example 2:\n# >>> pluck.call([1, 2, 3])\n# [2, 1]\n# Explanation: 2 has the smallest even value, and 2 has the smallest index.\n# Example 3:\n# >>> pluck.call([])\n# []\n# Example 4:\n# >>> pluck.call([5, 0, 3, 0, 4, 2])\n# [0, 1]\n# Explanation: 0 is the smallest value, but  there are two zeros,\n# so we will choose the first zero, which has the smallest index.\n# Constraints:\n# * 1 <= nodes.length <= 10000\n# * 0 <= node.value\ndef pluck(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_68_pluck.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_pluck\n    candidate = method(:pluck)\n    assert_equal([2, 1], candidate.call([4, 2, 3]))\n    assert_equal([2, 1], candidate.call([1, 2, 3]))\n    assert_equal([], candidate.call([]))\n    assert_equal([0, 1], candidate.call([5, 0, 3, 0, 4, 2]))\n    assert_equal([0, 3], candidate.call([1, 2, 3, 0, 5, 3]))\n    assert_equal([4, 1], candidate.call([5, 4, 8, 4, 8]))\n    assert_equal([6, 1], candidate.call([7, 6, 7, 1]))\n    assert_equal([], candidate.call([7, 9, 7, 1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_68_pluck", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_pluck\n    candidate = method(:pluck)\n    assert_equal([2, 1], candidate.call([4, 2, 3]))\n    assert_equal([2, 1], candidate.call([1, 2, 3]))\n    assert_equal([], candidate.call([]))\n    assert_equal([0, 1], candidate.call([5, 0, 3, 0, 4, 2]))\n    assert_equal([0, 3], candidate.call([1, 2, 3, 0, 5, 3]))\n    assert_equal([4, 1], candidate.call([5, 4, 8, 4, 8]))\n    assert_equal([6, 1], candidate.call([7, 6, 7, 1]))\n    assert_equal([], candidate.call([7, 9, 7, 1]))\n  end\nend\n"}
{"name": "HumanEval_147_get_max_triples", "language": "rb", "prompt": "# You are given a positive integer n. You have to create an integer array a of length n.\n# For each i (1 \u2264 i \u2264 n), the value of a[i] = i * i - i + 1.\n# Return the number of triples (a[i], a[j], a[k]) of a where i < j < k, \n# and a[i] + a[j] + a[k] is a multiple of 3.\n# Example :\n# >>> get_max_triples.call(5)\n# 1\n# Explanation: \n# a = [1, 3, 7, 13, 21]\n# The only valid triple is (1, 7, 13).\ndef get_max_triples(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_147_get_max_triples.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_max_triples\n    candidate = method(:get_max_triples)\n    assert_equal(1, candidate.call(5))\n    assert_equal(4, candidate.call(6))\n    assert_equal(36, candidate.call(10))\n    assert_equal(53361, candidate.call(100))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_147_get_max_triples", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_max_triples\n    candidate = method(:get_max_triples)\n    assert_equal(1, candidate.call(5))\n    assert_equal(4, candidate.call(6))\n    assert_equal(36, candidate.call(10))\n    assert_equal(53361, candidate.call(100))\n  end\nend\n"}
{"name": "HumanEval_110_exchange", "language": "rb", "prompt": "# In this problem, you will implement a function that takes two arrays of numbers,\n# and determines whether it is possible to perform an exchange of elements\n# between them to make lst1 an array of only even numbers.\n# There is no limit on the number of exchanged elements between lst1 and lst2.\n# If it is possible to exchange elements between the lst1 and lst2 to make\n# all the elements of lst1 to be even, return \"YES\".\n# Otherwise, return \"NO\".\n# For example:\n# >>> exchange.call([1, 2, 3, 4], [1, 2, 3, 4])\n# \"YES\"\n# >>> exchange.call([1, 2, 3, 4], [1, 5, 3, 4])\n# \"NO\"\n# It is assumed that the input arrays will be non-empty.\ndef exchange(lst1, lst2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_110_exchange.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_exchange\n    candidate = method(:exchange)\n    assert_equal(\"YES\", candidate.call([1, 2, 3, 4], [1, 2, 3, 4]))\n    assert_equal(\"NO\", candidate.call([1, 2, 3, 4], [1, 5, 3, 4]))\n    assert_equal(\"YES\", candidate.call([1, 2, 3, 4], [2, 1, 4, 3]))\n    assert_equal(\"YES\", candidate.call([5, 7, 3], [2, 6, 4]))\n    assert_equal(\"NO\", candidate.call([5, 7, 3], [2, 6, 3]))\n    assert_equal(\"NO\", candidate.call([3, 2, 6, 1, 8, 9], [3, 5, 5, 1, 1, 1]))\n    assert_equal(\"YES\", candidate.call([100, 200], [200, 200]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_110_exchange", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_exchange\n    candidate = method(:exchange)\n    assert_equal(\"YES\", candidate.call([1, 2, 3, 4], [1, 2, 3, 4]))\n    assert_equal(\"NO\", candidate.call([1, 2, 3, 4], [1, 5, 3, 4]))\n    assert_equal(\"YES\", candidate.call([1, 2, 3, 4], [2, 1, 4, 3]))\n    assert_equal(\"YES\", candidate.call([5, 7, 3], [2, 6, 4]))\n    assert_equal(\"NO\", candidate.call([5, 7, 3], [2, 6, 3]))\n    assert_equal(\"NO\", candidate.call([3, 2, 6, 1, 8, 9], [3, 5, 5, 1, 1, 1]))\n    assert_equal(\"YES\", candidate.call([100, 200], [200, 200]))\n  end\nend\n"}
{"name": "HumanEval_47_median", "language": "rb", "prompt": "# Return median of elements in the array l.\n# >>> median.call([3, 1, 2, 4, 5])\n# 3\n# >>> median.call([-10, 4, 6, 1000, 10, 20])\n# 15.0\ndef median(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_47_median.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_median\n    candidate = method(:median)\n    assert_equal(3, candidate.call([3, 1, 2, 4, 5]))\n    assert_equal(8.0, candidate.call([-10, 4, 6, 1000, 10, 20]))\n    assert_equal(5, candidate.call([5]))\n    assert_equal(5.5, candidate.call([6, 5]))\n    assert_equal(7, candidate.call([8, 1, 3, 9, 9, 2, 7]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_47_median", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_median\n    candidate = method(:median)\n    assert_equal(3, candidate.call([3, 1, 2, 4, 5]))\n    assert_equal(8.0, candidate.call([-10, 4, 6, 1000, 10, 20]))\n    assert_equal(5, candidate.call([5]))\n    assert_equal(5.5, candidate.call([6, 5]))\n    assert_equal(7, candidate.call([8, 1, 3, 9, 9, 2, 7]))\n  end\nend\n"}
{"name": "HumanEval_82_prime_length", "language": "rb", "prompt": "# Write a function that takes a string and returns true if the string\n# length is a prime number or false otherwise\n# Examples\n# >>> prime_length.call(\"Hello\")\n# true\n# >>> prime_length.call(\"abcdcba\")\n# true\n# >>> prime_length.call(\"kittens\")\n# true\n# >>> prime_length.call(\"orange\")\n# false\ndef prime_length(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_82_prime_length.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prime_length\n    candidate = method(:prime_length)\n    assert_equal(true, candidate.call(\"Hello\"))\n    assert_equal(true, candidate.call(\"abcdcba\"))\n    assert_equal(true, candidate.call(\"kittens\"))\n    assert_equal(false, candidate.call(\"orange\"))\n    assert_equal(true, candidate.call(\"wow\"))\n    assert_equal(true, candidate.call(\"world\"))\n    assert_equal(true, candidate.call(\"MadaM\"))\n    assert_equal(true, candidate.call(\"Wow\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(true, candidate.call(\"HI\"))\n    assert_equal(true, candidate.call(\"go\"))\n    assert_equal(false, candidate.call(\"gogo\"))\n    assert_equal(false, candidate.call(\"aaaaaaaaaaaaaaa\"))\n    assert_equal(true, candidate.call(\"Madam\"))\n    assert_equal(false, candidate.call(\"M\"))\n    assert_equal(false, candidate.call(\"0\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_82_prime_length", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prime_length\n    candidate = method(:prime_length)\n    assert_equal(true, candidate.call(\"Hello\"))\n    assert_equal(true, candidate.call(\"abcdcba\"))\n    assert_equal(true, candidate.call(\"kittens\"))\n    assert_equal(false, candidate.call(\"orange\"))\n    assert_equal(true, candidate.call(\"wow\"))\n    assert_equal(true, candidate.call(\"world\"))\n    assert_equal(true, candidate.call(\"MadaM\"))\n    assert_equal(true, candidate.call(\"Wow\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(true, candidate.call(\"HI\"))\n    assert_equal(true, candidate.call(\"go\"))\n    assert_equal(false, candidate.call(\"gogo\"))\n    assert_equal(false, candidate.call(\"aaaaaaaaaaaaaaa\"))\n    assert_equal(true, candidate.call(\"Madam\"))\n    assert_equal(false, candidate.call(\"M\"))\n    assert_equal(false, candidate.call(\"0\"))\n  end\nend\n"}
{"name": "HumanEval_73_smallest_change", "language": "rb", "prompt": "# Given an array arr of integers, find the minimum number of elements that\n# need to be changed to make the array palindromic. A palindromic array is an array that\n# is read the same backwards and forwards. In one change, you can change one element to any other element.\n# For example:\n# >>> smallest_change.call([1, 2, 3, 5, 4, 7, 9, 6])\n# 4\n# >>> smallest_change.call([1, 2, 3, 4, 3, 2, 2])\n# 1\n# >>> smallest_change.call([1, 2, 3, 2, 1])\n# 0\ndef smallest_change(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_73_smallest_change.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_smallest_change\n    candidate = method(:smallest_change)\n    assert_equal(4, candidate.call([1, 2, 3, 5, 4, 7, 9, 6]))\n    assert_equal(1, candidate.call([1, 2, 3, 4, 3, 2, 2]))\n    assert_equal(1, candidate.call([1, 4, 2]))\n    assert_equal(1, candidate.call([1, 4, 4, 2]))\n    assert_equal(0, candidate.call([1, 2, 3, 2, 1]))\n    assert_equal(0, candidate.call([3, 1, 1, 3]))\n    assert_equal(0, candidate.call([1]))\n    assert_equal(1, candidate.call([0, 1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_73_smallest_change", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_smallest_change\n    candidate = method(:smallest_change)\n    assert_equal(4, candidate.call([1, 2, 3, 5, 4, 7, 9, 6]))\n    assert_equal(1, candidate.call([1, 2, 3, 4, 3, 2, 2]))\n    assert_equal(1, candidate.call([1, 4, 2]))\n    assert_equal(1, candidate.call([1, 4, 4, 2]))\n    assert_equal(0, candidate.call([1, 2, 3, 2, 1]))\n    assert_equal(0, candidate.call([3, 1, 1, 3]))\n    assert_equal(0, candidate.call([1]))\n    assert_equal(1, candidate.call([0, 1]))\n  end\nend\n"}
{"name": "HumanEval_133_sum_squares", "language": "rb", "prompt": "# You are given an array of numbers.\n# You need to return the sum of squared numbers in the given array,\n# round each element in the array to the upper int(Ceiling) first.\n# Examples:\n# >>> lst.call([1.0, 2.0, 3.0])\n# 14\n# >>> lst.call([1.0, 4.0, 9.0])\n# 98\n# >>> lst.call([1.0, 3.0, 5.0, 7.0])\n# 84\n# >>> lst.call([1.4, 4.2, 0.0])\n# 29\n# >>> lst.call([-2.4, 1.0, 1.0])\n# 6\ndef sum_squares(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_133_sum_squares.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_squares\n    candidate = method(:sum_squares)\n    assert_equal(14, candidate.call([1.0, 2.0, 3.0]))\n    assert_equal(14, candidate.call([1.0, 2.0, 3.0]))\n    assert_equal(84, candidate.call([1.0, 3.0, 5.0, 7.0]))\n    assert_equal(29, candidate.call([1.4, 4.2, 0.0]))\n    assert_equal(6, candidate.call([-2.4, 1.0, 1.0]))\n    assert_equal(10230, candidate.call([100.0, 1.0, 15.0, 2.0]))\n    assert_equal(200000000, candidate.call([10000.0, 10000.0]))\n    assert_equal(75, candidate.call([-1.4, 4.6, 6.3]))\n    assert_equal(1086, candidate.call([-1.4, 17.9, 18.9, 19.9]))\n    assert_equal(0, candidate.call([0.0]))\n    assert_equal(1, candidate.call([-1.0]))\n    assert_equal(2, candidate.call([-1.0, 1.0, 0.0]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_133_sum_squares", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_squares\n    candidate = method(:sum_squares)\n    assert_equal(14, candidate.call([1.0, 2.0, 3.0]))\n    assert_equal(14, candidate.call([1.0, 2.0, 3.0]))\n    assert_equal(84, candidate.call([1.0, 3.0, 5.0, 7.0]))\n    assert_equal(29, candidate.call([1.4, 4.2, 0.0]))\n    assert_equal(6, candidate.call([-2.4, 1.0, 1.0]))\n    assert_equal(10230, candidate.call([100.0, 1.0, 15.0, 2.0]))\n    assert_equal(200000000, candidate.call([10000.0, 10000.0]))\n    assert_equal(75, candidate.call([-1.4, 4.6, 6.3]))\n    assert_equal(1086, candidate.call([-1.4, 17.9, 18.9, 19.9]))\n    assert_equal(0, candidate.call([0.0]))\n    assert_equal(1, candidate.call([-1.0]))\n    assert_equal(2, candidate.call([-1.0, 1.0, 0.0]))\n  end\nend\n"}
{"name": "HumanEval_141_file_name_check", "language": "rb", "prompt": "# Create a function which takes a string representing a file's name, and returns\n# 'Yes' if the the file's name is valid, and returns 'No' otherwise.\n# A file's name is considered to be valid if and only if all the following conditions \n# are met:\n# - There should not be more than three digits ('0'-'9') in the file's name.\n# - The file's name contains exactly one dot '.'\n# - The substring before the dot should not be empty, and it starts with a letter from \n# the latin alphapet ('a'-'z' and 'A'-'Z').\n# - The substring after the dot should be one of these: ['txt', 'exe', 'dll']\n# Examples:\n# >>> file_name_check.call(\"example.txt\")\n# \"Yes\"\n# >>> file_name_check.call(\"1example.dll\")\n# \"No\"\ndef file_name_check(file_name)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_141_file_name_check.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_file_name_check\n    candidate = method(:file_name_check)\n    assert_equal(\"Yes\", candidate.call(\"example.txt\"))\n    assert_equal(\"No\", candidate.call(\"1example.dll\"))\n    assert_equal(\"No\", candidate.call(\"s1sdf3.asd\"))\n    assert_equal(\"Yes\", candidate.call(\"K.dll\"))\n    assert_equal(\"Yes\", candidate.call(\"MY16FILE3.exe\"))\n    assert_equal(\"No\", candidate.call(\"His12FILE94.exe\"))\n    assert_equal(\"No\", candidate.call(\"_Y.txt\"))\n    assert_equal(\"No\", candidate.call(\"?aREYA.exe\"))\n    assert_equal(\"No\", candidate.call(\"/this_is_valid.dll\"))\n    assert_equal(\"No\", candidate.call(\"this_is_valid.wow\"))\n    assert_equal(\"Yes\", candidate.call(\"this_is_valid.txt\"))\n    assert_equal(\"No\", candidate.call(\"this_is_valid.txtexe\"))\n    assert_equal(\"No\", candidate.call(\"#this2_i4s_5valid.ten\"))\n    assert_equal(\"No\", candidate.call(\"@this1_is6_valid.exe\"))\n    assert_equal(\"No\", candidate.call(\"this_is_12valid.6exe4.txt\"))\n    assert_equal(\"No\", candidate.call(\"all.exe.txt\"))\n    assert_equal(\"Yes\", candidate.call(\"I563_No.exe\"))\n    assert_equal(\"Yes\", candidate.call(\"Is3youfault.txt\"))\n    assert_equal(\"Yes\", candidate.call(\"no_one#knows.dll\"))\n    assert_equal(\"No\", candidate.call(\"1I563_Yes3.exe\"))\n    assert_equal(\"No\", candidate.call(\"I563_Yes3.txtt\"))\n    assert_equal(\"No\", candidate.call(\"final..txt\"))\n    assert_equal(\"No\", candidate.call(\"final132\"))\n    assert_equal(\"No\", candidate.call(\"_f4indsartal132.\"))\n    assert_equal(\"No\", candidate.call(\".txt\"))\n    assert_equal(\"No\", candidate.call(\"s.\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_141_file_name_check", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_file_name_check\n    candidate = method(:file_name_check)\n    assert_equal(\"Yes\", candidate.call(\"example.txt\"))\n    assert_equal(\"No\", candidate.call(\"1example.dll\"))\n    assert_equal(\"No\", candidate.call(\"s1sdf3.asd\"))\n    assert_equal(\"Yes\", candidate.call(\"K.dll\"))\n    assert_equal(\"Yes\", candidate.call(\"MY16FILE3.exe\"))\n    assert_equal(\"No\", candidate.call(\"His12FILE94.exe\"))\n    assert_equal(\"No\", candidate.call(\"_Y.txt\"))\n    assert_equal(\"No\", candidate.call(\"?aREYA.exe\"))\n    assert_equal(\"No\", candidate.call(\"/this_is_valid.dll\"))\n    assert_equal(\"No\", candidate.call(\"this_is_valid.wow\"))\n    assert_equal(\"Yes\", candidate.call(\"this_is_valid.txt\"))\n    assert_equal(\"No\", candidate.call(\"this_is_valid.txtexe\"))\n    assert_equal(\"No\", candidate.call(\"#this2_i4s_5valid.ten\"))\n    assert_equal(\"No\", candidate.call(\"@this1_is6_valid.exe\"))\n    assert_equal(\"No\", candidate.call(\"this_is_12valid.6exe4.txt\"))\n    assert_equal(\"No\", candidate.call(\"all.exe.txt\"))\n    assert_equal(\"Yes\", candidate.call(\"I563_No.exe\"))\n    assert_equal(\"Yes\", candidate.call(\"Is3youfault.txt\"))\n    assert_equal(\"Yes\", candidate.call(\"no_one#knows.dll\"))\n    assert_equal(\"No\", candidate.call(\"1I563_Yes3.exe\"))\n    assert_equal(\"No\", candidate.call(\"I563_Yes3.txtt\"))\n    assert_equal(\"No\", candidate.call(\"final..txt\"))\n    assert_equal(\"No\", candidate.call(\"final132\"))\n    assert_equal(\"No\", candidate.call(\"_f4indsartal132.\"))\n    assert_equal(\"No\", candidate.call(\".txt\"))\n    assert_equal(\"No\", candidate.call(\"s.\"))\n  end\nend\n"}
{"name": "HumanEval_40_triples_sum_to_zero", "language": "rb", "prompt": "# triples_sum_to_zero takes an array of integers as an input.\n# it returns true if there are three distinct elements in the array that\n# sum to zero, and false otherwise.\n# >>> triples_sum_to_zero.call([1, 3, 5, 0])\n# false\n# >>> triples_sum_to_zero.call([1, 3, -2, 1])\n# true\n# >>> triples_sum_to_zero.call([1, 2, 3, 7])\n# false\n# >>> triples_sum_to_zero.call([2, 4, -5, 3, 9, 7])\n# true\n# >>> triples_sum_to_zero.call([1])\n# false\ndef triples_sum_to_zero(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_40_triples_sum_to_zero.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triples_sum_to_zero\n    candidate = method(:triples_sum_to_zero)\n    assert_equal(false, candidate.call([1, 3, 5, 0]))\n    assert_equal(false, candidate.call([1, 3, 5, -1]))\n    assert_equal(true, candidate.call([1, 3, -2, 1]))\n    assert_equal(false, candidate.call([1, 2, 3, 7]))\n    assert_equal(false, candidate.call([1, 2, 5, 7]))\n    assert_equal(true, candidate.call([2, 4, -5, 3, 9, 7]))\n    assert_equal(false, candidate.call([1]))\n    assert_equal(false, candidate.call([1, 3, 5, -100]))\n    assert_equal(false, candidate.call([100, 3, 5, -100]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_40_triples_sum_to_zero", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triples_sum_to_zero\n    candidate = method(:triples_sum_to_zero)\n    assert_equal(false, candidate.call([1, 3, 5, 0]))\n    assert_equal(false, candidate.call([1, 3, 5, -1]))\n    assert_equal(true, candidate.call([1, 3, -2, 1]))\n    assert_equal(false, candidate.call([1, 2, 3, 7]))\n    assert_equal(false, candidate.call([1, 2, 5, 7]))\n    assert_equal(true, candidate.call([2, 4, -5, 3, 9, 7]))\n    assert_equal(false, candidate.call([1]))\n    assert_equal(false, candidate.call([1, 3, 5, -100]))\n    assert_equal(false, candidate.call([100, 3, 5, -100]))\n  end\nend\n"}
{"name": "HumanEval_127_intersection", "language": "rb", "prompt": "# You are given two intervals,\n# where each interval is a pair of integers. For example, interval = (start, end) = (1, 2).\n# The given intervals are closed which means that the interval (start, end)\n# includes both start and end.\n# For each given interval, it is assumed that its start is less or equal its end.\n# Your task is to determine whether the length of intersection of these two \n# intervals is a prime number.\n# Example, the intersection of the intervals (1, 3), (2, 4) is (2, 3)\n# which its length is 1, which not a prime number.\n# If the length of the intersection is a prime number, return \"YES\",\n# otherwise, return \"NO\".\n# If the two intervals don't intersect, return \"NO\".\n# [input/output] samples:\n# >>> intersection.call([1, 2], [2, 3])\n# \"NO\"\n# >>> intersection.call([-1, 1], [0, 4])\n# \"NO\"\n# >>> intersection.call([-3, -1], [-5, 5])\n# \"YES\"\ndef intersection(interval1, interval2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_127_intersection.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_intersection\n    candidate = method(:intersection)\n    assert_equal(\"NO\", candidate.call([1, 2], [2, 3]))\n    assert_equal(\"NO\", candidate.call([-1, 1], [0, 4]))\n    assert_equal(\"YES\", candidate.call([-3, -1], [-5, 5]))\n    assert_equal(\"YES\", candidate.call([-2, 2], [-4, 0]))\n    assert_equal(\"NO\", candidate.call([-11, 2], [-1, -1]))\n    assert_equal(\"NO\", candidate.call([1, 2], [3, 5]))\n    assert_equal(\"NO\", candidate.call([1, 2], [1, 2]))\n    assert_equal(\"NO\", candidate.call([-2, -2], [-3, -2]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_127_intersection", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_intersection\n    candidate = method(:intersection)\n    assert_equal(\"NO\", candidate.call([1, 2], [2, 3]))\n    assert_equal(\"NO\", candidate.call([-1, 1], [0, 4]))\n    assert_equal(\"YES\", candidate.call([-3, -1], [-5, 5]))\n    assert_equal(\"YES\", candidate.call([-2, 2], [-4, 0]))\n    assert_equal(\"NO\", candidate.call([-11, 2], [-1, -1]))\n    assert_equal(\"NO\", candidate.call([1, 2], [3, 5]))\n    assert_equal(\"NO\", candidate.call([1, 2], [1, 2]))\n    assert_equal(\"NO\", candidate.call([-2, -2], [-3, -2]))\n  end\nend\n"}
{"name": "HumanEval_1_separate_paren_groups", "language": "rb", "prompt": "# Input to this function is a string containing multiple groups of nested parentheses. Your goal is to\n# separate those group into separate strings and return the array of those.\n# Separate groups are balanced (each open brace is properly closed) and not nested within each other\n# Ignore any spaces in the input string.\n# >>> separate_paren_groups.call(\"( ) (( )) (( )( ))\")\n# [\"()\", \"(())\", \"(()())\"]\ndef separate_paren_groups(paren_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_1_separate_paren_groups.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_separate_paren_groups\n    candidate = method(:separate_paren_groups)\n    assert_equal([\"(()())\", \"((()))\", \"()\", \"((())()())\"], candidate.call(\"(()()) ((())) () ((())()())\"))\n    assert_equal([\"()\", \"(())\", \"((()))\", \"(((())))\"], candidate.call(\"() (()) ((())) (((())))\"))\n    assert_equal([\"(()(())((())))\"], candidate.call(\"(()(())((())))\"))\n    assert_equal([\"()\", \"(())\", \"(()())\"], candidate.call(\"( ) (( )) (( )( ))\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_1_separate_paren_groups", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_separate_paren_groups\n    candidate = method(:separate_paren_groups)\n    assert_equal([\"(()())\", \"((()))\", \"()\", \"((())()())\"], candidate.call(\"(()()) ((())) () ((())()())\"))\n    assert_equal([\"()\", \"(())\", \"((()))\", \"(((())))\"], candidate.call(\"() (()) ((())) (((())))\"))\n    assert_equal([\"(()(())((())))\"], candidate.call(\"(()(())((())))\"))\n    assert_equal([\"()\", \"(())\", \"(()())\"], candidate.call(\"( ) (( )) (( )( ))\"))\n  end\nend\n"}
{"name": "HumanEval_152_compare", "language": "rb", "prompt": "# I think we all remember that feeling when the result of some long-awaited\n# event is finally known. The feelings and thoughts you have at that moment are\n# definitely worth noting down and comparing.\n# Your task is to determine if a person correctly guessed the results of a number of matches.\n# You are given two arrays of scores and guesses of equal length, where each index shows a match. \n# Return an array of the same length denoting how far off each guess was. If they have guessed correctly,\n# the value is 0, and if not, the value is the absolute difference between the guess and the score.\n# example:\n# >>> compare.call([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2])\n# [0, 0, 0, 0, 3, 3]\n# >>> compare.call([0, 5, 0, 0, 0, 4], [4, 1, 1, 0, 0, -2])\n# [4, 4, 1, 0, 0, 6]\ndef compare(game, guess)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_152_compare.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_compare\n    candidate = method(:compare)\n    assert_equal([0, 0, 0, 0, 3, 3], candidate.call([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2]))\n    assert_equal([0, 0, 0, 0, 0, 0], candidate.call([0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]))\n    assert_equal([2, 4, 6], candidate.call([1, 2, 3], [-1, -2, -3]))\n    assert_equal([2, 0, 0, 1], candidate.call([1, 2, 3, 5], [-1, 2, 3, 4]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_152_compare", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_compare\n    candidate = method(:compare)\n    assert_equal([0, 0, 0, 0, 3, 3], candidate.call([1, 2, 3, 4, 5, 1], [1, 2, 3, 4, 2, -2]))\n    assert_equal([0, 0, 0, 0, 0, 0], candidate.call([0, 0, 0, 0, 0, 0], [0, 0, 0, 0, 0, 0]))\n    assert_equal([2, 4, 6], candidate.call([1, 2, 3], [-1, -2, -3]))\n    assert_equal([2, 0, 0, 1], candidate.call([1, 2, 3, 5], [-1, 2, 3, 4]))\n  end\nend\n"}
{"name": "HumanEval_83_starts_one_ends", "language": "rb", "prompt": "# Given a positive integer n, return the count of the numbers of n-digit\n# positive integers that start or end with 1.\ndef starts_one_ends(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_83_starts_one_ends.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_starts_one_ends\n    candidate = method(:starts_one_ends)\n    assert_equal(1, candidate.call(1))\n    assert_equal(18, candidate.call(2))\n    assert_equal(180, candidate.call(3))\n    assert_equal(1800, candidate.call(4))\n    assert_equal(18000, candidate.call(5))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_83_starts_one_ends", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_starts_one_ends\n    candidate = method(:starts_one_ends)\n    assert_equal(1, candidate.call(1))\n    assert_equal(18, candidate.call(2))\n    assert_equal(180, candidate.call(3))\n    assert_equal(1800, candidate.call(4))\n    assert_equal(18000, candidate.call(5))\n  end\nend\n"}
{"name": "HumanEval_134_check_if_last_char_is_a_letter", "language": "rb", "prompt": "# Create a function that returns true if the last character\n# of a given string is an alphabetical character and is not\n# a part of a word, and false otherwise.\n# Note: \"word\" is a group of characters separated by space.\n# Examples:\n# >>> check_if_last_char_is_a_letter.call(\"apple pie\")\n# false\n# >>> check_if_last_char_is_a_letter.call(\"apple pi e\")\n# true\n# >>> check_if_last_char_is_a_letter.call(\"apple pi e \")\n# false\n# >>> check_if_last_char_is_a_letter.call(\"\")\n# false\ndef check_if_last_char_is_a_letter(txt)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_134_check_if_last_char_is_a_letter.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_check_if_last_char_is_a_letter\n    candidate = method(:check_if_last_char_is_a_letter)\n    assert_equal(false, candidate.call(\"apple\"))\n    assert_equal(true, candidate.call(\"apple pi e\"))\n    assert_equal(false, candidate.call(\"eeeee\"))\n    assert_equal(true, candidate.call(\"A\"))\n    assert_equal(false, candidate.call(\"Pumpkin pie \"))\n    assert_equal(false, candidate.call(\"Pumpkin pie 1\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"eeeee e \"))\n    assert_equal(false, candidate.call(\"apple pie\"))\n    assert_equal(false, candidate.call(\"apple pi e \"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_134_check_if_last_char_is_a_letter", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_check_if_last_char_is_a_letter\n    candidate = method(:check_if_last_char_is_a_letter)\n    assert_equal(false, candidate.call(\"apple\"))\n    assert_equal(true, candidate.call(\"apple pi e\"))\n    assert_equal(false, candidate.call(\"eeeee\"))\n    assert_equal(true, candidate.call(\"A\"))\n    assert_equal(false, candidate.call(\"Pumpkin pie \"))\n    assert_equal(false, candidate.call(\"Pumpkin pie 1\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"eeeee e \"))\n    assert_equal(false, candidate.call(\"apple pie\"))\n    assert_equal(false, candidate.call(\"apple pi e \"))\n  end\nend\n"}
{"name": "HumanEval_124_valid_date", "language": "rb", "prompt": "# You have to write a function which validates a given date string and\n# returns true if the date is valid otherwise false.\n# The date is valid if all of the following rules are satisfied:\n# 1. The date string is not empty.\n# 2. The number of days is not less than 1 or higher than 31 days for months 1,3,5,7,8,10,12. And the number of days is not less than 1 or higher than 30 days for months 4,6,9,11. And, the number of days is not less than 1 or higher than 29 for the month 2.\n# 3. The months should not be less than 1 or higher than 12.\n# 4. The date should be in the format: mm-dd-yyyy\n# >>> valid_date.call(\"03-11-2000\")\n# true\n# >>> valid_date.call(\"15-01-2012\")\n# false\n# >>> valid_date.call(\"04-0-2040\")\n# false\n# >>> valid_date.call(\"06-04-2020\")\n# true\n# >>> valid_date.call(\"06/04/2020\")\n# false\ndef valid_date(date)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_124_valid_date.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_valid_date\n    candidate = method(:valid_date)\n    assert_equal(true, candidate.call(\"03-11-2000\"))\n    assert_equal(false, candidate.call(\"15-01-2012\"))\n    assert_equal(false, candidate.call(\"04-0-2040\"))\n    assert_equal(true, candidate.call(\"06-04-2020\"))\n    assert_equal(true, candidate.call(\"01-01-2007\"))\n    assert_equal(false, candidate.call(\"03-32-2011\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"04-31-3000\"))\n    assert_equal(true, candidate.call(\"06-06-2005\"))\n    assert_equal(false, candidate.call(\"21-31-2000\"))\n    assert_equal(true, candidate.call(\"04-12-2003\"))\n    assert_equal(false, candidate.call(\"04122003\"))\n    assert_equal(false, candidate.call(\"20030412\"))\n    assert_equal(false, candidate.call(\"2003-04\"))\n    assert_equal(false, candidate.call(\"2003-04-12\"))\n    assert_equal(false, candidate.call(\"04-2003\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_124_valid_date", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_valid_date\n    candidate = method(:valid_date)\n    assert_equal(true, candidate.call(\"03-11-2000\"))\n    assert_equal(false, candidate.call(\"15-01-2012\"))\n    assert_equal(false, candidate.call(\"04-0-2040\"))\n    assert_equal(true, candidate.call(\"06-04-2020\"))\n    assert_equal(true, candidate.call(\"01-01-2007\"))\n    assert_equal(false, candidate.call(\"03-32-2011\"))\n    assert_equal(false, candidate.call(\"\"))\n    assert_equal(false, candidate.call(\"04-31-3000\"))\n    assert_equal(true, candidate.call(\"06-06-2005\"))\n    assert_equal(false, candidate.call(\"21-31-2000\"))\n    assert_equal(true, candidate.call(\"04-12-2003\"))\n    assert_equal(false, candidate.call(\"04122003\"))\n    assert_equal(false, candidate.call(\"20030412\"))\n    assert_equal(false, candidate.call(\"2003-04\"))\n    assert_equal(false, candidate.call(\"2003-04-12\"))\n    assert_equal(false, candidate.call(\"04-2003\"))\n  end\nend\n"}
{"name": "HumanEval_108_count_nums", "language": "rb", "prompt": "# Write a function count_nums which takes an array of integers and returns\n# the number of elements which has a sum of digits > 0.\n# If a number is negative, then its first signed digit will be negative:\n# e.g. -123 has signed digits -1, 2, and 3.\n# >>> count_nums.call([])\n# 0\n# >>> count_nums.call([-1, 11, -11])\n# 1\n# >>> count_nums.call([1, 1, 2])\n# 3\ndef count_nums(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_108_count_nums.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_nums\n    candidate = method(:count_nums)\n    assert_equal(0, candidate.call([]))\n    assert_equal(0, candidate.call([-1, -2, 0]))\n    assert_equal(6, candidate.call([1, 1, 2, -2, 3, 4, 5]))\n    assert_equal(5, candidate.call([1, 6, 9, -6, 0, 1, 5]))\n    assert_equal(4, candidate.call([1, 100, 98, -7, 1, -1]))\n    assert_equal(5, candidate.call([12, 23, 34, -45, -56, 0]))\n    assert_equal(1, candidate.call([0, 1]))\n    assert_equal(1, candidate.call([1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_108_count_nums", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_nums\n    candidate = method(:count_nums)\n    assert_equal(0, candidate.call([]))\n    assert_equal(0, candidate.call([-1, -2, 0]))\n    assert_equal(6, candidate.call([1, 1, 2, -2, 3, 4, 5]))\n    assert_equal(5, candidate.call([1, 6, 9, -6, 0, 1, 5]))\n    assert_equal(4, candidate.call([1, 100, 98, -7, 1, -1]))\n    assert_equal(5, candidate.call([12, 23, 34, -45, -56, 0]))\n    assert_equal(1, candidate.call([0, 1]))\n    assert_equal(1, candidate.call([1]))\n  end\nend\n"}
{"name": "HumanEval_86_anti_shuffle", "language": "rb", "prompt": "# Write a function that takes a string and returns an ordered version of it.\n# Ordered version of string, is a string where all words (separated by space)\n# are replaced by a new word where all the characters arranged in\n# ascending order based on ascii value.\n# Note: You should keep the order of words and blank spaces in the sentence.\n# For example:\n# >>> anti_shuffle.call(\"Hi\")\n# \"Hi\"\n# >>> anti_shuffle.call(\"hello\")\n# \"ehllo\"\n# >>> anti_shuffle.call(\"Hello World!!!\")\n# \"Hello !!!Wdlor\"\ndef anti_shuffle(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_86_anti_shuffle.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_anti_shuffle\n    candidate = method(:anti_shuffle)\n    assert_equal(\"Hi\", candidate.call(\"Hi\"))\n    assert_equal(\"ehllo\", candidate.call(\"hello\"))\n    assert_equal(\"bemnru\", candidate.call(\"number\"))\n    assert_equal(\"abcd\", candidate.call(\"abcd\"))\n    assert_equal(\"Hello !!!Wdlor\", candidate.call(\"Hello World!!!\"))\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\".Hi My aemn is Meirst .Rboot How aer ?ouy\", candidate.call(\"Hi. My name is Mister Robot. How are you?\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_86_anti_shuffle", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_anti_shuffle\n    candidate = method(:anti_shuffle)\n    assert_equal(\"Hi\", candidate.call(\"Hi\"))\n    assert_equal(\"ehllo\", candidate.call(\"hello\"))\n    assert_equal(\"bemnru\", candidate.call(\"number\"))\n    assert_equal(\"abcd\", candidate.call(\"abcd\"))\n    assert_equal(\"Hello !!!Wdlor\", candidate.call(\"Hello World!!!\"))\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\".Hi My aemn is Meirst .Rboot How aer ?ouy\", candidate.call(\"Hi. My name is Mister Robot. How are you?\"))\n  end\nend\n"}
{"name": "HumanEval_48_is_palindrome", "language": "rb", "prompt": "# Checks if given string is a palindrome\n# >>> is_palindrome.call(\"\")\n# true\n# >>> is_palindrome.call(\"aba\")\n# true\n# >>> is_palindrome.call(\"aaaaa\")\n# true\n# >>> is_palindrome.call(\"zbcd\")\n# false\ndef is_palindrome(text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_48_is_palindrome.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_palindrome\n    candidate = method(:is_palindrome)\n    assert_equal(true, candidate.call(\"\"))\n    assert_equal(true, candidate.call(\"aba\"))\n    assert_equal(true, candidate.call(\"aaaaa\"))\n    assert_equal(false, candidate.call(\"zbcd\"))\n    assert_equal(true, candidate.call(\"xywyx\"))\n    assert_equal(false, candidate.call(\"xywyz\"))\n    assert_equal(false, candidate.call(\"xywzx\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_48_is_palindrome", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_palindrome\n    candidate = method(:is_palindrome)\n    assert_equal(true, candidate.call(\"\"))\n    assert_equal(true, candidate.call(\"aba\"))\n    assert_equal(true, candidate.call(\"aaaaa\"))\n    assert_equal(false, candidate.call(\"zbcd\"))\n    assert_equal(true, candidate.call(\"xywyx\"))\n    assert_equal(false, candidate.call(\"xywyz\"))\n    assert_equal(false, candidate.call(\"xywzx\"))\n  end\nend\n"}
{"name": "HumanEval_118_get_closest_vowel", "language": "rb", "prompt": "# You are given a word. Your task is to find the closest vowel that stands between \n# two consonants from the right side of the word (case sensitive).\n# Vowels in the beginning and ending doesn't count. Return empty string if you didn't\n# find any vowel met the above condition. \n# You may assume that the given string contains English letter only.\n# Example:\n# >>> get_closest_vowel.call(\"yogurt\")\n# \"u\"\n# >>> get_closest_vowel.call(\"FULL\")\n# \"U\"\n# >>> get_closest_vowel.call(\"quick\")\n# \"\"\n# >>> get_closest_vowel.call(\"ab\")\n# \"\"\ndef get_closest_vowel(word)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_118_get_closest_vowel.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_closest_vowel\n    candidate = method(:get_closest_vowel)\n    assert_equal(\"u\", candidate.call(\"yogurt\"))\n    assert_equal(\"u\", candidate.call(\"full\"))\n    assert_equal(\"\", candidate.call(\"easy\"))\n    assert_equal(\"\", candidate.call(\"eAsy\"))\n    assert_equal(\"\", candidate.call(\"ali\"))\n    assert_equal(\"a\", candidate.call(\"bad\"))\n    assert_equal(\"o\", candidate.call(\"most\"))\n    assert_equal(\"\", candidate.call(\"ab\"))\n    assert_equal(\"\", candidate.call(\"ba\"))\n    assert_equal(\"\", candidate.call(\"quick\"))\n    assert_equal(\"i\", candidate.call(\"anime\"))\n    assert_equal(\"\", candidate.call(\"Asia\"))\n    assert_equal(\"o\", candidate.call(\"Above\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_118_get_closest_vowel", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_closest_vowel\n    candidate = method(:get_closest_vowel)\n    assert_equal(\"u\", candidate.call(\"yogurt\"))\n    assert_equal(\"u\", candidate.call(\"full\"))\n    assert_equal(\"\", candidate.call(\"easy\"))\n    assert_equal(\"\", candidate.call(\"eAsy\"))\n    assert_equal(\"\", candidate.call(\"ali\"))\n    assert_equal(\"a\", candidate.call(\"bad\"))\n    assert_equal(\"o\", candidate.call(\"most\"))\n    assert_equal(\"\", candidate.call(\"ab\"))\n    assert_equal(\"\", candidate.call(\"ba\"))\n    assert_equal(\"\", candidate.call(\"quick\"))\n    assert_equal(\"i\", candidate.call(\"anime\"))\n    assert_equal(\"\", candidate.call(\"Asia\"))\n    assert_equal(\"o\", candidate.call(\"Above\"))\n  end\nend\n"}
{"name": "HumanEval_31_is_prime", "language": "rb", "prompt": "# Return true if a given number is prime, and false otherwise.\n# >>> is_prime.call(6)\n# false\n# >>> is_prime.call(101)\n# true\n# >>> is_prime.call(11)\n# true\n# >>> is_prime.call(13441)\n# true\n# >>> is_prime.call(61)\n# true\n# >>> is_prime.call(4)\n# false\n# >>> is_prime.call(1)\n# false\ndef is_prime(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_31_is_prime.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_prime\n    candidate = method(:is_prime)\n    assert_equal(false, candidate.call(6))\n    assert_equal(true, candidate.call(101))\n    assert_equal(true, candidate.call(11))\n    assert_equal(true, candidate.call(13441))\n    assert_equal(true, candidate.call(61))\n    assert_equal(false, candidate.call(4))\n    assert_equal(false, candidate.call(1))\n    assert_equal(true, candidate.call(5))\n    assert_equal(true, candidate.call(11))\n    assert_equal(true, candidate.call(17))\n    assert_equal(false, candidate.call(85))\n    assert_equal(false, candidate.call(77))\n    assert_equal(false, candidate.call(255379))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_31_is_prime", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_prime\n    candidate = method(:is_prime)\n    assert_equal(false, candidate.call(6))\n    assert_equal(true, candidate.call(101))\n    assert_equal(true, candidate.call(11))\n    assert_equal(true, candidate.call(13441))\n    assert_equal(true, candidate.call(61))\n    assert_equal(false, candidate.call(4))\n    assert_equal(false, candidate.call(1))\n    assert_equal(true, candidate.call(5))\n    assert_equal(true, candidate.call(11))\n    assert_equal(true, candidate.call(17))\n    assert_equal(false, candidate.call(85))\n    assert_equal(false, candidate.call(77))\n    assert_equal(false, candidate.call(255379))\n  end\nend\n"}
{"name": "HumanEval_144_simplify", "language": "rb", "prompt": "# Your task is to implement a function that will simplify the expression\n# x * n. The function returns true if x * n evaluates to a whole number and false\n# otherwise. Both x and n, are string representation of a fraction, and have the following format,\n# <numerator>/<denominator> where both numerator and denominator are positive whole numbers.\n# You can assume that x, and n are valid fractions, and do not have zero as denominator.\n# >>> simplify.call(\"1/5\", \"5/1\")\n# true\n# >>> simplify.call(\"1/6\", \"2/1\")\n# false\n# >>> simplify.call(\"7/10\", \"10/2\")\n# false\ndef simplify(x, n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_144_simplify.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_simplify\n    candidate = method(:simplify)\n    assert_equal(true, candidate.call(\"1/5\", \"5/1\"))\n    assert_equal(false, candidate.call(\"1/6\", \"2/1\"))\n    assert_equal(true, candidate.call(\"5/1\", \"3/1\"))\n    assert_equal(false, candidate.call(\"7/10\", \"10/2\"))\n    assert_equal(true, candidate.call(\"2/10\", \"50/10\"))\n    assert_equal(true, candidate.call(\"7/2\", \"4/2\"))\n    assert_equal(true, candidate.call(\"11/6\", \"6/1\"))\n    assert_equal(false, candidate.call(\"2/3\", \"5/2\"))\n    assert_equal(false, candidate.call(\"5/2\", \"3/5\"))\n    assert_equal(true, candidate.call(\"2/4\", \"8/4\"))\n    assert_equal(true, candidate.call(\"2/4\", \"4/2\"))\n    assert_equal(true, candidate.call(\"1/5\", \"5/1\"))\n    assert_equal(false, candidate.call(\"1/5\", \"1/5\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_144_simplify", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_simplify\n    candidate = method(:simplify)\n    assert_equal(true, candidate.call(\"1/5\", \"5/1\"))\n    assert_equal(false, candidate.call(\"1/6\", \"2/1\"))\n    assert_equal(true, candidate.call(\"5/1\", \"3/1\"))\n    assert_equal(false, candidate.call(\"7/10\", \"10/2\"))\n    assert_equal(true, candidate.call(\"2/10\", \"50/10\"))\n    assert_equal(true, candidate.call(\"7/2\", \"4/2\"))\n    assert_equal(true, candidate.call(\"11/6\", \"6/1\"))\n    assert_equal(false, candidate.call(\"2/3\", \"5/2\"))\n    assert_equal(false, candidate.call(\"5/2\", \"3/5\"))\n    assert_equal(true, candidate.call(\"2/4\", \"8/4\"))\n    assert_equal(true, candidate.call(\"2/4\", \"4/2\"))\n    assert_equal(true, candidate.call(\"1/5\", \"5/1\"))\n    assert_equal(false, candidate.call(\"1/5\", \"1/5\"))\n  end\nend\n"}
{"name": "HumanEval_78_hex_key", "language": "rb", "prompt": "# You have been tasked to write a function that receives \n# a hexadecimal number as a string and counts the number of hexadecimal \n# digits that are primes (prime number, or a prime, is a natural number \n# greater than 1 that is not a product of two smaller natural numbers).\n# Hexadecimal digits are 0, 1, 2, 3, 4, 5, 6, 7, 8, 9, A, B, C, D, E, F.\n# Prime numbers are 2, 3, 5, 7, 11, 13, 17,...\n# So you have to determine a number of the following digits: 2, 3, 5, 7, \n# B (=decimal 11), D (=decimal 13).\n# Note: you may assume the input is always correct or empty string, \n# and symbols A,B,C,D,E,F are always uppercase.\n# Examples:\n# >>> hex_key.call(\"AB\")\n# 1\n# >>> hex_key.call(\"1077E\")\n# 2\n# >>> hex_key.call(\"ABED1A33\")\n# 4\n# >>> hex_key.call(\"123456789ABCDEF0\")\n# 6\n# >>> hex_key.call(\"2020\")\n# 2\ndef hex_key(num)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_78_hex_key.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_hex_key\n    candidate = method(:hex_key)\n    assert_equal(1, candidate.call(\"AB\"))\n    assert_equal(2, candidate.call(\"1077E\"))\n    assert_equal(4, candidate.call(\"ABED1A33\"))\n    assert_equal(2, candidate.call(\"2020\"))\n    assert_equal(6, candidate.call(\"123456789ABCDEF0\"))\n    assert_equal(12, candidate.call(\"112233445566778899AABBCCDDEEFF00\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_78_hex_key", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_hex_key\n    candidate = method(:hex_key)\n    assert_equal(1, candidate.call(\"AB\"))\n    assert_equal(2, candidate.call(\"1077E\"))\n    assert_equal(4, candidate.call(\"ABED1A33\"))\n    assert_equal(2, candidate.call(\"2020\"))\n    assert_equal(6, candidate.call(\"123456789ABCDEF0\"))\n    assert_equal(12, candidate.call(\"112233445566778899AABBCCDDEEFF00\"))\n  end\nend\n"}
{"name": "HumanEval_143_words_in_sentence", "language": "rb", "prompt": "# You are given a string representing a sentence,\n# the sentence contains some words separated by a space,\n# and you have to return a string that contains the words from the original sentence,\n# whose lengths are prime numbers,\n# the order of the words in the new string should be the same as the original one.\n# Example 1:\n# >>> words_in_sentence.call(\"This is a test\")\n# \"is\"\n# Example 2:\n# >>> words_in_sentence.call(\"lets go for swimming\")\n# \"go for\"\n# Constraints:\n# * 1 <= len(sentence) <= 100\n# * sentence contains only letters\ndef words_in_sentence(sentence)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_143_words_in_sentence.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_words_in_sentence\n    candidate = method(:words_in_sentence)\n    assert_equal(\"is\", candidate.call(\"This is a test\"))\n    assert_equal(\"go for\", candidate.call(\"lets go for swimming\"))\n    assert_equal(\"there is no place\", candidate.call(\"there is no place available here\"))\n    assert_equal(\"Hi am Hussein\", candidate.call(\"Hi I am Hussein\"))\n    assert_equal(\"go for it\", candidate.call(\"go for it\"))\n    assert_equal(\"\", candidate.call(\"here\"))\n    assert_equal(\"is\", candidate.call(\"here is\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_143_words_in_sentence", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_words_in_sentence\n    candidate = method(:words_in_sentence)\n    assert_equal(\"is\", candidate.call(\"This is a test\"))\n    assert_equal(\"go for\", candidate.call(\"lets go for swimming\"))\n    assert_equal(\"there is no place\", candidate.call(\"there is no place available here\"))\n    assert_equal(\"Hi am Hussein\", candidate.call(\"Hi I am Hussein\"))\n    assert_equal(\"go for it\", candidate.call(\"go for it\"))\n    assert_equal(\"\", candidate.call(\"here\"))\n    assert_equal(\"is\", candidate.call(\"here is\"))\n  end\nend\n"}
{"name": "HumanEval_111_histogram", "language": "rb", "prompt": "# Given a string representing a space separated lowercase letters, return a hash\n# of the letter with the most repetition and containing the corresponding count.\n# If several letters have the same occurrence, return all of them.\n# Example:\n# >>> histogram.call(\"a b c\")\n# {\"a\" => 1, \"b\" => 1, \"c\" => 1}\n# >>> histogram.call(\"a b b a\")\n# {\"a\" => 2, \"b\" => 2}\n# >>> histogram.call(\"a b c a b\")\n# {\"a\" => 2, \"b\" => 2}\n# >>> histogram.call(\"b b b b a\")\n# {\"b\" => 4}\n# >>> histogram.call(\"\")\n# {}\ndef histogram(test)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_111_histogram.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_histogram\n    candidate = method(:histogram)\n    assert_equal({\"a\" => 2, \"b\" => 2}, candidate.call(\"a b b a\"))\n    assert_equal({\"a\" => 2, \"b\" => 2}, candidate.call(\"a b c a b\"))\n    assert_equal({\"a\" => 1, \"b\" => 1, \"c\" => 1, \"d\" => 1, \"g\" => 1}, candidate.call(\"a b c d g\"))\n    assert_equal({\"r\" => 1, \"t\" => 1, \"g\" => 1}, candidate.call(\"r t g\"))\n    assert_equal({\"b\" => 4}, candidate.call(\"b b b b a\"))\n    assert_equal({\"r\" => 1, \"t\" => 1, \"g\" => 1}, candidate.call(\"r t g\"))\n    assert_equal({}, candidate.call(\"\"))\n    assert_equal({\"a\" => 1}, candidate.call(\"a\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_111_histogram", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_histogram\n    candidate = method(:histogram)\n    assert_equal({\"a\" => 2, \"b\" => 2}, candidate.call(\"a b b a\"))\n    assert_equal({\"a\" => 2, \"b\" => 2}, candidate.call(\"a b c a b\"))\n    assert_equal({\"a\" => 1, \"b\" => 1, \"c\" => 1, \"d\" => 1, \"g\" => 1}, candidate.call(\"a b c d g\"))\n    assert_equal({\"r\" => 1, \"t\" => 1, \"g\" => 1}, candidate.call(\"r t g\"))\n    assert_equal({\"b\" => 4}, candidate.call(\"b b b b a\"))\n    assert_equal({\"r\" => 1, \"t\" => 1, \"g\" => 1}, candidate.call(\"r t g\"))\n    assert_equal({}, candidate.call(\"\"))\n    assert_equal({\"a\" => 1}, candidate.call(\"a\"))\n  end\nend\n"}
{"name": "HumanEval_87_get_row", "language": "rb", "prompt": "# You are given a 2 dimensional data, as a nested arrays,\n# which is similar to matrix, however, unlike matrices,\n# each row may contain a different number of columns.\n# Given lst, and integer x, find integers x in the array,\n# and return array of arrays, [(x1, y1), (x2, y2) ...] such that\n# each array is a coordinate - (row, columns), starting with 0.\n# Sort coordinates initially by rows in ascending order.\n# Also, sort coordinates of the row by columns in descending order.\n# Examples:\n# >>> get_row.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1)\n# [[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]]\n# >>> get_row.call([], 1)\n# []\n# >>> get_row.call([[], [1], [1, 2, 3]], 3)\n# [[2, 2]]\ndef get_row(lst, x)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_87_get_row.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_row\n    candidate = method(:get_row)\n    assert_equal([[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1))\n    assert_equal([[0, 1], [1, 1], [2, 1], [3, 1], [4, 1], [5, 1]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6]], 2))\n    assert_equal([[0, 0], [1, 0], [2, 1], [2, 0], [3, 2], [3, 0], [4, 3], [4, 0], [5, 4], [5, 0], [6, 5], [6, 0]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 1, 3, 4, 5, 6], [1, 2, 1, 4, 5, 6], [1, 2, 3, 1, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1))\n    assert_equal([], candidate.call([], 1))\n    assert_equal([], candidate.call([[1]], 2))\n    assert_equal([[2, 2]], candidate.call([[], [1], [1, 2, 3]], 3))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_87_get_row", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_row\n    candidate = method(:get_row)\n    assert_equal([[0, 0], [1, 4], [1, 0], [2, 5], [2, 0]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1))\n    assert_equal([[0, 1], [1, 1], [2, 1], [3, 1], [4, 1], [5, 1]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6]], 2))\n    assert_equal([[0, 0], [1, 0], [2, 1], [2, 0], [3, 2], [3, 0], [4, 3], [4, 0], [5, 4], [5, 0], [6, 5], [6, 0]], candidate.call([[1, 2, 3, 4, 5, 6], [1, 2, 3, 4, 5, 6], [1, 1, 3, 4, 5, 6], [1, 2, 1, 4, 5, 6], [1, 2, 3, 1, 5, 6], [1, 2, 3, 4, 1, 6], [1, 2, 3, 4, 5, 1]], 1))\n    assert_equal([], candidate.call([], 1))\n    assert_equal([], candidate.call([[1]], 2))\n    assert_equal([[2, 2]], candidate.call([[], [1], [1, 2, 3]], 3))\n  end\nend\n"}
{"name": "HumanEval_123_get_odd_collatz", "language": "rb", "prompt": "# Given a positive integer n, return a sorted array that has the odd numbers in collatz sequence.\n# The Collatz conjecture is a conjecture in mathematics that concerns a sequence defined\n# as follows: start with any positive integer n. Then each term is obtained from the \n# previous term as follows: if the previous term is even, the next term is one half of \n# the previous term. If the previous term is odd, the next term is 3 times the previous\n# term plus 1. The conjecture is that no matter what value of n, the sequence will always reach 1.\n# Note: \n# 1. Collatz(1) is [1].\n# 2. returned array sorted in increasing order.\n# For example:\n# get_odd_collatz(5) returns [1, 5] # The collatz sequence for 5 is [5, 16, 8, 4, 2, 1], so the odd numbers are only 1, and 5.\n# >>> get_odd_collatz.call(5)\n# [1, 5]\ndef get_odd_collatz(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_123_get_odd_collatz.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_odd_collatz\n    candidate = method(:get_odd_collatz)\n    assert_equal([1, 5, 7, 11, 13, 17], candidate.call(14))\n    assert_equal([1, 5], candidate.call(5))\n    assert_equal([1, 3, 5], candidate.call(12))\n    assert_equal([1], candidate.call(1))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_123_get_odd_collatz", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_odd_collatz\n    candidate = method(:get_odd_collatz)\n    assert_equal([1, 5, 7, 11, 13, 17], candidate.call(14))\n    assert_equal([1, 5], candidate.call(5))\n    assert_equal([1, 3, 5], candidate.call(12))\n    assert_equal([1], candidate.call(1))\n  end\nend\n"}
{"name": "HumanEval_135_can_arrange", "language": "rb", "prompt": "# Create a function which returns the largest index of an element which\n# is not greater than or equal to the element immediately preceding it. If\n# no such element exists then return -1. The given array will not contain\n# duplicate values.\n# Examples:\n# >>> can_arrange.call([1, 2, 4, 3, 5])\n# 3\n# >>> can_arrange.call([1, 2, 3])\n# -1\ndef can_arrange(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_135_can_arrange.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_can_arrange\n    candidate = method(:can_arrange)\n    assert_equal(3, candidate.call([1, 2, 4, 3, 5]))\n    assert_equal(-1, candidate.call([1, 2, 4, 5]))\n    assert_equal(2, candidate.call([1, 4, 2, 5, 6, 7, 8, 9, 10]))\n    assert_equal(4, candidate.call([4, 8, 5, 7, 3]))\n    assert_equal(-1, candidate.call([]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_135_can_arrange", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_can_arrange\n    candidate = method(:can_arrange)\n    assert_equal(3, candidate.call([1, 2, 4, 3, 5]))\n    assert_equal(-1, candidate.call([1, 2, 4, 5]))\n    assert_equal(2, candidate.call([1, 4, 2, 5, 6, 7, 8, 9, 10]))\n    assert_equal(4, candidate.call([4, 8, 5, 7, 3]))\n    assert_equal(-1, candidate.call([]))\n  end\nend\n"}
{"name": "HumanEval_19_sort_numbers", "language": "rb", "prompt": "# Input is a space-delimited string of numberals from 'zero' to 'nine'.\n# Valid choices are 'zero', 'one', 'two', 'three', 'four', 'five', 'six', 'seven', 'eight' and 'nine'.\n# Return the string with numbers sorted from smallest to largest\n# >>> sort_numbers.call(\"three one five\")\n# \"one three five\"\ndef sort_numbers(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_19_sort_numbers.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_numbers\n    candidate = method(:sort_numbers)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"three\", candidate.call(\"three\"))\n    assert_equal(\"three five nine\", candidate.call(\"three five nine\"))\n    assert_equal(\"zero four five seven eight nine\", candidate.call(\"five zero four seven nine eight\"))\n    assert_equal(\"zero one two three four five six\", candidate.call(\"six five four three two one zero\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_19_sort_numbers", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_numbers\n    candidate = method(:sort_numbers)\n    assert_equal(\"\", candidate.call(\"\"))\n    assert_equal(\"three\", candidate.call(\"three\"))\n    assert_equal(\"three five nine\", candidate.call(\"three five nine\"))\n    assert_equal(\"zero four five seven eight nine\", candidate.call(\"five zero four seven nine eight\"))\n    assert_equal(\"zero one two three four five six\", candidate.call(\"six five four three two one zero\"))\n  end\nend\n"}
{"name": "HumanEval_65_circular_shift", "language": "rb", "prompt": "# Circular shift the digits of the integer x, shift the digits right by shift\n# and return the result as a string.\n# If shift > number of digits, return digits reversed.\n# >>> circular_shift.call(12, 1)\n# \"21\"\n# >>> circular_shift.call(12, 2)\n# \"12\"\ndef circular_shift(x, shift)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_65_circular_shift.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_circular_shift\n    candidate = method(:circular_shift)\n    assert_equal(\"001\", candidate.call(100, 2))\n    assert_equal(\"12\", candidate.call(12, 2))\n    assert_equal(\"79\", candidate.call(97, 8))\n    assert_equal(\"21\", candidate.call(12, 1))\n    assert_equal(\"11\", candidate.call(11, 101))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_65_circular_shift", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_circular_shift\n    candidate = method(:circular_shift)\n    assert_equal(\"001\", candidate.call(100, 2))\n    assert_equal(\"12\", candidate.call(12, 2))\n    assert_equal(\"79\", candidate.call(97, 8))\n    assert_equal(\"21\", candidate.call(12, 1))\n    assert_equal(\"11\", candidate.call(11, 101))\n  end\nend\n"}
{"name": "HumanEval_142_sum_squares", "language": "rb", "prompt": "# \"\n# This function will take an array of integers. For all entries in the array, the function shall square the integer entry if its index is a \n# multiple of 3 and will cube the integer entry if its index is a multiple of 4 and not a multiple of 3. The function will not \n# change the entries in the array whose indexes are not a multiple of 3 or 4. The function shall then return the sum of all entries. \n# Examples:\n# >>> lst\n# [1, 2, 3]\n# >>> lst\n# []\n# >>> lst\n# [-1, -5, 2, -1, -5]\ndef sum_squares(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_142_sum_squares.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_squares\n    candidate = method(:sum_squares)\n    assert_equal(6, candidate.call([1, 2, 3]))\n    assert_equal(14, candidate.call([1, 4, 9]))\n    assert_equal(0, candidate.call([]))\n    assert_equal(9, candidate.call([1, 1, 1, 1, 1, 1, 1, 1, 1]))\n    assert_equal(-3, candidate.call([-1, -1, -1, -1, -1, -1, -1, -1, -1]))\n    assert_equal(0, candidate.call([0]))\n    assert_equal(-126, candidate.call([-1, -5, 2, -1, -5]))\n    assert_equal(3030, candidate.call([-56, -99, 1, 0, -2]))\n    assert_equal(0, candidate.call([-1, 0, 0, 0, 0, 0, 0, 0, -1]))\n    assert_equal(-14196, candidate.call([-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37]))\n    assert_equal(-1448, candidate.call([-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_142_sum_squares", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_squares\n    candidate = method(:sum_squares)\n    assert_equal(6, candidate.call([1, 2, 3]))\n    assert_equal(14, candidate.call([1, 4, 9]))\n    assert_equal(0, candidate.call([]))\n    assert_equal(9, candidate.call([1, 1, 1, 1, 1, 1, 1, 1, 1]))\n    assert_equal(-3, candidate.call([-1, -1, -1, -1, -1, -1, -1, -1, -1]))\n    assert_equal(0, candidate.call([0]))\n    assert_equal(-126, candidate.call([-1, -5, 2, -1, -5]))\n    assert_equal(3030, candidate.call([-56, -99, 1, 0, -2]))\n    assert_equal(0, candidate.call([-1, 0, 0, 0, 0, 0, 0, 0, -1]))\n    assert_equal(-14196, candidate.call([-16, -9, -2, 36, 36, 26, -20, 25, -40, 20, -4, 12, -26, 35, 37]))\n    assert_equal(-1448, candidate.call([-1, -3, 17, -1, -15, 13, -1, 14, -14, -12, -5, 14, -14, 6, 13, 11, 16, 16, 4, 10]))\n  end\nend\n"}
{"name": "HumanEval_94_skjkasdkd", "language": "rb", "prompt": "# You are given an array of integers.\n# You need to find the largest prime value and return the sum of its digits.\n# Examples:\n# >>> skjkasdkd.call([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3])\n# 10\n# >>> skjkasdkd.call([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1])\n# 25\n# >>> skjkasdkd.call([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3])\n# 13\n# >>> skjkasdkd.call([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6])\n# 11\n# >>> skjkasdkd.call([0, 81, 12, 3, 1, 21])\n# 3\n# >>> skjkasdkd.call([0, 8, 1, 2, 1, 7])\n# 7\ndef skjkasdkd(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_94_skjkasdkd.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_skjkasdkd\n    candidate = method(:skjkasdkd)\n    assert_equal(10, candidate.call([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]))\n    assert_equal(25, candidate.call([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]))\n    assert_equal(13, candidate.call([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]))\n    assert_equal(11, candidate.call([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]))\n    assert_equal(3, candidate.call([0, 81, 12, 3, 1, 21]))\n    assert_equal(7, candidate.call([0, 8, 1, 2, 1, 7]))\n    assert_equal(19, candidate.call([8191]))\n    assert_equal(19, candidate.call([8191, 123456, 127, 7]))\n    assert_equal(10, candidate.call([127, 97, 8192]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_94_skjkasdkd", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_skjkasdkd\n    candidate = method(:skjkasdkd)\n    assert_equal(10, candidate.call([0, 3, 2, 1, 3, 5, 7, 4, 5, 5, 5, 2, 181, 32, 4, 32, 3, 2, 32, 324, 4, 3]))\n    assert_equal(25, candidate.call([1, 0, 1, 8, 2, 4597, 2, 1, 3, 40, 1, 2, 1, 2, 4, 2, 5, 1]))\n    assert_equal(13, candidate.call([1, 3, 1, 32, 5107, 34, 83278, 109, 163, 23, 2323, 32, 30, 1, 9, 3]))\n    assert_equal(11, candidate.call([0, 724, 32, 71, 99, 32, 6, 0, 5, 91, 83, 0, 5, 6]))\n    assert_equal(3, candidate.call([0, 81, 12, 3, 1, 21]))\n    assert_equal(7, candidate.call([0, 8, 1, 2, 1, 7]))\n    assert_equal(19, candidate.call([8191]))\n    assert_equal(19, candidate.call([8191, 123456, 127, 7]))\n    assert_equal(10, candidate.call([127, 97, 8192]))\n  end\nend\n"}
{"name": "HumanEval_8_sum_product", "language": "rb", "prompt": "# For a given array of integers, return an array consisting of a sum and a product of all the integers in an array.\n# Empty sum should be equal to 0 and empty product should be equal to 1.\n# >>> sum_product.call([])\n# [0, 1]\n# >>> sum_product.call([1, 2, 3, 4])\n# [10, 24]\ndef sum_product(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_8_sum_product.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_product\n    candidate = method(:sum_product)\n    assert_equal([0, 1], candidate.call([]))\n    assert_equal([3, 1], candidate.call([1, 1, 1]))\n    assert_equal([100, 0], candidate.call([100, 0]))\n    assert_equal([15, 105], candidate.call([3, 5, 7]))\n    assert_equal([10, 10], candidate.call([10]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_8_sum_product", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_product\n    candidate = method(:sum_product)\n    assert_equal([0, 1], candidate.call([]))\n    assert_equal([3, 1], candidate.call([1, 1, 1]))\n    assert_equal([100, 0], candidate.call([100, 0]))\n    assert_equal([15, 105], candidate.call([3, 5, 7]))\n    assert_equal([10, 10], candidate.call([10]))\n  end\nend\n"}
{"name": "HumanEval_102_choose_num", "language": "rb", "prompt": "# This function takes two positive numbers x and y and returns the\n# biggest even integer number that is in the range [x, y] inclusive. If \n# there's no such number, then the function should return -1.\n# For example:\n# >>> choose_num.call(12, 15)\n# 14\n# >>> choose_num.call(13, 12)\n# -1\ndef choose_num(x, y)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_102_choose_num.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_choose_num\n    candidate = method(:choose_num)\n    assert_equal(14, candidate.call(12, 15))\n    assert_equal(-1, candidate.call(13, 12))\n    assert_equal(12354, candidate.call(33, 12354))\n    assert_equal(-1, candidate.call(5234, 5233))\n    assert_equal(28, candidate.call(6, 29))\n    assert_equal(-1, candidate.call(27, 10))\n    assert_equal(-1, candidate.call(7, 7))\n    assert_equal(546, candidate.call(546, 546))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_102_choose_num", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_choose_num\n    candidate = method(:choose_num)\n    assert_equal(14, candidate.call(12, 15))\n    assert_equal(-1, candidate.call(13, 12))\n    assert_equal(12354, candidate.call(33, 12354))\n    assert_equal(-1, candidate.call(5234, 5233))\n    assert_equal(28, candidate.call(6, 29))\n    assert_equal(-1, candidate.call(27, 10))\n    assert_equal(-1, candidate.call(7, 7))\n    assert_equal(546, candidate.call(546, 546))\n  end\nend\n"}
{"name": "HumanEval_136_largest_smallest_integers", "language": "rb", "prompt": "# Create a function that returns an array (a, b), where 'a' is\n# the largest of negative integers, and 'b' is the smallest\n# of positive integers in an array.\n# If there is no negative or positive integers, return them as nil.\n# Examples:\n# >>> largest_smallest_integers.call([2, 4, 1, 3, 5, 7])\n# [nil, 1]\n# >>> largest_smallest_integers.call([])\n# [nil, nil]\n# >>> largest_smallest_integers.call([0])\n# [nil, nil]\ndef largest_smallest_integers(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_136_largest_smallest_integers.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_smallest_integers\n    candidate = method(:largest_smallest_integers)\n    assert_equal([nil, 1], candidate.call([2, 4, 1, 3, 5, 7]))\n    assert_equal([nil, 1], candidate.call([2, 4, 1, 3, 5, 7, 0]))\n    assert_equal([-2, 1], candidate.call([1, 3, 2, 4, 5, 6, -2]))\n    assert_equal([-7, 2], candidate.call([4, 5, 3, 6, 2, 7, -7]))\n    assert_equal([-9, 2], candidate.call([7, 3, 8, 4, 9, 2, 5, -9]))\n    assert_equal([nil, nil], candidate.call([]))\n    assert_equal([nil, nil], candidate.call([0]))\n    assert_equal([-1, nil], candidate.call([-1, -3, -5, -6]))\n    assert_equal([-1, nil], candidate.call([-1, -3, -5, -6, 0]))\n    assert_equal([-3, 1], candidate.call([-6, -4, -4, -3, 1]))\n    assert_equal([-3, 1], candidate.call([-6, -4, -4, -3, -100, 1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_136_largest_smallest_integers", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_largest_smallest_integers\n    candidate = method(:largest_smallest_integers)\n    assert_equal([nil, 1], candidate.call([2, 4, 1, 3, 5, 7]))\n    assert_equal([nil, 1], candidate.call([2, 4, 1, 3, 5, 7, 0]))\n    assert_equal([-2, 1], candidate.call([1, 3, 2, 4, 5, 6, -2]))\n    assert_equal([-7, 2], candidate.call([4, 5, 3, 6, 2, 7, -7]))\n    assert_equal([-9, 2], candidate.call([7, 3, 8, 4, 9, 2, 5, -9]))\n    assert_equal([nil, nil], candidate.call([]))\n    assert_equal([nil, nil], candidate.call([0]))\n    assert_equal([-1, nil], candidate.call([-1, -3, -5, -6]))\n    assert_equal([-1, nil], candidate.call([-1, -3, -5, -6, 0]))\n    assert_equal([-3, 1], candidate.call([-6, -4, -4, -3, 1]))\n    assert_equal([-3, 1], candidate.call([-6, -4, -4, -3, -100, 1]))\n  end\nend\n"}
{"name": "HumanEval_16_count_distinct_characters", "language": "rb", "prompt": "# Given a string, find out how many distinct characters (regardless of case) does it consist of\n# >>> count_distinct_characters.call(\"xyzXYZ\")\n# 3\n# >>> count_distinct_characters.call(\"Jerry\")\n# 4\ndef count_distinct_characters(string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_16_count_distinct_characters.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_distinct_characters\n    candidate = method(:count_distinct_characters)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(5, candidate.call(\"abcde\"))\n    assert_equal(5, candidate.call(\"abcdecadeCADE\"))\n    assert_equal(1, candidate.call(\"aaaaAAAAaaaa\"))\n    assert_equal(5, candidate.call(\"Jerry jERRY JeRRRY\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_16_count_distinct_characters", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_count_distinct_characters\n    candidate = method(:count_distinct_characters)\n    assert_equal(0, candidate.call(\"\"))\n    assert_equal(5, candidate.call(\"abcde\"))\n    assert_equal(5, candidate.call(\"abcdecadeCADE\"))\n    assert_equal(1, candidate.call(\"aaaaAAAAaaaa\"))\n    assert_equal(5, candidate.call(\"Jerry jERRY JeRRRY\"))\n  end\nend\n"}
{"name": "HumanEval_100_make_a_pile", "language": "rb", "prompt": "# Given a positive integer n, you have to make a pile of n levels of stones.\n# The first level has n stones.\n# The number of stones in the next level is:\n# - the next odd number if n is odd.\n# - the next even number if n is even.\n# Return the number of stones in each level in an array, where element at index\n# i represents the number of stones in the level (i+1).\n# Examples:\n# >>> make_a_pile.call(3)\n# [3, 5, 7]\ndef make_a_pile(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_100_make_a_pile.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_make_a_pile\n    candidate = method(:make_a_pile)\n    assert_equal([3, 5, 7], candidate.call(3))\n    assert_equal([4, 6, 8, 10], candidate.call(4))\n    assert_equal([5, 7, 9, 11, 13], candidate.call(5))\n    assert_equal([6, 8, 10, 12, 14, 16], candidate.call(6))\n    assert_equal([8, 10, 12, 14, 16, 18, 20, 22], candidate.call(8))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_100_make_a_pile", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_make_a_pile\n    candidate = method(:make_a_pile)\n    assert_equal([3, 5, 7], candidate.call(3))\n    assert_equal([4, 6, 8, 10], candidate.call(4))\n    assert_equal([5, 7, 9, 11, 13], candidate.call(5))\n    assert_equal([6, 8, 10, 12, 14, 16], candidate.call(6))\n    assert_equal([8, 10, 12, 14, 16, 18, 20, 22], candidate.call(8))\n  end\nend\n"}
{"name": "HumanEval_128_prod_signs", "language": "rb", "prompt": "# You are given an array arr of integers and you need to return\n# sum of magnitudes of integers multiplied by product of all signs\n# of each number in the array, represented by 1, -1 or 0.\n# Note: return nil for empty arr.\n# Example:\n# >>> prod_signs.call([1, 2, 2, -4])\n# 9\n# >>> prod_signs.call([0, 1])\n# 0\n# >>> prod_signs.call([])\n# nil\ndef prod_signs(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_128_prod_signs.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prod_signs\n    candidate = method(:prod_signs)\n    assert_equal(-9, candidate.call([1, 2, 2, -4]))\n    assert_equal(0, candidate.call([0, 1]))\n    assert_equal(-10, candidate.call([1, 1, 1, 2, 3, -1, 1]))\n    assert_equal(nil, candidate.call([]))\n    assert_equal(20, candidate.call([2, 4, 1, 2, -1, -1, 9]))\n    assert_equal(4, candidate.call([-1, 1, -1, 1]))\n    assert_equal(-4, candidate.call([-1, 1, 1, 1]))\n    assert_equal(0, candidate.call([-1, 1, 1, 0]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_128_prod_signs", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_prod_signs\n    candidate = method(:prod_signs)\n    assert_equal(-9, candidate.call([1, 2, 2, -4]))\n    assert_equal(0, candidate.call([0, 1]))\n    assert_equal(-10, candidate.call([1, 1, 1, 2, 3, -1, 1]))\n    assert_equal(nil, candidate.call([]))\n    assert_equal(20, candidate.call([2, 4, 1, 2, -1, -1, 9]))\n    assert_equal(4, candidate.call([-1, 1, -1, 1]))\n    assert_equal(-4, candidate.call([-1, 1, 1, 1]))\n    assert_equal(0, candidate.call([-1, 1, 1, 0]))\n  end\nend\n"}
{"name": "HumanEval_114_minSubArraySum", "language": "rb", "prompt": "# Given an array of integers nums, find the minimum sum of any non-empty sub-array\n# of nums.\n# Example\n# >>> minSubArraySum.call([2, 3, 4, 1, 2, 4])\n# 1\n# >>> minSubArraySum.call([-1, -2, -3])\n# -6\ndef minSubArraySum(nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_114_minSubArraySum.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_minSubArraySum\n    candidate = method(:minSubArraySum)\n    assert_equal(1, candidate.call([2, 3, 4, 1, 2, 4]))\n    assert_equal(-6, candidate.call([-1, -2, -3]))\n    assert_equal(-14, candidate.call([-1, -2, -3, 2, -10]))\n    assert_equal(-9999999999999999, candidate.call([-9999999999999999]))\n    assert_equal(0, candidate.call([0, 10, 20, 1000000]))\n    assert_equal(-6, candidate.call([-1, -2, -3, 10, -5]))\n    assert_equal(-6, candidate.call([100, -1, -2, -3, 10, -5]))\n    assert_equal(3, candidate.call([10, 11, 13, 8, 3, 4]))\n    assert_equal(-33, candidate.call([100, -33, 32, -1, 0, -2]))\n    assert_equal(-10, candidate.call([-10]))\n    assert_equal(7, candidate.call([7]))\n    assert_equal(-1, candidate.call([1, -1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_114_minSubArraySum", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_minSubArraySum\n    candidate = method(:minSubArraySum)\n    assert_equal(1, candidate.call([2, 3, 4, 1, 2, 4]))\n    assert_equal(-6, candidate.call([-1, -2, -3]))\n    assert_equal(-14, candidate.call([-1, -2, -3, 2, -10]))\n    assert_equal(-9999999999999999, candidate.call([-9999999999999999]))\n    assert_equal(0, candidate.call([0, 10, 20, 1000000]))\n    assert_equal(-6, candidate.call([-1, -2, -3, 10, -5]))\n    assert_equal(-6, candidate.call([100, -1, -2, -3, 10, -5]))\n    assert_equal(3, candidate.call([10, 11, 13, 8, 3, 4]))\n    assert_equal(-33, candidate.call([100, -33, 32, -1, 0, -2]))\n    assert_equal(-10, candidate.call([-10]))\n    assert_equal(7, candidate.call([7]))\n    assert_equal(-1, candidate.call([1, -1]))\n  end\nend\n"}
{"name": "HumanEval_15_string_sequence", "language": "rb", "prompt": "# Return a string containing space-delimited numbers starting from 0 upto n inclusive.\n# >>> string_sequence.call(0)\n# \"0\"\n# >>> string_sequence.call(5)\n# \"0 1 2 3 4 5\"\ndef string_sequence(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_15_string_sequence.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_sequence\n    candidate = method(:string_sequence)\n    assert_equal(\"0\", candidate.call(0))\n    assert_equal(\"0 1 2 3\", candidate.call(3))\n    assert_equal(\"0 1 2 3 4 5 6 7 8 9 10\", candidate.call(10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_15_string_sequence", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_sequence\n    candidate = method(:string_sequence)\n    assert_equal(\"0\", candidate.call(0))\n    assert_equal(\"0 1 2 3\", candidate.call(3))\n    assert_equal(\"0 1 2 3 4 5 6 7 8 9 10\", candidate.call(10))\n  end\nend\n"}
{"name": "HumanEval_154_cycpattern_check", "language": "rb", "prompt": "# You are given 2 words. You need to return true if the second word or any of its rotations is a substring in the first word\n# >>> cycpattern_check.call(\"abcd\", \"abd\")\n# false\n# >>> cycpattern_check.call(\"hello\", \"ell\")\n# true\n# >>> cycpattern_check.call(\"whassup\", \"psus\")\n# false\n# >>> cycpattern_check.call(\"abab\", \"baa\")\n# true\n# >>> cycpattern_check.call(\"efef\", \"eeff\")\n# false\n# >>> cycpattern_check.call(\"himenss\", \"simen\")\n# true\ndef cycpattern_check(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_154_cycpattern_check.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_cycpattern_check\n    candidate = method(:cycpattern_check)\n    assert_equal(false, candidate.call(\"xyzw\", \"xyw\"))\n    assert_equal(true, candidate.call(\"yello\", \"ell\"))\n    assert_equal(false, candidate.call(\"whattup\", \"ptut\"))\n    assert_equal(true, candidate.call(\"efef\", \"fee\"))\n    assert_equal(false, candidate.call(\"abab\", \"aabb\"))\n    assert_equal(true, candidate.call(\"winemtt\", \"tinem\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_154_cycpattern_check", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_cycpattern_check\n    candidate = method(:cycpattern_check)\n    assert_equal(false, candidate.call(\"xyzw\", \"xyw\"))\n    assert_equal(true, candidate.call(\"yello\", \"ell\"))\n    assert_equal(false, candidate.call(\"whattup\", \"ptut\"))\n    assert_equal(true, candidate.call(\"efef\", \"fee\"))\n    assert_equal(false, candidate.call(\"abab\", \"aabb\"))\n    assert_equal(true, candidate.call(\"winemtt\", \"tinem\"))\n  end\nend\n"}
{"name": "HumanEval_57_monotonic", "language": "rb", "prompt": "# Return true is array elements are monotonically increasing or decreasing.\n# >>> monotonic.call([1, 2, 4, 20])\n# true\n# >>> monotonic.call([1, 20, 4, 10])\n# false\n# >>> monotonic.call([4, 1, 0, -10])\n# true\ndef monotonic(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_57_monotonic.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_monotonic\n    candidate = method(:monotonic)\n    assert_equal(true, candidate.call([1, 2, 4, 10]))\n    assert_equal(true, candidate.call([1, 2, 4, 20]))\n    assert_equal(false, candidate.call([1, 20, 4, 10]))\n    assert_equal(true, candidate.call([4, 1, 0, -10]))\n    assert_equal(true, candidate.call([4, 1, 1, 0]))\n    assert_equal(false, candidate.call([1, 2, 3, 2, 5, 60]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 60]))\n    assert_equal(true, candidate.call([9, 9, 9, 9]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_57_monotonic", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_monotonic\n    candidate = method(:monotonic)\n    assert_equal(true, candidate.call([1, 2, 4, 10]))\n    assert_equal(true, candidate.call([1, 2, 4, 20]))\n    assert_equal(false, candidate.call([1, 20, 4, 10]))\n    assert_equal(true, candidate.call([4, 1, 0, -10]))\n    assert_equal(true, candidate.call([4, 1, 1, 0]))\n    assert_equal(false, candidate.call([1, 2, 3, 2, 5, 60]))\n    assert_equal(true, candidate.call([1, 2, 3, 4, 5, 60]))\n    assert_equal(true, candidate.call([9, 9, 9, 9]))\n  end\nend\n"}
{"name": "HumanEval_12_longest", "language": "rb", "prompt": "# Out of array of strings, return the longest one. Return the first one in case of multiple\n# strings of the same length. Return nil in case the input array is empty.\n# >>> longest.call([])\n# nil\n# >>> longest.call([\"a\", \"b\", \"c\"])\n# \"a\"\n# >>> longest.call([\"a\", \"bb\", \"ccc\"])\n# \"ccc\"\ndef longest(strings)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_12_longest.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_longest\n    candidate = method(:longest)\n    assert_equal(nil, candidate.call([]))\n    assert_equal(\"x\", candidate.call([\"x\", \"y\", \"z\"]))\n    assert_equal(\"zzzz\", candidate.call([\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_12_longest", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_longest\n    candidate = method(:longest)\n    assert_equal(nil, candidate.call([]))\n    assert_equal(\"x\", candidate.call([\"x\", \"y\", \"z\"]))\n    assert_equal(\"zzzz\", candidate.call([\"x\", \"yyy\", \"zzzz\", \"www\", \"kkkk\", \"abc\"]))\n  end\nend\n"}
{"name": "HumanEval_52_below_threshold", "language": "rb", "prompt": "# Return true if all numbers in the array l are below threshold t.\n# >>> below_threshold.call([1, 2, 4, 10], 100)\n# true\n# >>> below_threshold.call([1, 20, 4, 10], 5)\n# false\ndef below_threshold(l, t)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_52_below_threshold.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_below_threshold\n    candidate = method(:below_threshold)\n    assert_equal(true, candidate.call([1, 2, 4, 10], 100))\n    assert_equal(false, candidate.call([1, 20, 4, 10], 5))\n    assert_equal(true, candidate.call([1, 20, 4, 10], 21))\n    assert_equal(true, candidate.call([1, 20, 4, 10], 22))\n    assert_equal(true, candidate.call([1, 8, 4, 10], 11))\n    assert_equal(false, candidate.call([1, 8, 4, 10], 10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_52_below_threshold", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_below_threshold\n    candidate = method(:below_threshold)\n    assert_equal(true, candidate.call([1, 2, 4, 10], 100))\n    assert_equal(false, candidate.call([1, 20, 4, 10], 5))\n    assert_equal(true, candidate.call([1, 20, 4, 10], 21))\n    assert_equal(true, candidate.call([1, 20, 4, 10], 22))\n    assert_equal(true, candidate.call([1, 8, 4, 10], 11))\n    assert_equal(false, candidate.call([1, 8, 4, 10], 10))\n  end\nend\n"}
{"name": "HumanEval_75_is_multiply_prime", "language": "rb", "prompt": "# Write a function that returns true if the given number is the multiplication of 3 prime numbers\n# and false otherwise.\n# Knowing that (a) is less then 100. \n# Example:\n# >>> is_multiply_prime.call(30)\n# true\n# 30 = 2 * 3 * 5\ndef is_multiply_prime(a)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_75_is_multiply_prime.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_multiply_prime\n    candidate = method(:is_multiply_prime)\n    assert_equal(false, candidate.call(5))\n    assert_equal(true, candidate.call(30))\n    assert_equal(true, candidate.call(8))\n    assert_equal(false, candidate.call(10))\n    assert_equal(true, candidate.call(125))\n    assert_equal(true, candidate.call(105))\n    assert_equal(false, candidate.call(126))\n    assert_equal(false, candidate.call(729))\n    assert_equal(false, candidate.call(891))\n    assert_equal(true, candidate.call(1001))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_75_is_multiply_prime", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_is_multiply_prime\n    candidate = method(:is_multiply_prime)\n    assert_equal(false, candidate.call(5))\n    assert_equal(true, candidate.call(30))\n    assert_equal(true, candidate.call(8))\n    assert_equal(false, candidate.call(10))\n    assert_equal(true, candidate.call(125))\n    assert_equal(true, candidate.call(105))\n    assert_equal(false, candidate.call(126))\n    assert_equal(false, candidate.call(729))\n    assert_equal(false, candidate.call(891))\n    assert_equal(true, candidate.call(1001))\n  end\nend\n"}
{"name": "HumanEval_30_get_positive", "language": "rb", "prompt": "# Return only positive numbers in the array.\n# >>> get_positive.call([-1, 2, -4, 5, 6])\n# [2, 5, 6]\n# >>> get_positive.call([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10])\n# [5, 3, 2, 3, 9, 123, 1]\ndef get_positive(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_30_get_positive.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_positive\n    candidate = method(:get_positive)\n    assert_equal([4, 5, 6], candidate.call([-1, -2, 4, 5, 6]))\n    assert_equal([5, 3, 2, 3, 3, 9, 123, 1], candidate.call([5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]))\n    assert_equal([], candidate.call([-1, -2]))\n    assert_equal([], candidate.call([]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_30_get_positive", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_get_positive\n    candidate = method(:get_positive)\n    assert_equal([4, 5, 6], candidate.call([-1, -2, 4, 5, 6]))\n    assert_equal([5, 3, 2, 3, 3, 9, 123, 1], candidate.call([5, 3, -5, 2, 3, 3, 9, 0, 123, 1, -10]))\n    assert_equal([], candidate.call([-1, -2]))\n    assert_equal([], candidate.call([]))\n  end\nend\n"}
{"name": "HumanEval_33_sort_third", "language": "rb", "prompt": "# This function takes an array l and returns an array l' such that\n# l' is identical to l in the indicies that are not divisible by three, while its values at the indicies that are divisible by three are equal\n# to the values of the corresponding indicies of l, but sorted.\n# >>> sort_third.call([1, 2, 3])\n# [1, 2, 3]\n# >>> sort_third.call([5, 6, 3, 4, 8, 9, 2])\n# [2, 6, 3, 4, 8, 9, 5]\ndef sort_third(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_33_sort_third.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_third\n    candidate = method(:sort_third)\n    assert_equal([2, 6, 3, 4, 8, 9, 5], candidate.call([5, 6, 3, 4, 8, 9, 2]))\n    assert_equal([2, 8, 3, 4, 6, 9, 5], candidate.call([5, 8, 3, 4, 6, 9, 2]))\n    assert_equal([2, 6, 9, 4, 8, 3, 5], candidate.call([5, 6, 9, 4, 8, 3, 2]))\n    assert_equal([2, 6, 3, 4, 8, 9, 5, 1], candidate.call([5, 6, 3, 4, 8, 9, 2, 1]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_33_sort_third", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_third\n    candidate = method(:sort_third)\n    assert_equal([2, 6, 3, 4, 8, 9, 5], candidate.call([5, 6, 3, 4, 8, 9, 2]))\n    assert_equal([2, 8, 3, 4, 6, 9, 5], candidate.call([5, 8, 3, 4, 6, 9, 2]))\n    assert_equal([2, 6, 9, 4, 8, 3, 5], candidate.call([5, 6, 9, 4, 8, 3, 2]))\n    assert_equal([2, 6, 3, 4, 8, 9, 5, 1], candidate.call([5, 6, 3, 4, 8, 9, 2, 1]))\n  end\nend\n"}
{"name": "HumanEval_6_parse_nested_parens", "language": "rb", "prompt": "# Input to this function is a string represented multiple groups for nested parentheses separated by spaces.\n# For each of the group, output the deepest level of nesting of parentheses.\n# E.g. (()()) has maximum two levels of nesting while ((())) has three.\n# >>> parse_nested_parens.call(\"(()()) ((())) () ((())()())\")\n# [2, 3, 1, 3]\ndef parse_nested_parens(paren_string)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_6_parse_nested_parens.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_parse_nested_parens\n    candidate = method(:parse_nested_parens)\n    assert_equal([2, 3, 1, 3], candidate.call(\"(()()) ((())) () ((())()())\"))\n    assert_equal([1, 2, 3, 4], candidate.call(\"() (()) ((())) (((())))\"))\n    assert_equal([4], candidate.call(\"(()(())((())))\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_6_parse_nested_parens", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_parse_nested_parens\n    candidate = method(:parse_nested_parens)\n    assert_equal([2, 3, 1, 3], candidate.call(\"(()()) ((())) () ((())()())\"))\n    assert_equal([1, 2, 3, 4], candidate.call(\"() (()) ((())) (((())))\"))\n    assert_equal([4], candidate.call(\"(()(())((())))\"))\n  end\nend\n"}
{"name": "HumanEval_45_triangle_area", "language": "rb", "prompt": "# Given length of a side and high return area for a triangle.\n# >>> triangle_area.call(5, 3)\n# 7.5\ndef triangle_area(a, h)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_45_triangle_area.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triangle_area\n    candidate = method(:triangle_area)\n    assert_equal(7.5, candidate.call(5, 3))\n    assert_equal(2.0, candidate.call(2, 2))\n    assert_equal(40.0, candidate.call(10, 8))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_45_triangle_area", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_triangle_area\n    candidate = method(:triangle_area)\n    assert_equal(7.5, candidate.call(5, 3))\n    assert_equal(2.0, candidate.call(2, 2))\n    assert_equal(40.0, candidate.call(10, 8))\n  end\nend\n"}
{"name": "HumanEval_97_multiply", "language": "rb", "prompt": "# Complete the function that takes two integers and returns \n# the product of their unit digits.\n# Assume the input is always valid.\n# Examples:\n# >>> multiply.call(148, 412)\n# 16\n# >>> multiply.call(19, 28)\n# 72\n# >>> multiply.call(2020, 1851)\n# 0\n# >>> multiply.call(14, -15)\n# 20\ndef multiply(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_97_multiply.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_multiply\n    candidate = method(:multiply)\n    assert_equal(16, candidate.call(148, 412))\n    assert_equal(72, candidate.call(19, 28))\n    assert_equal(0, candidate.call(2020, 1851))\n    assert_equal(20, candidate.call(14, -15))\n    assert_equal(42, candidate.call(76, 67))\n    assert_equal(49, candidate.call(17, 27))\n    assert_equal(0, candidate.call(0, 1))\n    assert_equal(0, candidate.call(0, 0))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_97_multiply", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_multiply\n    candidate = method(:multiply)\n    assert_equal(16, candidate.call(148, 412))\n    assert_equal(72, candidate.call(19, 28))\n    assert_equal(0, candidate.call(2020, 1851))\n    assert_equal(20, candidate.call(14, -15))\n    assert_equal(42, candidate.call(76, 67))\n    assert_equal(49, candidate.call(17, 27))\n    assert_equal(0, candidate.call(0, 1))\n    assert_equal(0, candidate.call(0, 0))\n  end\nend\n"}
{"name": "HumanEval_4_mean_absolute_deviation", "language": "rb", "prompt": "# For a given array of input numbers, calculate Mean Absolute Deviation\n# around the mean of this dataset.\n# Mean Absolute Deviation is the average absolute difference between each\n# element and a centerpoint (mean in this case):\n# MAD = average | x - x_mean |\n# >>> mean_absolute_deviation.call([1.0, 2.0, 3.0, 4.0])\n# 1.0\ndef mean_absolute_deviation(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_4_mean_absolute_deviation.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_mean_absolute_deviation\n    candidate = method(:mean_absolute_deviation)\n    assert_equal(0.5, candidate.call([1.0, 2.0]))\n    assert_equal(1.0, candidate.call([1.0, 2.0, 3.0, 4.0]))\n    assert_equal(1.2, candidate.call([1.0, 2.0, 3.0, 4.0, 5.0]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_4_mean_absolute_deviation", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_mean_absolute_deviation\n    candidate = method(:mean_absolute_deviation)\n    assert_equal(0.5, candidate.call([1.0, 2.0]))\n    assert_equal(1.0, candidate.call([1.0, 2.0, 3.0, 4.0]))\n    assert_equal(1.2, candidate.call([1.0, 2.0, 3.0, 4.0, 5.0]))\n  end\nend\n"}
{"name": "HumanEval_58_common", "language": "rb", "prompt": "# Return sorted unique common elements for two arrays.\n# >>> common.call([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121])\n# [1, 5, 653]\n# >>> common.call([5, 3, 2, 8], [3, 2])\n# [2, 3]\ndef common(l1, l2)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_58_common.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_common\n    candidate = method(:common)\n    assert_equal([1, 5, 653], candidate.call([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121]))\n    assert_equal([2, 3], candidate.call([5, 3, 2, 8], [3, 2]))\n    assert_equal([2, 3, 4], candidate.call([4, 3, 2, 8], [3, 2, 4]))\n    assert_equal([], candidate.call([4, 3, 2, 8], []))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_58_common", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_common\n    candidate = method(:common)\n    assert_equal([1, 5, 653], candidate.call([1, 4, 3, 34, 653, 2, 5], [5, 7, 1, 5, 9, 653, 121]))\n    assert_equal([2, 3], candidate.call([5, 3, 2, 8], [3, 2]))\n    assert_equal([2, 3, 4], candidate.call([4, 3, 2, 8], [3, 2, 4]))\n    assert_equal([], candidate.call([4, 3, 2, 8], []))\n  end\nend\n"}
{"name": "HumanEval_156_int_to_mini_roman", "language": "rb", "prompt": "# Given a positive integer, obtain its roman numeral equivalent as a string,\n# and return it in lowercase.\n# Restrictions: 1 <= num <= 1000\n# Examples:\n# >>> int_to_mini_roman.call(19)\n# \"xix\"\n# >>> int_to_mini_roman.call(152)\n# \"clii\"\n# >>> int_to_mini_roman.call(426)\n# \"cdxxvi\"\ndef int_to_mini_roman(number)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_156_int_to_mini_roman.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_int_to_mini_roman\n    candidate = method(:int_to_mini_roman)\n    assert_equal(\"xix\", candidate.call(19))\n    assert_equal(\"clii\", candidate.call(152))\n    assert_equal(\"ccli\", candidate.call(251))\n    assert_equal(\"cdxxvi\", candidate.call(426))\n    assert_equal(\"d\", candidate.call(500))\n    assert_equal(\"i\", candidate.call(1))\n    assert_equal(\"iv\", candidate.call(4))\n    assert_equal(\"xliii\", candidate.call(43))\n    assert_equal(\"xc\", candidate.call(90))\n    assert_equal(\"xciv\", candidate.call(94))\n    assert_equal(\"dxxxii\", candidate.call(532))\n    assert_equal(\"cm\", candidate.call(900))\n    assert_equal(\"cmxciv\", candidate.call(994))\n    assert_equal(\"m\", candidate.call(1000))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_156_int_to_mini_roman", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_int_to_mini_roman\n    candidate = method(:int_to_mini_roman)\n    assert_equal(\"xix\", candidate.call(19))\n    assert_equal(\"clii\", candidate.call(152))\n    assert_equal(\"ccli\", candidate.call(251))\n    assert_equal(\"cdxxvi\", candidate.call(426))\n    assert_equal(\"d\", candidate.call(500))\n    assert_equal(\"i\", candidate.call(1))\n    assert_equal(\"iv\", candidate.call(4))\n    assert_equal(\"xliii\", candidate.call(43))\n    assert_equal(\"xc\", candidate.call(90))\n    assert_equal(\"xciv\", candidate.call(94))\n    assert_equal(\"dxxxii\", candidate.call(532))\n    assert_equal(\"cm\", candidate.call(900))\n    assert_equal(\"cmxciv\", candidate.call(994))\n    assert_equal(\"m\", candidate.call(1000))\n  end\nend\n"}
{"name": "HumanEval_67_fruit_distribution", "language": "rb", "prompt": "# In this task, you will be given a string that represents a number of apples and oranges \n# that are distributed in a basket of fruit this basket contains \n# apples, oranges, and mango fruits. Given the string that represents the total number of \n# the oranges and apples and an integer that represent the total number of the fruits \n# in the basket return the number of the mango fruits in the basket.\n# for examble:\n# >>> fruit_distribution.call(\"5 apples and 6 oranges\", 19)\n# 8\n# >>> fruit_distribution.call(\"0 apples and 1 oranges\", 3)\n# 2\n# >>> fruit_distribution.call(\"2 apples and 3 oranges\", 100)\n# 95\n# >>> fruit_distribution.call(\"100 apples and 1 oranges\", 120)\n# 19\ndef fruit_distribution(s, n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_67_fruit_distribution.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fruit_distribution\n    candidate = method(:fruit_distribution)\n    assert_equal(8, candidate.call(\"5 apples and 6 oranges\", 19))\n    assert_equal(10, candidate.call(\"5 apples and 6 oranges\", 21))\n    assert_equal(2, candidate.call(\"0 apples and 1 oranges\", 3))\n    assert_equal(2, candidate.call(\"1 apples and 0 oranges\", 3))\n    assert_equal(95, candidate.call(\"2 apples and 3 oranges\", 100))\n    assert_equal(0, candidate.call(\"2 apples and 3 oranges\", 5))\n    assert_equal(19, candidate.call(\"1 apples and 100 oranges\", 120))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_67_fruit_distribution", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_fruit_distribution\n    candidate = method(:fruit_distribution)\n    assert_equal(8, candidate.call(\"5 apples and 6 oranges\", 19))\n    assert_equal(10, candidate.call(\"5 apples and 6 oranges\", 21))\n    assert_equal(2, candidate.call(\"0 apples and 1 oranges\", 3))\n    assert_equal(2, candidate.call(\"1 apples and 0 oranges\", 3))\n    assert_equal(95, candidate.call(\"2 apples and 3 oranges\", 100))\n    assert_equal(0, candidate.call(\"2 apples and 3 oranges\", 5))\n    assert_equal(19, candidate.call(\"1 apples and 100 oranges\", 120))\n  end\nend\n"}
{"name": "HumanEval_112_reverse_delete", "language": "rb", "prompt": "# Task\n# We are given two strings s and c, you have to deleted all the characters in s that are equal to any character in c\n# then check if the result string is palindrome.\n# A string is called palindrome if it reads the same backward as forward.\n# You should return an array containing the result string and true/false for the check.\n# Example\n# >>> reverse_delete.call(\"abcde\", \"ae\")\n# [\"bcd\", false]\n# >>> reverse_delete.call(\"abcdef\", \"b\")\n# [\"acdef\", false]\n# >>> reverse_delete.call(\"abcdedcba\", \"ab\")\n# [\"cdedc\", true]\ndef reverse_delete(s, c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_112_reverse_delete.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_reverse_delete\n    candidate = method(:reverse_delete)\n    assert_equal([\"bcd\", false], candidate.call(\"abcde\", \"ae\"))\n    assert_equal([\"acdef\", false], candidate.call(\"abcdef\", \"b\"))\n    assert_equal([\"cdedc\", true], candidate.call(\"abcdedcba\", \"ab\"))\n    assert_equal([\"dik\", false], candidate.call(\"dwik\", \"w\"))\n    assert_equal([\"\", true], candidate.call(\"a\", \"a\"))\n    assert_equal([\"abcdedcba\", true], candidate.call(\"abcdedcba\", \"\"))\n    assert_equal([\"abcdedcba\", true], candidate.call(\"abcdedcba\", \"v\"))\n    assert_equal([\"abba\", true], candidate.call(\"vabba\", \"v\"))\n    assert_equal([\"\", true], candidate.call(\"mamma\", \"mia\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_112_reverse_delete", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_reverse_delete\n    candidate = method(:reverse_delete)\n    assert_equal([\"bcd\", false], candidate.call(\"abcde\", \"ae\"))\n    assert_equal([\"acdef\", false], candidate.call(\"abcdef\", \"b\"))\n    assert_equal([\"cdedc\", true], candidate.call(\"abcdedcba\", \"ab\"))\n    assert_equal([\"dik\", false], candidate.call(\"dwik\", \"w\"))\n    assert_equal([\"\", true], candidate.call(\"a\", \"a\"))\n    assert_equal([\"abcdedcba\", true], candidate.call(\"abcdedcba\", \"\"))\n    assert_equal([\"abcdedcba\", true], candidate.call(\"abcdedcba\", \"v\"))\n    assert_equal([\"abba\", true], candidate.call(\"vabba\", \"v\"))\n    assert_equal([\"\", true], candidate.call(\"mamma\", \"mia\"))\n  end\nend\n"}
{"name": "HumanEval_13_greatest_common_divisor", "language": "rb", "prompt": "# Return a greatest common divisor of two integers a and b\n# >>> greatest_common_divisor.call(3, 5)\n# 1\n# >>> greatest_common_divisor.call(25, 15)\n# 5\ndef greatest_common_divisor(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_13_greatest_common_divisor.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_greatest_common_divisor\n    candidate = method(:greatest_common_divisor)\n    assert_equal(1, candidate.call(3, 7))\n    assert_equal(5, candidate.call(10, 15))\n    assert_equal(7, candidate.call(49, 14))\n    assert_equal(12, candidate.call(144, 60))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_13_greatest_common_divisor", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_greatest_common_divisor\n    candidate = method(:greatest_common_divisor)\n    assert_equal(1, candidate.call(3, 7))\n    assert_equal(5, candidate.call(10, 15))\n    assert_equal(7, candidate.call(49, 14))\n    assert_equal(12, candidate.call(144, 60))\n  end\nend\n"}
{"name": "HumanEval_125_split_words", "language": "rb", "prompt": "# Given a string of words, return an array of words split on whitespace, if no whitespaces exists in the text you\n# should split on commas ',' if no commas exists you should return the number of lower-case letters with odd order in the\n# alphabet, ord('a') = 0, ord('b') = 1, ... ord('z') = 25\n# Examples\n# >>> split_words.call(\"Hello world!\")\n# [\"Hello\", \"world!\"]\n# >>> split_words.call(\"Hello,world!\")\n# [\"Hello\", \"world!\"]\n# >>> split_words.call(\"abcdef\")\n# 3\ndef split_words(txt)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_125_split_words.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_split_words\n    candidate = method(:split_words)\n    assert_equal([\"Hello\", \"world!\"], candidate.call(\"Hello world!\"))\n    assert_equal([\"Hello\", \"world!\"], candidate.call(\"Hello,world!\"))\n    assert_equal([\"Hello\", \"world,!\"], candidate.call(\"Hello world,!\"))\n    assert_equal([\"Hello,Hello,world\", \"!\"], candidate.call(\"Hello,Hello,world !\"))\n    assert_equal(3, candidate.call(\"abcdef\"))\n    assert_equal(2, candidate.call(\"aaabb\"))\n    assert_equal(1, candidate.call(\"aaaBb\"))\n    assert_equal(0, candidate.call(\"\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_125_split_words", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_split_words\n    candidate = method(:split_words)\n    assert_equal([\"Hello\", \"world!\"], candidate.call(\"Hello world!\"))\n    assert_equal([\"Hello\", \"world!\"], candidate.call(\"Hello,world!\"))\n    assert_equal([\"Hello\", \"world,!\"], candidate.call(\"Hello world,!\"))\n    assert_equal([\"Hello,Hello,world\", \"!\"], candidate.call(\"Hello,Hello,world !\"))\n    assert_equal(3, candidate.call(\"abcdef\"))\n    assert_equal(2, candidate.call(\"aaabb\"))\n    assert_equal(1, candidate.call(\"aaaBb\"))\n    assert_equal(0, candidate.call(\"\"))\n  end\nend\n"}
{"name": "HumanEval_116_sort_array", "language": "rb", "prompt": "# In this Kata, you have to sort an array of non-negative integers according to\n# number of ones in their binary representation in ascending order.\n# For similar number of ones, sort based on decimal value.\n# It must be implemented like this:\n# >>> sort_array.call([1, 5, 2, 3, 4])\n# [1, 2, 3, 4, 5]\n# >>> sort_array.call([-2, -3, -4, -5, -6])\n# [-6, -5, -4, -3, -2]\n# >>> sort_array.call([1, 0, 2, 3, 4])\n# [0, 1, 2, 3, 4]\ndef sort_array(arr)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_116_sort_array.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_array\n    candidate = method(:sort_array)\n    assert_equal([1, 2, 4, 3, 5], candidate.call([1, 5, 2, 3, 4]))\n    assert_equal([-4, -2, -6, -5, -3], candidate.call([-2, -3, -4, -5, -6]))\n    assert_equal([0, 1, 2, 4, 3], candidate.call([1, 0, 2, 3, 4]))\n    assert_equal([], candidate.call([]))\n    assert_equal([2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77], candidate.call([2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]))\n    assert_equal([32, 3, 5, 6, 12, 44], candidate.call([3, 6, 44, 12, 32, 5]))\n    assert_equal([2, 4, 8, 16, 32], candidate.call([2, 4, 8, 16, 32]))\n    assert_equal([2, 4, 8, 16, 32], candidate.call([2, 4, 8, 16, 32]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_116_sort_array", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_array\n    candidate = method(:sort_array)\n    assert_equal([1, 2, 4, 3, 5], candidate.call([1, 5, 2, 3, 4]))\n    assert_equal([-4, -2, -6, -5, -3], candidate.call([-2, -3, -4, -5, -6]))\n    assert_equal([0, 1, 2, 4, 3], candidate.call([1, 0, 2, 3, 4]))\n    assert_equal([], candidate.call([]))\n    assert_equal([2, 2, 4, 4, 3, 3, 5, 5, 5, 7, 77], candidate.call([2, 5, 77, 4, 5, 3, 5, 7, 2, 3, 4]))\n    assert_equal([32, 3, 5, 6, 12, 44], candidate.call([3, 6, 44, 12, 32, 5]))\n    assert_equal([2, 4, 8, 16, 32], candidate.call([2, 4, 8, 16, 32]))\n    assert_equal([2, 4, 8, 16, 32], candidate.call([2, 4, 8, 16, 32]))\n  end\nend\n"}
{"name": "HumanEval_28_concatenate", "language": "rb", "prompt": "# Concatenate array of strings into a single string\n# >>> concatenate.call([])\n# \"\"\n# >>> concatenate.call([\"a\", \"b\", \"c\"])\n# \"abc\"\ndef concatenate(strings)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_28_concatenate.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_concatenate\n    candidate = method(:concatenate)\n    assert_equal(\"\", candidate.call([]))\n    assert_equal(\"xyz\", candidate.call([\"x\", \"y\", \"z\"]))\n    assert_equal(\"xyzwk\", candidate.call([\"x\", \"y\", \"z\", \"w\", \"k\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_28_concatenate", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_concatenate\n    candidate = method(:concatenate)\n    assert_equal(\"\", candidate.call([]))\n    assert_equal(\"xyz\", candidate.call([\"x\", \"y\", \"z\"]))\n    assert_equal(\"xyzwk\", candidate.call([\"x\", \"y\", \"z\", \"w\", \"k\"]))\n  end\nend\n"}
{"name": "HumanEval_149_sorted_list_sum", "language": "rb", "prompt": "# Write a function that accepts an array of strings as a parameter,\n# deletes the strings that have odd lengths from it,\n# and returns the resulted array with a sorted order,\n# The array is always an array of strings and never an array of numbers,\n# and it may contain duplicates.\n# The order of the array should be ascending by length of each word, and you\n# should return the array sorted by that rule.\n# If two words have the same length, sort the array alphabetically.\n# The function should return an array of strings in sorted order.\n# You may assume that all words will have the same length.\n# For example:\n# >>> list_sort.call([\"aa\", \"a\", \"aaa\"])\n# [\"aa\"]\n# >>> list_sort.call([\"ab\", \"a\", \"aaa\", \"cd\"])\n# [\"ab\", \"cd\"]\ndef sorted_list_sum(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_149_sorted_list_sum.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sorted_list_sum\n    candidate = method(:sorted_list_sum)\n    assert_equal([\"aa\"], candidate.call([\"aa\", \"a\", \"aaa\"]))\n    assert_equal([\"AI\", \"asdf\", \"school\"], candidate.call([\"school\", \"AI\", \"asdf\", \"b\"]))\n    assert_equal([], candidate.call([\"d\", \"b\", \"c\", \"a\"]))\n    assert_equal([\"abcd\", \"dcba\"], candidate.call([\"d\", \"dcba\", \"abcd\", \"a\"]))\n    assert_equal([\"AI\", \"ai\", \"au\"], candidate.call([\"AI\", \"ai\", \"au\"]))\n    assert_equal([], candidate.call([\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]))\n    assert_equal([\"cc\", \"dd\", \"aaaa\", \"bbbb\"], candidate.call([\"aaaa\", \"bbbb\", \"dd\", \"cc\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_149_sorted_list_sum", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sorted_list_sum\n    candidate = method(:sorted_list_sum)\n    assert_equal([\"aa\"], candidate.call([\"aa\", \"a\", \"aaa\"]))\n    assert_equal([\"AI\", \"asdf\", \"school\"], candidate.call([\"school\", \"AI\", \"asdf\", \"b\"]))\n    assert_equal([], candidate.call([\"d\", \"b\", \"c\", \"a\"]))\n    assert_equal([\"abcd\", \"dcba\"], candidate.call([\"d\", \"dcba\", \"abcd\", \"a\"]))\n    assert_equal([\"AI\", \"ai\", \"au\"], candidate.call([\"AI\", \"ai\", \"au\"]))\n    assert_equal([], candidate.call([\"a\", \"b\", \"b\", \"c\", \"c\", \"a\"]))\n    assert_equal([\"cc\", \"dd\", \"aaaa\", \"bbbb\"], candidate.call([\"aaaa\", \"bbbb\", \"dd\", \"cc\"]))\n  end\nend\n"}
{"name": "HumanEval_7_filter_by_substring", "language": "rb", "prompt": "# Filter an input array of strings only for ones that contain given substring\n# >>> filter_by_substring.call([], \"a\")\n# []\n# >>> filter_by_substring.call([\"abc\", \"bacd\", \"cde\", \"array\"], \"a\")\n# [\"abc\", \"bacd\", \"array\"]\ndef filter_by_substring(strings, substring)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_7_filter_by_substring.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_by_substring\n    candidate = method(:filter_by_substring)\n    assert_equal([], candidate.call([], \"john\"))\n    assert_equal([\"xxx\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\"))\n    assert_equal([\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xx\"))\n    assert_equal([\"grunt\", \"prune\"], candidate.call([\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], \"run\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_7_filter_by_substring", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_filter_by_substring\n    candidate = method(:filter_by_substring)\n    assert_equal([], candidate.call([], \"john\"))\n    assert_equal([\"xxx\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"xxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xxx\"))\n    assert_equal([\"xxx\", \"aaaxxy\", \"xxxAAA\", \"xxx\"], candidate.call([\"xxx\", \"asd\", \"aaaxxy\", \"john doe\", \"xxxAAA\", \"xxx\"], \"xx\"))\n    assert_equal([\"grunt\", \"prune\"], candidate.call([\"grunt\", \"trumpet\", \"prune\", \"gruesome\"], \"run\"))\n  end\nend\n"}
{"name": "HumanEval_99_closest_integer", "language": "rb", "prompt": "# Create a function that takes a value (string) representing a number\n# and returns the closest integer to it. If the number is equidistant\n# from two integers, round it away from zero.\n# Examples\n# >>> closest_integer.call(\"10\")\n# 10\n# >>> closest_integer.call(\"15.3\")\n# 15\n# Note:\n# Rounding away from zero means that if the given number is equidistant\n# from two integers, the one you should return is the one that is the\n# farthest from zero. For example closest_integer(\"14.5\") should\n# return 15 and closest_integer(\"-14.5\") should return -15.\ndef closest_integer(value)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_99_closest_integer.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_closest_integer\n    candidate = method(:closest_integer)\n    assert_equal(10, candidate.call(\"10\"))\n    assert_equal(15, candidate.call(\"14.5\"))\n    assert_equal(-16, candidate.call(\"-15.5\"))\n    assert_equal(15, candidate.call(\"15.3\"))\n    assert_equal(0, candidate.call(\"0\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_99_closest_integer", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_closest_integer\n    candidate = method(:closest_integer)\n    assert_equal(10, candidate.call(\"10\"))\n    assert_equal(15, candidate.call(\"14.5\"))\n    assert_equal(-16, candidate.call(\"-15.5\"))\n    assert_equal(15, candidate.call(\"15.3\"))\n    assert_equal(0, candidate.call(\"0\"))\n  end\nend\n"}
{"name": "HumanEval_64_vowels_count", "language": "rb", "prompt": "# Write a function vowels_count which takes a string representing\n# a word as input and returns the number of vowels in the string.\n# Vowels in this case are 'a', 'e', 'i', 'o', 'u'. Here, 'y' is also a\n# vowel, but only when it is at the end of the given word.\n# Example:\n# >>> vowels_count.call(\"abcde\")\n# 2\n# >>> vowels_count.call(\"ACEDY\")\n# 3\ndef vowels_count(s)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_64_vowels_count.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_vowels_count\n    candidate = method(:vowels_count)\n    assert_equal(2, candidate.call(\"abcde\"))\n    assert_equal(3, candidate.call(\"Alone\"))\n    assert_equal(2, candidate.call(\"key\"))\n    assert_equal(1, candidate.call(\"bye\"))\n    assert_equal(2, candidate.call(\"keY\"))\n    assert_equal(1, candidate.call(\"bYe\"))\n    assert_equal(3, candidate.call(\"ACEDY\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_64_vowels_count", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_vowels_count\n    candidate = method(:vowels_count)\n    assert_equal(2, candidate.call(\"abcde\"))\n    assert_equal(3, candidate.call(\"Alone\"))\n    assert_equal(2, candidate.call(\"key\"))\n    assert_equal(1, candidate.call(\"bye\"))\n    assert_equal(2, candidate.call(\"keY\"))\n    assert_equal(1, candidate.call(\"bYe\"))\n    assert_equal(3, candidate.call(\"ACEDY\"))\n  end\nend\n"}
{"name": "HumanEval_158_find_max", "language": "rb", "prompt": "# Write a function that accepts an array of strings.\n# The array contains different words. Return the word with maximum number\n# of unique characters. If multiple strings have maximum number of unique\n# characters, return the one which comes first in lexicographical order.\n# >>> find_max.call([\"name\", \"of\", \"string\"])\n# \"string\"\n# >>> find_max.call([\"name\", \"enam\", \"game\"])\n# \"enam\"\n# >>> find_max.call([\"aaaaaaa\", \"bb\", \"cc\"])\n# \"aaaaaaa\"\ndef find_max(words)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_158_find_max.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_find_max\n    candidate = method(:find_max)\n    assert_equal(\"string\", candidate.call([\"name\", \"of\", \"string\"]))\n    assert_equal(\"enam\", candidate.call([\"name\", \"enam\", \"game\"]))\n    assert_equal(\"aaaaaaa\", candidate.call([\"aaaaaaa\", \"bb\", \"cc\"]))\n    assert_equal(\"abc\", candidate.call([\"abc\", \"cba\"]))\n    assert_equal(\"footbott\", candidate.call([\"play\", \"this\", \"game\", \"of\", \"footbott\"]))\n    assert_equal(\"gonna\", candidate.call([\"we\", \"are\", \"gonna\", \"rock\"]))\n    assert_equal(\"nation\", candidate.call([\"we\", \"are\", \"a\", \"mad\", \"nation\"]))\n    assert_equal(\"this\", candidate.call([\"this\", \"is\", \"a\", \"prrk\"]))\n    assert_equal(\"b\", candidate.call([\"b\"]))\n    assert_equal(\"play\", candidate.call([\"play\", \"play\", \"play\"]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_158_find_max", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_find_max\n    candidate = method(:find_max)\n    assert_equal(\"string\", candidate.call([\"name\", \"of\", \"string\"]))\n    assert_equal(\"enam\", candidate.call([\"name\", \"enam\", \"game\"]))\n    assert_equal(\"aaaaaaa\", candidate.call([\"aaaaaaa\", \"bb\", \"cc\"]))\n    assert_equal(\"abc\", candidate.call([\"abc\", \"cba\"]))\n    assert_equal(\"footbott\", candidate.call([\"play\", \"this\", \"game\", \"of\", \"footbott\"]))\n    assert_equal(\"gonna\", candidate.call([\"we\", \"are\", \"gonna\", \"rock\"]))\n    assert_equal(\"nation\", candidate.call([\"we\", \"are\", \"a\", \"mad\", \"nation\"]))\n    assert_equal(\"this\", candidate.call([\"this\", \"is\", \"a\", \"prrk\"]))\n    assert_equal(\"b\", candidate.call([\"b\"]))\n    assert_equal(\"play\", candidate.call([\"play\", \"play\", \"play\"]))\n  end\nend\n"}
{"name": "HumanEval_162_string_to_md5", "language": "rb", "prompt": "# Given a string 'text', return its md5 hash equivalent string.\n# If 'text' is an empty string, return nil.\n# >>> string_to_md5.call(\"Hello world\")\n# \"3e25960a79dbc69b674cd4ec67a72c62\"\ndef string_to_md5(text)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_162_string_to_md5.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_to_md5\n    candidate = method(:string_to_md5)\n    assert_equal(\"3e25960a79dbc69b674cd4ec67a72c62\", candidate.call(\"Hello world\"))\n    assert_equal(nil, candidate.call(\"\"))\n    assert_equal(\"0ef78513b0cb8cef12743f5aeb35f888\", candidate.call(\"A B C\"))\n    assert_equal(\"5f4dcc3b5aa765d61d8327deb882cf99\", candidate.call(\"password\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_162_string_to_md5", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_string_to_md5\n    candidate = method(:string_to_md5)\n    assert_equal(\"3e25960a79dbc69b674cd4ec67a72c62\", candidate.call(\"Hello world\"))\n    assert_equal(nil, candidate.call(\"\"))\n    assert_equal(\"0ef78513b0cb8cef12743f5aeb35f888\", candidate.call(\"A B C\"))\n    assert_equal(\"5f4dcc3b5aa765d61d8327deb882cf99\", candidate.call(\"password\"))\n  end\nend\n"}
{"name": "HumanEval_44_change_base", "language": "rb", "prompt": "# Change numerical base of input number x to base.\n# return string representation after the conversion.\n# base numbers are less than 10.\n# >>> change_base.call(8, 3)\n# \"22\"\n# >>> change_base.call(8, 2)\n# \"1000\"\n# >>> change_base.call(7, 2)\n# \"111\"\ndef change_base(x, base)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_44_change_base.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_change_base\n    candidate = method(:change_base)\n    assert_equal(\"22\", candidate.call(8, 3))\n    assert_equal(\"100\", candidate.call(9, 3))\n    assert_equal(\"11101010\", candidate.call(234, 2))\n    assert_equal(\"10000\", candidate.call(16, 2))\n    assert_equal(\"1000\", candidate.call(8, 2))\n    assert_equal(\"111\", candidate.call(7, 2))\n    assert_equal(\"2\", candidate.call(2, 3))\n    assert_equal(\"3\", candidate.call(3, 4))\n    assert_equal(\"4\", candidate.call(4, 5))\n    assert_equal(\"5\", candidate.call(5, 6))\n    assert_equal(\"6\", candidate.call(6, 7))\n    assert_equal(\"7\", candidate.call(7, 8))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_44_change_base", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_change_base\n    candidate = method(:change_base)\n    assert_equal(\"22\", candidate.call(8, 3))\n    assert_equal(\"100\", candidate.call(9, 3))\n    assert_equal(\"11101010\", candidate.call(234, 2))\n    assert_equal(\"10000\", candidate.call(16, 2))\n    assert_equal(\"1000\", candidate.call(8, 2))\n    assert_equal(\"111\", candidate.call(7, 2))\n    assert_equal(\"2\", candidate.call(2, 3))\n    assert_equal(\"3\", candidate.call(3, 4))\n    assert_equal(\"4\", candidate.call(4, 5))\n    assert_equal(\"5\", candidate.call(5, 6))\n    assert_equal(\"6\", candidate.call(6, 7))\n    assert_equal(\"7\", candidate.call(7, 8))\n  end\nend\n"}
{"name": "HumanEval_157_right_angle_triangle", "language": "rb", "prompt": "# Given the lengths of the three sides of a triangle. Return true if the three\n# sides form a right-angled triangle, false otherwise.\n# A right-angled triangle is a triangle in which one angle is right angle or \n# 90 degree.\n# Example:\n# >>> right_angle_triangle.call(3, 4, 5)\n# true\n# >>> right_angle_triangle.call(1, 2, 3)\n# false\ndef right_angle_triangle(a, b, c)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_157_right_angle_triangle.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_right_angle_triangle\n    candidate = method(:right_angle_triangle)\n    assert_equal(true, candidate.call(3, 4, 5))\n    assert_equal(false, candidate.call(1, 2, 3))\n    assert_equal(true, candidate.call(10, 6, 8))\n    assert_equal(false, candidate.call(2, 2, 2))\n    assert_equal(true, candidate.call(7, 24, 25))\n    assert_equal(false, candidate.call(10, 5, 7))\n    assert_equal(true, candidate.call(5, 12, 13))\n    assert_equal(true, candidate.call(15, 8, 17))\n    assert_equal(true, candidate.call(48, 55, 73))\n    assert_equal(false, candidate.call(1, 1, 1))\n    assert_equal(false, candidate.call(2, 2, 10))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_157_right_angle_triangle", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_right_angle_triangle\n    candidate = method(:right_angle_triangle)\n    assert_equal(true, candidate.call(3, 4, 5))\n    assert_equal(false, candidate.call(1, 2, 3))\n    assert_equal(true, candidate.call(10, 6, 8))\n    assert_equal(false, candidate.call(2, 2, 2))\n    assert_equal(true, candidate.call(7, 24, 25))\n    assert_equal(false, candidate.call(10, 5, 7))\n    assert_equal(true, candidate.call(5, 12, 13))\n    assert_equal(true, candidate.call(15, 8, 17))\n    assert_equal(true, candidate.call(48, 55, 73))\n    assert_equal(false, candidate.call(1, 1, 1))\n    assert_equal(false, candidate.call(2, 2, 10))\n  end\nend\n"}
{"name": "HumanEval_81_numerical_letter_grade", "language": "rb", "prompt": "# It is the last week of the semester and the teacher has to give the grades\n# to students. The teacher has been making her own algorithm for grading.\n# The only problem is, she has lost the code she used for grading.\n# She has given you an array of GPAs for some students and you have to write \n# a function that can output an array of letter grades using the following table:\n# GPA       |    Letter grade\n# 4.0                A+\n# > 3.7                A \n# > 3.3                A- \n# > 3.0                B+\n# > 2.7                B \n# > 2.3                B-\n# > 2.0                C+\n# > 1.7                C\n# > 1.3                C-\n# > 1.0                D+ \n# > 0.7                D \n# > 0.0                D-\n# 0.0                E\n# Example:\n# >>> grade_equation.call([4.0, 3, 1.7, 2, 3.5])\n# [\"A+\", \"B\", \"C-\", \"C\", \"A-\"]\ndef numerical_letter_grade(grades)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_81_numerical_letter_grade.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_numerical_letter_grade\n    candidate = method(:numerical_letter_grade)\n    assert_equal([\"A+\", \"B\", \"C-\", \"C\", \"A-\"], candidate.call([4.0, 3, 1.7, 2, 3.5]))\n    assert_equal([\"D+\"], candidate.call([1.2]))\n    assert_equal([\"D-\"], candidate.call([0.5]))\n    assert_equal([\"E\"], candidate.call([0.0]))\n    assert_equal([\"D\", \"D-\", \"C-\", \"B\", \"B+\"], candidate.call([1.0, 0.3, 1.5, 2.8, 3.3]))\n    assert_equal([\"E\", \"D-\"], candidate.call([0.0, 0.7]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_81_numerical_letter_grade", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_numerical_letter_grade\n    candidate = method(:numerical_letter_grade)\n    assert_equal([\"A+\", \"B\", \"C-\", \"C\", \"A-\"], candidate.call([4.0, 3, 1.7, 2, 3.5]))\n    assert_equal([\"D+\"], candidate.call([1.2]))\n    assert_equal([\"D-\"], candidate.call([0.5]))\n    assert_equal([\"E\"], candidate.call([0.0]))\n    assert_equal([\"D\", \"D-\", \"C-\", \"B\", \"B+\"], candidate.call([1.0, 0.3, 1.5, 2.8, 3.3]))\n    assert_equal([\"E\", \"D-\"], candidate.call([0.0, 0.7]))\n  end\nend\n"}
{"name": "HumanEval_5_intersperse", "language": "rb", "prompt": "# Insert a number 'delimeter' between every two consecutive elements of input array `numbers'\n# >>> intersperse.call([], 4)\n# []\n# >>> intersperse.call([1, 2, 3], 4)\n# [1, 4, 2, 4, 3]\ndef intersperse(numbers, delimeter)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_5_intersperse.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_intersperse\n    candidate = method(:intersperse)\n    assert_equal([], candidate.call([], 7))\n    assert_equal([5, 8, 6, 8, 3, 8, 2], candidate.call([5, 6, 3, 2], 8))\n    assert_equal([2, 2, 2, 2, 2], candidate.call([2, 2, 2], 2))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_5_intersperse", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_intersperse\n    candidate = method(:intersperse)\n    assert_equal([], candidate.call([], 7))\n    assert_equal([5, 8, 6, 8, 3, 8, 2], candidate.call([5, 6, 3, 2], 8))\n    assert_equal([2, 2, 2, 2, 2], candidate.call([2, 2, 2], 2))\n  end\nend\n"}
{"name": "HumanEval_146_specialFilter", "language": "rb", "prompt": "# Write a function that takes an array of numbers as input and returns \n# the number of elements in the array that are greater than 10 and both \n# first and last digits of a number are odd (1, 3, 5, 7, 9).\n# For example:\n# >>> specialFilter.call([15, -73, 14, -15])\n# 1\n# >>> specialFilter.call([33, -2, -3, 45, 21, 109])\n# 2\ndef specialFilter(nums)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_146_specialFilter.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_specialFilter\n    candidate = method(:specialFilter)\n    assert_equal(0, candidate.call([5, -2, 1, -5]))\n    assert_equal(1, candidate.call([15, -73, 14, -15]))\n    assert_equal(2, candidate.call([33, -2, -3, 45, 21, 109]))\n    assert_equal(4, candidate.call([43, -12, 93, 125, 121, 109]))\n    assert_equal(3, candidate.call([71, -2, -33, 75, 21, 19]))\n    assert_equal(0, candidate.call([1]))\n    assert_equal(0, candidate.call([]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_146_specialFilter", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_specialFilter\n    candidate = method(:specialFilter)\n    assert_equal(0, candidate.call([5, -2, 1, -5]))\n    assert_equal(1, candidate.call([15, -73, 14, -15]))\n    assert_equal(2, candidate.call([33, -2, -3, 45, 21, 109]))\n    assert_equal(4, candidate.call([43, -12, 93, 125, 121, 109]))\n    assert_equal(3, candidate.call([71, -2, -33, 75, 21, 19]))\n    assert_equal(0, candidate.call([1]))\n    assert_equal(0, candidate.call([]))\n  end\nend\n"}
{"name": "HumanEval_60_sum_to_n", "language": "rb", "prompt": "# sum_to_n is a function that sums numbers from 1 to n.\n# >>> sum_to_n.call(30)\n# 465\n# >>> sum_to_n.call(100)\n# 5050\n# >>> sum_to_n.call(5)\n# 15\n# >>> sum_to_n.call(10)\n# 55\n# >>> sum_to_n.call(1)\n# 1\ndef sum_to_n(n)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_60_sum_to_n.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_to_n\n    candidate = method(:sum_to_n)\n    assert_equal(1, candidate.call(1))\n    assert_equal(21, candidate.call(6))\n    assert_equal(66, candidate.call(11))\n    assert_equal(465, candidate.call(30))\n    assert_equal(5050, candidate.call(100))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_60_sum_to_n", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sum_to_n\n    candidate = method(:sum_to_n)\n    assert_equal(1, candidate.call(1))\n    assert_equal(21, candidate.call(6))\n    assert_equal(66, candidate.call(11))\n    assert_equal(465, candidate.call(30))\n    assert_equal(5050, candidate.call(100))\n  end\nend\n"}
{"name": "HumanEval_26_remove_duplicates", "language": "rb", "prompt": "# From an array of integers, remove all elements that occur more than once.\n# Keep order of elements left the same as in the input.\n# >>> remove_duplicates.call([1, 2, 3, 2, 4])\n# [1, 3, 4]\ndef remove_duplicates(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_26_remove_duplicates.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_remove_duplicates\n    candidate = method(:remove_duplicates)\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 2, 3, 4], candidate.call([1, 2, 3, 4]))\n    assert_equal([1, 4, 5], candidate.call([1, 2, 3, 2, 4, 3, 5]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_26_remove_duplicates", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_remove_duplicates\n    candidate = method(:remove_duplicates)\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 2, 3, 4], candidate.call([1, 2, 3, 4]))\n    assert_equal([1, 4, 5], candidate.call([1, 2, 3, 2, 4, 3, 5]))\n  end\nend\n"}
{"name": "HumanEval_163_generate_integers", "language": "rb", "prompt": "# Given two positive integers a and b, return the even digits between a\n# and b, in ascending order.\n# For example:\n# >>> generate_integers.call(2, 8)\n# [2, 4, 6, 8]\n# >>> generate_integers.call(8, 2)\n# [2, 4, 6, 8]\n# >>> generate_integers.call(10, 14)\n# []\ndef generate_integers(a, b)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_163_generate_integers.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_generate_integers\n    candidate = method(:generate_integers)\n    assert_equal([2, 4, 6, 8], candidate.call(2, 10))\n    assert_equal([2, 4, 6, 8], candidate.call(10, 2))\n    assert_equal([2, 4, 6, 8], candidate.call(132, 2))\n    assert_equal([], candidate.call(17, 89))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_163_generate_integers", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_generate_integers\n    candidate = method(:generate_integers)\n    assert_equal([2, 4, 6, 8], candidate.call(2, 10))\n    assert_equal([2, 4, 6, 8], candidate.call(10, 2))\n    assert_equal([2, 4, 6, 8], candidate.call(132, 2))\n    assert_equal([], candidate.call(17, 89))\n  end\nend\n"}
{"name": "HumanEval_9_rolling_max", "language": "rb", "prompt": "# From a given array of integers, generate an array of rolling maximum element found until given moment\n# in the sequence.\n# >>> rolling_max.call([1, 2, 3, 2, 3, 4, 2])\n# [1, 2, 3, 3, 3, 4, 4]\ndef rolling_max(numbers)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_9_rolling_max.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rolling_max\n    candidate = method(:rolling_max)\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 2, 3, 4], candidate.call([1, 2, 3, 4]))\n    assert_equal([4, 4, 4, 4], candidate.call([4, 3, 2, 1]))\n    assert_equal([3, 3, 3, 100, 100], candidate.call([3, 2, 3, 100, 3]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_9_rolling_max", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_rolling_max\n    candidate = method(:rolling_max)\n    assert_equal([], candidate.call([]))\n    assert_equal([1, 2, 3, 4], candidate.call([1, 2, 3, 4]))\n    assert_equal([4, 4, 4, 4], candidate.call([4, 3, 2, 1]))\n    assert_equal([3, 3, 3, 100, 100], candidate.call([3, 2, 3, 100, 3]))\n  end\nend\n"}
{"name": "HumanEval_3_below_zero", "language": "rb", "prompt": "# You're given an array of deposit and withdrawal operations on a bank account that starts with\n# zero balance. Your task is to detect if at any point the balance of account fallls below zero, and\n# at that point function should return true. Otherwise it should return false.\n# >>> below_zero.call([1, 2, 3])\n# false\n# >>> below_zero.call([1, 2, -4, 5])\n# true\ndef below_zero(operations)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_3_below_zero.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_below_zero\n    candidate = method(:below_zero)\n    assert_equal(false, candidate.call([]))\n    assert_equal(false, candidate.call([1, 2, -3, 1, 2, -3]))\n    assert_equal(true, candidate.call([1, 2, -4, 5, 6]))\n    assert_equal(false, candidate.call([1, -1, 2, -2, 5, -5, 4, -4]))\n    assert_equal(true, candidate.call([1, -1, 2, -2, 5, -5, 4, -5]))\n    assert_equal(true, candidate.call([1, -2, 2, -2, 5, -5, 4, -4]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_3_below_zero", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_below_zero\n    candidate = method(:below_zero)\n    assert_equal(false, candidate.call([]))\n    assert_equal(false, candidate.call([1, 2, -3, 1, 2, -3]))\n    assert_equal(true, candidate.call([1, 2, -4, 5, 6]))\n    assert_equal(false, candidate.call([1, -1, 2, -2, 5, -5, 4, -4]))\n    assert_equal(true, candidate.call([1, -1, 2, -2, 5, -5, 4, -5]))\n    assert_equal(true, candidate.call([1, -2, 2, -2, 5, -5, 4, -4]))\n  end\nend\n"}
{"name": "HumanEval_69_search", "language": "rb", "prompt": "# You are given a non-empty array of positive integers. Return the greatest integer that is greater than \n# zero, and has a frequency greater than or equal to the value of the integer itself. \n# The frequency of an integer is the number of times it appears in the array.\n# If no such a value exist, return -1.\n# Examples:\n# >>> search.call([4, 1, 2, 2, 3, 1])\n# 2\n# >>> search.call([1, 2, 2, 3, 3, 3, 4, 4, 4])\n# 3\n# >>> search.call([5, 5, 4, 4, 4])\n# -1\ndef search(lst)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_69_search.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_search\n    candidate = method(:search)\n    assert_equal(1, candidate.call([5, 5, 5, 5, 1]))\n    assert_equal(4, candidate.call([4, 1, 4, 1, 4, 4]))\n    assert_equal(-1, candidate.call([3, 3]))\n    assert_equal(8, candidate.call([8, 8, 8, 8, 8, 8, 8, 8]))\n    assert_equal(2, candidate.call([2, 3, 3, 2, 2]))\n    assert_equal(1, candidate.call([2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]))\n    assert_equal(2, candidate.call([3, 2, 8, 2]))\n    assert_equal(1, candidate.call([6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]))\n    assert_equal(-1, candidate.call([8, 8, 3, 6, 5, 6, 4]))\n    assert_equal(1, candidate.call([6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]))\n    assert_equal(1, candidate.call([1, 9, 10, 1, 3]))\n    assert_equal(5, candidate.call([6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]))\n    assert_equal(1, candidate.call([1]))\n    assert_equal(4, candidate.call([8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]))\n    assert_equal(2, candidate.call([2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]))\n    assert_equal(1, candidate.call([1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]))\n    assert_equal(4, candidate.call([9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]))\n    assert_equal(4, candidate.call([2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]))\n    assert_equal(2, candidate.call([9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]))\n    assert_equal(-1, candidate.call([5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]))\n    assert_equal(-1, candidate.call([10]))\n    assert_equal(2, candidate.call([9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]))\n    assert_equal(1, candidate.call([5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]))\n    assert_equal(1, candidate.call([7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]))\n    assert_equal(-1, candidate.call([3, 10, 10, 9, 2]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_69_search", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_search\n    candidate = method(:search)\n    assert_equal(1, candidate.call([5, 5, 5, 5, 1]))\n    assert_equal(4, candidate.call([4, 1, 4, 1, 4, 4]))\n    assert_equal(-1, candidate.call([3, 3]))\n    assert_equal(8, candidate.call([8, 8, 8, 8, 8, 8, 8, 8]))\n    assert_equal(2, candidate.call([2, 3, 3, 2, 2]))\n    assert_equal(1, candidate.call([2, 7, 8, 8, 4, 8, 7, 3, 9, 6, 5, 10, 4, 3, 6, 7, 1, 7, 4, 10, 8, 1]))\n    assert_equal(2, candidate.call([3, 2, 8, 2]))\n    assert_equal(1, candidate.call([6, 7, 1, 8, 8, 10, 5, 8, 5, 3, 10]))\n    assert_equal(-1, candidate.call([8, 8, 3, 6, 5, 6, 4]))\n    assert_equal(1, candidate.call([6, 9, 6, 7, 1, 4, 7, 1, 8, 8, 9, 8, 10, 10, 8, 4, 10, 4, 10, 1, 2, 9, 5, 7, 9]))\n    assert_equal(1, candidate.call([1, 9, 10, 1, 3]))\n    assert_equal(5, candidate.call([6, 9, 7, 5, 8, 7, 5, 3, 7, 5, 10, 10, 3, 6, 10, 2, 8, 6, 5, 4, 9, 5, 3, 10]))\n    assert_equal(1, candidate.call([1]))\n    assert_equal(4, candidate.call([8, 8, 10, 6, 4, 3, 5, 8, 2, 4, 2, 8, 4, 6, 10, 4, 2, 1, 10, 2, 1, 1, 5]))\n    assert_equal(2, candidate.call([2, 10, 4, 8, 2, 10, 5, 1, 2, 9, 5, 5, 6, 3, 8, 6, 4, 10]))\n    assert_equal(1, candidate.call([1, 6, 10, 1, 6, 9, 10, 8, 6, 8, 7, 3]))\n    assert_equal(4, candidate.call([9, 2, 4, 1, 5, 1, 5, 2, 5, 7, 7, 7, 3, 10, 1, 5, 4, 2, 8, 4, 1, 9, 10, 7, 10, 2, 8, 10, 9, 4]))\n    assert_equal(4, candidate.call([2, 6, 4, 2, 8, 7, 5, 6, 4, 10, 4, 6, 3, 7, 8, 8, 3, 1, 4, 2, 2, 10, 7]))\n    assert_equal(2, candidate.call([9, 8, 6, 10, 2, 6, 10, 2, 7, 8, 10, 3, 8, 2, 6, 2, 3, 1]))\n    assert_equal(-1, candidate.call([5, 5, 3, 9, 5, 6, 3, 2, 8, 5, 6, 10, 10, 6, 8, 4, 10, 7, 7, 10, 8]))\n    assert_equal(-1, candidate.call([10]))\n    assert_equal(2, candidate.call([9, 7, 7, 2, 4, 7, 2, 10, 9, 7, 5, 7, 2]))\n    assert_equal(1, candidate.call([5, 4, 10, 2, 1, 1, 10, 3, 6, 1, 8]))\n    assert_equal(1, candidate.call([7, 9, 9, 9, 3, 4, 1, 5, 9, 1, 2, 1, 1, 10, 7, 5, 6, 7, 6, 7, 7, 6]))\n    assert_equal(-1, candidate.call([3, 10, 10, 9, 2]))\n  end\nend\n"}
{"name": "HumanEval_61_correct_bracketing", "language": "rb", "prompt": "# brackets is a string of \"(\" and \")\".\n# return true if every opening bracket has a corresponding closing bracket.\n# >>> correct_bracketing.call(\"(\")\n# false\n# >>> correct_bracketing.call(\"()\")\n# true\n# >>> correct_bracketing.call(\"(()())\")\n# true\n# >>> correct_bracketing.call(\")(()\")\n# false\ndef correct_bracketing(brackets)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_61_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_correct_bracketing\n    candidate = method(:correct_bracketing)\n    assert_equal(true, candidate.call(\"()\"))\n    assert_equal(true, candidate.call(\"(()())\"))\n    assert_equal(true, candidate.call(\"()()(()())()\"))\n    assert_equal(true, candidate.call(\"()()((()()())())(()()(()))\"))\n    assert_equal(false, candidate.call(\"((()())))\"))\n    assert_equal(false, candidate.call(\")(()\"))\n    assert_equal(false, candidate.call(\"(\"))\n    assert_equal(false, candidate.call(\"((((\"))\n    assert_equal(false, candidate.call(\")\"))\n    assert_equal(false, candidate.call(\"(()\"))\n    assert_equal(false, candidate.call(\"()()(()())())(()\"))\n    assert_equal(false, candidate.call(\"()()(()())()))()\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_61_correct_bracketing", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_correct_bracketing\n    candidate = method(:correct_bracketing)\n    assert_equal(true, candidate.call(\"()\"))\n    assert_equal(true, candidate.call(\"(()())\"))\n    assert_equal(true, candidate.call(\"()()(()())()\"))\n    assert_equal(true, candidate.call(\"()()((()()())())(()()(()))\"))\n    assert_equal(false, candidate.call(\"((()())))\"))\n    assert_equal(false, candidate.call(\")(()\"))\n    assert_equal(false, candidate.call(\"(\"))\n    assert_equal(false, candidate.call(\"((((\"))\n    assert_equal(false, candidate.call(\")\"))\n    assert_equal(false, candidate.call(\"(()\"))\n    assert_equal(false, candidate.call(\"()()(()())())(()\"))\n    assert_equal(false, candidate.call(\"()()(()())()))()\"))\n  end\nend\n"}
{"name": "HumanEval_37_sort_even", "language": "rb", "prompt": "# This function takes an array l and returns an array l' such that\n# l' is identical to l in the odd indicies, while its values at the even indicies are equal\n# to the values of the even indicies of l, but sorted.\n# >>> sort_even.call([1, 2, 3])\n# [1, 2, 3]\n# >>> sort_even.call([5, 6, 3, 4])\n# [3, 6, 5, 4]\ndef sort_even(l)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_37_sort_even.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_even\n    candidate = method(:sort_even)\n    assert_equal([1, 2, 3], candidate.call([1, 2, 3]))\n    assert_equal([-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123], candidate.call([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]))\n    assert_equal([-12, 8, 3, 4, 5, 2, 12, 11, 23, -10], candidate.call([5, 8, -12, 4, 23, 2, 3, 11, 12, -10]))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_37_sort_even", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_sort_even\n    candidate = method(:sort_even)\n    assert_equal([1, 2, 3], candidate.call([1, 2, 3]))\n    assert_equal([-10, 3, -5, 2, -3, 3, 5, 0, 9, 1, 123], candidate.call([5, 3, -5, 2, -3, 3, 9, 0, 123, 1, -10]))\n    assert_equal([-12, 8, 3, 4, 5, 2, 12, 11, 23, -10], candidate.call([5, 8, -12, 4, 23, 2, 3, 11, 12, -10]))\n  end\nend\n"}
{"name": "HumanEval_54_same_chars", "language": "rb", "prompt": "# Check if two words have the same characters.\n# >>> same_chars.call(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\")\n# true\n# >>> same_chars.call(\"abcd\", \"dddddddabc\")\n# true\n# >>> same_chars.call(\"dddddddabc\", \"abcd\")\n# true\n# >>> same_chars.call(\"eabcd\", \"dddddddabc\")\n# false\n# >>> same_chars.call(\"abcd\", \"dddddddabce\")\n# false\n# >>> same_chars.call(\"eabcdzzzz\", \"dddzzzzzzzddddabc\")\n# false\ndef same_chars(s0, s1)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_54_same_chars.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_same_chars\n    candidate = method(:same_chars)\n    assert_equal(true, candidate.call(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"))\n    assert_equal(true, candidate.call(\"abcd\", \"dddddddabc\"))\n    assert_equal(true, candidate.call(\"dddddddabc\", \"abcd\"))\n    assert_equal(false, candidate.call(\"eabcd\", \"dddddddabc\"))\n    assert_equal(false, candidate.call(\"abcd\", \"dddddddabcf\"))\n    assert_equal(false, candidate.call(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"))\n    assert_equal(false, candidate.call(\"aabb\", \"aaccc\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_54_same_chars", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_same_chars\n    candidate = method(:same_chars)\n    assert_equal(true, candidate.call(\"eabcdzzzz\", \"dddzzzzzzzddeddabc\"))\n    assert_equal(true, candidate.call(\"abcd\", \"dddddddabc\"))\n    assert_equal(true, candidate.call(\"dddddddabc\", \"abcd\"))\n    assert_equal(false, candidate.call(\"eabcd\", \"dddddddabc\"))\n    assert_equal(false, candidate.call(\"abcd\", \"dddddddabcf\"))\n    assert_equal(false, candidate.call(\"eabcdzzzz\", \"dddzzzzzzzddddabc\"))\n    assert_equal(false, candidate.call(\"aabb\", \"aaccc\"))\n  end\nend\n"}
{"name": "HumanEval_56_correct_bracketing", "language": "rb", "prompt": "# brackets is a string of \"<\" and \">\".\n# return true if every opening bracket has a corresponding closing bracket.\n# >>> correct_bracketing.call(\"<\")\n# false\n# >>> correct_bracketing.call(\"<>\")\n# true\n# >>> correct_bracketing.call(\"<<><>>\")\n# true\n# >>> correct_bracketing.call(\"><<>\")\n# false\ndef correct_bracketing(brackets)\n", "doctests": "transform", "original": "/home/<USER>/repos/nuprl/MultiPL-E/datasets/../datasets/originals-with-cleaned-doctests/HumanEval_56_correct_bracketing.py", "prompt_terminology": "reworded", "tests": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_correct_bracketing\n    candidate = method(:correct_bracketing)\n    assert_equal(true, candidate.call(\"<>\"))\n    assert_equal(true, candidate.call(\"<<><>>\"))\n    assert_equal(true, candidate.call(\"<><><<><>><>\"))\n    assert_equal(true, candidate.call(\"<><><<<><><>><>><<><><<>>>\"))\n    assert_equal(false, candidate.call(\"<<<><>>>>\"))\n    assert_equal(false, candidate.call(\"><<>\"))\n    assert_equal(false, candidate.call(\"<\"))\n    assert_equal(false, candidate.call(\"<<<<\"))\n    assert_equal(false, candidate.call(\">\"))\n    assert_equal(false, candidate.call(\"<<>\"))\n    assert_equal(false, candidate.call(\"<><><<><>><>><<>\"))\n    assert_equal(false, candidate.call(\"<><><<><>><>>><>\"))\n  end\nend\n", "stop_tokens": ["\nclass", "\ndef", "\n#", "\n\n"], "task_id": "HumanEval_56_correct_bracketing", "test": "require 'test/unit'\nclass TestHumanEval < Test::Unit::TestCase\n  def test_correct_bracketing\n    candidate = method(:correct_bracketing)\n    assert_equal(true, candidate.call(\"<>\"))\n    assert_equal(true, candidate.call(\"<<><>>\"))\n    assert_equal(true, candidate.call(\"<><><<><>><>\"))\n    assert_equal(true, candidate.call(\"<><><<<><><>><>><<><><<>>>\"))\n    assert_equal(false, candidate.call(\"<<<><>>>>\"))\n    assert_equal(false, candidate.call(\"><<>\"))\n    assert_equal(false, candidate.call(\"<\"))\n    assert_equal(false, candidate.call(\"<<<<\"))\n    assert_equal(false, candidate.call(\">\"))\n    assert_equal(false, candidate.call(\"<<>\"))\n    assert_equal(false, candidate.call(\"<><><<><>><>><<>\"))\n    assert_equal(false, candidate.call(\"<><><<><>><>>><>\"))\n  end\nend\n"}
